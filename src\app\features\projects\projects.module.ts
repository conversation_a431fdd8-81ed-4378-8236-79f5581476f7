import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { AgGridModule } from 'ag-grid-angular';
import { DragScrollModule } from 'ngx-drag-scroll';
import { LottieModule } from 'ngx-lottie';

import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { SharedModule } from 'src/app/shared/shared.module';
import {
  PROJECTS_DECLARATIONS,
  ProjectsRoutingModule,
} from './projects.routing-components';
import { GoogleMapsModule } from '@angular/google-maps';

@NgModule({
  declarations: [...PROJECTS_DECLARATIONS],
  imports: [
    CommonModule,
    ProjectsRoutingModule,
    SharedModule,
    AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    DragScrollModule,
    NgxMatIntlTelInputComponent,
    GoogleMapsModule,
    FormsModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
      // to extend the I18n to use global en.json
      extend: true,
    }),
    LottieModule.forRoot({ player: playerFactory }),
  ],
  providers: [TranslateService],
})
export class ProjectsModule {}
