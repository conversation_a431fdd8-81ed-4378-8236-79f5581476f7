import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'property-details-submit',
  templateUrl: './property-details-submit.component.html',
})
export class PropertyDetailsSubmitComponent {
  @Input() viewListing: boolean;
  @Output() addMoreInfo: EventEmitter<any> = new EventEmitter<any>();
  @Output() closeForm: EventEmitter<any> = new EventEmitter<any>();
  constructor(private router: Router) { }


  nextFormModal() {
    this.addMoreInfo.emit();
  }

  closeModal() {
    this.closeForm.emit();
    if (this.viewListing) {
      this.router.navigate(['/properties/manage-listing']);
      return
    }
    this.router.navigate(['/properties/manage-properties']);
  }
}
