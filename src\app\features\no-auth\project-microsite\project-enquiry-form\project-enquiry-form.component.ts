import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { skipWhile, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import {
  formatBudget,
  onlyNumbers,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { AddProjectMicrositeLead } from 'src/app/reducers/Integration/integration.actions';
import { getProjectMSleadIsLoading } from 'src/app/reducers/Integration/integration.reducer';
import { FetchGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getProjectUnit } from 'src/app/reducers/project/project.reducer';
import { getMicrositeUserDetails } from 'src/app/reducers/property/property.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'project-enquiry-form',
  templateUrl: './project-enquiry-form.component.html',
})
export class ProjectEnquiryFormComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl = env.s3ImageBucketURL;
  enquiryForm: FormGroup;
  userData: any;
  unitList: Array<string> = [];
  MSProjectleadIsLoading: boolean = false;
  defaultCurrency: string = '';
  syncingCurrency: boolean = false;
  selectedLeadInfo: any;
  budgetInWords: string = '';
  currencyList: any[] = [];
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  @Input() defaultUnitId: string;
  @Input() isShowCloseBtn: any;
  @Input() projectUnit: any;
  @Input() projectInfo: any;
  preferredCountries = ['in'];
  hasInternationalSupport: boolean = false;
  @ViewChild('contactNoInput') contactNoInput: any;

  resetNumber: boolean = false;

  constructor(
    private fb: FormBuilder,
    public router: Router,
    public modalRef: BsModalRef,
    private store: Store<AppState>
  ) {
    this.store
      .select(getMicrositeUserDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.store.dispatch(new FetchGlobalSettingsAnonymous());

    this.enquiryForm = this.fb.group({
      name: ['', Validators.required],
      contactNo: ['', [this.contactNumberValidator('primary')]],
      email: ['', ValidationUtil.emailValidatorMinLength],
      budget: [null],
      unitId: [null],
      agreeTerms: [true],
      currency: [
        this.projectInfo?.monetaryInfo?.currency
          ? this.projectInfo?.monetaryInfo?.currency
          : this.defaultCurrency,
      ],
    });
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.enquiryForm?.get('currency')
          .patchValue(
            this.projectInfo?.monetaryInfo?.currency
              ? this.projectInfo?.monetaryInfo?.currency
              : this.defaultCurrency,
          );
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = data?.hasInternationalSupport ? data?.countries?.length ? [data.countries[0].code?.toLowerCase()] : ['in'] : ['in'];
        this.currencyList = data?.countries?.length
          ? data.countries[0].currencies
          : null;

      });
  }

  ngOnInit(): void {
    this.store
      .select(getProjectUnit)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.unitList = data;
      });

    this.enquiryForm.patchValue({
      unitId: this.defaultUnitId,
    });
    this.enquiryForm.get('currency')?.valueChanges.subscribe((value) => {
      if (!this.syncingCurrency) {
        this.syncingCurrency = true;
        this.enquiryForm.get('currency').setValue(value);
        this.syncingCurrency = false;
      }
    });

    this.enquiryForm.get('currency').valueChanges.subscribe((val) => {
      this.budgetInWords = formatBudget(this.enquiryForm.value.budget, val);
    });
    this.enquiryForm.get('budget').valueChanges?.subscribe((val) => {
      this.budgetInWords = formatBudget(val, this.enquiryForm.value.currency);
    });
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      if (numType == 'primary') {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          numType === 'primary' ? this.contactNoInput?.value : control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  postLead() {
    if (!this.enquiryForm.valid) {
      validateAllFormFields(this.enquiryForm);
      return;
    }
    // const leadData = this.enquiryForm.value;

    let userName: string;
    let serialNo: string;
    if (location.href.split('/').length > 1) {
      const router = location.href.split('/');
      userName = router[router.length - 2];
      serialNo = router[router.length - 1];
    }
    const payload = {
      name: this.enquiryForm.value.name,
      contactNo: this.enquiryForm.value.contactNo
        ? `${this.enquiryForm.value.contactNo?.toString()}`
        : null,
      serialNo: serialNo,
      userName: userName,
      email: this.enquiryForm.value.email,
      budget: this.enquiryForm.value.budget?.toString(),
      unitId: this.enquiryForm.value.unitId,
      currency: this.enquiryForm.value?.currency
        ? this.enquiryForm.value?.currency
        : this.defaultCurrency,
    };
    this.MSProjectleadIsLoading = true;
    this.store.dispatch(new AddProjectMicrositeLead(payload));
    this.store
      .select(getProjectMSleadIsLoading)
      .pipe(skipWhile((isLoading) => isLoading))
      .subscribe((isLoading: boolean) => {
        if (!isLoading) {
          this.MSProjectleadIsLoading = isLoading;
        }
      });
    this.enquiryForm?.reset();
    this.enquiryForm.patchValue({
      agreeTerms: true,
    });
    this.resetNumber = true;
    setTimeout(() => { this.resetNumber = false; }, 0);
    this.modalRef.hide();

  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
