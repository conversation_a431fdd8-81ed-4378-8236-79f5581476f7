<div *ngIf="params.colDef.field === 'Actions'" class="d-flex">
    <div *ngIf="canEdit" title="Edit" class="bg-accent-green icon-badge" (click)="editProject(params.data)"><span
            class="icon ic-pen m-auto ic-xxs"></span></div>
    <div *ngIf="canDelete" title="Delete" class="bg-light-red icon-badge" (click)="openDeleteProjectModal(params.data)">
        <span class="icon ic-delete m-auto ic-xxs"></span></div>
    <div title="Clone" class="bg-dark-red-40 icon-badge" (click)="duplicateBlock(params.data)"><span
            class="icon ic-split-arrows m-auto ic-xxxs"></span></div>
</div>
<div *ngIf="params.colDef.field === 'inventory'" class="d-flex">
    <img src="../../../../assets/projects/inventory.svg" alt="" />
</div>