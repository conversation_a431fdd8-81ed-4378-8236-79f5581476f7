import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TodoPayload } from 'src/app/core/interfaces/todo.interface';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class TodoService extends BaseService<TodoPayload> {
  getResourceUrl(): string {
    return 'todo';
  }

  constructor(protected httpClient: HttpClient) {
    super(httpClient);
  }
}
