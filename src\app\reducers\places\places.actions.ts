import { Action } from '@ngrx/store';

export enum PlacesActionTypes {
  FETCH_PLACES_LIST = '[PLACES] Fetch Places List',
  FETCH_PLACES_LIST_SUCCESS = '[PLACES] Fetch Places List Success',
  FETCH_QR_PLACES_LIST = '[PLACES] FetchQR Code Places List',
  FETCH_QR_PLACES_LIST_SUCCESS = '[PLACES] FetchQR Code Places List Success',
}
export class FetchPlacesList implements Action {
  readonly type: string = PlacesActionTypes.FETCH_PLACES_LIST;
  constructor(public payload: string, public page: number = 1, public pagesize: number = 500) { }
}

export class FetchPlacesListSuccess implements Action {
  readonly type: string = PlacesActionTypes.FETCH_PLACES_LIST_SUCCESS;
  constructor(public response: any[]) { }
}
export class FetchQRPlacesList implements Action {
  readonly type: string = PlacesActionTypes.FETCH_QR_PLACES_LIST;
  constructor(public payload: string, public page: number = 1, public pagesize: number = 500) { }
}

export class FetchQRPlacesListSuc<PERSON> implements Action {
  readonly type: string = PlacesActionTypes.FETCH_QR_PLACES_LIST_SUCCESS;
  constructor(public response: any[]) { }
}