import { Component } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { getAppName } from 'src/app/core/utils/common.util';

@Component({
  selector: 'ms-subscription',
  templateUrl: './ms-subscription.component.html',
})
export class MsSubscriptionComponent {
    getAppName = getAppName;
  
  constructor(public modalService: BsModalService,
    public modalRef: BsModalRef,) { }

}
