<div class="pt-30 pr-30 d-flex w-330 tb-w-100 tb-flex-center">
  <div class="border-right mb-40 mr-20"></div>
  <div>
    <div class="bg-white py-10 px-20 flex-center br-5 mb-24 tb-w-100">
      <img src="../../../../../assets/images/property-preview.svg" alt="property">
      <div class="ml-16">
        <div class="fw-600 text-sm">{{ 'PROPERTY.property-page-preview' | translate }}</div>
        <div class="fw-semi-bold text-xxs">{{ 'PROPERTY.property-finish-data' | translate }}</div>
      </div>
    </div>
    <div class="prop-phone tb-w-100">
      <img src="../../../../../assets/images/3-4star.svg" alt="pattern" class="position-absolute top-0 nleft-5">
      <div class="scrollbar h-100-225 scroll-hide">
        <div class="br-5 border-gray mb-8 bg-light-pearl position-relative overflow-hidden w-100">
          <img *ngIf="formValues?.highlighted" src="../../../../../assets/images/right-highlight-tag.svg"
            class="position-absolute right-0 top-12">
          <img class="w-100" *ngIf="imgPath?.length else dummyImg" [appImage]="getAWSImagePath(imgPath)"
            [type]="'property'" alt="property image" height="130" class="obj-cover w-100"
            [ngClass]="{'gray-scale': isPropertySoldOut}">
          <!-- Sold Out Watermark -->
          <div *ngIf="isPropertySoldOut" class="position-absolute w-100 h-100 flex-center"
            style="pointer-events: none;">
            <img src="assets/images/soldout.svg" alt="Sold Out"
              class="w-50 position-absolute ntop-50 left-50 pulse-animation">
          </div>

          <ng-template #dummyImg>
            <div class="pt-8 pl-8">
              <div class="text-sm fw-600">{{ 'PROPERTY.add-property-images' | translate }}</div>
              <div class="d-flex">
                <div class="mb-30">
                  <div class="d-flex mt-8">
                    <img src="../../../../../assets/images/check-dot.svg" alt="check" width="12" height="12">
                    <span class="text-xxs text-gray ml-4">{{ 'PROPERTY.property-quality-images' | translate }}</span>
                  </div>
                  <div class="d-flex mt-8">
                    <img src="../../../../../assets/images/check-dot.svg" alt="check" width="12" height="12">
                    <span class="text-xxs text-gray ml-4">{{ 'PROPERTY.quality-score' | translate }}</span>
                  </div>
                </div>
                <div class="justify-end align-end">
                  <img src="../../../../../assets/images/preview-gallery.svg" alt="Gallery" width="100" height="60">
                </div>
              </div>
            </div>
          </ng-template>
        </div>
        <div>
          <div class="justify-between">
            <div>
              <i class="text-accent-green text-xxs fw-600 word-break">"<span>{{formValues?.title ? formValues?.title
                  : 'property title'}}</span>"</i>
              <div class="text-sm fw-700 mt-4">
                <span *ngIf="formValues?.propertyType == 'Residential' && formValues?.propertySubType !== 'Plot'">
                  {{formValues?.noOfBHK ? (isListing ? getBRDisplayString(formValues?.noOfBHK) :
                  getBHKDisplayString(formValues?.noOfBHK)) : ''}}
                  {{formValues?.bhkType ? formValues?.bhkType : ''}}
                  <span *ngIf="formValues?.noOfBHK || formValues?.bhkType">,</span>
                </span>
                {{formValues?.propertySubType ? formValues?.propertySubType : 'property sub-type'}},
                {{formValues?.propertyType ? formValues?.propertyType : 'property type'}}
              </div>
            </div>
            <div class="align-end flex-column" [ngClass]="{'pe-none blinking': isGlobalSettingsLoading}">
              <div class="text-sm text-nowrap fw-600">{{propFormValues?.budget ?
                formatCurrency(propFormValues?.budget,
                'en-IN',propFormValues?.currency || currencySymbol) + '/-' : 'property budget'}}</div>
              <div class="neg-block" *ngIf="propFormValues?.negotiable">
                <ng-lottie [options]='tick' height='15px' width="10px" class="lottie"></ng-lottie>
                <span>{{'PROPERTY.PROPERTY_DETAIL.negotiable' | translate }}</span>
              </div>
            </div>
          </div>
          <div class="text-gray text-xxs mt-4 fw-600"><i> "{{formValues?.propertySubType ?
              formValues?.propertySubType : 'property sub-type'}} for
              {{formValues?.enquiredFor == 'None' ? 'enquiry' : formValues?.enquiredFor || 'enquiry'}}"
            </i>
            {{ 'GLOBAL.in' | translate }}
            <span class="border-bottom">{{ propFormValues?.city ? propFormValues?.city :
              'property location'}}</span>
          </div>
        </div>
        <div class="mt-12">
          <div class="align-center flex-wrap mt-10">
            <div *ngIf="formValues?.propertySize" class="align-center w-33 mb-8">
              <div class="bg-ash dot mr-4"><span class="icon ic-diamond ic-xxxs ic-black"></span>
              </div>
              <div class="text-truncate-1 break-all fw-600 text-xxs">{{formValues?.propertySize}}
                <span class="fw-400 text-xxxs">{{formValues?.areaUnit}}</span>
              </div>
            </div>
            <div *ngIf="propFormValues?.possessionDate" class="align-center w-33 mb-8">
              <div class="bg-ash dot mr-4"><span class="icon ic-key ic-xxxs ic-black"></span></div>
              <div class="text-truncate-1 break-all fw-600 text-xxs">
                <span *ngIf="moment(propFormValues?.possessionDate) <= currentDate else date">
                  {{ 'PROPERTY.ready-to-move' | translate }}
                </span>
                <ng-template #date>
                  <span>{{propFormValues.possessionDate | date: 'dd-MM-yyyy'}}</span>
                </ng-template>
              </div>
            </div>
            <ng-container *ngIf="formValues?.propertyType !== 'Agricultural'">
              <ng-container
                *ngIf="attrValues?.furnishStatus || attrValues?.facing || attrValues?.numberOfFloors || attrValues?.floorNumber else attributes">
                <ng-container *ngIf="formValues?.propertyType != 'Agricultural'">
                  <div *ngIf="attrValues?.furnishStatus" class="align-center w-33 mb-8">
                    <div class="bg-ash dot mr-4"><span class="icon ic-sofa ic-xxxs ic-black"></span>
                    </div>
                    <div class="text-truncate-1 break-all fw-600 text-xxs">{{attrValues.furnishStatus}}</div>
                  </div>
                  <div *ngIf="attrValues?.facing" class="align-center w-33 mb-8">
                    <div class="bg-ash dot mr-4"><span class="icon ic-compass ic-xxxs ic-black"></span>
                    </div>
                    <div class="text-truncate-1 break-all fw-600 text-xxs">{{attrValues.facing}}</div>
                  </div>
                  <div *ngIf="attrValues?.numberOfFloors || attrValues?.floorNumber" class="align-center w-33 mb-8">
                    <div class="bg-ash dot mr-4"><span class="icon ic-stairs ic-xxxs ic-black"></span>
                    </div>
                    <div class="text-xxs text-truncate">
                      <span class="fw-600" *ngIf="attrValues?.floorNumber">
                        {{attrValues.floorNumber}}
                        <sup>{{nth(attrValues.floorNumber)}}</sup></span>
                      <span *ngIf="attrValues?.numberOfFloors && attrValues?.floorNumber">
                        {{ 'GLOBAL.of' | translate }}</span>
                      {{attrValues.numberOfFloors}}
                    </div>
                  </div>
                </ng-container>
                <ng-container *ngIf="formValues?.propertyType == 'Residential'">
                  <ng-container *ngFor="let attr of attrMap">
                    <div *ngIf="liveAttrFormValues?.[attr.name]" class="align-center w-33 mb-8 text-nowrap">
                      <div class="bg-ash dot mr-4"><span class="icon ic-xxxs ic-black" [ngClass]="attr.icon"></span>
                      </div>
                      <div class="text-truncate">
                        <span class="fw-600 text-xxs">{{liveAttrFormValues?.[attr.name]}} </span>
                        <span class="text-xxxs">
                          {{ liveAttrFormValues?.[attr.name] > 1 ? attr.pluralShortName :
                          attr.singularShortName}}</span>
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
              </ng-container>
              <ng-template #attributes>
                <div class="bg-white br-8 p-8 flex-between w-100">
                  <img src="../../../../../assets/images/prop-attr.svg" alt="attr" class="mx-10">
                  <div class="ml-20">
                    <div class="fw-600 text-sm">{{ 'PROPERTY.add-property-attributes' | translate }}</div>
                    <div class="text-xxs mt-4">{{ 'PROPERTY.attributes-attract' | translate }}</div>
                  </div>
                </div>
              </ng-template>
            </ng-container>
          </div>
        </div>
        <ng-container *ngIf="filteredSelectedAmenitiesData?.length else noAmenities">
          <div class="fw-600 my-8">{{ 'PROPERTY.STEPS.amenities' | translate }}</div>
          <div class="d-flex flex-wrap">
            <ng-container *ngFor="let aType of filteredSelectedAmenitiesData">
              <div class="align-center-col text-center">
                <img *ngIf="aType.imageURL; else dummy" [src]="aType.imageURL" alt="amenity"
                  class="m-4 bg-green-350 rounded-circle">
                <div class="w-60 fw-semi-bold text-xxs">{{aType.amenityDisplayName}}</div>
              </div>
              <ng-template #dummy>
                <span class="icon ic-black ic-sm mb-4">
                  {{getFirstCharacter(aType?.amenityDisplayName)}}</span>
              </ng-template>
            </ng-container>
          </div>
        </ng-container>
        <ng-template #noAmenities>
          <div class="bg-white br-8 p-8 flex-between mt-12" *ngIf="!isFilteredSelectedAmenitiesDataLoading">
            <img src="../../../../../assets/images/prop-amenities.svg" alt="amenities" class="mx-10">
            <div class="ml-20">
              <div class="fw-600 text-sm">{{ 'PROPERTY.add-property-amenities' | translate }}</div>
              <div class="text-xxs mt-4">{{ 'PROPERTY.amenities-attract' | translate }}</div>
            </div>
          </div>
          <div *ngIf="isFilteredSelectedAmenitiesDataLoading">
            <div class="flex-center h-120">
              <div class="w-240 flex-center-col">
                <h5 class="mt-8">fetching amenities...</h5>
                <ng-lottie [options]="loading" class="nmt-20 h-80"></ng-lottie>
              </div>
            </div>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>