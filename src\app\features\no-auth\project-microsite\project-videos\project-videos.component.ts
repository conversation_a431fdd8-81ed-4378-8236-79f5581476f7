import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { getLocationDetailsByObj } from 'src/app/core/utils/common.util';

@Component({
  selector: 'project-videos',
  templateUrl: './project-videos.component.html',
})
export class ProjectVideosComponent implements OnInit {
  selectedVideo:any;
  @Input() projectInfo: any;
  getLocationDetailsByObj = getLocationDetailsByObj;
  @ViewChild('videoPlayer') videoPlayer: ElementRef;

  constructor() { }

  ngOnInit(): void {
    this.selectedVideo = this.projectInfo?.videos.length > 0 ? this.projectInfo?.videos[0] : null;
  }

  selectVideo(index: number) {   
    this.selectedVideo = this.projectInfo?.videos[index];
    this.videoPlayer.nativeElement.src = this.selectedVideo?.imageFilePath;
    this.videoPlayer.nativeElement.load();
  }

}
