import { Component, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { RATING_LIST } from 'src/app/app.constants';
@Component({
  selector: 'rating',
  templateUrl: './rating.component.html',
})
export class RatingComponent implements OnInit, OnChanges {
  @Input() userInput: string;
  @Output() onRatingInput: EventEmitter<string> = new EventEmitter<string>();
  ratings: Array<any> = RATING_LIST;
  userInputRating = new FormControl('');

  constructor() {}

  ngOnInit() { 
    this.updateRatingFormControl();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (!this.userInput)
      this.userInput = "0";
    if (changes.userInput) {
      this.updateRatingFormControl();
    }
  }

  updateRatingFormControl(): void {
    if (this.userInput) {
      this.userInputRating.setValue(this.userInput);
    }
  }

  emitRating(i: string) {
    this.onRatingInput.emit(i);
  }
}
