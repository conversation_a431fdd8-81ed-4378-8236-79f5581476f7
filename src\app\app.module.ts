import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import {
  OWL_DATE_TIME_LOCALE,
  OwlDateTimeModule,
  OwlNativeDateTimeModule,
} from '@danielmoncada/angular-datetime-picker';

import { APP_DECLARATIONS, AppRoutingModule } from 'src/app/app-routing.module';
import { AppComponent } from 'src/app/app.component';
import { APP_IMPORTS } from 'src/app/app.imports';
import { AuthInterceptor } from 'src/app/core/interceptors/auth.interceptor';
import { LoginService } from './services/controllers/login.service';
import { ManageSourceIndexDBService } from './services/shared/managesource.indexdb.service';
import { AlertComponent } from './features/subscription/alert/alert.component';

@NgModule({
  declarations: [AppComponent, ...APP_DECLARATIONS, AlertComponent],
  imports: [...APP_IMPORTS, AppRoutingModule],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: (appService: LoginService) => appService.doesTenantExist,
      multi: true,
      deps: [LoginService],
    },
    { provide: OWL_DATE_TIME_LOCALE, useValue: 'en-IN' },
  ],
  bootstrap: [AppComponent],
  exports: [OwlDateTimeModule, OwlNativeDateTimeModule],
})
export class AppModule { }
