import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { DeleteMember } from 'src/app/reducers/teams/teams.actions';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'member-actions',
  templateUrl: './member-actions.component.html',
})
export class MemberActionsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  teamId: string;
  canDelete: boolean = false;

  constructor(
    private modalRef: BsModalRef,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    public trackingService: TrackingService
  ) { }

  ngOnInit(): void {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canDelete = permissionsSet.has('Permissions.Teams.Delete');
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  deleteMember(event: any, data: any) {
    this.trackingService.trackFeature(`Web.ManageMembers.Button.Delete.Click`)
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: data?.name,
      fieldType: 'from team',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide)
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          const payload = {
            teamId: data?.teamId,
            userIds: [data?.id]
          };
          this._store.dispatch(new DeleteMember(payload));
        }
      });
  }
}
