import { Action } from '@ngrx/store';

export enum WhatsappCloudActionTypes {
  FETCH_WHATSAPP_CLOUD = '[WHATSAPP_CLOUD] Fetch Whatsapp Cloud',
  FETCH_WHATSAPP_CLOUD_SUCCESS = '[WHATSAPP_CLOUD] Fetch Whatsapp Cloud Success',
  ADD_WHATSAPP_CLOUD = '[WHATSAPP_CLOUD] Add Whatsapp Cloud',
  ADD_WHATSAPP_CLOUD_TEST = '[WHATSAPP_CLOUD] Add Whatsapp Cloud Test',
  SEND_EMAIL_FORM = '[UTILITY] Send Email Form',
}

export class FetchWhatsappCloud implements Action {
  readonly type: string = WhatsappCloudActionTypes.FETCH_WHATSAPP_CLOUD;
  constructor() { }
}
export class FetchWhatsappCloudSuccess implements Action {
  readonly type: string = WhatsappCloudActionTypes.FETCH_WHATSAPP_CLOUD_SUCCESS;
  constructor(public resp: string = '') { }
}
export class AddWhatsappCloud implements Action {
  readonly type: string = WhatsappCloudActionTypes.ADD_WHATSAPP_CLOUD;
  constructor(public payload: any) { }
}
export class AddWhatsappCloudTest implements Action {
  readonly type: string = WhatsappCloudActionTypes.ADD_WHATSAPP_CLOUD_TEST;
  constructor(public payload: any) { }
}

export class SendEmailForm implements Action {
  readonly type: string = WhatsappCloudActionTypes.SEND_EMAIL_FORM;
  constructor(public payload: any) { }
}