import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { debounceTime, Subject, takeUntil } from 'rxjs';

import { EMPTY_GUID, VALIDATION_CLEAR } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { Subscription } from 'src/app/core/interfaces/profile.interface';
import {
  assignToSort,
  getAppName,
  getAssignedToDetails,
  getTimeZoneDate,
  matchValidator,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { AddRoleComponent } from 'src/app/features/teams/manage-role/add-role/add-role.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import {
  getAddPermissions,
  getEditPermissions,
  getViewPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchSubscription,
  FetchTimeZoneInfo
} from 'src/app/reducers/profile/profile.actions';
import {
  getSubscription,
  getTimeZoneInfo,
} from 'src/app/reducers/profile/profile.reducers';
import {
  AddDepartment,
  AddDesignation,
  AddUser,
  DoesEmailExists,
  DoesPhoneNoExists,
  DoesUsernameExists,
  FetchDepartmentsList,
  FetchDesignationsList,
  FetchManageUserRolesList,
  FetchUserById,
  FetchUsersListForReassignment,
  UpdateUser,
} from 'src/app/reducers/teams/teams.actions';
import {
  doesEmailExists,
  doesPhoneNoxists,
  doesUserNameExists,
  getDepartmentsList,
  getDepartmentsListIsLoading,
  getDesignationsList,
  getDesignationsListIsLoading,
  getIsRolesLoading,
  getRolesList,
  getSelectedUser,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'add-user',
  templateUrl: './add-user.component.html',
})
export class AddUserComponent implements OnInit, OnDestroy {
  @Input() value: boolean;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchSubject = new Subject<string>();
  addUserModalRef: BsModalRef;
  closeModalRef: BsModalRef;
  departmentList: any[] = [];
  designationList: any[] = [];
  timeZoneList: any[] = [];
  isShowPassword: boolean = false;
  isShowConfPassword: boolean = false;
  password: boolean = false;
  randomPassword: string;
  userBasicInfoForm: FormGroup;
  doesUserNameExist: boolean = false;
  doesEmailExist: boolean = false;
  doesPhoneNoExist: boolean = false;
  noRoleSelected: boolean = false;
  newDesignation: string = '';
  newDepartment: string = '';
  emptyGuid = EMPTY_GUID;
  searchTerm: string;

  userRole: any = {
    admin: 'Admin',
    hr: 'HR',
    executive: 'Executive',
    manager: 'Manager',
    sales: 'Sales',
  };
  rolesList: any;
  selectedRoles: any[] = [];
  users: any[] = [];
  activeUsers: any[];
  selectedUserId: any;
  selectedUserInfo: any;
  getAssignedToDetails = getAssignedToDetails;
  moment = moment;
  getAppName = getAppName;
  canViewComponent: boolean = false;
  canAddRole: boolean = false;
  canViewRole: boolean;
  subscription: Subscription;
  preferredCountries = ['in'];
  hasInternationalSupport: boolean = false;
  @ViewChild('contactNoInput') contactNoInput: any;
  @ViewChild('alternateNoInput') alternateNoInput: any;
  isUserListLoading: boolean;
  isDesignationLoading: boolean;
  isDepartmentLoading: boolean;
  isRolesLoading: boolean;

  getTimeZoneDate = getTimeZoneDate;
  userData: any;
  showLeftNav: boolean = true;
  canViewUsers: any;

  constructor(
    private formBuilder: FormBuilder,
    private headerTitle: HeaderTitleService,
    private bsModalService: BsModalService,
    private modalRef: BsModalRef,
    private modalRefClose: BsModalRef,
    private store: Store<AppState>,
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    public trackingService: TrackingService
  ) {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.userBasicInfoForm = formBuilder.group({
      userId: ['', [Validators.required, Validators.pattern(/^\s*\S{6,}\s*$/)]],
      Selecteduser: [''],
      password: ['', Validators.required],
      confirmPassword: ['', Validators.required],
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      contactNo: ['', [this.contactNumberValidator(true)]],
      email: ['', [Validators.required, Validators.email]],
      altPhoneNumber: ['', [this.contactNumberValidator()]],
      homeLocation: [''],
      empNo: [''],
      reporting: [null],
      generalManager: [null],
      department: [null, Validators.required],
      newDepartment: ['', Validators.required],
      designation: [null, Validators.required],
      timeZone: [null],
      licenseNo: [null],
      shouldShowTimeZone: [false],
      newDesignation: ['', Validators.required],
      description: [''],
      roleValue: [''],
      myChoices: new FormArray([]),
      otherControls: [''],
    });

    if (!this.selectedUserId) {
      const confirmPasswordControl = this.userBasicInfoForm.get('confirmPassword');
      confirmPasswordControl.valueChanges.subscribe((value: string) => {
        if (!value) {
          const errors = confirmPasswordControl.errors;
          if (errors?.['match_password']) {
            delete errors['match_password'];
            confirmPasswordControl.setErrors(Object.keys(errors).length ? errors : null);
          }
        } else {
          this.userBasicInfoForm.get('confirmPassword').addValidators(
            matchValidator(
              this.userBasicInfoForm.get('password'),
              this.userBasicInfoForm.get('confirmPassword')
            )
          );
        }
      });

    }

    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {})?.id) {
        this.selectedUserId = params?.id;
        this.store.dispatch(new FetchUserById(this.selectedUserId));
      }
    });
    this.store.dispatch(new FetchUsersListForReassignment());
    this.store.dispatch(new FetchDesignationsList());
    this.store.dispatch(new FetchDepartmentsList());
    this.store.dispatch(new FetchSubscription(this.userData?.timeZoneInfo));
    this.store.dispatch(new FetchTimeZoneInfo());

    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Users') && this.router.url?.includes('edit'))
          this.canViewComponent = true;
      });
    this.store.select(getAddPermissions).subscribe((canAdd: any) => {
      if (canAdd?.includes('Users') && this.router.url?.includes('add'))
        this.canViewComponent = true;
      if (canAdd?.includes('Roles')) this.canAddRole = true;
    });
    this.store.select(getViewPermissions).subscribe((canView: any) => {
      this.canViewUsers = canView?.includes('Users');
      if (canView?.includes('Users') && this.router.url?.includes('add'))
        this.canViewComponent = true;
      if (canView?.includes('Roles')) this.canViewRole = true;
    });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = data?.hasInternationalSupport
          ? data.countries.length
            ? [data.countries[0].code.toLowerCase()]
            : ['in']
          : ['in'];
      });
    this.store.dispatch(new FetchManageUserRolesList());
  }

  ngOnInit(): void {
    this.shareDataService.showLeftNav$.subscribe(show => {
      this.showLeftNav = show;
    });
    this.searchSubject.pipe(debounceTime(300)).subscribe((searchText) => {
      this.trackingService.trackFeature(`Web.TeamUser.DataEntry.SearchRole.DataEntry`)
    });
    this.userBasicInfoForm.get('shouldShowTimeZone').valueChanges.subscribe((data: any) => {
      if (data) this.trackingService.trackFeature(`Web.TeamUser.Buttom.TimeZone.Click`)
    })
    if (this.selectedUserId) {
      //pacthing users form
      this.store
        .select(getSelectedUser)
        .pipe(takeUntil(this.stopper))
        .subscribe((item: any) => {
          this.selectedRoles = [];
          this.selectedUserInfo = item;
          this.selectedUserInfo = {
            ...this.selectedUserInfo,
            rolePermission: this.selectedUserInfo?.rolePermission,
          };
          this.selectedUserInfo?.rolePermission?.map((role: any) => {
            this.assignRole(true, role);
          });
          this.userBasicInfoForm.patchValue({
            ...this.selectedUserInfo,
            firstName: this.selectedUserInfo?.firstName,
            Selecteduser: this.selectedUserInfo?.userName,
            reporting:
              this.selectedUserInfo?.reportsTo?.id == EMPTY_GUID
                ? null
                : this.selectedUserInfo?.reportsTo?.id,
            generalManager:
              this.selectedUserInfo?.generalManager?.id == EMPTY_GUID
                ? null
                : this.selectedUserInfo?.generalManager?.id,
            department: this.selectedUserInfo?.department?.id,
            contactNo: this.selectedUserInfo?.phoneNumber,
            designation: this.selectedUserInfo?.designation?.id,
            homeLocation: this.selectedUserInfo?.address,
            altPhoneNumber: this.selectedUserInfo?.altPhoneNumber || null,
            timeZone: this.selectedUserInfo?.timeZoneInfo?.timeZoneDisplay
              ? this.selectedUserInfo?.timeZoneInfo?.timeZoneDisplay
              : null,
            licenseNo: this.selectedUserInfo?.licenseNo ? this.selectedUserInfo?.licenseNo : null,
            shouldShowTimeZone:
              this.selectedUserInfo?.shouldShowTimeZone || false,
          });
        });
    }

    this.store
      .select(getRolesList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data && data.items) {
          const sortedData = data.items?.slice().sort((a: any, b: any) => {
            const role1 = a.name.toLowerCase();
            const role2 = b.name.toLowerCase();
            if (role1 < role2) {
              return -1;
            }
            if (role1 > role2) {
              return 1;
            }
            return 0;
          });
          this.rolesList = sortedData;
          this.assignRole(true, this.selectedRoles);
          if (!this.selectedUserId) {
            let basicRole = this.rolesList?.filter(
              (role: any) => role.name === 'Basic'
            );
            this.assignRole(true, basicRole?.[0]);
          }
        }
      });

    this.store
      .select(getTimeZoneInfo)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.timeZoneList = data;
      });

    this.store
      .select(getIsRolesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isRolesLoading = data));

    this.store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isUserListLoading = data));
    this.store
      .select(getDesignationsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isDesignationLoading = data));
    this.store
      .select(getDepartmentsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isDepartmentLoading = data));
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.users = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        let loggedInUser = JSON.parse(localStorage.getItem('userDetails'));
        if (loggedInUser?.sub && !this.selectedUserId) {
          this.userBasicInfoForm.patchValue({
            ...this.selectedUserInfo,
            reporting: loggedInUser?.sub,
            generalManager: loggedInUser?.sub,
          });
        }
        this.users = assignToSort(this.users, '', true);
      });

    this.store
      .select(getDesignationsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.designationList = data
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
        if (this.newDesignation.length) {
          let newDesignation: any = this.designationList.filter(
            (item) => item?.name === this.newDesignation
          );
          if (newDesignation[0]?.id) {
            let userData = this.userBasicInfoForm.value;
            this.userBasicInfoForm.patchValue({
              ...userData,
              designation: newDesignation[0]?.id,
            });
          }
        }
      });

    this.store
      .select(getDepartmentsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.departmentList = data
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
        if (this.newDepartment.length) {
          let newDepartment: any = this.departmentList.filter(
            (item) => item?.name === this.newDepartment
          );
          if (newDepartment[0]?.id) {
            let userData = this.userBasicInfoForm.value;
            this.userBasicInfoForm.patchValue({
              ...userData,
              department: newDepartment[0]?.id,
            });
          }
        }
      });

    this.headerTitle.setLangTitle(
      this.selectedUserId ? 'USER.edit-user' : 'USER.add-user'
    );

    this.userBasicInfoForm
      .get('userId')
      .valueChanges.pipe(debounceTime(300))
      .subscribe((value: any) => {
        if (
          value &&
          this.userBasicInfoForm.controls.userId.status === 'VALID'
        ) {
          this.doesUserNameExists();
        }
      });
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  getSelectedCountryCodeAlternateNo(): any {
    return this.alternateNoInput?.selectedCountry;
  }

  contactNumberValidator(isPrimaryNo: boolean = false): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      if (isPrimaryNo) {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
        this.doesPhoneNoExists(control?.value);
      } else {
        const input = document.querySelector(
          '.alternateNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeAlternateNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          (isPrimaryNo
            ? this.contactNoInput?.value
            : this.alternateNoInput?.value) || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  setPassword() {
    this.userBasicInfoForm.setValue({
      // randomPassword: this.generatePassword(10),
    });
  }

  get f() {
    return this.userBasicInfoForm.controls;
  }

  isRoleAssignedToUser(roleId: string) {
    return this.selectedRoles?.filter((role: any) => roleId == role.roleId)?.[0]
      ?.enabled;
  }

  assignRole(isChecked: boolean, role: any, canSortRoles: boolean = true) {
    if (isChecked) {
      let userRole = {
        roleId: role?.id,
        description: role?.description,
        enabled: true,
        roleName: role?.name,
      };
      this.noRoleSelected = false;
      this.rolesList = this.rolesList || [];
      if (userRole.roleId) {
        this.selectedRoles.push(userRole);
      }
    } else {
      this.rolesList = this.rolesList || [];
      if (
        this.selectedUserInfo?.rolePermission.some(
          (item: any) => item?.id == role?.id
        )
      ) {
        this.selectedRoles = this.selectedRoles.filter(
          (item: any) => role?.id != item.roleId
        );
        let userRole = {
          roleId: role?.id,
          description: role?.description,
          enabled: false,
          roleName: role?.name,
        };
        this.selectedRoles.push(userRole);
      } else {
        this.selectedRoles = this.selectedRoles.filter(
          (item: any) => role?.id != item.roleId
        );
      }
    }
    if (canSortRoles) this.sortRoles();
  }

  sortRoles() {
    this.rolesList.sort((a: any, b: any) => {
      const aIsSelected = this.selectedRoles.some(
        (item) => item.roleId === a.id
      );
      const bIsSelected = this.selectedRoles.some(
        (item) => item.roleId === b.id
      );
      if (aIsSelected && !bIsSelected) {
        return -1;
      } else if (!aIsSelected && bIsSelected) {
        return 1;
      } else {
        return a.name.localeCompare(b.name);
      }
    });
  }

  addNewDesignation = (designation: string = '') => {
    if (designation.length) {
      this.store.dispatch(new AddDesignation(designation));
      this.newDesignation = designation;
    }
  };

  addNewDepartment = (department: string = '') => {
    if (department.length) {
      this.store.dispatch(new AddDepartment(department));
      this.newDepartment = department;
    }
  };

  postData(licenseBoughtModal: TemplateRef<any>) {
    //TODO: for the below code lets take it to a new formGroup
    toggleValidation(VALIDATION_CLEAR, this.userBasicInfoForm, 'newDepartment');
    toggleValidation(
      VALIDATION_CLEAR,
      this.userBasicInfoForm,
      'newDesignation'
    );
    const userData = this.userBasicInfoForm.value;
    if (this.selectedUserId) {
      toggleValidation(VALIDATION_CLEAR, this.userBasicInfoForm, 'password');
      toggleValidation(
        VALIDATION_CLEAR,
        this.userBasicInfoForm,
        'confirmPassword'
      );
    }
    if (
      !this.userBasicInfoForm.valid ||
      userData.password !== userData.confirmPassword ||
      this.doesUserNameExist ||
      this.doesPhoneNoExist ||
      this.doesEmailExist
    ) {
      validateAllFormFields(this.userBasicInfoForm);
      return;
    }
    // Filter out objects without roleId key
    // this.selectedRoles = this.selectedRoles.filter((item) => item.roleId);

    // // Use Set object to remove duplicates
    // this.selectedRoles = Array.from(new Set(this.selectedRoles.map((item) => JSON.stringify(item)))).map((item) => JSON.parse(item));
    let selectedTZ = this.timeZoneList.find(
      (item: any) => item.displayName === userData?.timeZone
    );

    let adduserData = {
      ...(this.selectedUserId && {
        userId: this.selectedUserInfo?.userId,
        ...this.selectedUserInfo,
      }),
      userName: this.selectedUserId
        ? userData.Selecteduser.trim()
        : userData.userId.trim(),
      password: userData.password?.toString(),
      confirmPassword: userData.password?.toString(),
      firstName: userData.firstName.trim(),
      lastName: userData.lastName.trim(),
      email: userData.email,
      phoneNumber: userData.contactNo?.toString(),
      altPhoneNumber: userData.altPhoneNumber
        ? userData.altPhoneNumber?.toString()
        : '',
      address: userData.homeLocation,
      empNo: userData.empNo,
      reportsTo: userData.reporting,
      generalManager: userData?.generalManager,
      departmentId: userData.department,
      designationId: userData.designation,
      description: userData.description,
      timeZoneInfo: selectedTZ?.displayName ? {
        timeZoneId: selectedTZ?.ianaZoneId,
        timeZoneDisplay: selectedTZ?.displayName,
        baseUTcOffset: selectedTZ?.baseUtcOffset,
        timeZoneName: selectedTZ?.displayName
      } : null,
      timeZone: selectedTZ?.displayName || null,
      timeZoneId: selectedTZ?.ianaZoneId || null,
      baseUTcOffset: selectedTZ?.baseUtcOffset || null,
      shouldShowTimeZone:
        selectedTZ?.displayName && userData.shouldShowTimeZone,
      userRoles: this.selectedRoles.filter((item) => item.roleId),
      rolePermission: null,
      licenseNo: userData?.licenseNo,
    };
    if (this.selectedUserId) {
      if (
        !this.selectedRoles.some(
          (item: any) => item.enabled === true && item?.roleName !== 'Default'
        ) ||
        (this.selectedRoles.length == 1 &&
          this.selectedRoles?.[0]?.roleName == 'Default')
      ) {
        this.noRoleSelected = true;
        return;
      }
      this.store.dispatch(new UpdateUser(this.selectedUserId, adduserData));
      this.goToManageUser();
      this.userBasicInfoForm.reset();
      this.selectedRoles = [];
    } else {
      this.store
        .select(getSubscription)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          const { licenseBought, activeUsers } = data;
          if (licenseBought <= activeUsers) {
            this.modalRef = this.modalService.show(licenseBoughtModal, {
              class: 'modal-600 top-modal ip-modal-unset',
            });
          } else {
            this.store.dispatch(new AddUser(adduserData));
            this.goToManageUser();
            this.userBasicInfoForm.reset();
            this.selectedRoles = [];
          }
        });
    }
    this.assignRole(true, this.selectedRoles);
  }

  goToManageUser() {
    return this.router.navigate(['teams/manage-user']);
  }

  checkLicenseBought() {
    this.goToManageUser();
    this.userBasicInfoForm.reset();
    this.selectedRoles = [];
    this.modalRef.hide();
  }

  doesUserNameExists() {
    if (!this.selectedUserId) {
      let userName = this.userBasicInfoForm.value.userId;
      this.store.dispatch(new DoesUsernameExists(userName));
      this.store.dispatch(new LoaderHide());
      this.store
        .select(doesUserNameExists)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.doesUserNameExist = data;
        });
    }
  }

  doesEmailExists() {
    if (!this.userBasicInfoForm.controls.email.valid) {
      return;
    }
    let email = this.userBasicInfoForm.value.email;
    this.store.dispatch(new DoesEmailExists(email));
    this.store.dispatch(new LoaderHide());
    this.store
      .select(doesEmailExists)
      .pipe(debounceTime(300), takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.doesEmailExist = data;
      });
  }

  doesPhoneNoExists(number: string) {
    if (
      !this.userBasicInfoForm.controls.contactNo.valid ||
      this.selectedUserInfo?.phoneNumber === number
    ) {
      return;
    }
    this.store.dispatch(new DoesPhoneNoExists(number));
    this.store.dispatch(new LoaderHide());
    this.store
      .select(doesPhoneNoxists)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.doesPhoneNoExist = data;
      });
  }

  navigateToRoleModal(saveDataModal: TemplateRef<any>) {
    this.trackingService.trackFeature(`Web.TeamUser.Button.AddNewRole.Click`)
    this.modalRef = this.bsModalService.show(saveDataModal, {
      class: 'modal-350 modal-dialog-centered',
    });
  }

  saveInModal(licenseBoughtModal: any) {
    this.postData(licenseBoughtModal);
    this.navigateToAddRole();
  }

  discardInModal() {
    this.navigateToAddRole();
  }

  closeModal() {
    this.modalRef.hide();
  }

  navigateToAddRole() {
    this.modalRef.hide();
    this.modalRef = this.bsModalService.show(AddRoleComponent, {
      class: 'right-modal modal-350',
    });
    this.router.navigate(['/teams/manage-role']);
  }

  getSanitizedHtml(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  searchRoleName(searchTerm: string) {
    this.searchTerm = searchTerm.trim().toLowerCase();
    this.searchSubject.next(searchTerm)
  }

  renamePermission(permissionDescription: string): string {
    const permissionMap: Record<string, string> = {
      "Todos": "Tasks",
      "RoleClaims": "Permissions",
      "Prospects": "Data",
    };
    let updatedPermissionDescription = permissionDescription.replace(
      /\b(Todos|RoleClaims|Prospects)\b/g,
      (match) => permissionMap[match] || match
    );
    // Then replace words within compound words like ViewUnAssignedProspects
    updatedPermissionDescription = updatedPermissionDescription.replace(
      /(ViewUnAssigned|CreateDuplicate|BulkConvertTo|ConvertTo)Prospects/g,
      "$1Data"
    );
    return updatedPermissionDescription;
  }
  convertPascalToNormal(text: string): string {
    let result = text.replace(/([A-Z])/g, ' $1').trim();
    return result.replace(/Prospects/g, 'Data');
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
