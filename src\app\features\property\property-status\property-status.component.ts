import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { PropertyStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { UpdatePropertyStatus } from 'src/app/reducers/property/property.actions';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
@Component({
  selector: 'property-status',
  templateUrl: './property-status.component.html',
})
export class PropertyStatusComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  status: boolean;
  params: any;
  canEditProperty: boolean = false;

  constructor(
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>,
    public trackingService: TrackingService
  ) {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Properties')) {
          this.canEditProperty = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  ngOnInit(): void {
    this.status =
      PropertyStatus[this.params.data.status] == 'Sold' ? false : true;
  }

  openConfirmModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'change the status of',
      title: data?.title,
      fieldType: 'property',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new UpdatePropertyStatus(this.params.data.id));
          this.trackingService.trackFeature(`Web.Property.Button.Status.Click`)
        } else {
          this.status =
            PropertyStatus[this.params.data.status] == 'Sold' ? false : true;
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
