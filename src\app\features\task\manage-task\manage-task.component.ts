import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { CellClickedEvent } from 'ag-grid-community';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';

import { takeUntil } from 'rxjs';
import {
  PAGE_SIZE,
  SHOW_ENTRIES,
  TASK_FILTER_TYPES,
} from 'src/app/app.constants';
import { TodoFilterType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  AssignedTodoUser,
  FilterApplied,
  TasksFilter,
  Todo,
  TodoFiltersCount,
} from 'src/app/core/interfaces/todo.interface';
import {
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { AddTaskComponent } from 'src/app/features/task/add-task/add-task.component';
import { TaskActionsComponent } from 'src/app/features/task/task-actions/task-actions.component';
import { TaskPreviewComponent } from 'src/app/features/task/task-preview/task-preview.component';
import { TaskPriorityComponent } from 'src/app/features/task/task-priority/task-priority.component';
import {
  getAddPermissions,
  getViewPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import {
  FetchTodoList,
  UpdateTodoFilter,
} from 'src/app/reducers/todo/todo.actions';
import {
  getIsTaskLoading,
  getTodo,
  getTodosCount,
  TodoState,
} from 'src/app/reducers/todo/todo.reducer';
import { TodoService } from 'src/app/services/controllers/todo.service';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';

@Component({
  selector: 'manage-task',
  templateUrl: './manage-task.component.html',
})
export class ManageTaskComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  pageSize: number = PAGE_SIZE;
  todoInfo: TodoState;
  filteredTodoList: Array<Todo> = [];
  selectedTodoItem: string = '';
  assignedTo: AssignedTodoUser = { id: '', displayName: 'All' };
  selectedTodoState: string = 'All';
  appliedFilter: FilterApplied = {
    todoFilterType: this.selectedTodoState,
    pageNumber: 1,
    pageSize: this.pageSize,
  };
  filtersPayload: TasksFilter = {
    todoFilterType: 0,
    pageNumber: 1,
    pageSize: this.pageSize,
    path: 'todo',
  };
  todoTotalCount: number;
  todoItemCount: number;
  taskFilterTypes = TASK_FILTER_TYPES;
  todoFiltersCount: TodoFiltersCount = {
    today: 0,
    upcoming: 0,
    overdue: 0,
    completed: 0,
    all: 0,
  };

  //grid Variables
  gridOptions: any;
  rowData: Array<Todo> = [];
  gridApi: any;
  gridColumnApi: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  currOffset: number = 0;
  paginationPageSize: number;
  defaultColDef: any;
  musoTask: AnimationOptions = { path: 'assets/animations/no-task.json' };
  selectedPageSize: number;
  canView: boolean = false;
  canAdd: boolean = false;
  getPages = getPages;
  isTaskLoading: boolean;

  userBasicDetails: any;

  constructor(
    private modalService: BsModalService,
    public todoService: TodoService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private gridOptionsService: GridOptionsService,
    public metaTitle: Title,
    private cdr: ChangeDetectorRef,
    public trackingService: TrackingService
  ) {
    super();
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();

    this._store.select(getViewPermissions).subscribe((canView: any) => {
      if (canView?.includes('Todos')) this.canView = true;
    });

    this._store.select(getAddPermissions).subscribe((canAdd: any) => {
      if (canAdd?.includes('Todos')) this.canAdd = true;
    });

    this._store.select(getTodo).subscribe((data: any) => {
      this.todoInfo = data || {};
      this.filteredTodoList = this.todoInfo.todos || [];
      this.rowData = this.filteredTodoList;
    });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
      });
  }

  ngOnInit(): void {
    this.selectedPageSize = 10;
    this.cdr.detectChanges();
    this.appliedFilter = {
      ...this.appliedFilter,
      timeZoneId:
        this.userBasicDetails?.timeZoneInfo?.timeZoneId ||
        getSystemTimeZoneId(),
      baseUTcOffset:
        this.userBasicDetails?.timeZoneInfo?.baseUTcOffset ||
        getSystemTimeOffset(),
    };
    this.filtersPayload = {
      ...this.filtersPayload,
      timeZoneId:
        this.userBasicDetails?.timeZoneInfo?.timeZoneId ||
        getSystemTimeZoneId(),
      baseUTcOffset:
        this.userBasicDetails?.timeZoneInfo?.baseUTcOffset ||
        getSystemTimeOffset(),
    };
    this._store.dispatch(new UpdateTodoFilter(this.filtersPayload));
    this._store.dispatch(new FetchTodoList(this.filtersPayload));
    this._store
      .select(getIsTaskLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isTaskLoading = data));
    this._store.select(getTodosCount).subscribe((count: any) => {
      this.todoItemCount = count.itemsCount;
      this.todoFiltersCount = {
        today: count.today,
        upcoming: count.upcoming,
        overdue: count.overdue,
        completed: count.completed,
        all: count.all,
      };
      this.todoTotalCount =
        this.todoFiltersCount[
        this.selectedTodoState.toLocaleLowerCase() as keyof TodoFiltersCount
        ];
    });
    this.headerTitle.setLangTitle('TASK.manage-tasks');
    this.metaTitle.setTitle('CRM | Task');
    this.initializeGridSettings();
    this.trackingService.trackFeature(`Web.Task.Page.Task.Visit`);
  }

  //datefilter Logic
  // filterParams = {
  //   comparator: function (filterLocalDateAtMidnight: any, cellValue: any) {
  //     let dateAsString = cellValue;
  //     dateAsString = dateAsString + '';
  //     if (dateAsString == '') return -1;
  //     let dateParts = dateAsString.split('T')[0].split('-').reverse().join('-');
  //     if (
  //       moment(dateParts, 'DD-MM-YYYY').format() ==
  //       moment(filterLocalDateAtMidnight).format()
  //     ) {
  //       return 0;
  //     } else if (
  //       moment(dateParts, 'DD-MM-YYYY').format() <
  //       moment(filterLocalDateAtMidnight).format()
  //     ) {
  //       return -1;
  //     } else if (
  //       moment(dateParts, 'DD-MM-YYYY').format() >
  //       moment(filterLocalDateAtMidnight).format()
  //     ) {
  //       return 1;
  //     } else {
  //       return 2;
  //     }
  //   },
  //   browserDatePicker: true,
  //   minValidYear: 2000,
  //   filterOptions: ['equals', 'inRange'],
  // };

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Priority',
        field: 'Priority',
        minWidth: 85,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [params.data.priority],
        cellRenderer: TaskPriorityComponent,
      },
      {
        headerName: 'Task Name',
        field: 'Task Name',
        minWidth: 120,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [params.data.title],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'Assigned To',
        minWidth: 100,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data.assignedUsers.map((item: any) => {
            return item.firstName + ' ' + item.lastName;
          }),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">
          ${params.value[0] ? params.value[0] : ''}</p>`;
        },
      },
      {
        headerName: 'Scheduled Date',
        field: 'Scheduled Date',
        minWidth: 163,
        cellClass: 'cursor-pointer',
        // filter: 'agDateColumnFilter',
        // filterParams: this.filterParams,
        // valueGetter: (params: any) => [params.data.scheduledDateTime],
        // cellRenderer: (params: any) => {
        //   return `<p>${moment(new Date(params.value[0])).utcOffset(`${this.userBasicDetails?.timeZoneInfo?.baseUTcOffset?.charAt(0) === '-' ? '' : '+'}${this.userBasicDetails?.timeZoneInfo?.baseUTcOffset}`).format('DD-MM-YYYY | hh:mm a')}</p>`;
        // },
        // comparator: (valueA: any, valueB: any) => {
        //   const dateA = new Date(valueA).getTime();
        //   const dateB = new Date(valueB).getTime();
        //   if (dateA === dateB) return 0;
        //   else if (dateA > dateB) return 1;
        //   return -1;
        // },
        valueGetter: (params: any) => [
          params.data.scheduledDateTime
            ? getTimeZoneDate(
              params.data.scheduledDateTime,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]}</p> 
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userBasicDetails?.timeZoneInfo?.timeZoneName &&
              this.userBasicDetails?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userBasicDetails?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Task Description',
        field: 'Task Description',
        minWidth: 100,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [params.data.notes],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 text-break">${params.value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        minWidth: 115,
        maxWidth: 115,
        filter: false,
        cellRenderer: TaskActionsComponent,
      },
    ];

    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onCellClicked(event: CellClickedEvent) {
    const headerName = event.colDef.headerName;
    let initialState: any = {
      data: event.data,
    };
    if (headerName !== 'Actions') {
      this.modalService.show(TaskPreviewComponent, {
        class: 'right-modal modal-350 ph-modal-unset',
        initialState,
      });
    }
  }

  addTaskModal() {
    this.trackingService.trackFeature(`Web.Task.Button.AddTask.Click`);
    this.modalService.show(AddTaskComponent, {
      class: 'right-modal modal-350',
    });
  }
  filterTodoList(state: string) {
    this.selectedTodoState = state;
    this.filtersPayload = {
      ...this.filtersPayload,
      todoFilterType: TodoFilterType[state as keyof typeof TodoFilterType],
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      timeZoneId:
        this.userBasicDetails?.timeZoneInfo?.timeZoneId ||
        getSystemTimeZoneId(),
      baseUTcOffset:
        this.userBasicDetails?.timeZoneInfo?.baseUTcOffset ||
        getSystemTimeOffset(),
    };
    this.currOffset = 0;
    this.trackingService.trackFeature(`Web.Task.Button.Filter${state}.Click`);
    this._store.dispatch(new UpdateTodoFilter(this.filtersPayload));
    this._store.dispatch(new FetchTodoList(this.filtersPayload));
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.trackingService.trackFeature(`Web.Task.Option.${this.pageSize}.Click`);
    this._store.dispatch(new UpdateTodoFilter(this.filtersPayload));
    this._store.dispatch(new FetchTodoList(this.filtersPayload));
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    this.gridApi.applyTransaction({ add: this.rowData });
    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this._store.dispatch(new UpdateTodoFilter(this.filtersPayload));
    this._store.dispatch(new FetchTodoList(this.filtersPayload));
  }

  addToDoModal() {
    this.modalService.show(AddTaskComponent, {
      class: 'right-modal modal-350',
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
