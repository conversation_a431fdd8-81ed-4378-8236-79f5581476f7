import { Component, ElementRef, EventEmitter, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import {
  ADDRESS_EXCEL_TEMPLATE,
  AddressDataColumns,
  AddressDisplayColumns
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getAppName, getSystemTimeOffset, getSystemTimeZoneId, validateAllFormFields } from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { ExcelUpload, FetchExcelTrackerList, UploadAddressMappedColumns } from 'src/app/reducers/listing-site/listing-site.actions';
import { getAddressColumnHeadings } from 'src/app/reducers/listing-site/listing-site.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
@Component({
  selector: 'address-bulk-upload',
  templateUrl: './address-bulk-upload.component.html',
})
export class AddressBulkUploadComponent implements OnInit {
  @ViewChild('validModal') validModal: any;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  excelTemplatePath: string = ADDRESS_EXCEL_TEMPLATE;
  addressData = AddressDisplayColumns;
  addressMappingForm: FormGroup;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  isValidModal: boolean = false;
  selectedFile: File;
  formKeys: Array<string> = [];
  s3BucketKey: string;
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  selectedTitle: string;
  userData: any;
  sourceId: any;
  getAppName = getAppName;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private router: Router,
    private fb: FormBuilder,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private route: ActivatedRoute
  ) {
    this.addressMappingForm = this.fb.group({
      towerName: [null],
      subCommunity: [null],
      community: [null],
      city: [null],
    });
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.sourceId = params?.id;
    });

    this.metaTitle.setTitle('CRM | Import Addresses');
    this.headerTitle.setLangTitle('Import Addresses');

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep = this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  uploadFile() {
    this._store.dispatch(new ExcelUpload(this.selectedFile))
    this._store
      .select(getAddressColumnHeadings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.excelSheets = data?.multiSheetColumnNames;
        if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
        this.selectedSheet = this.sheetNames[0];
        this.formKeys = data?.columnNames;
        this.s3BucketKey = data?.s3BucketKey;
        if (this.formKeys?.length) {
          this.currentStep = 3;
        }
        this.resetForms();
        this.onAutoMapChange();
      });
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  isValidForm() {
    if (!this.selectedSheet || !this.addressMappingForm.valid) {
      validateAllFormFields(this.addressMappingForm);
      return;
    } else {
      this.isValidModal = true;
      this.modalRef = this.modalService.show(this.validModal, {
        class: 'modal-350 modal-dialog-centered ip-modal-unset',
        keyboard: false,
      });
    }
  }

  confirmSheet(trackerInfoModal: TemplateRef<any>) {
    this.modalRef.hide();
    this.sendMappingDetails(trackerInfoModal);
  }

  sendMappingDetails(trackerInfoModal: TemplateRef<any>) {
    if (
      !this.selectedSheet) {
      return;
    }
    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnsData: {},
      timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      listingSourceId: this.sourceId,
    };
    AddressDataColumns.map((item: any) => {
      if (this.addressMappingForm.value[item.value]) {
        payload.mappedColumnsData[item.displayName] =
          this.addressMappingForm.value[item.value];
      }
    });
    payload.sheetName = this.selectedSheet || this.sheetNames[0];
    this._store.dispatch(new UploadAddressMappedColumns(payload));
    this._store.dispatch(new FetchExcelTrackerList(1, 10));

    this.modalRef = this.modalService.show(
      trackerInfoModal,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  onAutoMapChange() {
    const mappingColumns = AddressDisplayColumns;
    const form = this.addressMappingForm;

    mappingColumns.forEach(column => {
      if (column?.displayName && form?.controls[column?.value] && this.formKeys?.includes(column?.displayName)) {
        form.patchValue({
          [column?.value]: column?.displayName
        });
      }
    });

  }

  openBulkUploadedStatusModal() {
    this.navigateToHome();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: 'address',
    };
    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState
    });
  }

  onSheetSelection() {
    this.formKeys = this.excelSheets?.[this.selectedSheet];
  }

  navigateToHome() {
    this.router.navigate(['global-config/listing-management']);
  }

  resetForms() {
    if (this.addressMappingForm)
      this.addressMappingForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}

