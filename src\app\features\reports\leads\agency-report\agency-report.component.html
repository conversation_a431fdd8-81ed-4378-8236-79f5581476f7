<div class="py-24 bg-white position-fixed w-100 z-index-2 border-bottom"></div>


<div *ngIf="canViewAllUsers || canViewReportees" class="mx-24 mt-40">
  <!-- <div class="mt-16 fw-600 header-3">{{ 'GLOBAL.lead' | translate }} {{ 'REPORTS.agency' | translate }} {{
    'SIDEBAR.reports' | translate }}</div> -->
  <ng-template #AdvancedFilters>
    <div class="lead-adv-filter p-30 bg-white brbl-15 brbr-15">
      <div class="adv-filter">
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="justify-between align-end mr-20">
              <div class="field-label">{{'USER.user' | translate }} </div>
              <label class="checkbox-container mb-4">
                <input type="checkbox" [(ngModel)]="appliedFilter.withTeam">
                <span class="checkmark"></span>{{'DASHBOARD.with-team' | translate}}
              </label>
            </div>
            <div class="position-relative mr-20 ph-mr-0">
              <ng-select [virtualScroll]="true" [items]="canViewAllUsers ? allUsers : onlyReportees" ResizableDropdown
                [ngClass]="{'blinking pe-none': (canViewAllUsers ? isAllUsersLoading : isOnlyReporteesLoading )}"
                [multiple]="true" [closeOnSelect]="false" name="user" placeholder="ex. Manasa Pampana"
                [(ngModel)]="appliedFilter.users" bindLabel="fullName" bindValue="id">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{item.firstName + ' ' +
                    item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="flex-between">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all"> {{item.firstName}}
                        {{item.lastName}}</span>
                    </div>
                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.source' | translate }} </div>
            <div class="mr-20 ph-mr-0">
              <ng-select [virtualScroll]="true" [items]="leadSources" [ngClass]="{'blinking pe-none': isSourcesLoading}"
                bindLabel="displayName" bindValue="displayName" [multiple]="true" [closeOnSelect]="false"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.sources"
                (change)="updateSubSource()">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all"> {{item?.displayName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LEADS.sub-source' | translate}}</div>
            <div class="mr-20 ph-mr-0">
              <ng-select [virtualScroll]="true" [items]="subSourceList"
                [ngClass]="{'blinking pe-none': allSubSourceListIsLoading}" [multiple]="true" [closeOnSelect]="false"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.subSources">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all"> {{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'SIDEBAR.project' | translate }} </div>
            <div class="mr-20 ph-mr-0 datawrap">
              <ng-select [virtualScroll]="true" [items]="projectList" ResizableDropdown
                [ngClass]="{'blinking pe-none': isProjectListLoading}" [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.projects" bindValue="id"
                bindLabel="displayName">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all"> {{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'INTEGRATION.agency-name' | translate}}</div>
            <div class="mr-20 ph-mr-0">
              <ng-select [virtualScroll]="true" [items]="agencyNameList" ResizableDropdown
                [ngClass]="{'blinking pe-none': agencyNameListIsLoading}" [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item"
                [(ngModel)]="appliedFilter.agencyNames">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all"> {{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Country</div>
            <div class="mr-20 ph-mr-0">
              <ng-select [virtualScroll]="true" [items]="countryList" [ngClass]="{'blinking pe-none': countryIsLoading}"  ResizableDropdown
                [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name"
                bindValue="name" [(ngModel)]="appliedFilter.Countries">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all"> {{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">State</div>
            <div class="mr-20 ph-mr-0">
              <ng-select [virtualScroll]="true" [items]="states" [ngClass]="{'blinking pe-none': statesIsLoading}"
                ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item"
                [(ngModel)]="appliedFilter.states">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all"> {{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">City</div>
            <div class="mr-20 ph-mr-0">
              <ng-select [virtualScroll]="true" [items]="cities" [ngClass]="{'blinking pe-none': citiesIsLoading}"
                ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item"
                [(ngModel)]="appliedFilter.cities">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all"> {{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'REPORTS.date-filters' | translate }} </div>
            <div class="w-100">
              <div class="mr-20 ph-mr-0 align-center">
                <div class="w-33">
                  <ng-select [virtualScroll]="true" placeholder=All [searchable]="false" class="mr-10 ng-select-w-171"
                    ResizableDropdown [(ngModel)]="appliedFilter.dateType">
                    <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                      [value]="dType">{{dType}}</ng-option>
                  </ng-select>
                </div>
                <div class="w-67 align-center position-relative filters-grid clear-padding">
                  <div class="date-picker border pt-8 pb-6 br-4 align-center w-100" id="reportsAppointmentDate"
                    data-automate-id="reportsAppointmentDate">
                    <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt2"></span>
                    <input type="text" readonly placeholder="ex. 5-03-2025 - 14-03-2025" class="pl-20 text-large w-100"
                      [max]="appliedFilter.dateType === 'Modified Date' || appliedFilter.dateType === 'Created Date' ? maxDate : ''"
                      [owlDateTimeTrigger]="dt2" [owlDateTime]="dt2" [selectMode]="'range'"
                      (ngModelChange)="appliedFilter.date = $event" [ngModel]="appliedFilter.date"
                      [disabled]="!appliedFilter.dateType" />
                    <owl-date-time [pickerType]="'calendar'" #dt2
                      (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                  </div>
                  <div *ngIf="appliedFilter?.date?.[0]" class="right-4 align-center cursor-pointer position-absolute"
                    (click)="onResetDateFilter()">
                    <span class="ic-refresh ic-coal"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="field-label">Agency Date Filter</div>
          <ul class="d-flex text-large flex-wrap filters-grid">
            <button class="mr-8 br-4 py-6 px-12 border-black ip-mb-10" (click)="filterByDate('today')"
              [(ngModel)]="appliedFilter.dateForAgency" ngDefaultControl name="dateFilter"
              [ngClass]="isDateFilter == 'today' ? 'bg-accent-green text-white border-accent-green' : 'bg-white'">
              {{'GLOBAL.today' | translate }}</button>
            <button class="mr-8 br-4 py-6 px-12 border-black ip-mb-10" (click)="filterByDate('yesterday')"
              [(ngModel)]="appliedFilter.dateForAgency" ngDefaultControl name="dateFilter"
              [ngClass]="isDateFilter == 'yesterday' ? 'bg-accent-green text-white border-accent-green' : 'bg-white'">
              {{'REPORTS.yesterday' | translate }}</button>
            <button class="mr-8 br-4 py-6 px-12 border-black ip-mb-10" (click)="filterByDate('sevenDays')"
              [(ngModel)]="appliedFilter.dateForAgency" ngDefaultControl name="dateFilter"
              [ngClass]="isDateFilter == 'sevenDays' ? 'bg-accent-green text-white border-accent-green' : 'bg-white'">
              {{'REPORTS.last-7-days' | translate }}</button>
            <button class="mr-8 br-4 py-6 px-12 border-black ip-mb-10" (click)="filterByDate('custom')"
              [ngClass]="isDateFilter == 'custom' ? 'bg-accent-green text-white border-accent-green' : 'bg-white'">
              {{ 'DASHBOARD.custom' | translate}}</button>
            <div *ngIf="isDateFilter == 'custom'" [owlDateTimeTrigger]="dt1"
              class="date-picker border br-6 align-center mr-8 ip-mb-10">
              <span class="ic-appointment icon ic-xxs ic-black"></span>
              <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                (ngModelChange)="appliedFilter.dateForAgency = $event;" [ngModel]="appliedFilter.dateForAgency"
                placeholder="ex. 19-06-2025 - 29-06-2025" class="pl-20 text-large" />
              <owl-date-time [pickerType]="'calendar'" #dt1
                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
            </div>
            <span *ngIf="appliedFilter?.dateForAgency?.[0] || isDateFilter == 'custom'"
              class="ic-refresh ic-xxs w-30px h-30px flex-center bg-coal br-4 cursor-pointer ic-white"
              (click)="resetDate()"></span>
          </ul>
        </div>
      </div>
      <div class="flex-end mt-10 tb-mr-20 ph-mr-0">
        <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalService.hide()">{{'BUTTONS.cancel'
          |
          translate }}</u>
        <button class="btn-gray" (click)="reset()">{{ 'GLOBAL.reset' | translate }}</button>
        <button class="btn-coal ml-20" (click)="applyAdvancedFilter()">{{ 'GLOBAL.search' | translate
          }}</button>
      </div>
    </div>
  </ng-template>

  <div class="pt-20">
    <div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
      <div class="align-center border-end flex-grow-1 no-validation">
        <ng-container>
          <div class="align-center w-100 px-10 py-12">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
            <input placeholder="Search by Agency Name" (keydown)="onSearch($event)" (input)="isEmptyInput($event)"
              autocomplete="off" name="search" [(ngModel)]="searchTerm" class="border-0 outline-0 w-100">
          </div>
          <small class="text-muted text-nowrap ph-d-none pr-6">({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </ng-container>
        <div *ngIf="canExportAllUsers || canExportReportees" class="bg-accent-green text-white border-end"
          [ngClass]="{ 'pe-none': currentView === 'graph' && !isGraphExportEnabled() }">
          <div class="px-20 py-5 h-100 align-center cursor-pointer flex-col"
               *ngIf="currentView === 'table'" (click)="exportAgencyReport()">
            <div>{{ 'REPORTS.export' | translate }}</div>
            <div class="mt-2 fw-300 text-xs">(tabular)</div>
          </div>
          <div class="px-20 py-5 h-100 align-center cursor-pointer flex-col"
               *ngIf="currentView === 'graph'" (click)="!isExporting && exportGraphAsPDF()" [class.pe-none]="isExporting">
            <ng-container *ngIf="!isExporting; else buttonDots">
              <div>{{ 'REPORTS.export' | translate }}</div>
              <div class="mt-2 fw-300 text-xs">(visual)</div>
            </ng-container>
            <ng-template #buttonDots>
              <div class="container flex-center py-12">
                <ng-container *ngFor="let dot of [1,2,3]">
                  <div class="dot-falling dot-white"></div>
                </ng-container>
              </div>
            </ng-template>
          </div>
        </div>
        <div class="bg-coal text-white px-20 py-12 h-100 align-center cursor-pointer border-end ph-d-none"
          (click)="toggleView()">
          <span class="text-nowrap ic-cube ic-white ic-large m-2" *ngIf="currentView === 'graph'"></span>
          <span class="text-nowrap ic-chart-pie ic-white ic-large m-2" *ngIf="currentView === 'table'"></span>
        </div>
      </div>
      <div class="d-flex tb-br-top">
        <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1"
          (click)="openAdvFiltersModal(AdvancedFilters)">
          <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
          <span class="fw-600">{{'PROPERTY.advanced-filters' | translate}}</span>
        </div>
        <div class="show-dropdown-white align-center position-relative">
          <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">{{ 'GLOBAL.show' |
              translate
              }}</span> {{ 'GLOBAL.entries' |
            translate }}</span>
          <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-150 tb-w-120px"
            ResizableDropdown [(ngModel)]="selectedPageSize" (change)="assignCount()" [searchable]="false">
            <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
              {{pageSize}}</ng-option>
          </ng-select>
        </div>
      </div>
    </div>
    <div class="bg-white px-4 py-12 tb-w-100-34" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
      <ng-container *ngIf="showFilters">
        <div class="bg-secondary flex-between">
          <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
            <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
              <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                {{reportFiltersKeyLabel[filter.key] || filter.key}}:
                {{ filter.key === 'users' ? getUserName(value) :
                value}}
                <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                  (click)="onRemoveFilter(filter.key, value)"></span>
              </div>
            </div>
          </drag-scroll>
          <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
            (click)="reset(); isDateFilter = 'null'">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
          </div>
        </div>
      </ng-container>
    </div>
    <ng-template #agencyData>
      <div [style.display]="currentView === 'table' ? 'block' : 'none'">
        <div class="reports pinned-grid">
          <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize + 1"
            [gridOptions]="gridOptions" [rowData]="rowData" [suppressPaginationPanel]="true"
            [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (gridReady)="onGridReady($event)"
            (cellClickedEvent)="onCellClicked($event)">
          </ag-grid-angular>
        </div>
        <div class="my-20 flex-end">
          <div class="mr-10" *ngIf="agencyTotalCount">{{ 'GLOBAL.showing' | translate }}
            {{(currOffset * pageSize) + 1}} {{ 'GLOBAL.to-small' | translate }}
            {{rowData?.length > 1 ? currOffset*pageSize + rowData?.length - 1 : currOffset*pageSize + rowData?.length}}
            {{ 'GLOBAL.of-small' | translate }} {{agencyTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
          <pagination [offset]=currOffset [limit]="1" [range]="1" [size]='getPages(agencyTotalCount,pageSize)'
            (pageChange)="onPageChange($event)"></pagination>
        </div>
      </div>
      <div [style.display]="currentView === 'graph' ? 'block' : 'none'">
        <report-graph  [payload]="filtersPayload"#reportsGraph [rowData]="rowData" [gridOptions]="gridOptions" [xAxisData]="isCustomStatusEnabled ? 'name': 'agencyName'"
          [filteredColumnDefsCache]="filteredColumnDefsCache" reportType = "agency-report" (exportStarted)="isExporting = true" (exportFinished)="isExporting = false"></report-graph>
      </div>
    </ng-template>
    <ng-container
      *ngIf="!rowData?.length || (isCustomStatusEnabled ? isAgencyCustomReportLoading : isAgencyReportLoading); else agencyData">
      <div *ngIf="(isCustomStatusEnabled ? isAgencyCustomReportLoading : isAgencyReportLoading)"
        class="flex-center w-100 h-100-337 min-h-250">
        <ng-container [ngTemplateOutlet]="reportsLoader"></ng-container>
      </div>
      <div *ngIf="(isCustomStatusEnabled ? !isAgencyCustomReportLoading : !isAgencyReportLoading) && !rowData?.length"
        class="flex-center-col h-100-337">
        <img src="assets/images/layered-cards.svg" alt="No Data Found">
        <div class="header-3 fw-600 text-center">{{'PROFILE.no-data-found' | translate }}</div>
      </div>
    </ng-container>
  </div>
</div>

<ng-template #reportsLoader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>