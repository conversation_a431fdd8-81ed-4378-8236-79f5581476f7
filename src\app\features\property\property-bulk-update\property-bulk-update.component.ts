import { Component, EventEmitter, Input, OnInit, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Grid<PERSON>pi } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, takeUntil } from 'rxjs';

import { NotificationsService } from 'angular2-notifications';
import { AppState } from 'src/app/app.reducer';
import { BulkAssignProperty } from 'src/app/core/interfaces/property.interface';
import { getAssignedToDetails, validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchAllListing } from 'src/app/reducers/listing-site/listing-site.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { Bulk<PERSON>eassignProperty, FetchArchived<PERSON>ropertyList, FetchPropertyList, UpdateFilterPayload } from 'src/app/reducers/property/property.actions';
import { ShareExternalComponent } from 'src/app/shared/components/share-external/share-external.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { PropertyListingComponent } from '../listing-management/property-listing/property-listing.component';
import { PropertyService } from 'src/app/services/controllers/properties.service';

@Component({
  selector: 'property-bulk-update',
  templateUrl: './property-bulk-update.component.html',
})
export class PropertyBulkUpdateComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() gridApi: GridApi;
  @Input() allUserList: any;
  @Input() filtersPayload: any
  @Input() pageSize: number;
  @Input() isViewAllProperties: any;
  @Input() listing: boolean;
  bulkReassignForm: FormGroup;
  selectedBulkReassign: any[];
  selectedNodes: any[];
  canEdit: boolean = false;
  canDelete: boolean = false;
  canAssign: boolean = false;
  currentPath: string;
  isBulkDeleting: boolean = false
  canList: boolean;
  isPermanentDelete: boolean;
  canBulkReassign: boolean = false;
  canPermanentDelete: boolean = false;
  canBulkDelete: boolean = false;
  canBulkShare: boolean = false;
  canBulkRestore: boolean = false;
  canBulkList: boolean = false;
  canBulkDeList: boolean = false;
  canBulkModifyListing: boolean = false;

  get shouldShowBulkRestore(): boolean {
    return this.currentPath?.includes('listing')
      ? this.filtersPayload?.PropertyVisiblity === 4
      : !this.isViewAllProperties;
  }

  constructor(private modalService: BsModalService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private bulkReassignModalRef: BsModalRef,
    private bulkDeleteModalRef: BsModalRef,
    private modalRef: BsModalRef,
    public router: Router,
    private api: PropertyService,
    private _notificationService: NotificationsService,
  ) { }

  ngOnInit(): void {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canBulkReassign = permissionsSet.has('Permissions.Properties.BulkReassign');
        this.canBulkShare = permissionsSet.has('Permissions.Properties.BulkShare');
        this.canPermanentDelete = permissionsSet.has('Permissions.Properties.BulkPermanentDelete');
        this.canBulkDelete = permissionsSet.has('Permissions.Properties.BulkDelete');
        this.canBulkRestore = permissionsSet.has('Permissions.Properties.BulkRestore');
        this.canBulkList = permissionsSet.has('Permissions.Properties.BulkList');
        this.canBulkDeList = permissionsSet.has('Permissions.Properties.BulkDeList');
        this.canBulkModifyListing = permissionsSet.has('Permissions.Properties.BulkModifyListing');
      })
  }

  assignToUserListChanged($event: any): void {
    const assignedToUsers = $event
      .filter((user: any) => user.isActive)
      .map((user: any) => user.id);
    this.bulkReassignForm.patchValue({
      assignedToUsers: assignedToUsers,
    });
  }

  openBulkReassignModal(BulkReassignModal: TemplateRef<any>) {
    this.bulkReassignForm = this.fb.group({
      assignedToUsers: ['', Validators.required],
    });
    this.selectedBulkReassign = this.gridApi
      .getSelectedNodes()
      .map((prop: any) => prop.data);
    let initialState: any = {
      data: this.selectedBulkReassign,
      class: 'modal-500 right-modal ip-modal-unset',
    };
    this.bulkReassignModalRef = this.modalService.show(
      BulkReassignModal,
      initialState
    );
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>, isPermanentDelete: boolean = false) {
    this.isPermanentDelete = isPermanentDelete
    let selectedNodes = this.gridApi?.getSelectedNodes().map((project: any) => {
      return project.data;
    });
    this.selectedNodes = selectedNodes;
    let initialState: any = {
      data: selectedNodes,
      class: `right-modal ${!this.shouldShowBulkRestore ? 'modal-350' : 'modal-500'}`,
    };
    this.bulkDeleteModalRef = this.modalService.show(BulkDeleteModal, initialState);
  }

  assignToName(property: any) {
    return property.assignedTo?.map((id: any) => getAssignedToDetails(id, this.allUserList, true) || '--');
  }

  removeProperty(id: string): void {
    const selectedNode = this.gridApi
      ?.getSelectedNodes()
      ?.find((lead: any) => lead?.data?.id === id);
    if (selectedNode) {
      this.gridApi?.deselectNode(selectedNode);
    }

    if (this.selectedBulkReassign) {
      this.selectedBulkReassign = this.selectedBulkReassign.filter(
        (prop: any) => prop.id !== id
      );
      if (this.selectedBulkReassign?.length <= 0) this.bulkReassignModalRef.hide();
    }
    if (this.selectedNodes) {
      this.selectedNodes = this.selectedNodes?.filter(
        (prop: any) => prop.id !== id
      );

      if (this.selectedNodes?.length <= 0) this.bulkDeleteModalRef.hide();

    }
  }

  openListProperty(action: string) {
    const list = action === 'list';

    this.gridApi?.getSelectedNodes().forEach((node: any) => {
      const shouldBeSelected = list ? !node.data?.shouldVisisbleOnListing : node.data?.shouldVisisbleOnListing;
      node.setSelected(shouldBeSelected);
    });

    const initialState: any = {
      bulkOperation: true,
      gridApi: this.gridApi,
      action: action,
    };

    this.modalRef = this.modalService.show(
      PropertyListingComponent,
      Object.assign({}, {
        class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      })
    );
  }

  openConfirmDeleteModal(Title: string, id: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: Title,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeProperty(id);
        }
      });
    }
  }

  prepareBulkReassignRequestBody(): BulkAssignProperty {
    const userIds = this.bulkReassignForm.value.assignedToUsers;
    const propertiesId: string[] = this.selectedBulkReassign.map(
      (prop: any) => prop?.id
    );
    return {
      userIds,
      propertiesId,
    };
  }

  updateBulkAssign() {
    if (!this.bulkReassignForm.valid) {
      validateAllFormFields(this.bulkReassignForm);
      return;
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(
      new BulkReassignProperty(this.prepareBulkReassignRequestBody())
    );
    if (this.bulkReassignModalRef) {
      this.bulkReassignModalRef.hide();
    }
  }

  openShareExternalModal() {
    let selectedNodes: any;
    if (this.gridApi) {
      selectedNodes = this.gridApi?.getSelectedNodes();
    }
    let selectedData = selectedNodes.map((node: any) => node?.data);

    let initialState: any = {
      data: selectedData,
      key: 'bulk-share-property',
      type: 'modal',
      closeShareExternalComponent: () => {
        shareExternalComponentRef.hide();
      },
    };
    const shareExternalComponentRef = this.modalService.show(
      ShareExternalComponent,
      {
        class: 'modal-350 top-modal',
        ignoreBackdropClick: true,
        keyboard: false,
        initialState,
      }
    );
  }

  bulkDelete(): void {
    const ids = this.gridApi?.getSelectedNodes()?.map((node: any) => node?.data?.id);
    if (!ids || ids.length === 0) {
      return;
    }
    // for single delete
    if (this.isPermanentDelete && this.shouldShowBulkRestore) {
      this.api.deletePropertyPermanently(ids).subscribe({
        next: (res: any) => {
          if (res) {
            this.bulkDeleteModalRef.hide();
            this._notificationService.success('Properties deleted successfully');
            if (this.currentPath === '/properties/manage-listing') {
              this._store.dispatch(new FetchAllListing());
            } else {
              this._store.dispatch(new FetchArchivedPropertyList());
            }
            return
          }
        }
      });
      return
    }
    const shouldDelete =
      this.currentPath?.includes('listing')
        ? this.filtersPayload?.PropertyVisiblity !== 4
        : this.isViewAllProperties;
    const action = shouldDelete
      ? this.api.bulkDeleteProperties(ids)
      : this.api.bulkRestoreProperties(ids);

    const successMessage = this.isPermanentDelete 
      ? 'Properties deleted successfully'
      : 'Properties restored successfully';

    let dispatchAction: any;
    if (this.currentPath?.includes('listing')) {
      dispatchAction = new FetchAllListing
    } else {
      dispatchAction = this.isViewAllProperties
        ? new FetchPropertyList()
        : new FetchArchivedPropertyList();
    }
    this.isBulkDeleting = true;
    action.subscribe({
      next: (res: any) => {
        this.bulkDeleteModalRef.hide();
        this.isBulkDeleting = false;
        this._notificationService.success(successMessage);
        this._store.dispatch(dispatchAction);
      },
    });
  }
}
