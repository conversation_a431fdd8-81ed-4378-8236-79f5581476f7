import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalService } from 'ngx-bootstrap/modal';
import { of, throwError } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom } from 'rxjs/operators';
import { CloseModal, OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { ReferenceIdService } from 'src/app/services/controllers/referenceIdManagement.service';
import { AddReferenceId, AddReferenceIdSuccess, DeleteReferenceId, DoesRefIdExist, DoesRefIdExistSuccess, FetchAllReferenceIds, FetchAllReferenceIdsListSuccess, FetchAllReferenceIdsSuccess, FetchReferenceExcelUploadedList, FetchReferenceExcelUploadedSuccess, FetchReferenceIdCounts, FetchReferenceIdCountsSuccess, FetchReferenceListingSourceSuccess, ReferenceBulkDelete, ReferenceBulkDeleteSuccess, ReferenceExcelUpload, ReferenceExcelUploadSuccess, ReferenceIdManagementActionTypes, UpdateReferenceId, UploadReferenceMappedColumns } from './reference-id-management.action';
import { getFiltersPayload } from './reference-id-management.reducer';

@Injectable()
export class ReferenceIdEffects {

    fetchReferenceIdsOnFilterUpdate$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.UPDATE_FILTERS_PAYLOAD),
            switchMap(() => {
                return [new FetchAllReferenceIds()];
            })
        )
    );

    fetchAllReferenceIds$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID),
            withLatestFrom(this.store.pipe(select(getFiltersPayload))),
            switchMap(([action, filters]) => {
                this.store.dispatch(new FetchReferenceIdCounts())
                return this.referenceIdService.fetchAllReferenceIds(filters).pipe(
                    map((response: any) => {
                        if (response.succeeded) {
                            return new FetchAllReferenceIdsSuccess(response);
                        }
                        return new FetchAllReferenceIdsSuccess({})
                    }),
                    catchError((error) => {
                        return of(new OnError(error));
                    })
                );
            })
        )
    );

    fetchAllReferenceIdsCount$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_COUNT),
            withLatestFrom(this.store.pipe(select(getFiltersPayload))),
            switchMap(([action, filters]) => {
                return this.referenceIdService.fetchAllReferenceIdsCounts(filters).pipe(
                    map((response: any) => {
                        if (response.succeeded) {
                            return new FetchReferenceIdCountsSuccess(response?.data);
                        }
                        return new FetchReferenceIdCountsSuccess({})
                    }),
                    catchError((error) => {
                        return of(new OnError(error));
                    })
                );
            })
        )
    );

    addReferenceId$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.ADD_REFERENCE_ID),
            switchMap((action: AddReferenceId) => {
                return this.referenceIdService.addReferenceId(action.payload).pipe(
                    map((response: any) => {
                        if (response.succeeded) {
                            this.modalService.hide()
                            this.store.dispatch(new FetchAllReferenceIds());
                            this._notificationService.success('Reference Id Created Successfully')
                        }
                        return new AddReferenceIdSuccess();
                    }),
                    catchError((error) => {
                        return of(new OnError(error));
                    })
                );
            })
        )
    );

    updateReferenceId$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.UPDATE_REFERENCE_ID),
            switchMap((action: UpdateReferenceId) => {
                return this.referenceIdService.updateReferenceId(action.payload).pipe(
                    map((response: any) => {
                        if (response.succeeded) {
                            this.modalService.hide()
                            this.store.dispatch(new FetchAllReferenceIds());
                            this._notificationService.success('Reference Id Updated Successfully')
                        }
                        return new AddReferenceIdSuccess();
                    }),
                    catchError((error) => {
                        return of(new OnError(error));
                    })
                );
            })
        )
    );

    deleteReferenceId$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.DELETE_REFERENCE_ID),
            switchMap((action: DeleteReferenceId) => {
                return this.referenceIdService.deleteReferenceId(action.id).pipe(
                    map((response: any) => {
                        if (response.succeeded) {
                            this.modalService.hide()
                            this.store.dispatch(new FetchAllReferenceIds());
                            this._notificationService.success('Reference Id Deleted Successfully')
                        }
                        return new AddReferenceIdSuccess();
                    }),
                    catchError((error) => {
                        return of(new OnError(error));
                    })
                );
            })
        )
    );



    addBulkExcel$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.REFERENCE_EXCEL_UPLOAD),
            switchMap((action: ReferenceExcelUpload) => {
                return this.referenceIdService.uploadExcel(action.file).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                'Reference Excel uploaded Successfully'
                            );
                            return new ReferenceExcelUploadSuccess(resp.data);
                        } else {
                            this.store.dispatch(new CloseModal());
                            this._notificationService.warn(`${resp.message}`);
                            return new FetchAllReferenceIds();
                        }
                    }),
                    catchError((err: any) => {
                        throwError(err);
                        Array.isArray(err?.error?.messages)
                            ? this._notificationService.error(err.error.messages[0])
                            : this._notificationService.error(err?.error?.messages);
                        return throwError(() => err);
                    })
                );
            })
        )
    );

    uploadMappedColumns$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.UPLOAD_MAPPED_COLUMNS),
            switchMap((action: UploadReferenceMappedColumns) => {
                return this.referenceIdService.uploadMappedColumns(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            if (resp.data) {
                                if (resp.data?.excelUrl) {
                                    const dataCount = resp.message?.DataCount || '';
                                    this._notificationService.success(
                                        `${dataCount} Invalid Data Not Uploaded`
                                    );
                                    return new ReferenceExcelUploadSuccess(resp.data);
                                } else {
                                    this._notificationService.success(
                                        'Excel Uploaded Successfully'
                                    );
                                    return new ReferenceExcelUploadSuccess(resp.data);
                                }
                            } else {
                                this._notificationService.error(resp.message);
                            }
                            return new FetchAllReferenceIds();
                        }
                        return new FetchAllReferenceIdsSuccess({});
                    }),
                    catchError((err: any) => {
                        Array.isArray(err?.error?.messages)
                            ? this._notificationService.error(err.error.messages[0])
                            : this._notificationService.error(err?.error?.messages);
                        return throwError(() => err);
                    })
                );
            })
        )
    );

    getExcelUploadedList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST),
            map((action: FetchReferenceExcelUploadedList) => action),
            switchMap((data: any) => {
                return this.referenceIdService
                    .getExcelUploadedList(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchReferenceExcelUploadedSuccess(resp);
                            }
                            return new FetchReferenceExcelUploadedSuccess();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    fetchAllReferenceIdSources$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.FETCH_LISTING_SOURCE),
            switchMap(() => {
                return this.referenceIdService.fetchAllListingSources().pipe(
                    map((response: any) => {
                        if (response?.succeeded) {
                            return new FetchReferenceListingSourceSuccess(response?.data);
                        }
                        return new FetchReferenceListingSourceSuccess([]);
                    }),
                    catchError((error) => {
                        return of(new OnError(error));
                    })
                );
            })
        )
    );

    fetchAllReferenceIdsList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_LIST),
            switchMap(() => {
                return this.referenceIdService.fetchAllReferenceIdsList().pipe(
                    map((response: any) => {
                        if (response?.succeeded) {
                            return new FetchAllReferenceIdsListSuccess(response?.data);
                        }
                        return new FetchAllReferenceIdsListSuccess([]);
                    }),
                    catchError((error) => {
                        return of(new OnError(error));
                    })
                );
            })
        )
    );

    doesReferenceIdExist$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.DOES_REFERENCE_ID_EXIST),
            switchMap((action: DoesRefIdExist) => {
                return this.referenceIdService.doesRefIdExist(action.refId, action.sourceId).pipe(
                    map((resp: any) => {
                        return new DoesRefIdExistSuccess(resp.data);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    referenceBulkDelete$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ReferenceIdManagementActionTypes.REFERENCE_BULK_DELETE),
            map((action: ReferenceBulkDelete) => action),
            switchMap((data: any) => {
                return this.referenceIdService.refBulkDelete(data.ids).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this.store.dispatch(new FetchAllReferenceIds());
                            this._notificationService.success('Reference Id Deleted Successfully')
                            return new ReferenceBulkDeleteSuccess(resp.data);
                        }
                        return new ReferenceBulkDeleteSuccess(resp.data);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private actions$: Actions,
        private store: Store<AppState>,
        private referenceIdService: ReferenceIdService,
        private _notificationService: NotificationsService,
        private modalService: BsModalService,
    ) { }
}
