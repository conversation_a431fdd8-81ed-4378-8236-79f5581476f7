import { Component, Input, ViewChild } from '@angular/core';
import { MapInfoWindow } from '@angular/google-maps';
import { getLocationDetailsByObj } from 'src/app/core/utils/common.util';

@Component({
  selector: 'project-location',
  templateUrl: './project-location.component.html',
})
export class ProjectLocationComponent {
  @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
  markers: any[] = [];
  center = {
    lat: 12.9106262,
    lng: 77.6405173,
  };
  latitude: number = 12.8898194;
  longitude: number = 77.64237;
  selectedAddress: string;
  @Input() projectInfo: any;
  getLocationDetailsByObj = getLocationDetailsByObj;

  constructor() { }

  openInfoWindow(marker: any) {
    this.infoWindow.open(marker);
  }

  showLocation(lat: any, long: any, loc: any, label: any) {
    this.markers[label - 1] = {
      latitude: Number(lat),
      longitude: Number(long),
      label: String(label),
      address: loc,
    };
    this.selectedAddress = this.markers[label - 1]?.address;
    this.center = {
      lat: Number(lat),
      lng: Number(long),
    };
  }
}
