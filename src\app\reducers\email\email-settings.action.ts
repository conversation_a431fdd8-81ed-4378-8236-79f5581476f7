import { Action } from '@ngrx/store';
export enum EmailSettingsActions {
  CREATE_EMAIL = '[EMAIL_SETTINGS] Create Email SMTP Server',
  CREATE_EMAIL_SUCCESS = '[EMAIL_SETTINGS] Create Email SMTP Server Success',
  GET_EMAIL_SMTP = '[EMAIL_SETTINGS] Get all Email SMTP List',
  GET_EMAIL_SMTP_SUCCESS = '[EMAIL_SETTINGS] Get all Email SMTP List Success',
  SEND_TEST_EMAIL = '[EMAIL_SETTINGS] Send Test Email',
  SEND_TEST_EMAIL_BULK = '[EMAIL_SETTINGS] Send Test Email Bulk',
  EDIT_EMAIL_SMTP = '[EMAIL_SETTINGS] Edit Email SMTP Settings',
  EDIT_EMAIL_SMTP_SUCCESS = '[EMAIL_SETTINGS] Edit Email SMTP Settings Success',
  Delete_EMAIL_SMTP = '[EMAIL_SETTINGS] Delete Email SMTP',
  ASSIGN_FILTER_PAYLOAD = '[EMAIL_SETTINGS] Assign Filter Payload',
  ASSIGN_TOTAL_COUNT = '[EMAIL_SETTINGS] Assign Total Count',
  GET_EMAIL_SMTP_BY_USERID = '[EMAIL_SETTINGS] Get Email SMTP List by UserId',
  GET_EMAIL_SMTP_BY_USERID_SUCCESS = '[EMAIL_SETTINGS] Get Email SMTP List by UserId Success',
}

export class CreateEmailSMTP implements Action {
  readonly type: string = EmailSettingsActions.CREATE_EMAIL;
  constructor(public payload: any) { }
}

export class CreateEmailSMTPSuccess implements Action {
  readonly type: string = EmailSettingsActions.CREATE_EMAIL_SUCCESS;
  constructor(public resp: any='') { }
}

export class getEmailSMTPList implements Action {
  readonly type: string = EmailSettingsActions.GET_EMAIL_SMTP;
  constructor() { }
}

export class getEmailSMTPListSuccess implements Action {
  readonly type: string = EmailSettingsActions.GET_EMAIL_SMTP_SUCCESS;
  constructor(public resp: any = []) { }
}

export class SendTestEmail implements Action {
  readonly type: string = EmailSettingsActions.SEND_TEST_EMAIL;
  constructor(public payload: any) { }
}

export class SendTestEmailBulk implements Action {
  readonly type: string = EmailSettingsActions.SEND_TEST_EMAIL_BULK;
  constructor(public payload: any, public Files: any) { }
}

export class EditEmailSMTP implements Action {
  readonly type: string = EmailSettingsActions.EDIT_EMAIL_SMTP;
  constructor(public payload: any) { }
}

export class EditEmailSMTPSuccess implements Action {
  readonly type: string = EmailSettingsActions.EDIT_EMAIL_SMTP_SUCCESS;
  constructor(public resp: any = '') { }
}

export class DeleteEmailSMTP implements Action {
  readonly type: string = EmailSettingsActions.Delete_EMAIL_SMTP;
  constructor(public id: string) { }
}

export class AssignFilterPayload implements Action {
  readonly type: string = EmailSettingsActions.ASSIGN_FILTER_PAYLOAD;
  constructor(public payload: any) { }
}

export class AssignTotalCount implements Action {
  readonly type: string = EmailSettingsActions.ASSIGN_TOTAL_COUNT;
  constructor(public TotalCount: number) { }
}

export class FetchEmailSMTPListByUserId implements Action {
  readonly type: string = EmailSettingsActions.GET_EMAIL_SMTP_BY_USERID;
  constructor(public id: string) { }
}

export class FetchEmailSMTPListByUserIdSuccess implements Action {
  readonly type: string = EmailSettingsActions.GET_EMAIL_SMTP_BY_USERID_SUCCESS;
  constructor(public resp?: any) { }
}
