import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LottieModule } from 'ngx-lottie';
import { HttpClient } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';

import { ForgotPasswordComponent } from 'src/app/features/login/forgot-password/forgot-password.component';
import { TwoFactorAuthenticationComponent } from './Two-factor-authentication/two-factor-authentication.component';
import { LoginRoutingModule } from 'src/app/features/login/login-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { LoginFormComponent } from 'src/app/features/login/login-form/login-form.component';
import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { SupportComponent } from './support/support.component';

@NgModule({
  declarations: [
    ForgotPasswordComponent,
    LoginFormComponent,
    SupportComponent,
    TwoFactorAuthenticationComponent,
  ],
  imports: [
    CommonModule,
    LoginRoutingModule,
    SharedModule,
    ReactiveFormsModule,
    FormsModule,
    LottieModule.forRoot({ player: playerFactory }),
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
      // to extend the I18n to use global en.json
      extend: true,
    }),
  ],
  providers: [TranslateService],
})
export class LoginModule { }
