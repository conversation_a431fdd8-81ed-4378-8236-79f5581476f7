import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AgGridModule } from 'ag-grid-angular';
import { HttpClient } from '@angular/common/http';
import {
  TranslateModule,
  TranslateLoader,
  TranslateService,
} from '@ngx-translate/core';
import { LottieModule } from 'ngx-lottie';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { SharedModule } from 'src/app/shared/shared.module';
import { routes } from 'src/app/features/leads/leads.routes';
import { TaskComponent } from 'src/app/features/task/task.component';
import { AddTaskComponent } from 'src/app/features/task/add-task/add-task.component';
import { TaskRoutingModule } from 'src/app/features/task/task-routing.module';
import { ManageTaskComponent } from 'src/app/features/task/manage-task/manage-task.component';
import { TaskPriorityComponent } from 'src/app/features/task/task-priority/task-priority.component';
import { TaskActionsComponent } from 'src/app/features/task/task-actions/task-actions.component';
import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { TaskPreviewComponent } from 'src/app/features/task/task-preview/task-preview.component';

@NgModule({
  declarations: [
    TaskComponent,
    AddTaskComponent,
    ManageTaskComponent,
    TaskPriorityComponent,
    TaskActionsComponent,
    TaskPreviewComponent,
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    TaskRoutingModule,
    SharedModule,
    AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    LottieModule.forRoot({ player: playerFactory }),
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
    }),
  ],
  providers: [TranslateService],
})
export class TaskModule { }
