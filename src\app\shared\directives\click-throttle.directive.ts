import { Directive, ElementRef, Output } from '@angular/core';
import { debounceTime, fromEvent, throttleTime } from 'rxjs';
const IDLE_TIME = 300;
@Directive({
  selector: '[click.throttle], [click.debounce]'
})
export class ClickThrottleDirective {

  @Output('click.throttle') throttledClicks$ = fromEvent(
    this.el.nativeElement,
    'click'
  ).pipe(throttleTime(IDLE_TIME));

  @Output('click.debounce') debouncedClicks$ = fromEvent(
    this.el.nativeElement,
    'click'
  ).pipe(debounceTime(IDLE_TIME));
  
  constructor(private el: ElementRef) {}
}


