import { Action, createSelector } from '@ngrx/store';
import { TodoPriority } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { TasksFilter, Todo } from 'src/app/core/interfaces/todo.interface';

import {
  FetchTodoListSuccess,
  TaskActionTypes,
  UpdateTodoFilter,
} from 'src/app/reducers/todo/todo.actions';

export type TodoState = {
  todaysTodosCount?: number;
  upcomingTodosCount?: number;
  completedTodosCount?: number;
  overdueTodosCount?: number;
  allTodosCount?: number;
  todos?: Array<Todo>;
  totalCount?: number;
  itemsCount?: number;
  filtersPayload: TasksFilter;
  isTaskLoading: boolean;
};

const initialState: TodoState = {
  todaysTodosCount: 0,
  upcomingTodosCount: 0,
  completedTodosCount: 0,
  overdueTodosCount: 0,
  allTodosCount: 0,
  todos: [],
  totalCount: 0,
  itemsCount: 0,
  filtersPayload: {
    todoFilterType: 0,
    pageNumber: 1,
    pageSize: 10,
    path: 'todo',
  },
  isTaskLoading: false,
};

export function todoReducer(state: TodoState = initialState, action: Action): TodoState {
  switch (action.type) {
    case TaskActionTypes.FETCH_TODO_LIST:
      return {
        ...state,
        isTaskLoading: true
      };
    case TaskActionTypes.FETCH_TODO_LIST_SUCCESS:
      const response = (action as FetchTodoListSuccess).response || {};
      return {
        ...state,
        todos: response.items,
        totalCount: response.totalCount,
        ...response.data,
        isTaskLoading: false,
      };
    case TaskActionTypes.UPDATE_TODO_FILTER:
      return {
        ...state,
        filtersPayload: (action as UpdateTodoFilter).payload
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.todo;

export const getTodo = createSelector(selectFeature, (state: TodoState) => {
  return {
    ...state,
    todos: Object.assign([], state.todos)
      .sort((a, b) => a.isMarkedDone - b.isMarkedDone)
      .map((item) => {
        return {
          ...item,
          priority: TodoPriority[item.priority],
        };
      }),
  };
});

export const getTodosCount = createSelector(selectFeature, (state: TodoState) => {
  return {
    today: state.todaysTodosCount,
    upcoming: state.upcomingTodosCount,
    overdue: state.overdueTodosCount,
    completed: state.completedTodosCount,
    all: state.allTodosCount,
    itemsCount: state.itemsCount,
  };
});

export const getTodoFilters = createSelector(selectFeature, (state: TodoState) => {
  return state.filtersPayload;
});

export const getIsTaskLoading = createSelector(selectFeature, (state: TodoState) => {
  return state.isTaskLoading;
});
