import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  TwoFactorOtpGeneration,
  TwoFactorOtpVerify,
} from 'src/app/reducers/login/login.actions';
import {
  getSessionId,
  getUserName,
} from 'src/app/reducers/login/login.reducer';
// import { VerifyUsername } from 'src/app/reducers/forgot-password/forgot-password.actions';
// import { getUser } from 'src/app/reducers/forgot-password/forgot-password.reducers';
// import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';

@Component({
  selector: 'two-factor-authentication',
  templateUrl: './two-factor-authentication.component.html',
})
export class TwoFactorAuthenticationComponent implements OnInit, OnD<PERSON>roy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  otp: any = [null, null, null, null, null, null];
  username: string;
  sessionId: string;
  isExpired: boolean = false;
  countdownTimer: any;
  timerSeconds: number = 180;
  showTryAnotherWay: boolean = false;
  tryAnotherWayCounter = 5;
  tryAnotherWayCounterCompleted: boolean = false;
  userData: any;

  constructor(private store: Store<AppState>, private router: Router) {
    //commented for try another way
    // this.store
    //   .select(getGlobalSettingsAnonymous)
    //   .pipe(takeUntil(this.stopper))
    //   .subscribe((data: any) => {
    //     this.tryAnotherWayCounter = data?.otpSettings?.retryInSecs
    //   })
    this.store
      .select(getUserName)
      .pipe(takeUntil(this.stopper))
      .subscribe((username: string) => {
        if (username === null) {
          this.router.navigate(['login']);
        } else {
          this.username = username;
        }
      });

    this.store
      .select(getSessionId)
      .pipe(takeUntil(this.stopper))
      .subscribe((sessionId: string) => {
        if (sessionId === null) {
          this.router.navigate(['login']);
        } else {
          this.sessionId = sessionId;
        }
      });
  }

  ngOnInit(): void {
    this.startTimer();
    // setTimeout(() => {
    //   this.tryAnotherWayCounterCompleted = true;
    // }, this.tryAnotherWayCounter * 1000);
  }

  startTimer() {
    this.countdownTimer = setInterval(() => {
      if (this.timerSeconds > 0) {
        this.timerSeconds--;
      } else {
        this.isExpired = true;
        clearInterval(this.countdownTimer);
      }
    }, 1000);
  }
  //commented for try another way
  // tryAnotherWay() {
  //   if (!this.showTryAnotherWay) {
  //     this.store.dispatch(new VerifyUsername(this.username));
  //     this.store
  //       .select(getUser)
  //       .pipe(takeUntil(this.stopper))
  //       .subscribe((data: any) => {
  //         this.userData = data;
  //         if (this.userData?.id) {
  //           this.showTryAnotherWay = true;
  //         }
  //       });
  //   }

  // }
  // submitTryAnotherWay() {
  //   let payload = {
  //     userName: this.userData.userName,
  //     mobileNumbers: [this.userData.phoneNumber],
  //     emailIds: [this.userData.email]
  //   };

  //   if (this.userData) {
  //     this.store.dispatch(new TryAnotherWay(payload));
  //     this.showTryAnotherWay = false;
  //     this.tryAnotherWayCounterCompleted = false;
  //   }
  // }
  resendOtp() {
    if (this.isExpired) {
      this.isExpired = false;
      this.timerSeconds = 180;
      this.startTimer();
      this.store.dispatch(new TwoFactorOtpGeneration(this.username));
      for (let i = 0; i < this.otp.length; i++) {
        this.otp[i] = null;
      }
    }
  }

  submitOTP() {
    let otpString = '';
    this.otp.forEach((number: string) => {
      otpString = otpString + number;
    });
    this.store.dispatch(
      new TwoFactorOtpVerify(this.username, this.sessionId, otpString)
    );

    for (let i = 0; i < this.otp.length; i++) {
      this.otp[i] = null;
    }
  }
  getTime() {
    const minutes = Math.floor(this.timerSeconds / 60);
    const seconds = this.timerSeconds % 60;
    const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds.toString();
    return `${minutes}:${formattedSeconds}`;
  }

  otpClickEvent(e: any, next: string, prev?: string) {
    if (e.key === 'Backspace') {
      document.getElementById(prev).innerHTML = null;
      document.getElementById(prev)?.focus();
    } else {
      document.getElementById(next).innerHTML = null;
      document.getElementById(next)?.focus();
    }
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
