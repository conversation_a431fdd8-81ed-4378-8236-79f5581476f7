import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  PermissionsActionTypes,
  UpdatePermissions,
} from './permissions.actions';

export type PermissionsState = {
  permissions: string[];
  canView: string[];
  canAdd: string[];
  canEdit: string[];
  canDelete: string[];
  canExport: string[];
  canBulkUpload: string[];
  canAssign: string[];
  canViewAll: string[];
  isPermissionsLoading: boolean;
};

const initialState: PermissionsState = {
  permissions: [],
  canView: [],
  canAdd: [],
  canEdit: [],
  canDelete: [],
  canExport: [],
  canBulkUpload: [],
  canAssign: [],
  canViewAll: [],
  isPermissionsLoading: true,
};

export function permissionsReducer(
  state: PermissionsState = initialState,
  action: Action
): PermissionsState {
  switch (action.type) {
    case PermissionsActionTypes.UPDATE_PERMISSIONS:
      let permissions = (action as UpdatePermissions).payload;
      let canView: string[] = [];
      let canAdd: string[] = [];
      let canEdit: string[] = [];
      let canDelete: string[] = [];
      let canExport: string[] = [];
      let canBulkUpload: string[] = [];
      let canAssign: string[] = [];
      let canViewAll: string[] = [];

      permissions.forEach((permission: string) => {
        let permissionArray = permission.split('.');
        if (permission.includes('View')) {
          canView.push(permissionArray[1]);
        } else if (permission.includes('Create')) {
          canAdd.push(permissionArray[1]);
        } else if (permission.includes('Update')) {
          canEdit.push(permissionArray[1]);
        } else if (permission.includes('Delete')) {
          canDelete.push(permissionArray[1]);
        } else if (permission.includes('Export')) {
          canExport.push(permissionArray[1]);
        } else if (permission.includes('BulkUpload')) {
          canBulkUpload.push(permissionArray[1]);
        } else if (permission.includes('Assign')) {
          canAssign.push(permissionArray[1]);
        } else if (permission.includes('ViewAll')) {
          canViewAll.push(permissionArray[1]);
        }
      });
      return {
        ...state,
        permissions: [...permissions],
        canView: canView,
        canAdd: canAdd,
        canEdit: canEdit,
        canDelete: canDelete,
        canExport: canExport,
        canBulkUpload: canBulkUpload,
        canAssign: canAssign,
        canViewAll: canViewAll,
        isPermissionsLoading: false
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.permissions;

export const getPermissionsState = createSelector(
  selectFeature,
  (state: PermissionsState) => state
);

export const getPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.permissions
);

export const getPermissionsIsLoading = createSelector(
  selectFeature,
  (state: PermissionsState) => state.isPermissionsLoading
);

export const getViewPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canView
);

export const getAddPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canAdd
);

export const getEditPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canEdit
);

export const getDeletePermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canDelete
);

export const getExportPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canExport
);

export const getBulkUploadPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canBulkUpload
);

export const getAssignPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canAssign
);

export const getViewAllPermissions = createSelector(
  selectFeature,
  (state: PermissionsState) => state.canViewAll
);
