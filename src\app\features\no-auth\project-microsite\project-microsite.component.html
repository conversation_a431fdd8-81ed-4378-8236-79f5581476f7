<div class="m-auto max-w-1260 bg-mixed-pattern tb-bg-mixed-pattern position-relative"
    *ngIf="projectInfo && projectUnit && projectAmentities;else loading">
    <div class="w-100 justify-between min-h-100 tb-h-unset tb-flex-col">
        <div (scroll)="onScroll($event)" #scrollDiv class="flex-grow-1 scrollbar scroll-hide">
            <div class="pt-40 px-40 ph-p-20">
                <div class="sticky" [ngClass]="{'is-sticky': isSticky}">
                    <div class="flex-between ph-flex-between-unset ph-flex-col"
                        [ngClass]="isSticky ? 'bg-coal text-white px-10 pt-10' : 'text-black-200'">
                        <div>
                            <h3 class="text-accent-green fw-600 text-truncate-1 break-all max-w-200">
                                “{{projectInfo?.name}}”</h3>
                            <h4 class="fw-semi-bold mt-4 ml-10" *ngIf="projectInfo?.projectType?.displayName">
                                {{projectInfo?.projectType?.displayName}} </h4>
                            <div class="align-center mr-12 mt-4" *ngIf="getLocationDetailsByObj(projectInfo?.address)">
                                <div class="dot dot-lg bg-ash mr-6">
                                    <div class="icon ic-xxs ic-location-circle ic-coal"></div>
                                </div>
                                <div class="fw-600 text-sm">
                                    {{getLocationDetailsByObj(projectInfo?.address)}}</div>
                            </div>
                        </div>
                        <div>
                            <h6 class="ph-mt-10" *ngIf="projectInfo?.minimumPrice || projectInfo?.maximumPrice">Price
                            </h6>
                            <div class="align-center mt-4">
                                <ng-container *ngIf="projectInfo?.minimumPrice">
                                    <h4 class="fw-700">{{ projectInfo?.minimumPrice ?
                                        formatBudget(projectInfo?.minimumPrice, projectInfo?.monetaryInfo?.currency) :
                                        '' }}</h4>
                                </ng-container>
                                <div class="mx-6" *ngIf="projectInfo?.minimumPrice && projectInfo?.maximumPrice">-</div>
                                <ng-container *ngIf="projectInfo?.maximumPrice">
                                    <h4 class="fw-700">{{ projectInfo?.maximumPrice ?
                                        formatBudget(projectInfo?.maximumPrice, projectInfo?.monetaryInfo?.currency) :
                                        '' }}</h4>
                                </ng-container>
                            </div>
                            <div *ngIf="globalSettingsDetails?.shouldEnableEnquiryForm"
                                class="br-20 py-8 d-flex fw-semi-bold text-sm pr-16 pl-30 mt-10 w-115 cursor-pointer position-relative"
                                [ngClass]="isSticky ? 'bg-white text-black-200 mb-10' : 'bg-coal text-white'"
                                (click)="showEnquire()"> <ng-lottie [options]='tick' height='20px' width="20px"
                                    class="position-absolute left-6 bottom-4"></ng-lottie>contact seller</div>
                        </div>
                    </div>
                    <div class="proj-details d-none">
                        <div class="bg-white w-100 px-24 py-12 brbl-20 brbr-20 mb-24 shadow">
                            <div class="d-flex section-border">
                                <div class="activation" [ngClass]="{ 'active': currentActive == 0 }"
                                    (click)="scrollTo('overview', 0)">
                                    <span>Overview</span>
                                    <span class="down-border"></span>
                                </div>
                                <div *ngIf="sortedUniqueNoOfBHK?.length > 0" class="activation"
                                    [ngClass]="{ 'active': currentActive == 1 }" (click)="scrollTo('unitRange', 1)">
                                    <span>Unit Range</span>
                                    <span class="down-border"></span>
                                </div>
                                <div *ngIf="projectAmentities?.amenitiesDto?.length" class="activation"
                                    [ngClass]="{ 'active': currentActive == 2 }" (click)="scrollTo('amenities', 2)">
                                    <span>Amenities</span>
                                    <span class="down-border"></span>
                                </div>
                                <div *ngIf="projectInfo?.videos?.length" class="activation"
                                    [ngClass]="{ 'active': currentActive == 3 }" (click)="scrollTo('video', 3)">
                                    <span>Video</span>
                                    <span class="down-border"></span>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <project-images [projectInfo]="projectInfo" [projectUnit]="projectUnit"></project-images>
            </div>
            <div class="tb-d-none">
                <div class="pink-circle"></div>
                <div class="ellipse"></div>
                <div class="blue-dot"></div>
                <div class="rectangle"></div>
                <div class="green-circle"></div>
                <div class="rhombus"></div>
                <!-- <div class="purple-dot"></div> -->
                <div class="triangle"></div>
                <div class="dot-mixed"></div>
                <div class="shake"></div>
                <div class="polygon"></div>
                <div class="dot-accent-green"></div>
                <div class="bubble"></div>
                <div class="red-dot"></div>
                <div class="octa"></div>
            </div>
            <div [ngClass]="!globalSettingsDetails?.shouldEnableEnquiryForm ? 'justify-center' : 'justify-between'"
                class="w-100 tb-h-unset tb-flex-col">
                <div class="pl-40 pb-20 ph-pl-20">
                    <div class="max-w-730 min-w-730 w-730 tb-w-unset tb-max-w-unset tb-min-w-unset">
                        <div class="mr-24 tb-mr-0 scrollbar max-h-100-176 tb-max-h-unset">
                            <div class="position-relative" #overview>
                                <div class="position-absolute ntop-10" id="overview"></div>
                                <project-details [projectInfo]="projectInfo" [projectUnit]="projectUnit"
                                    [areaSizeUnits]="areaSizeUnits"></project-details>
                            </div>
                            <div class="position-relative" #unitRange>
                                <div class="position-absolute ntop-10" id="unitRange"></div>
                                <h4 class="fw-600 text-decoration-underline mt-20"
                                    *ngIf="sortedUniqueNoOfBHK?.length > 0">Unit Range</h4>
                                <div class="mt-10 mr-20">
                                    <unit-range [projectUnit]="projectUnit" [projectInfo]="projectInfo"
                                        [areaSizeUnits]="areaSizeUnits"
                                        (sortedUniqueNoOfBHKChange)="updateSortedUniqueNoOfBHK($event)"></unit-range>
                                </div>
                            </div>
                            <div class="position-relative" *ngIf="projectAmentities?.amenitiesDto?.length" #amenities>
                                <div class="position-absolute ntop-10" id="amenities"></div>
                                <h4 class="fw-600 text-decoration-underline mt-20">Amenities</h4>
                                <project-amenities [projectAmentities]="projectAmentities"></project-amenities>
                            </div>
                            <div class="position-relative" *ngIf="projectInfo?.videos?.length">
                                <div class="position-absolute ntop-10" id="video"></div>
                                <h4 class="fw-600 text-decoration-underline mt-20">
                                    Project Walk-through</h4>
                                <project-videos [projectInfo]="projectInfo"></project-videos>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="min-w-490 tb-min-w-unset tb-mt-0 tb-mb-100 scrollbar scroll-hide max-h-100-176">
                    <div *ngIf="globalSettingsDetails?.shouldEnableEnquiryForm" class="w-460 tb-d-none">
                        <project-enquiry-form [projectUnit]="projectUnit"
                            [projectInfo]="projectInfo"></project-enquiry-form>
                    </div>
                    <div class="pb-20 tb-pr-0 tb-pl-40 ph-pl-20 w-460 tb-w-unset"
                        *ngIf="projectInfo?.brochures?.length">
                        <brochures [projectInfo]="projectInfo"></brochures>
                    </div>
                    <div *ngIf="globalSettingsDetails?.shouldEnableEnquiryForm"
                        class="tb-d-block d-none w-100 position-fixed bottom-0 p-20 bg-white brtl-10 brtr-10">
                        <h4 class="bg-black-100 text-white fw-600 justify-center py-16 br-10" (click)="showEnquire()">
                            Enquire Now</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<ng-template #loading>
    <div class="flex-center h-100">
        <application-loader></application-loader>
    </div>
</ng-template>