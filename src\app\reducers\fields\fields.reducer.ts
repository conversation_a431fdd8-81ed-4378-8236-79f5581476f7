import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
    FetchFieldsSuccess,
    FetchSelectedFieldsSuccess,
    FieldsActionTypes,
} from './fields.action';

export type FieldsState = {
    AllFields: any;
    SelectedFields: any;
    SelectedFieldsIsLoading: boolean;
};

const initialState: FieldsState = {
    AllFields: [],
    SelectedFields: [],
    SelectedFieldsIsLoading: true,
};

export function FieldsReducer(
    state: FieldsState = initialState,
    action: Action
): FieldsState {
    switch (action.type) {
        case FieldsActionTypes.FETCH_FIELDS_SUCCESS:
            return {
                ...state,
                AllFields: (action as FetchFieldsSuccess).response,
            };
        case FieldsActionTypes.FETCH_SELECTED_FIELDS:
            return {
                ...state,
                SelectedFieldsIsLoading: true,
            };
        case FieldsActionTypes.FETCH_SELECTED_FIELDS_SUCCESS:
            return {
                ...state,
                SelectedFields: (action as FetchSelectedFieldsSuccess).response,
                SelectedFieldsIsLoading: false,
            };

        default:
            return state;
    }
}

export const selectFeature = (state: AppState) => state.fields;

export const getFields = createSelector(
    selectFeature,
    (state: FieldsState) => state.AllFields
);

export const getSelectedFields = createSelector(
    selectFeature,
    (state: FieldsState) => {
        return {
            info: state.SelectedFields,
            loader: state.SelectedFieldsIsLoading
        };
    }
);
