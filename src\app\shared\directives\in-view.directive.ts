import { Directive, ElementRef, Output, EventEmitter, OnDestroy } from '@angular/core';

@Directive({
  selector: '[inView]'
})
export class InViewDirective implements OnDestroy {
  private observer: IntersectionObserver;

  @Output() inView: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(private elementRef: ElementRef) {
    this.observer = new IntersectionObserver(entries => {
      this.handleIntersection(entries);
    });
    this.observer.observe(this.elementRef.nativeElement);
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    const isVisible = entries.some(entry => entry.isIntersecting);
    this.inView.emit(isVisible);
  }

  ngOnDestroy() {
    this.observer.disconnect();
  }
}
