import { Component, ElementRef, EventEmitter, On<PERSON><PERSON>roy, TemplateRef, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { USER_EXCEL_TEMPLATE } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { FetchUserExcelUploadedList, UploadMappedColumns, UserExcelUpload } from 'src/app/reducers/teams/teams.actions';
import { getDuplicateUsers } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'excel-upload',
  templateUrl: './excel-upload.component.html',
})
export class ExcelUploadComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  isTemplateDownloaded: boolean = false;
  isFileDataUpdated: boolean = false;
  isFileUploadTriggered: boolean = false;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  selectedFile: File;
  excelTemplatePath: string = USER_EXCEL_TEMPLATE;
  duplicateUsers: any[] = [];
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  formKeys: Array<string> = [];
  s3BucketKey: string;
  origin: any;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private router: Router,
  ) { }

  ngOnInit() {
    this.metaTitle.setTitle('CRM | Import Users');
    this.headerTitle.setLangTitle('Import Users');
  }

  checkFileFormat(fileName: string) {
    const allowedFiles = ['xls', 'xlx', 'csv', 'xlsx'];
    const regex = /(?:\.([^.]+))?$/;
    const extension = regex.exec(fileName);
    this.isFileTypeSupported = allowedFiles.includes(extension[1]);
    return this.isFileTypeSupported;
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep = this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  uploadFile() {
    if (!this.isFileTypeSupported) {
      return;
    }
    this.store.dispatch(new UserExcelUpload(this.selectedFile));
    this.store.select(getDuplicateUsers).subscribe((data: any) => {
      this.excelSheets = data?.multiSheetColumnNames;
      if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
      this.selectedSheet = this.sheetNames[0];
      this.formKeys = data?.columnNames;
      this.origin = data?.origin;
      this.s3BucketKey = data?.s3BucketKey;
      if (this.formKeys?.length) {
        this.currentStep = 3;
      }
    });
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  confirmSheet() {
    this.currentStep = 4;
    this.modalRef.hide();
  }

  sendDetails(trackerInfoModal: TemplateRef<any>) {
    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      origin: this.origin
    };

    payload.sheetName = this.selectedSheet || this.sheetNames[0];
    this.store.dispatch(new UploadMappedColumns(payload));
    this.store.dispatch(new FetchUserExcelUploadedList(1, 10));
    this.modalRef = this.modalService.show(
      trackerInfoModal,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  openBulkUploadedStatusModal() {
    this.navigateToHome();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: 'user',
    };
    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState
    });
  }

  navigateToHome() {
    this.router.navigate(['/teams/manage-user']);
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
