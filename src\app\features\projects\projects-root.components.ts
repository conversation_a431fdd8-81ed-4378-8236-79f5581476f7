import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'root-layout',
  template: `<router-outlet></router-outlet>`,
})
export class ProjectsRootLayoutComponent implements OnInit {
  currentLang: string = localStorage.getItem('locale')
    ? localStorage.getItem('locale')
    : 'en';
  constructor(private translate: TranslateService) {}

  ngOnInit() {
    /* this language will be used as a fallback when a translation isn't found in the current language */
    this.translate.setDefaultLang('en');
    /* the lang to use */
    this.translate.use(this.currentLang);
  }
}
