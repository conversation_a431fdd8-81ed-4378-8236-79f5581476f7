<div class="bg-coal w-100 px-20 py-12 text-white brtl-10 brtr-10 flex-between">
    <h3 class="fw-semi-bold">Project - Export Tracker</h3>
    <button class="btn btn-sm btn-linear-green align-center" (click)="updateTrackerList()">
        <span class="ic-refresh icon ic-xxs mr-8 ph-mr-0"></span>
        <span class="text-white text-normal ph-d-none">{{'BULK_LEAD.refresh-data' | translate}}</span>
    </button>
    <a class="ic-close-secondary ic-close-modal tb-ic-close-secondary" (click)="modalService.hide()"></a>
</div>
<div class="max-h-100-176 scrollbar">
    <ag-grid-angular #agGrid *ngIf="!isDataExportStatusLoading; else gridLoader" class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize"
        [gridOptions]="gridOptions" [rowData]="rowData" [suppressPaginationPanel]="true"
        (gridReady)="onGridReady($event)">
    </ag-grid-angular>
</div>
<div class="flex-end m-20">
    <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(totalReqCount,pageSize)"
        (pageChange)="onPageChange($event)">
    </pagination>
</div>

<ng-template #gridLoader>
    <div class="flex-center h-100 mt-60">
        <application-loader></application-loader>
    </div>
</ng-template>