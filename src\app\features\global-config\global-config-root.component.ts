import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'global-config',
  template: `<router-outlet></router-outlet>`,
})
export class GlobalConfigComponent implements OnInit {
  currentLang: string = localStorage.getItem('locale')
    ? localStorage.getItem('locale')
    : 'en';
  constructor(private translate: TranslateService) {}
  ngOnInit(): void {
    this.translate.setDefaultLang('en');
    this.translate.use(this.currentLang);
  }
}
