import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { getTenantName } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
    providedIn: 'root'
})
export class CustomSubStatusService extends BaseService<any> {
    public page: number;
    public count: number;
    serviceBaseUrl: string;
    tenant: string = getTenantName();
    headers: HttpHeaders = new HttpHeaders().set('tenant', this.tenant);

    constructor(private http: HttpClient) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    }

    getResourceUrl(): string {
        return 'customsubstatus';
    }

    getCustomSubStatus() {
        return this.http.get(`${this.serviceBaseUrl}/custom/masterlead-sub-statuses`);
    }

    getCustomSubStatusId(id: string) {
        return this.http.get(`${this.serviceBaseUrl}/custom/masterlead-sub-statuses/${id}`);
    }

    getCustomLeadCount() {
        return this.http.get(`${this.serviceBaseUrl}/get/all/leadscountsubstatus`);
    }

    updateCustomSubStatus(payload: any) {
        return this.http.put(`${this.serviceBaseUrl}/update`, payload);
    }

    createCustomSubStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/substatus`, payload);
    }

    deleteSubStatus(id: string) {
        return this.http.delete(`${this.serviceBaseUrl}/delete/custom-lead-sub-status/${id}`);
    }

    existSubStatus(subStatus:string) {
        return this.http.get(`${this.serviceBaseUrl}/custom/status-exist?status=${subStatus}`);
    }
}
