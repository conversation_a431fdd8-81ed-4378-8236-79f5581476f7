.ag-theme-alpine {
    .ag-root-wrapper {
        font-family: "Lexend Deca", "DM Sans", sans-serif;

        .ag-root-wrapper-body {
            .ag-header {
                height: 40px !important;
                min-height: 30px !important;
                background-color: $white;

                .ag-header-row {
                    height: 30px !important;

                    .ag-header-cell {
                        .ag-header-cell-resize::after {
                            top: auto !important;
                            height: 100% !important;
                        }

                        .ag-header-cell-label {
                            @extend .justify-content-between;

                            .ag-header-icon {
                                .ic-filter {
                                    display: none;
                                }

                                .ag-icon {
                                    font-size: 10px;
                                    font-family: "leadRat" !important;
                                    margin: 0 4px 0px 0px !important;

                                    &.ag-icon-desc::after {
                                        content: "\e909";
                                    }

                                    &.ag-icon-asc {
                                        transform: rotate(180deg);

                                        &::before {
                                            display: none;
                                        }

                                        &::after {
                                            content: "\e909";
                                        }
                                    }
                                }
                            }

                            .ag-header-cell-text {
                                font-size: 11px;
                                font-weight: 600;
                                color: $slate-40;
                                @extend .fv-sm-caps;
                            }
                        }
                    }
                }
            }

            .ag-body-viewport {
                .ag-center-cols-clipper {
                    .ag-center-cols-container {
                        .ag-row {
                            border-top: 0 !important;
                            border-bottom: 0 !important;

                            .ag-cell-focus {
                                border: 1px solid transparent;
                            }

                            .ag-cell {
                                display: flex;
                                align-items: center;
                            }

                            &.ag-row-hover {
                                background-color: $dark-550;
                            }

                            &.ag-row-selected {
                                background-color: $dark-550;
                            }
                        }
                    }
                }
            }
        }

        .ag-checkbox-input-wrapper {
            background: none !important;
            font-family: "leadRat" !important;

            &:focus-within {
                box-shadow: none !important;
            }

            input {
                @extend .cursor-pointer;
                display: block !important;
                opacity: 0;
                z-index: 1;
            }

            &::after {
                content: "\e98d";
                @extend .position-absolute;
            }

            &.ag-checked::after {
                content: "\e98b";
                @extend .position-absolute;
            }

            &.ag-indeterminate::after {
                content: "\e98c";
                @extend .position-absolute;
            }
        }
    }
}

.ag-theme-alpine .ag-icon-menu::before {
    font-size: 10px;
    font-family: "leadRat" !important;
    content: "\e935" !important;
    color: $dark-400;
    @extend .cursor-pointer;
}

.ag-body-viewport {
    @extend .scrollbar;
}

.manage-leads,
.manage-property {
    .ag-body-viewport {
        max-height: calc(100dvh - 167px) !important;
    }
}

.manage-listing {
    .ag-body-viewport {
        max-height: calc(100dvh - 179px) !important;
    }
}

.manage-block {
    .ag-body-viewport {
        max-height: calc(100dvh - 380px) !important;
    }
}

.manage-data {
    .ag-body-viewport {
        max-height: calc(100dvh - 177px) !important;
    }
}

.locality {
    .ag-body-viewport {
        max-height: calc(100dvh - 220px) !important;
    }
}

.manage-project {
    .ag-body-viewport {
        max-height: calc(100dvh - 167px) !important;
    }
}

.manage-user {
    .ag-body-viewport {
        max-height: calc(100dvh - 300px) !important;
    }
}

.manage-reference {
    .ag-body-viewport {
        max-height: calc(100dvh - 187px) !important;
    }
}

.manage-members {
    .ag-body-viewport {
        max-height: calc(100dvh - 290px) !important;
    }
}

.reports {
    .ag-body-viewport {
        max-height: calc(100dvh - 330px) !important;
    }
}

.matching {
    .ag-body-viewport {
        max-height: calc(100dvh - 320px) !important;
    }

    .ag-body-horizontal-scroll {
        min-width: calc(100vw - 391px) !important;
    }
}

.user-details {
    .ag-body-viewport {
        max-height: calc(100dvh - 320px) !important;
    }
}

.user-previous-exp,
.user-signature {
    .ag-body-viewport {
        @extend .hmq-max-h-100-600;
        max-height: calc(100dvh - 400px) !important;
    }
}

.attendance-grid {
    .ag-header-cell {
        @extend .border-right;
    }

    .ag-body-viewport {
        max-height: calc(100dvh - 300px) !important;
    }

    .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-body-viewport .ag-center-cols-clipper .ag-center-cols-container .ag-row .ag-cell {
        display: flex !important;
        padding-top: 0px !important;
        padding-left: 0px;
        padding-right: 0px;
        @extend .bg-light-pearl;
    }

    .ag-row.ag-row-level-0.ag-row-position-absolute {
        border: none !important;
        border-bottom: 1px solid $dark-400 !important;
    }

    .ag-theme-alpine .ag-cell {
        border-right: 1px solid $dark-400 !important;
    }

    .ag-cell-value.ag-cell.ag-cell-not-inline-editing.ag-cell-normal-height.ag-cell-last-left-pinned.lock-pinned.ag-cell-focus {
        border-color: $white !important;
        border-right-color: $dark-400 !important;
    }

    .ag-row.ag-row-level-0.ag-row-position-absolute.ag-row-focus {
        z-index: 0;
    }
}

.pinned-grid {

    //right-grid
    .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-body-viewport .ag-center-cols-clipper .ag-center-cols-container .ag-row .ag-cell {
        background-color: $white !important;
    }

    .ag-row.ag-row-level-0.ag-row-position-absolute {
        border: none !important;
        border-bottom: 1px solid $dark-400 !important;
    }

    //  if need border-right in ag-grid
    // .ag-theme-alpine .ag-cell {
    //     border-right: 1px solid $dark-400 !important;
    // }
    //left-grid
    .ag-theme-alpine .ag-row {
        background-color: $white !important;
    }

    .ag-cell-value.ag-cell.ag-cell-not-inline-editing.ag-cell-normal-height.ag-cell-last-left-pinned.lock-pinned.ag-cell-focus {
        border-color: $white !important;
        border-right-color: $dark-400 !important;
    }

    .ag-theme-alpine .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
        border-color: $white !important;
        border-right-color: $dark-400 !important;
    }

    .ag-theme-alpine .ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {
        border-color: $white !important;
    }

    .ag-row.ag-row-level-0.ag-row-position-absolute.ag-row-focus {
        z-index: 0;
    }
}

.ag-header-row-floating-filter {
    transform: translateY(-42px);
}

.ag-row.ag-row-level-0.ag-row-position-absolute.ag-row-focus {
    z-index: 999;
}

.ag-row.ag-row-level-0.ag-row-position-absolute .ag-cell-focus {
    overflow: visible;
}

.ag-icon {
    &.ag-icon-menu {
        &::before {
            color: $dark-400 !important;
        }
    }

    &.ag-icon.ag-icon-none {
        opacity: 0;
        transition: opacity 0.2s;

        .ag-header-cell:hover & {
            opacity: 1;
        }
    }

    &.ag-icon.ag-icon-desc,
    &.ag-icon.ag-icon-asc,
    &.ag-icon.ag-icon-none {
        margin-top: 2px !important;
        color: $dark-400 !important;

        &::after {
            color: $dark-400 !important;
        }
    }
}

.ag-theme-alpine .ag-root-wrapper {
    border: none !important;

    .ag-root-wrapper-body {
        .ag-body-viewport .ag-center-cols-clipper .ag-center-cols-container .ag-row {
            border-bottom: 1px solid $dark-400 !important;
        }

        .ag-header {
            background-color: $primary-black !important;
            border: 1px solid $dark-400 !important;

            .ag-header-row {
                height: 35px !important;

                .ag-header-cell .ag-header-cell-label .ag-header-icon .ag-icon {
                    margin: 4px 8px 0px 0px !important;
                }
            }
        }
    }

    .ag-header-cell-resize {
        &::after {
            background: none !important;
            border-left: 1px solid $dark-400;
        }
    }
}

.ag-body-horizontal-scroll {
    max-height: 5px !important;
    min-height: 5px !important;
    min-width: calc(100vw - 166px) !important;

    .ag-body-horizontal-scroll-viewport {
        @extend .scrollbar;
        max-height: 5px !important;
        min-height: 5px !important;
    }
}

.ag-cell {
    white-space: normal !important;
}

.ag-theme-alpine .ag-row {
    @extend .text-coal, .fw-semi-bold;
}

.ag-theme-alpine .ag-row-selected::before {
    background-color: unset !important;
}

.ag-theme-alpine .ag-row-hover:not(.ag-full-width-row)::before,
.ag-theme-alpine .ag-row-hover.ag-full-width-row.ag-row-group::before {
    background-color: unset;
}

.ag-theme-alpine .ag-row-hover.ag-row-selected::before {
    background-image: unset;
}

.ag-theme-alpine .ag-checkbox-input-wrapper.ag-checked::after,
.ag-theme-alpine .ag-checkbox-input-wrapper.ag-indeterminate::after {
    border-radius: 3px;
    // border: 1px solid $green-50;
    color: $accent-green;
    background-color: $white;
}

.checkbox-align-h-60 {
    .ag-theme-alpine .ag-cell-wrapper>*:not(.ag-cell-value):not(.ag-group-value) {
        height: 60px !important;
    }

    .ag-theme-alpine .ag-cell {
        --ag-internal-padded-row-height: 60px !important;
    }
}

.ag-grid-grouping {
    .ag-header-row {
        position: unset !important;
    }

    .ag-header-group-cell {
        border-left: 1px solid white !important;
    }

    .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-header {
        min-height: 50px !important;
    }

    .ag-header-group-cell-label,
    .ag-header-cell-label {
        flex-direction: column;
    }

    .ag-header-cell-label {
        display: flex;
        align-items: start !important;
    }

    .ag-header-cell-resize {
        height: 150% !important;
    }

    .ag-header-group-text {
        border-bottom: 1px solid $slate-40 !important;
        @extend .text-sm, .text-light-slate, .fw-600;
        margin-top: 6px !important;
    }

    .ag-theme-alpine .ag-pinned-left-header {
        border-right: unset !important;
    }

    .ag-theme-alpine .ag-header-row:not(:first-child) .ag-header-cell {
        border: unset !important;
    }

    .ag-theme-alpine .ag-root-wrapper .ag-header-cell-resize::after {
        border-left: unset !important;
    }

    .ag-cell-label-container {
        height: 130% !important;
        align-items: start !important;
    }

    .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-header .ag-header-row {
        height: 30px !important;
    }
}
.ag-theme-alpine .ag-row.bg-red-410 {
    background-color: $red-410 !important;
  }