import { Component, EventEmitter, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'save-changes',
  templateUrl: './save-changes.component.html',
})
export class SaveChangesComponent {
  @Output() SaveChanges: EventEmitter<any> = new EventEmitter<any>();
  @Output() DiscardChanges: EventEmitter<any> = new EventEmitter<any>();
  @Output() Hide: EventEmitter<any> = new EventEmitter<any>();

  constructor(public modalRef: BsModalRef) {}


  discardChanges(e?: any) {
    e ? this.DiscardChanges.emit(e) : this.DiscardChanges.emit();
  }

  saveChanges(e?: any) {
    e ? this.SaveChanges.emit(e) : this.SaveChanges.emit();
  }

  hide(e?: any) {
    e ? this.Hide.emit(e) : this.Hide.emit();
  }
}
