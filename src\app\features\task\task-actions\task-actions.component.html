<div class="align-center">
  <div title="Edit" *ngIf="canEditTask" class="bg-accent-green icon-badge">
    <span class="icon ic-pen m-auto ic-xxs" id="clkEditTodo" data-automate-id="clkEditTodo"
      (click)="initEditTodo(params.data)"></span></div>
  <div title="Delete" *ngIf="canDeleteTask" class="bg-light-red icon-badge">
    <span class="icon ic-delete m-auto ic-xxs" id="clkDeleteTodo" data-automate-id="clkDeleteTodo"
      (click)="openDeleteToDoModal(params.data)"></span></div>
  <div [title]="params.data?.isMarkedDone ? 'Completed' : 'Mark as complete'">
    <div class="icon-badge" (click)="markAsDone(params.data)"
      [ngClass]="params.data?.isMarkedDone ? 'bg-linear-gradient-green pe-none' : 'bg-pearl bg-hover-green'">
      <span class="icon ic-tick m-auto ic-xxs ic-pale icon-hover-white" id="clkSaveTodo"
        data-automate-id="clkSaveTodo"></span>
    </div>
  </div>
</div>