import { Action, createSelector } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import {
  Profile,
  Recognition,
  Subscription,
  Testimonial,
  Transaction,
} from 'src/app/core/interfaces/profile.interface';
import {
  AssignPageSize,
  FetchProfileSuccess,
  FetchQRCodeSuccess,
  FetchRecognitionSuccess,
  FetchSubscriptionSuccess,
  FetchTestimonialSuccess,
  FetchTimeZoneInfoSuccess,
  FetchTransactionInfoSuccess,
  FetchTransactionSuccess,
  profileActionTypes,
} from 'src/app/reducers/profile/profile.actions';

export type ProfileState = {
  transactionInfo: any;
  profile: Profile;
  isProfileLoading: boolean;
  testimonials: Testimonial[];
  recognition: Recognition[];
  qrCode: any;
  subscription: Subscription;
  transaction: Transaction;
  pageSize: number;
  pageNumber: number;
  timeZoneInfo: any,

};

const initialState: ProfileState = {
  profile: {} as Profile,
  isProfileLoading: true,
  testimonials: [] as Testimonial[],
  recognition: [] as Recognition[],
  qrCode: [],
  subscription: {} as Subscription,
  transaction: {} as Transaction,
  transactionInfo: {},
  pageSize: 10,
  pageNumber: 1,
  timeZoneInfo: [],
};

export function profileReducer(
  state: ProfileState = initialState,
  action: Action
): ProfileState {
  switch (action.type) {
    case profileActionTypes.FETCH_PROFILE:
      return {
        ...state,
        isProfileLoading: true
      };
    case profileActionTypes.FETCH_PROFILE_SUCCESS:
      return {
        ...state,
        profile: (action as FetchProfileSuccess).profile,
        isProfileLoading: false
      };
    case profileActionTypes.FETCH_TESTIMONIALS_SUCCESS:
      return {
        ...state,
        testimonials: (action as FetchTestimonialSuccess).testimonials,
      };
    case profileActionTypes.FETCH_RECOGNITION_SUCCESS:
      return {
        ...state,
        recognition: (action as FetchRecognitionSuccess).recognitionList,
      };
    case profileActionTypes.FETCH_QR_CODE_SUCCESS:
      return {
        ...state,
        qrCode: (action as FetchQRCodeSuccess).response,
      };
    case profileActionTypes.FETCH_TRANSACTION_SUCCESS:
      return {
        ...state,
        transaction: (action as FetchTransactionSuccess).response,
      };
    case profileActionTypes.FETCH_SUBSCRIPTION_SUCCESS:
      return {
        ...state,
        subscription: (action as FetchSubscriptionSuccess).subscription,
      };
    case profileActionTypes.FETCH_TRANSACTION_INFO_SUCCESS:
      return {
        ...state,
        transactionInfo: (action as FetchTransactionInfoSuccess).response,
      };
    case profileActionTypes.ASSIGN_PAGE_SIZE:
      return {
        ...state,
        pageSize: (action as AssignPageSize).pageSize,
        pageNumber: (action as AssignPageSize).pageNumber,
      }
    case profileActionTypes.FETCH_TIMEZONE_INFO_SUCCESS:
      return {
        ...state,
        timeZoneInfo: (action as FetchTimeZoneInfoSuccess).response,
      }
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.profile;

export const getProfile = createSelector(
  selectFeature,
  (state: ProfileState) => state.profile
);

export const getProfileIsLoading = createSelector(
  selectFeature,
  (state: ProfileState) => state.isProfileLoading
);

export const getTestimonials = createSelector(
  selectFeature,
  (state: ProfileState) => state.testimonials
);

export const getRecognition = createSelector(
  selectFeature,
  (state: ProfileState) => state.recognition
);

export const getQRCode = createSelector(
  selectFeature,
  (state: ProfileState) => state.qrCode
);

export const getSubscription = createSelector(
  selectFeature,
  (state: ProfileState) => state.subscription
);

export const getTransaction = createSelector(
  selectFeature,
  (state: ProfileState) => state.transaction
);

export const getTransactionInfo = createSelector(
  selectFeature,
  (state: ProfileState) => state.transactionInfo
);

export const getPageSize = createSelector(
  selectFeature,
  (state: ProfileState) => state.pageSize
);
export const getPageNumber = createSelector(
  selectFeature,
  (state: ProfileState) => state.pageNumber
)

export const getTimeZoneInfo = createSelector(
  selectFeature,
  (state: ProfileState) => state.timeZoneInfo
);
