import { Action } from '@ngrx/store';
import { LeadSource } from 'src/app/app.enum';

export enum AutomationActionTypes {
  FETCH_PRIORITY_LIST = '[AUTOMATION] Fetch Priority List',
  FETCH_PRIORITY_LIST_SUCCESS = '[AUTOMATION] Fetch Priority List Success',
  UPDATE_PRIORITY_LIST = '[AUTOMATION] Update Priority List',
  FETCH_INTEGRATION_ASSIGNMENT = '[AUTOMATION] Fetch Integration Assignment',
  FETCH_INTEGRATION_ASSIGNMENT_SUCCESS = '[AUTOMATION] Fetch Integration Assignment Success',
  FETCH_USER_ASSIGNMENT = '[AUTOMATION] Fetch User Assignment',
  FETCH_USER_ASSIGNMENT_SUCCESS = '[AUTOMATION] Fetch User Assignment Success',
  FETCH_USER_ASSIGNMENT_BY_ID = '[AUTOMATION] Fetch User Assignment By Id',
  FETCH_USER_ASSIGNMENT_BY_ID_SUCCESS = '[AUTOMATION] Fetch User Assignment By Id Success',
  FETCH_USER_ASSIGNMENT_BY_ENTITY = '[AUTOMATION] Fetch User Assignment By Entity',
  FETCH_USER_ASSIGNMENT_BY_ENTITY_SUCCESS = '[AUTOMATION] Fetch User Assignment By Entity Success',
  UPDATE_USER_ASSIGNMENT = '[AUTOMATION] Update User Assignment',
  UPDATE_MULTI_USER_ASSIGNMENT = '[AUTOMATION] Update Multiple User Assignment',
  UPDATE_INTEGRATION_ASSIGNMENT = '[AUTOMATION] Update Integration Assignment',
  UPDATE_PROJECT_ASSIGNMENT = '[AUTOMATION] Update Project Assignment',
  UPDATE_LOCATION_ASSIGNMENT = '[AUTOMATION] Update Location Assignment',
  UPDATE_COUNTRY_CODE_ASSIGNMENT = '[AUTOMATION] Update Country Code Assignment',
}

export class FetchPriorityList implements Action {
  readonly type: string = AutomationActionTypes.FETCH_PRIORITY_LIST;
  constructor() { }
}

export class FetchPriorityListSuccess implements Action {
  readonly type: string = AutomationActionTypes.FETCH_PRIORITY_LIST_SUCCESS;
  constructor(public response: any = {}) { }
}

export class updatePriorityList implements Action {
  readonly type: string = AutomationActionTypes.UPDATE_PRIORITY_LIST;
  constructor(public payload: any) { }
}

export class FetchUserAssignment implements Action {
  readonly type: string = AutomationActionTypes.FETCH_USER_ASSIGNMENT;
  constructor() { }
}

export class FetchUserAssignmentSuccess implements Action {
  readonly type: string = AutomationActionTypes.FETCH_USER_ASSIGNMENT_SUCCESS;
  constructor(public response: any = {}) { }
}

export class UpdateUserAssignment implements Action {
  readonly type: string = AutomationActionTypes.UPDATE_USER_ASSIGNMENT;
  constructor(
    public payload: any,
    public moduleName?: any,
    public isMultiple?: boolean
  ) { }
}

export class UpdateMultiUserAssignment implements Action {
  readonly type: string = AutomationActionTypes.UPDATE_MULTI_USER_ASSIGNMENT;
  constructor(public payload: any, public moduleName: any) { }
}

export class FetchUserAssignmentById implements Action {
  readonly type: string = AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ID;
  constructor(public id: string) { }
}

export class FetchUserAssignmentByIdSuccess implements Action {
  readonly type: string =
    AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ID_SUCCESS;
  constructor(public response: any = {}) { }
}

export class FetchUserAssignmentByEntity implements Action {
  readonly type: string = AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ENTITY;
  constructor(public entityId: string) { }
}

export class FetchUserAssignmentByEntitySuccess implements Action {
  readonly type: string =
    AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ENTITY_SUCCESS;
  constructor(public response: any = {}) { }
}

export class FetchIntegrationAssignment implements Action {
  readonly type: string = AutomationActionTypes.FETCH_INTEGRATION_ASSIGNMENT;
  constructor(
    public queryParams: {
      id: string;
      source: LeadSource;
    }
  ) { }
}

export class FetchIntegrationAssignmentSuccess implements Action {
  readonly type: string =
    AutomationActionTypes.FETCH_INTEGRATION_ASSIGNMENT_SUCCESS;
  constructor(public response: any = {}) { }
}

export class updateIntegrationAssignment implements Action {
  readonly type: string = AutomationActionTypes.UPDATE_INTEGRATION_ASSIGNMENT;
  constructor(public payload: any, public isMultiple: boolean = false) { }
}

export class updateProjectAssignment implements Action {
  readonly type: string = AutomationActionTypes.UPDATE_PROJECT_ASSIGNMENT;
  constructor(public payload: any) { }
}

export class updateLocationAssignment implements Action {
  readonly type: string = AutomationActionTypes.UPDATE_LOCATION_ASSIGNMENT;
  constructor(public payload: any) { }
}

export class updateCountryCodeAssignment implements Action {
  readonly type: string = AutomationActionTypes.UPDATE_COUNTRY_CODE_ASSIGNMENT;
  constructor(public payload: any) { }
}
