import { Action } from '@ngrx/store';

export enum FieldsActionTypes {
    FETCH_FIELDS = '[FIELDS] Fetch Fields',
    FETCH_FIELDS_SUCCESS = '[FIELDS] Fetch Fields Success',
    FETCH_SELECTED_FIELDS = '[FIELDS] Fetch Selected Fields',
    FETCH_SELECTED_FIELDS_SUCCESS = '[FIELDS] Fetch Selected Fields Success',
    UPDATE_FORM = '[FIELDS] Update  Form',
    UPDATE_FORM_SUCCESS = '[FIELDS] Update Form Success',
}

export class FetchFields implements Action {
    readonly type: string = FieldsActionTypes.FETCH_FIELDS;
    constructor() { }
}
export class FetchFieldsSuccess implements Action {
    readonly type: string = FieldsActionTypes.FETCH_FIELDS_SUCCESS;
    constructor(public response: any = {}) { }
}

export class FetchSelectedFields implements Action {
    readonly type: string = FieldsActionTypes.FETCH_SELECTED_FIELDS;
    constructor() { }
}
export class FetchSelectedFieldsSuccess implements Action {
    readonly type: string = FieldsActionTypes.FETCH_SELECTED_FIELDS_SUCCESS;
    constructor(public response: any = {}) { }
}

export class UpdateForm implements Action {
    readonly type: string = FieldsActionTypes.UPDATE_FORM;
    constructor(public payload: any) { }
}

export class UpdateFormSuccess implements Action {
    readonly type: string = FieldsActionTypes.UPDATE_FORM_SUCCESS;
    constructor() { }
}