import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { WhatsAppInboxComponent } from './whatsApp-inbox/whatsApp-inbox.component';
import { WhatsAppInboxAdvFiltersComponent } from './whatsApp-inbox/whatsApp-inbox-adv-filters/whatsApp-inbox-adv-filters.component';

export const routes: Routes = [
    { path: '', redirectTo: 'whatsApp-inbox', pathMatch: 'full' },
    { path: 'whatsApp-inbox', component: WhatsAppInboxComponent },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class WhatsAppRoutingModule { }

export const WHATSAPP_DECLARATIONS = [
    WhatsAppInboxComponent,
    WhatsAppInboxAdvFiltersComponent,
];
