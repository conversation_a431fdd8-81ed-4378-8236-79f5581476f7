<div class="my-20 mx-30">
    <div class="text-coal header-2">
        <span class="fw-semi-bold mr-4">{{'USER.total-employees' | translate }}:</span>
        <span class="fw-600">40</span>
    </div>
    <div class="d-flex w-100 flex-wrap">
        <div class="w-33 tv-w-25 tb-w-50 ph-w-100">
            <div class="mt-24 mr-40">
                <div class="align-center p-16 bg-white border brtl-6 brtr-6">
                    <img [appImage]="userData?.imageUrl ? s3BucketUrl+userData?.imageUrl : ''"
                        [type]="'squareAvatar'" class="obj-cover br-4" width="60px" height="60px">
                    <div class="ml-16">
                        <div class="fw-700 header-3 text-coal"><PERSON></div>
                        <div class="fw-600 text-large text-dark-gray">software Engineer</div>
                        <div class="align-center mt-6">
                            <div class="bg-green-900 dot-sm rounded-circle">
                                <div class="icon ic-clock-nine  ic-x-xs m-4"></div>
                            </div>
                            <span class="text-accent-green fw-600 ml-6 mr-10 text-xs">{{clockData?.[0].clockInTime ?
                                getTimeZoneDate(clockData?.[0].clockInTime,userData?.timeZoneInfo?.baseUTcOffset, 'timeWithMeridiem')
                                : '--'}}</span>
                            <span class="border-bottom w-16"></span>
                            <div class="bg-red-350 dot-sm ml-10 br-50">
                                <div class="icon ic-clock-eight  ic-x-xs m-4"></div>
                            </div>
                            <span class="text-red-350 fw-600 ml-6 text-xs">{{clockData?.[0].clockOutTime ?
                                getTimeZoneDate(clockData?.[0].clockOutTime,userData?.timeZoneInfo?.baseUTcOffset, 'timeWithMeridiem')
                                : '--'}}</span>
                        </div>
                    </div>
                </div>
                <div class="responsive-map h-120">
                    <google-map [center]="{lat: center?.lat, lng: center?.lng}" class="remove-text">
                        <map-marker #mapMarker="mapMarker" (mapClick)="openInfoWindow(mapMarker)"
                            *ngFor="let marker of markers"
                            [position]="marker?.latitude ? {lat:marker?.latitude, lng:marker?.longitude} : ''"
                            [label]="marker?.label">
                        </map-marker>
                    </google-map>
                </div>
                <div class="py-12 px-8 bg-white brbr-4 brbl-4">
                    <div class="align-center">
                        <span class="ic-person-circle ic-black-200 ic-xxs mr-8"></span>
                        <span class="text-black-200 fw-600 text-xxs">1st A, 1st Cross Rd, 5th Block, HBR Layout 4th
                            Block,
                            HBR Layout, Bengaluru, Karnataka 560043</span>
                    </div>
                    <div class="align-center mt-8">
                        <span class="ic-alarm-solid ic-black-200 ic-xxs mr-8"></span>
                        <span class="text-black-200 fw-600 text-xxs mr-6">26-05-2025</span>
                        <span class="border-left-black-200 h-10 mr-6"></span>
                        <span class="text-black-200 fw-600 text-xxs">12:41 PM</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>