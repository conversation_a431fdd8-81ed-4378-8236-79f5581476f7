import { Component, EventEmitter, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { DocumentType, FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  DeleteUserDocuments,
  UploadUserDocuments,
} from 'src/app/reducers/teams/teams.actions';
import { getUserProfile } from 'src/app/reducers/teams/teams.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'user-uploaded-documents',
  templateUrl: './user-uploaded-documents.component.html',
})
export class UserUploadedDocumentsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() userData: any;
  @Input() loggedInUser: boolean;
  allDoc: any;
  s3BucketPath: string = env.s3ImageBucketURL;
  isFileTypeSupported: boolean = true;
  selectedSection: any = 'Identity';
  selectedDocName: any;
  validationPerformed = false;
  searchTerm: string;
  hoveredIndex: number = -1;
  canEdit: boolean = false;

  documents: Array<any> = [
    { id: 1, name: 'Driving License' },
    { id: 2, name: 'Aadhaar Card' },
    { id: 3, name: 'Passport' },
    { id: 4, name: 'PAN Card' },
    { id: 5, name: 'Voter ID Card' },
  ];
  filesToBeUploadToS3Bucket: any;
  isShowDocUpload: boolean = false;
  constructor(
    private s3UploadService: BlobStorageService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    public trackingService: TrackingService
  ) { }

  ngOnInit() {
    this.store.select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        this.canEdit = canEdit.includes('Users');
      });
    this.store.select(getUserProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        const combinedArray = [
          ...(this.userData.documents?.Identity || []),
          ...(this.userData.documents?.Experience || []),
          ...(this.userData.documents?.Signature || []),
        ];
        this.allDoc = combinedArray;
      });
  }

  fileType(filePath: string) {
    if (filePath && filePath.substring(filePath.length - 3) === 'pdf') {
      return 'pdf';
    } else if (
      (filePath && filePath.substring(filePath.length - 3) === 'jpg') ||
      (filePath && filePath.substring(filePath.length - 3) === 'jpeg') ||
      (filePath && filePath.substring(filePath.length - 3) === 'png')
    ) {
      return 'img';
    } else if (
      filePath &&
      filePath.substring(filePath.length - 8) === 'document'
    ) {
      return 'document';
    } else if (
      filePath &&
      filePath.substring(filePath.length - 5) === 'sheet'
    ) {
      return 'xls';
    } else {
      return 'file';
    }
  }

  onFileSelection(e: any) {
    this.filesToBeUploadToS3Bucket = e;
  }

  searchDocuments(searchTerm: string) {
    this.searchTerm = searchTerm.trim().toLowerCase();
    if (searchTerm) this.trackingService.trackFeature('Web.UserDetails.DataEntry.DocumentsSearch.DataEntry')
  }

  openDeleteConfirmModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.docName,
      fieldType: 'document',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteUserDocuments(data?.id));
        }
      });
    }
  }

  canUploadDoc(): boolean {
    if (
      (this.selectedSection == 'Identity' && !this.selectedDocName) ||
      (this.selectedSection == 'Experience' && !this.selectedDocName)
    ) {
      return false;
    }
    return true;
  }

  UploadDoc() {
    if (!this.canUploadDoc()) {
      return;
    }
    this.validationPerformed = true;
    if (this.selectedSection === 'Signature') {
      this.selectedDocName = 'Signature';
    }
    if (
      (this.selectedSection === 'Identity' &&
        this.selectedDocName?.length < 1) ||
      (this.selectedSection === 'Experience' &&
        this.selectedDocName?.length < 1) ||
      !this.filesToBeUploadToS3Bucket
    ) {
      return;
    }
    let payload = {
      docName: this.selectedDocName || '',
      filePath: '',
      documentType: DocumentType[this.selectedSection],
      userId: this.userData.userId,
    };
    this.s3UploadService
      .uploadDocBase64(
        this.filesToBeUploadToS3Bucket,
        FolderNamesS3.IdentityDocs
      )
      .pipe(takeUntil(this.stopper))
      .subscribe((response: any) => {
        if (response.data.length) {
          payload.filePath = response.data?.[0];
        }
        this.selectedDocName = null;
        this.filesToBeUploadToS3Bucket = null;
        this.store.dispatch(
          new UploadUserDocuments(payload, this.userData.userId)
        );
      });
    this.validationPerformed = false;
    this.isShowDocUpload = false;
  }

  onSectionSelect(section: string) {
    let track: any = {
      Identity: 'IndentityDocuments',
      Experience: 'PreviousExperience',
      Signature: 'Signature'
    }
    this.trackingService.trackFeature(`Web.UserDetails.Documents.${track[section]}.Click`)
    this.selectedDocName = null;
    this.selectedSection = section;
    this.filesToBeUploadToS3Bucket = null;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
