{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.0.2", "a": "", "k": "", "d": "", "tc": ""}, "fr": 39.544921875, "ip": 0, "op": 404.999199883045, "w": 300, "h": 640, "nm": "404 error", "ddd": 0, "assets": [{"id": "image_0", "w": 343, "h": 354, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Tree", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 270.999, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 390.999, "s": [20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 508.999, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 627.999, "s": [-20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 706.999, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 825.998, "s": [20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 943.998, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 1062.998, "s": [-20]}, {"t": 1141.99774386775, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [231.396, 272.623, 0], "ix": 2}, "a": {"a": 0, "k": [241.544, 277.223, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2.3, 1.1], [-0.8, 2.3], [0, 0], [0.3, 0.7], [0.8, 0.3], [0.8, -0.3], [0.3, -0.7], [0, 0], [1.7, 0.5], [0, 0], [0, 0], [0.7, 1.5], [1.6, 0.6], [1.5, -0.7], [0.6, -1.5], [0, 0], [0, 0], [0.4, 0.8], [-0.3, 0.7], [0, 0], [0.3, 0.7], [0.8, 0.3], [0.8, -0.3], [0.3, -0.7], [0, 0], [-1, -2.2], [-2.4, -0.8]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [2.3, 0.8], [2.2, -1], [0, 0], [0.3, -0.8], [-0.4, -0.7], [-0.8, -0.3], [-0.7, 0.3], [0, 0], [-0.6, 1.6], [0, 0], [0, 0], [0.5, -1.5], [-0.7, -1.5], [-1.6, -0.5], [-1.5, 0.7], [0, 0], [0, 0], [-0.8, -0.3], [-0.4, -0.7], [0, 0], [0.3, -0.8], [-0.4, -0.7], [-0.8, -0.3], [-0.7, 0.3], [0, 0], [-0.8, 2.3], [1.1, 2.2], [0, 0]], "v": [[-9.15, 9.85], [-12.35, 19.25], [-0.65, 23.35], [0.65, 19.75], [3.65, 20.75], [10.85, 20.35], [15.55, 15.15], [18.55, 6.45], [18.45, 4.15], [16.65, 2.55], [14.25, 2.65], [12.65, 4.35], [9.65, 13.05], [5.65, 14.95], [2.65, 13.95], [12.75, -14.95], [12.45, -19.65], [8.85, -22.85], [4.05, -22.65], [0.85, -19.15], [-7.25, 4.05], [-10.25, 3.05], [-12.05, 1.45], [-12.15, -0.85], [-10.15, -6.65], [-10.25, -8.95], [-12.05, -10.55], [-14.45, -10.45], [-16.05, -8.75], [-18.05, -2.95], [-17.65, 4.05], [-12.25, 8.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.313725490196, 0.745098039216, 0.654901960784, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [249.45, 256.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 405.999197907447, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Tree", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 238, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 356.999, "s": [-20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 435.999, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 554.999, "s": [20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 672.999, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 791.998, "s": [-20]}, {"t": 870.998279254647, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [56.396, 281.623, 0], "ix": 2}, "a": {"a": 0, "k": [56.396, 279.623, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.66, 0.749], [0.586, 1.675], [0, 0], [-0.277, 0.54], [-0.587, 0.205], [-0.554, -0.25], [-0.196, -0.559], [0, 0], [-1.218, 0.427], [0, 0], [0, 0], [-0.554, 1.08], [-1.174, 0.411], [-1.106, -0.498], [-0.391, -1.117], [0, 0], [0, 0], [-0.277, 0.54], [0.195, 0.558], [0, 0], [-0.277, 0.541], [-0.587, 0.205], [-0.553, -0.249], [-0.195, -0.558], [0, 0], [0.831, -1.621], [1.761, -0.616]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-1.761, 0.616], [-1.66, -0.748], [0, 0], [-0.195, -0.559], [0.277, -0.541], [0.587, -0.206], [0.553, 0.249], [0, 0], [0.412, 1.179], [0, 0], [0, 0], [-0.39, -1.116], [0.554, -1.08], [1.174, -0.411], [1.107, 0.5], [0, 0], [0, 0], [0.587, -0.206], [0.277, -0.54], [0, 0], [-0.196, -0.557], [0.277, -0.54], [0.587, -0.206], [0.553, 0.251], [0, 0], [0.586, 1.674], [-0.831, 1.619], [0, 0]], "v": [[6.81, 7.103], [9.119, 13.945], [0.34, 17.018], [-0.572, 14.413], [-2.786, 15.188], [-8.129, 14.98], [-11.637, 11.197], [-13.847, 4.882], [-13.719, 3.167], [-12.37, 2.001], [-10.588, 2.07], [-9.419, 3.332], [-7.209, 9.647], [-4.259, 10.977], [-2.045, 10.203], [-9.412, -10.849], [-9.156, -14.279], [-6.457, -16.608], [-2.895, -16.47], [-0.556, -13.947], [5.337, 2.893], [7.551, 2.12], [8.9, 0.954], [9.028, -0.76], [7.555, -4.971], [7.682, -6.686], [9.032, -7.85], [10.813, -7.783], [11.982, -6.521], [13.456, -2.31], [13.073, 2.836], [9.024, 6.328]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.313725490196, 0.745098039216, 0.654901960784, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [51.972, 263.806], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 405.999197907447, "st": -246.999512027437, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Half circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [149.9, 278.6, 0], "ix": 2}, "a": {"a": 0, "k": [149.9, 278.6, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-121.1, -79.2], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-133.7, 39.6], [133.7, 39.6], [-0.959, 39.6]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 2, "k": {"a": 0, "k": [0, 1, 1, 1, 1, 0, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [4.004, 35.451], "ix": 5}, "e": {"a": 0, "k": [0.585, -44.23], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [149.9, 256.6], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 405.999197907447, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "404 error Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [150, 320, 0], "ix": 2}, "a": {"a": 0, "k": [150, 320, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-39.488, 0], [0, -7.456], [39.488, 0], [0, 7.456]], "o": [[39.488, 0], [0, 7.456], [-39.488, 0], [0, -7.456]], "v": [[0, -13.5], [71.5, 0], [0, 13.5], [-71.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.921568687289, 0.921568687289, 0.921568687289, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [158.5, 274.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 405.999197907447, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "404 error Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [150, 320, 0], "ix": 2}, "a": {"a": 0, "k": [150, 320, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.5, 0], [-5.7, 0], [-4.1, -0.2], [0, -0.6], [0, 0]], "o": [[-3.5, -0.2], [0, 0], [0, -0.5], [4, -0.2], [6, 0], [0.5, 0], [0, 0], [-11.7, -1.6]], "v": [[-3.15, 0.85], [-26.95, 2.25], [-26.95, -0.25], [-26.05, -1.25], [-0.45, -2.25], [26.05, -1.25], [26.95, -0.25], [26.95, 2.15]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [159.35, 260.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 405.999197907447, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "30 1.png", "cl": "png", "td": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143, "s": [100]}, {"t": 284.999436954735, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 143.999715513971, "s": [40]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [154.007, 258.564, 0], "to": [0, 8.167, 0], "ti": [0, -8.167, 0]}, {"t": 327.999352004046, "s": [154.007, 307.564, 0]}], "ix": 2}, "a": {"a": 0, "k": [224.016, 229.516, 0], "ix": 1}, "s": {"a": 0, "k": [19.994, 19.994, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 405.999197907447, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape Layer 1", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [150, 320, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [84.706, 64.706], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.427450980392, 0.886274509804, 0.788235294118, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 2, "k": {"a": 0, "k": [0, 1, 1, 1, 1, 0, 0, 0], "ix": 9}}, "s": {"a": 0, "k": [0, 61.176], "ix": 5}, "e": {"a": 0, "k": [3.529, -30.588], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-5.294, -67.647], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 404.999199883045, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "404 error Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [150, 320, 0], "ix": 2}, "a": {"a": 0, "k": [150, 320, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2, 0], [0, 2.2], [-2, 0], [0, -2.2]], "o": [[-2, 0], [0, -2.2], [2, 0], [0, 2.2]], "v": [[91.8, 3.95], [88.3, 0.25], [91.8, -3.45], [95.3, 0.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.5, 0], [0, -2.9], [-2.7, 0], [-0.8, 1.3], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-0.8, -1.2], [-2.7, 0], [0, 2.9], [1.6, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[95.3, -8.45], [95.3, -2.75], [91.7, -4.65], [87, 0.15], [91.7, 4.95], [95.4, 2.95], [95.4, 4.85], [96.6, 4.85], [96.6, -8.55], [95.3, -8.55]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[2.3, 0], [0.7, -1.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2, 0], [0, -2], [0, 0], [0, 0], [0, 0]], "o": [[-1.6, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.2], [1.8, 0], [0, 0], [0, 0], [0, 0], [0, -2.8]], "v": [[80.5, -4.55], [76.9, -2.75], [76.9, -4.45], [75.7, -4.45], [75.7, 5.05], [77, 5.05], [77, 0.05], [80.3, -3.35], [83.1, -0.35], [83.1, 5.05], [84.4, 5.05], [84.4, -0.45]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [2, 0], [0, 2], [0, 0], [0, 0], [0, 0], [-2.4, 0], [-0.7, 1.1], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 2.2], [-1.8, 0], [0, 0], [0, 0], [0, 0], [0, 2.7], [1.5, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70.8, -4.55], [70.8, 0.45], [67.6, 3.85], [64.8, 0.85], [64.8, -4.55], [63.5, -4.55], [63.5, 0.95], [67.5, 5.05], [70.9, 3.25], [70.9, 4.95], [72.1, 4.95], [72.1, -4.55]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[2.1, 0], [0, 2.2], [-2, 0], [0, -2.2]], "o": [[-2, 0], [0, -2.2], [2, 0], [0.1, 2.2]], "v": [[56.1, 3.95], [52.6, 0.25], [56.1, -3.45], [59.6, 0.25]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[-2.7, 0], [0, 2.8], [2.8, 0], [0, -2.8]], "o": [[2.8, 0], [0, -2.8], [-2.8, 0], [0, 2.8]], "v": [[56.1, 5.05], [60.9, 0.25], [56.1, -4.55], [51.3, 0.25]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[-1.1, 0], [-0.4, -0.3], [0, 0], [0.7, 0], [0, -1.7], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0.5, 0], [0, 0], [-0.5, -0.4], [-1.9, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.2]], "v": [[49.5, -7.45], [50.9, -6.95], [51.3, -7.95], [49.4, -8.55], [46.5, -5.75], [46.5, -4.55], [44.8, -4.55], [44.8, -3.45], [46.5, -3.45], [46.5, 4.95], [47.8, 4.95], [47.8, -3.45], [50.7, -3.45], [50.7, -4.55], [47.8, -4.55], [47.8, -5.65]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0.5, 0], [0, 1.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.8, 0], [-0.5, 0.5]], "o": [[-0.4, 0.3], [-1.1, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.7], [0.7, 0], [0, 0]], "v": [[38.8, 3.45], [37.4, 3.95], [35.8, 2.25], [35.8, -3.45], [38.7, -3.45], [38.7, -4.55], [35.8, -4.55], [35.8, -6.65], [34.5, -6.65], [34.5, -4.55], [32.8, -4.55], [32.8, -3.45], [34.5, -3.45], [34.5, 2.25], [37.3, 5.05], [39.3, 4.35]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[2, 0], [0, 2.2], [-2, 0], [0, -2.2]], "o": [[-2, 0], [0, -2.2], [2, 0], [0, 2.2]], "v": [[26.9, 3.95], [23.4, 0.25], [26.9, -3.45], [30.4, 0.25]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 9, "ty": "sh", "ix": 10, "ks": {"a": 0, "k": {"i": [[-2.8, 0], [0, 2.8], [2.8, 0], [0, -2.8]], "o": [[2.8, 0], [0, -2.8], [-2.8, 0], [0, 2.8]], "v": [[26.9, 5.05], [31.7, 0.25], [26.9, -4.55], [22.1, 0.25]], "c": true}, "ix": 2}, "nm": "Path 10", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 10, "ty": "sh", "ix": 11, "ks": {"a": 0, "k": {"i": [[2.3, 0], [0.7, -1.1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2, 0], [0, -2], [0, 0], [0, 0], [0, 0]], "o": [[-1.6, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.2], [1.8, 0], [0, 0], [0, 0], [0, 0], [0, -2.8]], "v": [[15.6, -4.55], [12, -2.75], [12, -4.45], [10.8, -4.45], [10.8, 5.05], [12.1, 5.05], [12.1, 0.05], [15.4, -3.35], [18.2, -0.35], [18.2, 5.05], [19.5, 5.05], [19.5, -0.45]], "c": true}, "ix": 2}, "nm": "Path 11", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 11, "ty": "sh", "ix": 12, "ks": {"a": 0, "k": {"i": [[-1.8, 0], [-0.2, -1.8], [0, 0]], "o": [[1.9, 0], [0, 0], [0.1, -1.8]], "v": [[-1.1, -3.45], [2.3, -0.35], [-4.4, -0.35]], "c": true}, "ix": 2}, "nm": "Path 12", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 12, "ty": "sh", "ix": 13, "ks": {"a": 0, "k": {"i": [[0, 0.1], [2.7, 0], [0, -2.8], [-3, 0], [-0.9, 1], [0, 0], [1.2, 0], [0.1, 2], [0, 0]], "o": [[0, -2.8], [-2.7, 0], [0, 2.8], [1.5, 0], [0, 0], [-0.7, 0.8], [-2.1, 0], [0, 0], [-0.1, -0.2]], "v": [[3.5, 0.25], [-1.1, -4.55], [-5.7, 0.25], [-0.7, 5.05], [3, 3.55], [2.3, 2.75], [-0.6, 3.95], [-4.3, 0.65], [3.6, 0.65]], "c": true}, "ix": 2}, "nm": "Path 13", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 13, "ty": "sh", "ix": 14, "ks": {"a": 0, "k": {"i": [[2.1, 0], [0, 2.1], [-2.1, 0], [0, -2.1]], "o": [[-2.1, 0], [0, -2.1], [2.1, 0], [0, 2.1]], "v": [[-13.2, 3.45], [-16.8, -0.05], [-13.2, -3.55], [-9.6, -0.05]], "c": true}, "ix": 2}, "nm": "Path 14", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 14, "ty": "sh", "ix": 15, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.5, 0], [0, -2.7], [-2.7, 0], [-0.8, 1.2], [0, 0], [2.4, 0], [0.9, 0.8], [0, 0], [-1.7, 0], [0, 3.2], [0, 0], [0, 0]], "o": [[0, 0], [-0.8, -1.2], [-2.7, 0], [0, 2.7], [1.5, 0], [0, 0], [0, 2.3], [-1.5, 0], [0, 0], [1, 0.9], [3.1, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.6, -4.55], [-9.6, -2.75], [-13.3, -4.65], [-18.1, -0.05], [-13.3, 4.55], [-9.6, 2.75], [-9.6, 3.95], [-13.1, 7.35], [-16.8, 6.05], [-17.4, 7.05], [-13, 8.55], [-8.3, 3.85], [-8.3, -4.45], [-9.6, -4.45]], "c": true}, "ix": 2}, "nm": "Path 15", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 15, "ty": "sh", "ix": 16, "ks": {"a": 0, "k": {"i": [[1.4, 0], [0, 1.1], [-1.8, 0], [0, 0], [0, 0]], "o": [[-1.5, 0], [0, -1], [0, 0], [0, 0], [-0.5, 1.3]], "v": [[-24.9, 4.05], [-27.3, 2.25], [-24.9, 0.55], [-21.9, 0.55], [-21.9, 2.05]], "c": true}, "ix": 2}, "nm": "Path 16", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 16, "ty": "sh", "ix": 17, "ks": {"a": 0, "k": {"i": [[2.4, 0], [1, -0.8], [0, 0], [-1.3, 0], [0, -1.6], [0, 0], [0, 0], [0, -1.5], [-2.2, 0], [-0.6, 1], [0, 0], [0, 0], [0, 0]], "o": [[-1.5, 0], [0, 0], [0.8, -0.7], [1.8, 0], [0, 0], [0, 0], [-2.7, 0], [0, 1.6], [1.6, 0], [0, 0], [0, 0], [0, 0], [-0.1, -2.6]], "v": [[-24.4, -4.55], [-28.3, -3.25], [-27.7, -2.25], [-24.5, -3.35], [-21.8, -0.85], [-21.8, -0.25], [-24.8, -0.25], [-28.5, 2.45], [-25, 5.25], [-21.7, 3.65], [-21.7, 5.15], [-20.5, 5.15], [-20.5, -0.75]], "c": true}, "ix": 2}, "nm": "Path 17", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 17, "ty": "sh", "ix": 18, "ks": {"a": 0, "k": {"i": [[2, 0], [0, 2.2], [-2, 0], [0, -2.2]], "o": [[-2, 0], [0, -2.2], [2, 0], [0, 2.2]], "v": [[-35.1, 3.95], [-38.6, 0.25], [-35.1, -3.45], [-31.6, 0.25]], "c": true}, "ix": 2}, "nm": "Path 18", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 18, "ty": "sh", "ix": 19, "ks": {"a": 0, "k": {"i": [[2.7, 0], [0.8, -1.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, 0], [0, 2.9]], "o": [[-1.6, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.8, 1.2], [2.7, 0], [0, -2.9]], "v": [[-35, -4.55], [-38.7, -2.65], [-38.7, -4.55], [-39.9, -4.55], [-39.9, 8.45], [-38.6, 8.45], [-38.6, 3.15], [-35, 5.05], [-30.3, 0.25]], "c": true}, "ix": 2}, "nm": "Path 19", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 19, "ty": "sh", "ix": 20, "ks": {"a": 0, "k": {"i": [[0.6, 0], [0, -0.6], [-0.3, -0.1], [0, 0], [0, 0], [0, 0], [0, 0.3]], "o": [[-0.5, 0], [0, 0.5], [0, 0], [0, 0], [0, 0], [0.2, -0.5], [0, -0.5]], "v": [[-48.3, 3.05], [-49.3, 4.05], [-48.7, 4.95], [-49.3, 7.55], [-48.4, 7.55], [-47.6, 5.05], [-47.3, 4.05]], "c": true}, "ix": 2}, "nm": "Path 20", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 20, "ty": "sh", "ix": 21, "ks": {"a": 0, "k": {"i": [[-1.6, 0], [0, 1.6], [0, 2.6], [-1.7, 0], [-0.8, -0.6], [0, 0], [1.2, 0], [0, -1.5], [0, -2.5], [1.8, 0], [0.8, 0.6], [0, 0]], "o": [[2.5, 0], [0, -3.6], [0, -0.9], [1, 0], [0, 0], [-0.8, -0.5], [-2.4, 0], [0, 3.7], [0, 0.9], [-1.3, 0], [0, 0], [0.6, 0.8]], "v": [[-54.7, 5.05], [-50.8, 2.35], [-56.9, -1.95], [-54.4, -3.55], [-51.6, -2.75], [-51, -3.75], [-54.3, -4.65], [-58, -1.95], [-51.9, 2.35], [-54.4, 3.85], [-57.8, 2.75], [-58.4, 3.75]], "c": true}, "ix": 2}, "nm": "Path 21", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 21, "ty": "sh", "ix": 22, "ks": {"a": 0, "k": {"i": [[2, 0], [0, 2.2], [-2, 0], [0, -2.2]], "o": [[-2, 0], [0, -2.2], [2, 0], [0, 2.2]], "v": [[-64.6, 3.95], [-68.1, 0.25], [-64.6, -3.45], [-61.1, 0.25]], "c": true}, "ix": 2}, "nm": "Path 22", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 22, "ty": "sh", "ix": 23, "ks": {"a": 0, "k": {"i": [[2.7, 0], [0.8, -1.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.5, 0], [0, 2.9]], "o": [[-1.6, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.8, 1.2], [2.7, 0], [0.1, -2.9]], "v": [[-64.6, -4.55], [-68.3, -2.65], [-68.3, -4.55], [-69.5, -4.55], [-69.5, 8.45], [-68.2, 8.45], [-68.2, 3.15], [-64.6, 5.05], [-59.9, 0.25]], "c": true}, "ix": 2}, "nm": "Path 23", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 23, "ty": "sh", "ix": 24, "ks": {"a": 0, "k": {"i": [[2, 0], [0, 2.2], [-2, 0], [0, -2.2]], "o": [[-2, 0], [0, -2.2], [2, 0], [0, 2.2]], "v": [[-76.8, 3.95], [-80.3, 0.25], [-76.8, -3.45], [-73.3, 0.25]], "c": true}, "ix": 2}, "nm": "Path 24", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 24, "ty": "sh", "ix": 25, "ks": {"a": 0, "k": {"i": [[-2.8, 0], [0, 2.8], [2.8, 0], [0, -2.8]], "o": [[2.8, 0], [0, -2.8], [-2.8, 0], [-0.1, 2.8]], "v": [[-76.8, 5.05], [-72, 0.25], [-76.8, -4.55], [-81.6, 0.25]], "c": true}, "ix": 2}, "nm": "Path 25", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 25, "ty": "sh", "ix": 26, "ks": {"a": 0, "k": {"i": [[3, 0], [0, 3], [-3, 0], [0, -3]], "o": [[-3, 0], [0, -3], [3, 0], [-0.1, 3]], "v": [[-90, 3.85], [-95.3, -1.35], [-90, -6.55], [-84.7, -1.35]], "c": true}, "ix": 2}, "nm": "Path 26", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 26, "ty": "sh", "ix": 27, "ks": {"a": 0, "k": {"i": [[-3.9, 0], [0, 3.7], [3.8, 0], [0, -3.7]], "o": [[3.8, 0], [0, -3.7], [-3.8, 0], [0, 3.7]], "v": [[-90, 5.05], [-83.4, -1.35], [-90, -7.75], [-96.6, -1.35]], "c": true}, "ix": 2}, "nm": "Path 27", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.349019607843, 0.337254901961, 0.403921598547, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [161.6, 476.05], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 29, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.25, 1.4], [17.95, 1.4], [17.95, -1.2], [14.85, -1.2], [14.85, 1.4], [11.05, 1.4], [17.15, -7], [13.75, -7], [7.15, 1.9], [7.15, 4.1], [14.65, 4.1], [14.65, 7], [17.85, 7], [17.85, 4.1], [20.15, 4.1], [20.15, 1.4]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.6, 0], [0, 3.2], [-1.6, 0], [0, -3.2]], "o": [[-1.6, 0], [0, -3.2], [1.6, 0], [0, 3.2]], "v": [[-0.15, 4.5], [-2.85, 0], [-0.15, -4.5], [2.55, 0]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[-3.5, 0], [0, 4.6], [3.5, 0], [0, -4.6]], "o": [[3.5, 0], [0, -4.6], [-3.4, 0], [0, 4.6]], "v": [[-0.15, 7.2], [5.85, 0], [-0.15, -7.2], [-6.15, 0]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-7.15, 1.4], [-9.45, 1.4], [-9.45, -1.2], [-12.55, -1.2], [-12.55, 1.4], [-16.35, 1.4], [-10.25, -7], [-13.65, -7], [-20.25, 1.9], [-20.25, 4.1], [-12.75, 4.1], [-12.75, 7], [-9.55, 7], [-9.55, 4.1], [-7.25, 4.1], [-7.25, 1.4]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.349019607843, 0.337254901961, 0.403921598547, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [161.75, 443], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 6, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 405.999197907447, "st": 0, "bm": 0}], "markers": []}