import {
  Component,
  EventEmitter,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { ContactType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { isEmptyObject } from 'src/app/core/utils/common.util';
import {
  CommunicationDataCount,
  CommunicationDataMessage,
} from 'src/app/reducers/data/data-management.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  CommunicationCount,
  CommunicationMessage,
} from 'src/app/reducers/lead/lead.actions';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { NotificationCall } from 'src/app/reducers/notification-info/notification-info.action';

@Component({
  selector: 'lead-call',
  templateUrl: './lead-call.component.html',
})
export class LeadCallComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('mobileNotification') mobileNotification!: TemplateRef<any>;
  data: any;
  selectedCallType: any;
  userId: any;
  globalSettingsData: any;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    public NotifyModalRef: BsModalRef,
    private store: Store<AppState>
  ) {
    this.userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          this.globalSettingsData = data;
        }
      });
  }

  ngOnInit() {
    this.selectedCallType = this.data?.contactNo;
  }

  initCall() {
    if (this.globalSettingsData.isMobileCallEnabled) {
      let payload = {
        contactNo: this.selectedCallType,
        userId: this.userId,
        name: this.data?.name,
      };
      this.store.dispatch(new NotificationCall(payload));
      this.mobileNotifyInfo();
    } else {
      location.href = 'tel:' + this.selectedCallType;
    }
    this.data?.isData ? this.onInitDataCall(1) : this.onInitiateCall(1);
    this.modalRef.hide();
  }

  mobileNotifyInfo() {
    let initialState: any = {
      class: 'modal-400 top-modal ph-modal-unset',
    };
    this.NotifyModalRef = this.modalService.show(
      this.mobileNotification,
      initialState
    );
    const intervalId = setInterval(() => {
      this.NotifyModalRef.hide();
      clearInterval(intervalId);
    }, 5000);
  }

  openDialerPad() {
    location.href = 'tel:' + this.selectedCallType;
    this.modalRef.hide();
  }

  onInitDataCall(contactType: number) {
    let payload = {
      prospectId: this.data?.id,
      contactType: contactType,
    };
    let payloadCount = {
      id: this.data?.id,
      contactType: contactType,
    };
    this.store.dispatch(
      new CommunicationDataCount(payloadCount.id, payloadCount)
    );
    this.store.dispatch(new CommunicationDataMessage(payload));
    this.store.dispatch(new LoaderHide());
  }

  onInitiateCall(contactType: number) {
    const payload: any = {
      contactType: ContactType['Call'],
      leadId: this.data?.id,
    };
    let payloadCount = {
      id: this.data?.id,
      contactType: contactType,
    };
    this.store.dispatch(new CommunicationCount(payloadCount.id, payloadCount));
    this.store.dispatch(new CommunicationMessage(payload));
  }
}
