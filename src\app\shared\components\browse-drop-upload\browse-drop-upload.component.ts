import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NotificationsService } from 'angular2-notifications';
import { NgxFileDropEntry } from 'ngx-file-drop';
import { FILE_FORMAT } from 'src/app/app.constants';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'browse-drop-upload',
  templateUrl: './browse-drop-upload.component.html',
})
export class BrowseDropUploadComponent {
  isFileDataUpdated: boolean;
  selectedFile: any;
  @Output() uploadedFile = new EventEmitter();
  @Output() uploadedFileName = new EventEmitter();
  @Output() uploadedFileSize = new EventEmitter();
  @Output() uploadedFileWithName = new EventEmitter();
  @Output() WaterMarkImage: EventEmitter<any> = new EventEmitter<any>();
  isFileTypeSupported: boolean;
  @Input() allowedFileFormat: string;
  @Input() allowedFileType: string;
  @Input() isExcelFile: boolean = false;
  @Input() fileMessage: string;
  @Input() isEmail: boolean;
  currentFileName: string = null;
  selectedFileArr: any = [];
  allTypeFileArr: any = [];

  constructor(
    private _notificationsService: NotificationsService,
    public trackingService: TrackingService
  ) { }

  onFileDrop(files: NgxFileDropEntry[]) {
    for (const droppedFile of files) {
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          const fileSize = this.formatBytes(file.size);
          this.uploadedFileSize.emit(fileSize);
        });
      } else if (droppedFile.fileEntry.isDirectory) {
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
      }
    }

    this.selectedFileArr = [];
    this.allTypeFileArr = [];
    for (const droppedFile of files) {
      /* check if dropped file is a file */
      if (droppedFile.fileEntry.isFile) {
        const fileEntry = droppedFile.fileEntry as FileSystemFileEntry;
        fileEntry.file((file: File) => {
          this.checkFileFormat(file.name);
          this.isFileDataUpdated = true;
          this.selectedFile = file;
          if (this.checkFileFormat(file.name)) {
            if (this.isExcelFile || this.isEmail) {
              this.uploadedFile.emit(this.selectedFile);
            } else {
              var reader = new FileReader();
              reader.readAsDataURL(file);
              reader.onload = () => {
                if (reader.result) {
                  this.selectedFile = reader.result;
                  this.uploadedFile.emit(this.selectedFile.split(' '));
                  this.selectedFileArr.push([
                    this.selectedFile.split(' ')[0],
                    file?.name,
                    file?.size,
                  ]);
                  this.allTypeFileArr.push(file);
                  if (files?.length === this.selectedFileArr?.length) {
                    this.uploadedFileWithName.emit(this.selectedFileArr);
                    this.WaterMarkImage.emit(this.allTypeFileArr);
                  }
                }
              };
              this.sendFileName(file?.name);
            }
          } else {
            this.selectedFile = null;
            this._notificationsService.warn(
              'Unsupported Format',
              'File type not supported'
            );
          }
        });
      } else {
        // It was a directory (empty directories are added, otherwise only files)
        const fileEntry = droppedFile.fileEntry as FileSystemDirectoryEntry;
      }
    }
  }

  formatBytes(bytes: number, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  checkFileFormat(fileName: string) {
    let allowedFiles: any[];
    switch (this.allowedFileType) {
      case 'excel':
        allowedFiles = FILE_FORMAT.excel;
        break;
      case 'pdf':
        allowedFiles = FILE_FORMAT.pdf;
        break;
      case 'image':
        allowedFiles = FILE_FORMAT.image;
        break;
      case 'media':
        allowedFiles = FILE_FORMAT.media;
        break;
      case 'imgPdfDoc':
        allowedFiles = [
          ...FILE_FORMAT.image,
          ...FILE_FORMAT.pdf,
          ...FILE_FORMAT.doc,
        ];
        break;
    }

    const regex = /(?:\.([^.]+))?$/;
    const extension = regex.exec(fileName);
    this.isFileTypeSupported = allowedFiles.includes(extension[1]);
    return this.isFileTypeSupported;
  }

  onFileSelection(file: any) {
    this.selectedFile = file;
    this.isFileDataUpdated = true;
    if (this.checkFileFormat(file.name)) {
      this.uploadedFile.emit(this.selectedFile); //if file is uploaded directly
    } else if (Array.isArray(file)) {
      if (file.length == 1 && this.checkFileFormat(this.currentFileName)) {
        this.uploadedFile.emit(this.selectedFile);
        return;
      } else if (
        file.length == 1 &&
        !this.checkFileFormat(this.currentFileName)
      ) {
        this._notificationsService.warn(
          'Unsupported Format',
          'File type not supported'
        );
        return;
      }
      this.uploadedFile.emit(this.selectedFile);
    } else {
      this.selectedFile = null;
      this._notificationsService.warn(
        'Unsupported Format',
        'File type not supported'
      );
    }
  }

  sendFileName(e: any) {
    this.currentFileName = e;
    this.uploadedFileName.emit(e);
  }

  sendFileSize(e: any) {
    this.uploadedFileSize.emit(e);
  }
}
