import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { combineLatest, filter, skipWhile, take, takeUntil } from 'rxjs';

import { EMPTY_GUID } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { ReassignBoth, ReassignLead } from 'src/app/reducers/lead/lead.actions';
import {
  getReassignBothIsLoading,
  getReassignLeadIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { LeadPreviewChanged } from 'src/app/reducers/loader/loader.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';

@Component({
  selector: 'individual-reassign',
  templateUrl: './individual-reassign.component.html',
})
export class IndividualReassignComponent
  implements OnInit, OnChanges, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() data: any;
  @Input() isLastLead: boolean = false;
  @Input() whatsAppComp: boolean = false;
  canAssignLead: boolean = false;
  canAssignInvoice: boolean = false;
  isShowAssign: boolean = false;
  reassignLeadIsLoading: boolean = false;
  reassignBothIsLoading: boolean = false;
  EMPTY_GUID = EMPTY_GUID;
  getAssignedToDetails = getAssignedToDetails;
  users: Array<Object> = [];
  deactiveUsers: Array<Object> = [];
  assignedToUserId: string;
  secondaryAssignTo: string;
  primaryUserList: Object[];
  primaryAgentList: Object[];
  secondaryUserList: Object[];
  secondaryAgentList: Object[];
  globalSettingsData: any;
  currentPath: string;

  constructor(
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    private _leadPreviewComponent: LeadPreviewComponent,
    private _notificationsService: NotificationsService,
    public router: Router,
    public trackingService: TrackingService
  ) { }

  ngOnInit() {
    this.data = { ...this.data };
    const adminsWithReportees$ = this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    const allUsers$ = this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    const permissions$ = this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      adminsWithReportees: adminsWithReportees$,
      allUsers: allUsers$,
      permissions: permissions$,
    }).subscribe(({ adminsWithReportees, allUsers, permissions }) => {
      if (permissions?.includes('Permissions.Leads.Assign')) {
        this.canAssignLead = true;
      }
      if (permissions?.includes('Permissions.Invoice.Assign')) {
        this.canAssignInvoice = true;
      }
      if (permissions?.includes('Permissions.Users.AssignToAny')) {
        this.users = allUsers;
      } else {
        this.users = adminsWithReportees;
      }

      this.prepareUsers();
    });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
      });

    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
  }

  prepareUsers(): void {
    let activeUsers = this.users
      ?.filter((user: any) => user.isActive)
      ?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        };
        return user;
      });
    this.deactiveUsers = this.users
      ?.filter((user: any) => !user.isActive)
      ?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        };
        return user;
      });
    this.primaryUserList = assignToSort(activeUsers, this.assignedToUserId);
    this.primaryAgentList = assignToSort(activeUsers, this.assignedToUserId);
    this.secondaryUserList = assignToSort(activeUsers, this.secondaryAssignTo);
    this.secondaryAgentList = assignToSort(activeUsers, this.secondaryAssignTo);
    this.primaryAgentList = this.primaryUserList?.filter(
      (el: any) => !this.data?.secondaryUserId?.includes(el?.id)
    );
    this.secondaryAgentList = this.secondaryUserList?.filter(
      (el: any) => !this.data?.assignTo?.includes(el?.id)
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.data?.currentValue) {
      this.prepareUsers();
      this.data = changes.data.currentValue;
      this.assignedToUserId =
        this.data?.assignTo == EMPTY_GUID ? null : this.data?.assignTo;
      this.secondaryAssignTo =
        this.data?.secondaryUserId == EMPTY_GUID
          ? null
          : this.data?.secondaryUserId;
    }
  }

  onAssignToChange() {
    this.secondaryAgentList = this.secondaryUserList.filter(
      (el: any) => el.id !== this.assignedToUserId
    );
  }

  onSecondaryAssignToChange() {
    this.primaryAgentList = this.primaryUserList.filter(
      (el: any) => el.id !== this.secondaryAssignTo
    );
  }

  userAssign(isSaveAndNext: boolean = false) {
    if (!this.data.name.trim()) {
      this._notificationsService.warn(
        'Lead Name is invalid, Please Rename to continue'
      );
      return;
    }
    if (this.globalSettingsData?.isDualOwnershipEnabled) {
      if (!(this.secondaryAssignTo && !this.assignedToUserId)) {
        this.reassignBothIsLoading = true;
        let payload: any = {
          leadIds: [this.data.id],
          userId: this.assignedToUserId,
          secondaryUserId:
            this.assignedToUserId == EMPTY_GUID
              ? EMPTY_GUID
              : this.secondaryAssignTo,
        };

        this._store.dispatch(new ReassignBoth(payload, false));
        this._store
          .select(getReassignBothIsLoading)
          .pipe(
            takeUntil(this.stopper),
            skipWhile((isLoading: boolean) => isLoading),
            take(1)
          )
          .subscribe((isLoading: boolean) => {
            this.reassignBothIsLoading = isLoading;
            this.data.assignTo = this.assignedToUserId;
            this.data.secondaryUserId = this.secondaryAssignTo;
            if (isSaveAndNext && !this.reassignBothIsLoading) {
              this._store.dispatch(new LeadPreviewChanged());
              this._leadPreviewComponent.nextData();
              return;
            }
            this._store.dispatch(new LeadPreviewChanged());
            !this.reassignBothIsLoading && this.modalService.hide();
          });
      }
    } else {
      this.reassignLeadIsLoading = true;
      let payload: any = {
        leadIds: [this.data.id],
        userId: this.assignedToUserId || this.data?.assignTo,
      };
      this._store.dispatch(new ReassignLead(payload, false));
      this._store
        .select(getReassignLeadIsLoading)
        .pipe(
          takeUntil(this.stopper),
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.reassignLeadIsLoading = isLoading;
          this.data.assignTo = this.assignedToUserId;
          if (isSaveAndNext && !this.reassignLeadIsLoading) {
            this._store.dispatch(new LeadPreviewChanged());
            this._leadPreviewComponent.nextData();
            return;
          }
          this._store.dispatch(new LeadPreviewChanged());
          !this.reassignLeadIsLoading && this.modalService.hide();
        });
    }
    if (this.whatsAppComp) {
      this.isShowAssign = false;
      this.data = {
        ...this.data,
        assignTo: this.assignedToUserId,
        secondaryAssignTo: this.secondaryAssignTo,
      };
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
