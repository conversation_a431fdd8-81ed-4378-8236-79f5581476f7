import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE } from 'src/app/app.constants';
import { FileUploadStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getPages, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getListingSiteLoaders, getSyncListing } from 'src/app/reducers/listing-site/listing-site.reducer';
import { FetchExportPropertyStatus } from 'src/app/reducers/property/property.actions';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { environment as env } from 'src/environments/environment';
@Component({
  selector: 'listing-sync-tracker',
  templateUrl: './listing-sync-tracker.component.html',
})
export class ListingSyncTrackerComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  rowData: any = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  defaultColDef: any;
  totalReqCount: number;
  s3BucketUrl: string = env.s3ImageBucketURL;
  getPages = getPages;
  isDataExportStatusLoading: boolean;
  filtersPayload = {
    pageNumber: 1,
    pageSize: this.pageSize,
    path: 'Property',
  };
  userData: any;
  allUsers: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    public modalService: BsModalService,
    private _store: Store<AppState>
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();

    this._store.dispatch(new FetchUsersListForReassignment());
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const sortedUsers = data?.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
      });

    this._store
      .select(getSyncListing)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data?.items;
        this.totalReqCount = data?.totalCount;
      });
    this._store
      .select(getListingSiteLoaders)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isDataExportStatusLoading = data?.syncListing;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 85,
        valueGetter: (params: any) => [FileUploadStatus[params.data?.status]],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Done By',
        field: 'Done By',
        minWidth: 180,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.allUsers, true) ||
          '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap text-truncate-1 break-all">${params.value[0]
            }</p>
            <p class="text-nowrap text-truncate-1 break-all">${params.value[1]
            }</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Added Count',
        field: 'Added Count',
        minWidth: 80,
        filter: false,
        valueGetter: (params: any) => [params.data?.addedCount],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Updated Count',
        field: 'Updated Count',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.updatedCount],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Sync Count',
        field: 'Sync Count',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.syncCount],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Error Message',
        field: 'Error Message',
        minWidth: 180,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.message ? params.data?.message : '--',
        ],
        cellRenderer: (params: any) => {
          const message = params.value[0];
          const isError = message !== '--';
          const className = isError ? 'text-danger' : '';
          return `<p class="${className} text-truncate-2 break-all text-sm">${message}</p>`;
        },
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.updateTrackerList();
  }

  updateTrackerList() {
    this._store.dispatch(new FetchExportPropertyStatus(this.filtersPayload?.pageNumber, this.filtersPayload?.pageSize));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
