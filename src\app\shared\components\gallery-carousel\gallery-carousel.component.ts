import { Component, EventEmitter, Input, Output } from '@angular/core';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'gallery-carousel',
  templateUrl: './gallery-carousel.component.html',
})
export class GalleryCarouselComponent {
  @Input() imageGallery: Array<string>;
  @Input() index: number;

  @Output() closeCarousel: EventEmitter<any> = new EventEmitter<boolean>();
  s3BucketUrl = env.s3ImageBucketURL;

  constructor() {}


  onPreviousClick = () =>
    (this.index = this.index == 0 ? 0 : (this.index -= 1));

  onNextClick = () =>
    (this.index =
      this.index == this.imageGallery.length - 1 ? 0 : (this.index += 1));

  onCloseClick() {
    this.closeCarousel.emit(false);
  }
}
