import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import FingerprintJS from '@fingerprintjs/fingerprintjs';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';

import {
  APP_STORE_URL,
  PLAY_STORE_URL,
  SUPP0RT_NO,
  WHATSAPP_SHARE_API,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAppImages,
  getAppName
} from 'src/app/core/utils/common.util';
import { PnsService } from 'src/app/services/controllers/PnsRegistration.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'login',
  templateUrl: './login.component.html',
})
export class LoginComponent implements OnInit {
  isShowSupport: boolean = false;
  currentYear: number = new Date().getFullYear();
  building: AnimationOptions = {
    path: 'assets/animations/building.json',
  };
  online: AnimationOptions = {
    path: 'assets/animations/circle-green.json',
  };
  supportNumber: string = SUPP0RT_NO;
  appStoreUrl: string = APP_STORE_URL;
  playStorUrl: string = PLAY_STORE_URL;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  getAppName = getAppName;
  appImages: { appFull: string } = getAppImages();

  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    public metaTitle: Title,
    private PnsService: PnsService,
    public trackingService: TrackingService
  ) {
    this.metaTitle.setTitle('CRM | Login');
  }

  async ngOnInit(): Promise<void> {
    try {
      await this.deleteDb('CachingDb');
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        if (!registration.scope.includes('ngsw')) {
          await registration.unregister();
          console.log(
            `Unregistered service worker with scope: ${registration.scope}`
          );
        }
      }

      const fp = await FingerprintJS.load();
      const result = await fp.get();

      await this.PnsService.deleteDevice(result.visitorId).subscribe(
        (resp: any) => {
          if (resp.succeeded) {
            console.log('Device successfully unregistered.');
          } else {
            console.log('Device unregistration failed.');
          }
        }
      );
    } catch (error) {
      console.error(
        'Error while unregistering the device or retrieving fingerprint:',
        error
      );
    }
  }

  deleteDb(dbName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase(dbName);

      deleteRequest.onsuccess = () => {
        console.log(`[IndexedDB] Database "${dbName}" deleted successfully.`);
        resolve();
      };

      deleteRequest.onerror = (event: any) => {
        console.error(`[IndexedDB] Failed to delete "${dbName}".`, event);
        reject(deleteRequest.error);
      };

      deleteRequest.onblocked = () => {
        console.warn(`[IndexedDB] Delete blocked: Please close all open connections to "${dbName}".`);
      };
    });
  }


  getWhatsappLink(): string {
    return window.innerWidth <= 600
      ? `${WHATSAPP_SHARE_API}?phone=+918050385050`
      : `${WHATSAPP_SHARE_API}?phone=918050385050`;
  }
}
