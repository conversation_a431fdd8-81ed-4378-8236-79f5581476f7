import { Injectable } from '@angular/core';
import { GridOptions } from 'ag-grid-community';

@Injectable({
  providedIn: 'root',
})
export class GridOptionsService {
  private gridSettings: GridOptions;
  public data: any;
  public status: string;
  public subStatus: string;
  public payload: any;
  public meetingStatus: any;
  public scheduledType: string;
  public dateType: any;
  public leadTags: any;
  public secondLevelFilterId: any;
  constructor() {
    this.gridSettings = {
      rowHeight: 44,
      rowStyle: {
        'border-bottom': 'white 12px solid',
        'border-top': 'white 12px solid',
        'border-left': 'transparent',
        'border-right': 'transparent',
      },
      headerHeight: 80,
      rowSelection: 'multiple',
      suppressRowClickSelection: true,
      suppressContextMenu: true,
      suppressMovableColumns: true,
      domLayout: 'autoHeight',
      icons: {
        groupExpanded: '<i class="ic-move-down cursor-pointer"/>',
        groupContracted:
          '<i class="ic-move-up transform-90 cursor-pointer d-inline-block"/>',
      },
      defaultColDef: {},
      overlayNoRowsTemplate:
        '<div class="flex-center"><h4 class="text-hint">No records found</h4></div>',

      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressPivotMode: true,
              suppressRowGroups: true,
              suppressValues: true,
            },
          },
        ],
      },
    };

    this.gridSettings.defaultColDef = {
      filter: true,
      resizable: true,
      hide: false,
      flex: 1,
      minWidth: 150,
      floatingFilterComponentParams: {
        suppressFilterButton: true,
        filterOptions: ['search']
      },
    };

  }
  getGridSettings(parentComponent?: any) {
    if (parentComponent) {
      this.gridSettings.context = { componentParent: parentComponent };
    }

    return Object.assign({}, this.gridSettings);
  }
  clearAllFilters(): void {
    this.data = null;
    this.status = null;
    this.subStatus = null;
    this.payload = null;
    this.meetingStatus = null;
    this.scheduledType = null;
    this.dateType = null;
    this.leadTags = null;
  }
}
