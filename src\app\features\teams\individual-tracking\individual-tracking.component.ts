import { Component, OnInit } from '@angular/core';
import * as moment from 'moment';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'individual-tracking',
  templateUrl: './individual-tracking.component.html',
})
export class IndividualTrackingComponent implements OnInit {
  moment = moment;
  markers: any[] = [];
  center = {
    lat: 12.9106262,
    lng: 77.6405173,
  };
  userData: any;
  s3BucketUrl: string = env.s3ImageBucketURL;

  constructor(private headerTitle: HeaderTitleService) { }

  ngOnInit(): void {
    this.headerTitle.setTitle('Tracking');
  }
}
