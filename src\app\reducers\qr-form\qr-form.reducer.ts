import { Action, createSelector } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import {
  AssignPageSize,
  FetchAllQrFormsSuccess,
  FetchQRBySearchSuccess,
  FetchQrFormByIdSuccess,
  FetchQrFormsFieldsSuccess,
  FetchQrFormsListSuccess,
  QRFormActionTypes,
  TemplateNameExistsSuccess,
} from './qr-form.action';

export type QrFormState = {
  allQRForms: any;
  allQRFormsIsLoading: boolean;
  QRFormList: any;
  selectedForm: any;
  QRFormFields: any;
  isTemplateNameExists:boolean;
  totalQRForms:number;
  pageSize:number;
  pageNumber:number;
};

const initialState: QrFormState = {
  allQRForms: [],
  allQRFormsIsLoading: true,
  QRFormList: [],
  selectedForm: {},
  QRFormFields: [],
  isTemplateNameExists:false,
  totalQRForms: 0,
  pageSize:10,
  pageNumber:1,
};

export function qrFormReducer(
  state: QrFormState = initialState,
  action: Action
): QrFormState {
  switch (action.type) {
    case QRFormActionTypes.FETCH_QR_FORM:
      return {
        ...state,
        allQRFormsIsLoading: true,
      };
    case QRFormActionTypes.FETCH_QR_FORM_SUCCESS:
      return {
        ...state,
        allQRForms: (action as FetchAllQrFormsSuccess).response.items,
        totalQRForms:(action as FetchAllQrFormsSuccess).response.totalCount,
        allQRFormsIsLoading: false,
      };
    case QRFormActionTypes.FETCH_QR_BY_SEARCH_SUCCESS:
      return {
        ...state,
        allQRForms:(action as FetchQRBySearchSuccess).response.items,
        totalQRForms:(action as FetchAllQrFormsSuccess).response.totalCount,
        allQRFormsIsLoading: false,
      };
    case QRFormActionTypes.FETCH_QR_FORM_LIST_SUCCESS:
      return {
        ...state,
        QRFormList: (action as FetchQrFormsListSuccess).response,
      };
    case QRFormActionTypes.FETCH_QR_FORM_BY_ID_SUCCESS:
      return {
        ...state,
        selectedForm: (action as FetchQrFormByIdSuccess).response,
      };
    case QRFormActionTypes.FETCH_QR_FORM_FIELDS_SUCCESS:
      return {
        ...state,
        QRFormFields: (action as FetchQrFormsFieldsSuccess).response,
      };
    case QRFormActionTypes.TEMPLATE_NAME_EXISTS_SUCCESS:
      return {
        ...state,
        isTemplateNameExists:(action as TemplateNameExistsSuccess).isTemplateNameExists
      };
    case QRFormActionTypes.ASSIGN_PAGE_SIZE:
      return {
        ...state,
        pageSize:(action as AssignPageSize).pageSize,
        pageNumber:(action as AssignPageSize).pageNumber
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.qrForm;

export const getAllQRForms = createSelector(
  selectFeature,
  (state: QrFormState) => state.allQRForms
);

export const getAllQRFormsIsLoading = createSelector(
  selectFeature,
  (state: QrFormState) => state.allQRFormsIsLoading
);

export const getQRFormList = createSelector(
  selectFeature,
  (state: QrFormState) => state.QRFormList
);

export const getQRFormById = createSelector(
  selectFeature,
  (state: QrFormState) => state.selectedForm
);
export const getTotalQRCount = createSelector(
  selectFeature,
  (state: QrFormState) => state.totalQRForms
);

export const getQRFormFields = createSelector(
  selectFeature,
  (state: QrFormState) => state.QRFormFields
);

export const getTemplateNameExists = createSelector(
  selectFeature,
  (state: QrFormState) => state.isTemplateNameExists
);
export const getPageSize = createSelector(
  selectFeature,
  (state: QrFormState) => state.pageSize
);
export const getPageNumber = createSelector(
  selectFeature,
  (state: QrFormState) => state.pageNumber
);