import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
    providedIn: 'root',
})
export class QrFormService extends BaseService<any> {
    serviceBaseUrl: string;

    constructor(private http: HttpClient) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    }

    getResourceUrl(): string {
        return 'qrformtemplate';
    }

    getAllQrForm(pageNumber: number, pageSize: number, keyword: string = '', shouldGetDeletedTemplatesOnly: boolean = false) {
        return this.http.get(`${this.serviceBaseUrl}?PageNumber=${pageNumber}&PageSize=${pageSize}&TemplateName=${keyword}&ShouldGetDeletedTemplatesOnly=${shouldGetDeletedTemplatesOnly}`);
    }

    getQrFormList() {
        return this.http.get(`${this.serviceBaseUrl}/template`);
    }

    getQrFormById(id: string) {
        return this.http.get(`${this.serviceBaseUrl}/id?id=${id}`);
    }

    updateQrForm(payload: any, id: string) {
        return this.http.put(`${this.serviceBaseUrl}?id=${id}`, payload);
    }

    updateStatus(id: string) {
        return this.http.put(`${this.serviceBaseUrl}/status/${id}?id=${id}`, '');
    }

    getQrFormFields() {
        return this.http.get(`${this.serviceBaseUrl}/qr-form`);
    }

    deleteQrForm(id: string, shouldDeletePermanently: boolean) {
        const url = `${this.serviceBaseUrl}/?id=${id}&ShouldDeletePermanently=${shouldDeletePermanently}`;
        return this.http.delete(url);
    }

    isTemplateNameExists(name: string) {
        return this.http.get(`${this.serviceBaseUrl}/existing/qrtemplate?templateName=${name}`);
    }

    restoreTemplate(id: string) {
        return this.http.put(`${this.serviceBaseUrl}/restore/${id}?id=${id}`, '');
    }

    bulkDelete(ids: string[], shouldDeletePermanently: boolean) {
        const url = `${this.serviceBaseUrl}/bulk/delete`;
        const payload = {
            ids: ids,
            shouldDeletePermanently: shouldDeletePermanently
        };
        return this.http.delete(url, { body: payload });
    }

    bulkRestore(ids: string[]) {
        const url = `${this.serviceBaseUrl}/restore/bulk`;
        return this.http.put(url, ids);
    }
}