import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';

import { of } from 'rxjs';
import {
  catchError,
  map,
  switchMap,
} from 'rxjs/operators';
import { OnError } from 'src/app/app.actions';
import { NotificationCall, NotificationCallSuccess, NotificationInfoActionTypes } from './notification-info.action';
import { GetNotificationInfoService } from 'src/app/services/controllers/notificationInfo.service';

@Injectable()
export class NotificationInfoEffects {

  notificationCall$ = createEffect(() =>
    this.actions$.pipe(
      ofType(NotificationInfoActionTypes.NOTIFICATION),
      switchMap((action: NotificationCall) => {        
        return this.api.notificationCall(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new NotificationCallSuccess(resp);
            }
            return new NotificationCallSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: GetNotificationInfoService,
  ) { }
}
