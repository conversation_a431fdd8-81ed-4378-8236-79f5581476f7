<ng-container *ngIf="showForgetPassword">
  <div class="flex-between mt-30">
    <div>
      <h4 class="fw-600">{{ 'AUTH.forgot-password' | translate }}?</h4>
      <div class="mt-4">{{ 'AUTH.password-reset' | translate }}</div>
    </div>
    <img src="../../../../assets/images/side-laptop.svg" alt="laptop">
  </div>
  <div class="field-label">{{ 'AUTH.user-name' | translate }}</div>
  <form-errors-wrapper>
    <input type="text" id="inpLoginUsername" data-automate-id="inpLoginUsername" placeholder="Enter your username"
      (keyup.enter)="focusableLink.click()" [(ngModel)]="userName">
  </form-errors-wrapper>
  <h4 #focusableLink class="btn-accent-green-xl mt-20" [ngClass]="{'pe-none disabled' : !userName}"
    (click)="verifyUsername();trackingService?.trackFeature('Web.Login.Button.ForgotPasswordSubmit.Click')">{{
    'BUTTONS.submit' | translate }}
  </h4>
  <div class="justify-end">
    <div class="flex-end mt-10 cursor-pointer"
      (click)="navigateToLogin();trackingService?.trackFeature('Web.Login.Button.GoBackToLogin.Click')">
      <span class="icon ic-sm ic-arrow-left ic-dark"></span>
      <h5 class="fw-semi-bold ml-4">{{ 'AUTH.go-back-login' | translate }}</h5>
    </div>
  </div>
</ng-container>

<!---Account recover-->
<ng-container *ngIf="showAccount && userData">
  <div class="flex-between my-30">
    <div>
      <h4 class="fw-600 w-150">{{ 'AUTH.recover-account' | translate }}</h4>
    </div>
    <img src="../../../../assets/images/laptop-hand-wave.svg" alt="laptop" width="75" height="70">
  </div>
  <div>
    <h5 class="fw-600 text-center">{{ 'AUTH.recovery-options' | translate }}:</h5>
  </div>
  <div class="bg-light-pearl p-16 mt-20 br-6 bg-mail fw-700">
    <div>{{ 'GLOBAL.your' | translate }} {{ 'SHARE.email' | translate }}</div>
    <p class="fw-semi-bold mb-16 text-sm">{{ 'AUTH.otp-to' | translate }} {{ 'GLOBAL.your' | translate }}
      {{ 'SHARE.email' | translate }}</p>
    <div>{{ userData?.email | slice:0:1 }}******{{ userData?.email | slice:userData?.email.indexOf('@') }}</div>
  </div>
  <div class="bg-light-pearl p-16 mt-20 br-6 bg-phone fw-700">
    <div>{{ 'GLOBAL.your' | translate }} {{ 'USER.phone-number' | translate }}</div>
    <p class="fw-semi-bold mb-16 text-sm">{{ 'AUTH.otp-to' | translate }} {{ 'GLOBAL.your' | translate }}
      {{ 'USER.phone-number' | translate }}</p>
    <div>
      {{userData?.phoneNumber | slice:0:4 }}*******{{ userData?.phoneNumber | slice:userData?.phoneNumber.length-2}}
    </div>
  </div>
  <h4 class="btn-accent-green-xl mt-40"
    (click)="getOtp();trackingService?.trackFeature('Web.Login.Button.GetOTP.Click')">
    {{ 'AUTH.get' | translate }} {{ 'LEAD_FORM.otp' | translate }}</h4>
</ng-container>

<!---Otp--->
<ng-container *ngIf="showOtp">
  <div class="flex-between my-30">
    <div>
      <h4 class="fw-600">{{ 'AUTH.enter-otp' | translate }}</h4>
      <h6 class="w-170">{{ 'AUTH.we-sent-an' | translate }} <div class="fw-700 d-inline">
          {{ 'LEAD_FORM.otp' | translate }} </div> {{ 'AUTH.to-email-and-phone' | translate }}</h6>
    </div>
    <img src="../../../../assets/images/message-otp.svg" alt="otp">
  </div>
  <div class="gap-3 align-center">
    <input type="text" id="digit-1" (keyup)="otpClickEvent($event,'digit-2')" [(ngModel)]="otp[0]" class="otp-block"
      maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-2" (keyup)="otpClickEvent($event,'digit-3','digit-1')" [(ngModel)]="otp[1]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-3" (keyup)="otpClickEvent($event,'digit-4','digit-2')" [(ngModel)]="otp[2]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <div>-</div>
    <input type="text" id="digit-4" (keyup)="otpClickEvent($event,'digit-5','digit-3')" [(ngModel)]="otp[3]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-5" (keyup)="otpClickEvent($event,'digit-6','digit-4')" [(ngModel)]="otp[4]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-6" (keyup)="otpClickEvent($event,'','digit-5')" [(ngModel)]="otp[5]" class="otp-block"
      maxlength="1" placeholder="&#9679;" autocomplete="off" (keyup.enter)="focusableLink.click()" />
  </div>
  <div class="d-flex mt-30 gap-2">
    <div class="fw-semi-bold">{{ 'AUTH.not-receive' | translate }} {{ 'LEAD_FORM.otp' | translate }}?</div>
    <h5 class="fw-semi-bold" [ngClass]="isExpired ? 'text-accent-green cursor-pointer' : 'text-gray-110 pe-none'"
      (click)="resendOtp(); trackingService?.trackFeature('Web.Login.Button.ResendOTP.Click')"><u>{{ 'AUTH.resend' |
        translate }} {{ 'LEAD_FORM.otp' | translate }}</u>
    </h5>
    <!-- <div *ngIf="!isExpired">{{ minutes }}:{{ seconds }}</div> -->
    <div *ngIf="!isExpired">00:{{ formattedSeconds }}</div>
  </div>
  <h4 #focusableLink class="btn-accent-green-xl mt-50"
    (click)="verifyOtp();trackingService?.trackFeature('Web.Login.Button.SubmitOTP.Click')">{{ 'BUTTONS.submit' |
    translate }}</h4>
</ng-container>

<!---Reset Password--->
<ng-container *ngIf="otpVerified">
  <div class="flex-between mt-30">
    <div>
      <h4 class="fw-600 w-150">{{ 'AUTH.change-password' | translate }}</h4>
      <div class="w-160 mt-160">{{ 'AUTH.enter-new-password' | translate }}</div>
    </div>
    <img src="../../../../assets/images/side-laptop.svg" alt="otp">
  </div>
  <!-- <h5 class="fw-600 text-center my-20">{{ 'AUTH.type-twice' | translate }}. {{ 'AUTH.password' | translate }}
    {{ 'AUTH.can-contain' | translate }}:</h5>
  <div class="align-center fw-600">
    <div class="align-center-col">
      <div class="text-sm">8+</div>
      <div class="mt-4 text-xs text-slate">{{ 'GLOBAL.character' | translate }}</div>
    </div>
    <div class="align-center-col ml-20">
      <div class="text-sm">AA</div>
      <div class="mt-4 text-xs text-slate">{{ 'GLOBAL.uppercase' | translate }}</div>
    </div>
    <div class="align-center-col ml-20">
      <div class="text-sm">aa</div>
      <div class="mt-4 text-xs text-slate">{{ 'GLOBAL.lowercase' | translate }}</div>
    </div>
    <div class="align-center-col ml-20">
      <div class="text-sm">123</div>
      <div class="mt-4 text-xs text-slate">{{ 'GLOBAL.number' | translate }} </div>
    </div>
    <div class="align-center-col ml-20">
      <div class="text-sm">@$#</div>
      <div class="mt-4 text-xs text-slate">{{ 'GLOBAL.symbol' | translate }}</div>
    </div>
  </div> -->
  <form [formGroup]="resetPasswordForm">
    <div class="field-label-req">{{ 'GLOBAL.new' | translate }} {{ 'AUTH.password' |
      translate }}</div>
    <form-errors-wrapper label="New Password" class="text-large" [control]="resetPasswordForm?.controls['newPassword']">
      <input [type]="showNewPassword?'text':'password'" placeholder="enter the new password"
        formControlName="newPassword" minlength="5">
      <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
        [ngClass]="showNewPassword ? 'ic-eye-slash' : 'ic-eye-solid'" (click)="showNewPassword = !showNewPassword"></a>
    </form-errors-wrapper>
    <div class="field-label-req">{{ 'AUTH.confirm-password' | translate }}</div>
    <form-errors-wrapper label="Confirm Password" text-large [control]="resetPasswordForm?.controls['confirmPassword']">
      <input [type]="showConfirmPassword?'text':'password'" placeholder="re-enter the new password" minlength="5"
        formControlName="confirmPassword" (keyup.enter)="focusableLink.click()">
      <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
        [ngClass]="showConfirmPassword ? 'ic-eye-slash' : 'ic-eye-solid'"
        (click)="showConfirmPassword = !showConfirmPassword"></a>
      <div class="error-message mt-5" *ngIf="resetPasswordForm?.controls?.confirmPassword?.status==='VALID'">
        {{resetPasswordForm?.errors?.match_error}}</div>
      <div class="error-message text-accent-green mt-5" *ngIf="resetPasswordForm?.valid">
        {{ 'AUTH.password' | translate }} {{ 'AUTH.matches' | translate }}</div>
    </form-errors-wrapper>
  </form>
  <h4 class="mt-40 btn-accent-green-xl"
    (click)="resetPassword();trackingService?.trackFeature('Web.Login.Button.ChangePassword.Click')" #focusableLink>
    {{ 'USER_MANAGEMENT.change-password' | translate }}</h4>
</ng-container>