import {
  Compo<PERSON>,
  <PERSON>E<PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs/operators';

import { ATTRIBUTES_KEY, IMAGES } from 'src/app/app.constants';
import { EnquiryType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { AreaUnits } from 'src/app/core/interfaces/property-microsite.interface';
import { changeCalendar, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { platformShareLinks } from 'src/app/core/utils/share.util';
import { EnquiryFormComponent } from 'src/app/features/no-auth/property-microsite/enquiry-form/enquiry-form.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchAnonymousAttributeList,
  FetchQRAreaUnitList,
} from 'src/app/reducers/master-data/master-data.actions';
import { getAttrAnonymousMasterData, getQrAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { FetchMicrositeProperty } from 'src/app/reducers/property/property.actions';
import { getMicrositeProperty } from 'src/app/reducers/property/property.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'property-microsite',
  templateUrl: './property-microsite.component.html',
})
export class PropertyMicrositeComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedSection: string = 'Amenities';
  images = IMAGES;
  currentSlide = 0;
  slides: Array<{ src: string }> = [];
  propertyInfo: any;
  userData: any;
  userMobileNumber: string;
  mobileNumber: string;
  amenities: any[] = [];
  attributes: any[] = [];
  propertyImages: Array<any> = [];
  s3BucketUrl = env.s3ImageBucketURL;
  attributesSelection: any = {};
  areaSizeUnits: Array<AreaUnits> = [];
  currentYear: number = new Date().getFullYear();
  env: any = env;
  mobileView: boolean = false;
  isShareVisible: boolean = false;
  possessionDate: string;
  propertiesList: Array<any> = [];
  propertyId: string;
  attributesItems: any = ATTRIBUTES_KEY;
  additionalAttrData: any[];
  isSticky = false;
  isMicrositeFeatureEnabled: boolean;
  getTimeZoneDate = getTimeZoneDate
  currentDate: Date = new Date();
  isListing: boolean;
  isPropertySoldOut: boolean = false;
  globalSettingsDetails: any;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private activatedRoute: ActivatedRoute,
    private store: Store<AppState>,
    public metaTitle: Title
  ) {
    this.metaTitle.setTitle('CRM | Microsite');
  }

  async ngOnInit() {
    this.isListing = window.location.pathname.includes('listing')
    this.activatedRoute.params.subscribe((params: any) => {
      if (params && params.serialNo) {
        this.store.dispatch(new FetchMicrositeProperty(params.serialNo));
      }
    });
    this.store.dispatch(new FetchAnonymousAttributeList());
    this.store.dispatch(new FetchQRAreaUnitList());

    this.store
      .select(getQrAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((qrunits: any) => {
        this.areaSizeUnits = qrunits || [];
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        this.isMicrositeFeatureEnabled = data?.isMicrositeFeatureEnabled;
      });
    this.store
      .select(getMicrositeProperty)
      .pipe(takeUntil(this.stopper))
      .subscribe((propertyData: any) => {
        if (Object.values(propertyData || {}).length) {
          this.propertyInfo = propertyData;
          this.propertyInfo = Object.assign({}, this.propertyInfo);
          this.amenities = this.propertyInfo.amenities;          
          this.attributes = this.propertyInfo.attributes;
          this.additionalAttrData = this.propertyInfo?.attributes?.filter(
            (item: any) => item.attributeType === 'Additional'
          );
          this.propertyInfo.enquiredFor =
            EnquiryType[this.propertyInfo.enquiredFor];
          this.propertyInfo.furnishStatus = this.propertyInfo.furnishStatus;
          this.propertyInfo.facing = this.propertyInfo.facing;
          this.possessionDate = this.propertyInfo.possessionDate;
          this.checkPropertyStatus();
          if (this.propertyInfo?.imageUrls) {
            const allImageCategories = Object.keys(this.propertyInfo.imageUrls);
            this.propertyImages = [];
            allImageCategories.forEach((category) => {
              const images = this.propertyInfo.imageUrls[category];
              images.forEach((image: any) => {
                const imagePath = image.imageFilePath.replace(
                  this.s3BucketUrl,
                  ''
                );
                this.propertyImages.push({
                  src: imagePath,
                  isCoverImage: image.isCoverImage,
                  orderRank: image.orderRank || 0
                });
              });
            });

            if (this.propertyImages.length > 0) {
              this.propertyImages.sort((a, b) => a.orderRank - b.orderRank);
              const coverImage = this.propertyImages.find(img => img.isCoverImage);
              if (coverImage) {
                this.propertyImages = this.propertyImages.filter(img => !img.isCoverImage);
                this.propertyImages.unshift(coverImage);
              }
              this.slides = [...this.propertyImages];
            } else {
              this.slides = [
                { src: 'assets/images/default-property-image.jpg' },
              ];
            }
          } else {
            this.slides = [{ src: 'assets/images/default-property-image.jpg' }];
          }
        }
        this.store
          .select(getAttrAnonymousMasterData)
          .pipe(takeUntil(this.stopper))
          .subscribe((data: any) => {
            if (this.propertyInfo?.attributes?.length && data?.length) {
              this.propertyInfo?.attributes.map((attr: any) => {
                data.map((item: any) => {
                  if (
                    item.id == attr.masterPropertyAttributeId &&
                    this.attributesItems.includes(item.attributeDisplayName)
                  ) {
                    this.attributesSelection[item.attributeName] = {
                      id: item.id,
                      attributeDisplayName: item.attributeDisplayName,
                      value: attr.value,
                    };
                  }
                });
              });
            }
          });
      });
    // setInterval(() => {
    //   if (!this.isMicrositeFeatureEnabled) {
    //     if (this.modalService.getModalsCount() === 0) {
    //       this.modalRef = this.modalService.show(MsSubscriptionComponent, {
    //         class: 'modal-300 modal-dialog-centered ph-modal-unset',
    //       });
    //     }
    //   }
    // }, 5000);
  }

  checkPropertyStatus(): void {
    if (this.propertyInfo &&
      (this.propertyInfo.status === 1 ||
        this.propertyInfo.status === 'Sold')) {
      this.isPropertySoldOut = true;
    }
  }

  onPreviousClick() {
    const previous = this.currentSlide - 1;
    this.currentSlide = previous < 0 ? this.slides.length - 1 : previous;
  }

  onNextClick() {
    const next = this.currentSlide + 1;
    this.currentSlide = next === this.slides.length ? 0 : next;
  }

  openImage(
    image: TemplateRef<any>,
    mbleImage: TemplateRef<any>,
    index: number
  ) {
    this.currentSlide = index;
    if (window.screen?.width > 1150) {
      this.modalRef = this.modalService.show(image, {
        class: 'modal-dialog-centered no-bg',
      });
    } else {
      this.modalRef = this.modalService.show(mbleImage, {
        class: 'modal-dialog-centered no-bg',
      });
    }
  }

  showEnquire() {
    this.modalService.show(EnquiryFormComponent, {
      class:
        window.innerWidth > 480
          ? 'modal-450 modal-dialog-centered'
          : 'down-modal modal-unset',
    });
  }

  // onScroll(event: Event) {
  //   const element = event.target as HTMLElement;
  //   this.isSticky = element.scrollTop > 310; // Adjust the scroll offset as needed
  //   if (!this.isMicrositeFeatureEnabled) {
  //     if (this.modalService.getModalsCount() === 0) {
  //       this.modalRef = this.modalService.show(MsSubscriptionComponent, {
  //         class: 'modal-300 modal-dialog-centered ph-modal-unset',
  //       });
  //     }
  //   }
  // }

  shareInfo(via?: string) {
    switch (via) {
      case 'email':
        window.location.href = platformShareLinks(
          'email',
          location.href,
          '',
          ''
        );
        break;

      case 'whatsApp':
        window.open(
          platformShareLinks('whatsApp', location.href, '', ''),
          '_blank'
        );
        break;
    }
  }

  // @HostListener('document:click', ['$event'])
  // @HostListener('document:scroll', ['$event'])
  // offClickHandler(event: MouseEvent): void {
  //   if (!this.isMicrositeFeatureEnabled) {
  //     if (this.modalService.getModalsCount() === 0) {
  //       this.modalRef = this.modalService.show(MsSubscriptionComponent, {
  //         class: 'modal-300 modal-dialog-centered ph-modal-unset',
  //       });
  //     }
  //   }
  // }

  hideModal() {
    this.modalRef.hide();
  }
  
  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
