/* You can add global styles to this file, and also import other style files */
@import 'node_modules/bootstrap/scss/bootstrap.scss';
@import './utilities/utlities.scss';
@import './components/components.scss';
@import 'ag-grid-community/dist/styles/ag-grid.css';
@import 'ag-grid-community/dist/styles/ag-theme-alpine.css';
@import "~@danielmoncada/angular-datetime-picker/assets/style/picker.min.css";
@import "~@ng-select/ng-select/themes/material.theme.css";

// @import '~@fullcalendar/core/main.css';
// @import '~@fullcalendar/daygrid/main.css';
// @import 'ag-grid-community';

/* Importing Bootstrap SCSS file. */
// @import "~bootstrap/scss/bootstrap";

.dropzone {
  height: 200px;
  display: table;
  width: 100%;
  background-color: #eee;
  border: dotted 1px #aaa;
}

input[type='file'] {
  display: none;
}
