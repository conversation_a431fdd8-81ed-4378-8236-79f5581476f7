import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';

@Component({
  selector: 'property-videos',
  templateUrl: './property-videos.component.html',
})
export class PropertyVideosComponent implements OnInit {
  @Input() propertyInfo: any;
  @Input() isPropertySoldOut:boolean;
  selectedVideo: any;
  @ViewChild('videoPlayer') videoPlayer: ElementRef;

  constructor() { }

  ngOnInit(): void {
    this.selectedVideo = this.propertyInfo?.videos?.length > 0 ? this.propertyInfo?.videos?.[0] : null;
  }

  selectVideo(index: number) {   
    this.selectedVideo = this.propertyInfo?.videos[index];
    this.videoPlayer.nativeElement.src = this.selectedVideo?.imageFilePath;
    this.videoPlayer.nativeElement.load();
  }

}
