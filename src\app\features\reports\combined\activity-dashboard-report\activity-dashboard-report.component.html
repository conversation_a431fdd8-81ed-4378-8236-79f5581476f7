<div class="d-flex bg-white py-8 px-16 border-top border-bottom position-fixed w-100 z-index-2">
    <div class="d-flex text-nowrap ph-w-100-40 scrollbar scroll-hide">
        <ul class="d-flex cursor-pointer ip-mb-4">
            <button class="align-center mr-10 px-12 header-5 py-8 border-0 br-4" id="clkAll" data-automate-id="clkAll"
                [ngClass]="selectedSection !== 'All' ? 'text-light-gray' : 'text-dark-250 fw-700'"
                (click)="handleSelection('All')">
                <span class="icon ic-folder-solid ic-sm mr-8"
                    [ngClass]="selectedSection !== 'All' ? 'ic-gray' : 'ic-black'"></span>
                <span class="ph-d-none">All</span>
            </button>
            <button class="align-center mr-10 px-12 header-5 py-8 border-0 br-4" id="clkLead" data-automate-id="clkLead"
                [ngClass]="selectedSection !== 'Lead' ? 'text-light-gray' : 'text-dark-250 fw-700'"
                (click)="handleSelection('Lead')">
                <span class="icon ic-secondary-filter-solid ic-sm mr-8"
                    [ngClass]="selectedSection !== 'Lead' ? 'ic-gray' : 'ic-black'"></span>
                <span class="ph-d-none">Leads</span>
            </button>
            <button class="align-center mr-10 px-12 header-5 py-8 border-0 br-4" id="clkData" data-automate-id="clkData"
                [ngClass]="selectedSection !== 'Data' ? 'text-light-gray' : 'text-dark-250 fw-700'"
                (click)="handleSelection('Data')">
                <span class="icon ic-address-card-solid ic-sm mr-8"
                    [ngClass]="selectedSection !== 'Data' ? 'ic-gray' : 'ic-black'"></span>
                <span class="ph-d-none">Data</span> </button>
        </ul>
    </div>
</div>
<div *ngIf="selectedSection === 'Lead'">
    <activity-report></activity-report>
</div>
<div *ngIf="selectedSection === 'Data'">
    <data-activity-report></data-activity-report>
</div>
<div *ngIf="selectedSection === 'All'">
    <combined-activity-report></combined-activity-report>
</div>