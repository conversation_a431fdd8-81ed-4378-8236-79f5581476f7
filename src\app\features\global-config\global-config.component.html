<!-- <div class="position-absolute bottom-0 p-20 bg-alert z-index-1021 brtr-10 brtl-10" *ngIf="!closeAlertBox">
  <div title="close alert" (click)="closeFBAlert()" class="position-absolute right-70 top-10 cursor-pointer"><span
      class="icon ic-close ic-sm ic-dark-red"></span></div>
  <h4 class="text-danger fw-600 mb-10 align-center">
    <span class="icon ic-notification ic-sm ic-dark-red mr-4"></span>
    Urgent: Major Facebook Outage Impacting Lead dropping to CRM
  </h4>
  <div>
    <div>This is an urgent and critical update for all users running Facebook or Instagram ads for lead generation.
      Facebook
      is currently experiencing a <span class="fw-600">major outage</span> that is severely affecting the transfer of
      leads through
      webhooks
      or
      API
      connections. As a result, <span class="fw-600">you may face issues with leads not dropping into your CRM</span>.
      <span *ngIf="!viewMore" class="fw-600 mx-6 cursor-pointer text-secondary" (click)="viewMore = true">see
        more...</span>
    </div>

    <i [ngClass]="viewMore ? 'd-block' : 'd-none'">
      <h4 class="fw-600 mt-6 mb-4"><i>Immediate Action Required:</i></h4>
      <div class="align-center gap-1">
        <h3>&#x2022;</h3> Please check your META Ad
        Account immediately to
        monitor any disruptions.
      </div>
      <div class="align-center gap-1">
        <h3>&#x2022;</h3>Due to this global outage, lead
        transfers via API are not functioning, and leads are not being automatically
        pushed
        to your CRM.
      </div>
      <div class="align-center gap-1">
        <h3>&#x2022;</h3> <span class="fw-600" class="mr-2">The only current
          workaround </span>
        to get your leads into the CRM is through manual bulk upload.
      </div>
    </i>
    <div *ngIf="viewMore" class="fw-600 mx-6 cursor-pointer text-secondary" (click)="viewMore = false">see less...</div>


    <h5 class="mt-6 mb-2 fw-600">Please find the link below for more information on the bug reported by multiple
      organizations
      worldwide.</h5>
    <a href="https://developers.facebook.com/support/bugs/****************/" target="_blank"
      class="text-primary break-all"><u>https://developers.facebook.com/support/bugs/****************/</u></a>
  </div>
</div> -->
<!-- [ngClass]="{
  'h-100-280': viewMore && !closeAlertBox,
  'h-100-180': !viewMore && !closeAlertBox,
  'h-100': closeAlertBox
}" -->
<div class="px-30 py-20 scrollbar scroll-hide">
  <!-- <div class="banner-hover px-20 py-16 flex-between br-6">
    <div>
      <h5 class="fw-600 text-coal">{{ 'SETTINGS.notify-on-update' | translate }}</h5>
      <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.notified-when-anyone'| translate }}</h6>
    </div>
    <div>
      <div class="align-center br-10 p-10 border-gray">
          <div class="text-xs mr-8">Off</div>
          <input type="checkbox" name="darkTheme" class="toggle-switch toggle-active-sold">
          <label for="chkToggle" class="switch-label"></label>
        </div>
      </div>
    </div>
  </div>
  <div> -->
  <div class="ip-flex-col ip-align-center-unset flex-between" *ngIf="permissionsSet?.has('Permissions.OrgProfile.View')">
    <div class="align-center ip-mb-20" [ngClass]="{'pe-none blinking' : isProfileLoading}">
      <div class="border mr-8 align-center br-6">
        <img [appImage]="profile?.logoImgUrl ? s3BucketUrl+profile.logoImgUrl : ''" [type]="'orgProfileLogo'"
          class="br-6 obj-cover" alt="logo" width=45px height=45px>
      </div>
      <div>
        <div>
          <h4 class="text-black-10 fw-semi-bold clear-margin">{{profile?.displayName || ''}}</h4>
        </div>
        <div class="fw-semi-bold text-dark-800 text-sm">{{tenantUrl}}</div>
      </div>
    </div>
    <!-- <div class="d-flex">
      <div class="align-center px-30 no-validation py-8 br-20 border mr-12">
        <input placeholder="Search" (input)="searchRoleName($event.target.value)" name="search"
          class="border-0 outline-0 w-100 bg-light-pearl">
        <span class="justify-end search icon ic-search ic-xxs ic-slate-90"> </span>
      </div>
    </div> -->
  </div>
  <ng-container *ngIf="canGlobalSettingsView">
    <div class="pt-30 align-center">
      <span class="icon ic-monitor-phone ic-sm ic-coal mr-8"></span>
      <!-- App Tour id="customerlist"  -->
      <span class="fw-600">{{'SIDEBAR.module-settings'| translate}}</span>
      <div class="flex-grow-1 border-bottom ml-12 mr-40"></div>
    </div>
    <div class="d-flex flex-wrap" *ngIf="!isGlobalSettingsLoading else moduleSettingsCardSkeleton">
      <module-settings-card *ngFor="let module of filteredModuleSettings" [moduleSetting]="module"
        class="w-25 ip-w-50 tb-w-33 ph-w-100 pt-16">
      </module-settings-card>
    </div>
    <ng-template #moduleSettingsCardSkeleton>
      <div class="d-flex flex-wrap">
        <module-settings-card *ngFor="let module of [1,2,3,4,5,6,7,8,9,10,11]" [isSkeleton]="true"
          [moduleSetting]="module" class="w-25 ip-w-50 tb-w-33 ph-w-100 pt-16">
        </module-settings-card>
      </div>
    </ng-template>
  </ng-container>
  <ng-container *ngIf="canIntegrationView && hasAnySources">
    <div class="pt-30 align-center ip-flex-col ip-align-center-unset flex-between">
      <div class="align-center ip-mb-20">
        <span class="icon ic-plug-circle ic-sm ic-coal mr-8"></span>
        <span class="fw-600">{{'SIDEBAR.integration'| translate}}</span>
      </div>
      <div class="flex-grow-1 border-bottom ml-12 mr-12 ip-d-none"></div>
      <div class="d-flex">
        <div class="border br-20 bg-white align-center user p-4 ">
          <div *ngIf="hasThirdPartySources" class="px-10 text-light-gray ip-px-8 align-center br-20 cursor-pointer h-28 fw-semi-bold"
            [ngClass]="{'text-white fw-700 bg-black-100' : selectedSection == 'Third'}"
            (click)="selectTab('Third')">
            <span class="ph-mr-4 mr-8 icon ic-circle-nodes ic-sm"
              [ngClass]="{'ic-light-gray' : selectedSection !== 'Third'}"></span>
            <span>3rd Parties</span>
          </div>
          <div *ngIf="hasSocialSources" class="px-10 text-light-gray ip-px-8 align-center br-20 cursor-pointer h-28 fw-semi-bold"
            [ngClass]="{'text-white fw-700 bg-black-100' : selectedSection == 'Social'}"
            (click)="selectTab('Social')">
            <span class="ph-mr-4 mr-8 icon ic-circuit-board ic-sm"
              [ngClass]="{'ic-light-gray' : selectedSection !== 'Social'}"></span>
            <span>Social Platforms</span>
          </div>
          <div *ngIf="hasOthersSources" class="px-10 text-light-gray  ip-px-8  align-center br-20 cursor-pointer h-28 fw-semi-bold"
            [ngClass]="{'text-white fw-700 bg-black-100' : selectedSection == 'Others'}"
            (click)="selectTab('Others')">
            <span class="mr-8 ph-mr-0 icon ic-hexagon-arrow ic-sm"
              [ngClass]="{'ic-light-gray' : selectedSection !== 'Others'}"></span>
            <span>Others</span>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="canIntegrationView && hasAnySources">
    <div class="pt-16">
      <div class="d-flex flex-wrap w-100">
        <!-- Main integrations (filtered based on current section) -->
        <ng-container *ngFor="let integration of filteredIntegrationMap">
          <!-- Skip Facebook and Instagram in the main list as they're handled separately -->
          <ng-container *ngIf="!(selectedSection=='Social' && (integration.name === 'Facebook' || integration.name === 'Instagram')) &&
                              !(selectedSection=='Third' && (integration.name === 'CommonFloor' || integration.name === 'JustLead'))">
            <integration-card [integration]="integration" [isSkeleton]="isGlobalSettingsLoading"
              class="w-14pr tv-w-12 mb-20 tb-w-20 ip-w-50 ph-w-50">
            </integration-card>
          </ng-container>
        </ng-container>
        <!-- common Floor & Just Lead-->
        <ng-container *ngFor="let integration of integrationCommonFloorJustLead">
          <ng-container *ngIf="selectedSection=='Third' && hasThirdPartySources && isSourceEnabled(integration.name)">
            <integration-card [isSkeleton]="isGlobalSettingsLoading"
              [integration]="integration" class="w-14pr tv-w-12 mb-20 tb-w-20 ip-w-50 ph-w-50"
              [functionType]="'commonFloor'"
              (connectNowClicked)="openCommonFloorJustLeadIntegration(integration?.image, integration?.displayName, integration?.name, accountCount[integration?.name]?.accountCount)">
            </integration-card>
          </ng-container>
        </ng-container>
        <!-- Social Media Integrations (Facebook, Instagram, etc.) -->
        <ng-container *ngIf="selectedSection=='Social' && hasSocialSources">
          <!-- Facebook -->
          <ng-container *ngFor="let integration of filteredIntegrationMap">
            <ng-container *ngIf="integration.name === 'Facebook'">
              <integration-card [integration]="integration"
                [isSkeleton]="isGlobalSettingsLoading" class="w-14pr tv-w-12 mb-20 tb-w-20 ip-w-50 ph-w-50"
                [functionType]="'facebook'"
                (connectNowClicked)="openOthersIntegration(integration?.image, integration?.displayName, integration?.name)">
              </integration-card>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </ng-container>
  <div *ngIf="canTemplatesView || canIntegrationView">
    <div class="pt-20 align-center">
      <span class="icon ic-head-phone-solid ic-sm ic-coal mr-8"></span>
      <span class="fw-600">{{'SIDEBAR.communication'| translate}}</span>
      <div class="flex-grow-1 border-bottom ml-12 mr-40"></div>
    </div>
    <div class="pt-16">
      <div class="d-flex tb-flex-wrap">
        <module-settings-card *ngFor="let communicationSetting of checkpermission(communicationSettings)"
          [isSkeleton]="isGlobalSettingsLoading" [moduleSetting]="communicationSetting"
          class="w-25 ip-w-50 tb-w-33 ph-w-100 pt-16">
        </module-settings-card>
      </div>
    </div>
  </div>
</div>