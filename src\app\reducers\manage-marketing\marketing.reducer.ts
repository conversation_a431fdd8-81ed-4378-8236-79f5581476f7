import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { ExistAgencySuccess, ExistCampaignSuccess, ExistChannelPartnerSuccess, FetchAgencyExcelUploadedSuccess, FetchAgencyExportTrackerSuccess, FetchAgencyNameSuccess, FetchCampaignExcelUploadedSuccess, FetchCampaignExportTrackerSuccess, FetchCampaignNameSuccess, FetchChannelPartnerExcelUploadedSuccess, FetchChannelPartnerExportTrackerSuccess, FetchChannelPartnerLocationsSuccess, FetchChannelPartnerSuccess, MarketingAction, MarketingExcelUploadSuccess, UpdateAgencyFiltersPayload, UpdateCampaignFiltersPayload, UpdateChannelPartnerFiltersPayload } from './marketing.action';

export interface AgencyFiltersPayload {
    PageNumber: number;
    PageSize: number,
    Location: string;
    AssociatedLeadsCount: string;
    AssociatedPospectsCount: string;
    AssociateProperty: string;
    SearchText: string;
    FromDate: any;
    ToDate: any;
    DateType: string;
}

export interface CampaignFiltersPayload {
    CampaignName: string;
    PhoneNumber: string;
    EmailId: string;
    Location: string;
    AssociateLead: string;
    AssociateData: string;
    AssociateProperty: string;
    CreatedBy: string;
    ModifiedBy: string;
}

export type MarketingState = {
    agencyFiltersPayload: AgencyFiltersPayload;
    campaignFiltersPayload: any,
    channelPartnerNameFiltersPayload: any
    agencyNameList?: any;
    agencyLocationList: any,
    marketingExcelColumnHeading?: any;
    exportAgencyStatus: any;
    isExportStatusLoading: boolean;
    isFetchAgencyLoading: boolean,
    isAgencyLocationLoading: boolean,
    isBulkDeleteAgencyLoading: boolean,
    isExcelUploadedListLoading: boolean;
    isAgencyExcelUploadedListLoading: boolean,
    isAgencyUpdateLoading: boolean,
    isAgencyExistLoading: boolean,
    isAgencyAddLoading: boolean,
    excelUploadedList?: any;
    agencyExist: boolean,

    //   ------------- CAMPAING--------------
    campaignNameList: any,
    campaignListIsLoading: boolean;
    isBulkDeleteCampaignLoading: boolean;
    isCampaignExcelUploadedListLoading: boolean;
    exportCampaignStatus: any;
    campaignExist: boolean,
    isCampaignExistLoading: boolean,

    // ------------ CHANNEL PARTNER ----------------


    isCPLocationLoading: boolean;
    channelPartnerLocationList: any,
    channelPartnerNameList: any[];
    channelPartnerListIsLoading: boolean;
    exportStatus: any;
    channelPartnerExist: boolean,
    isBulkDeleteCPLoading: boolean,
    isCPUpdateLoading: boolean,
    isCPAddLoading: boolean,
    isCPExistLoading: boolean,
    isCPExcelUploadedListLoading: boolean,
};

const initialState: MarketingState = {
    agencyNameList: [],
    agencyLocationList: [],
    marketingExcelColumnHeading: {},
    agencyFiltersPayload: {
        PageNumber: 1,
        PageSize: 10,
        SearchText: null,
        Location: null,
        AssociatedLeadsCount: null,
        AssociatedPospectsCount: null,
        AssociateProperty: null,
        FromDate: null,
        ToDate: null,
        DateType: null,
    },

    exportAgencyStatus: [],
    isExportStatusLoading: true,
    isBulkDeleteAgencyLoading: true,
    isExcelUploadedListLoading: true,
    isAgencyExcelUploadedListLoading: true,
    isAgencyUpdateLoading: true,
    isAgencyLocationLoading: true,
    isAgencyAddLoading: true,
    isAgencyExistLoading: true,
    isFetchAgencyLoading: true,
    excelUploadedList: [],
    agencyExist: false,


    //   --------------- CAMPAIGN ------------------
    campaignNameList: [],
    campaignListIsLoading: true,
    isBulkDeleteCampaignLoading: true,
    isCampaignExcelUploadedListLoading: true,
    exportCampaignStatus: [],
    campaignExist: false,
    isCampaignExistLoading: true,
    campaignFiltersPayload: {
        PageNumber: 1,
        PageSize: 10,
    },
    // -------------------- CHANNEL PARTNER --------------------
    isCPLocationLoading: true,
    channelPartnerNameList: [],
    channelPartnerLocationList: [],
    channelPartnerListIsLoading: true,
    channelPartnerNameFiltersPayload: {
        DateType: null,
        Date: null,
        SearchText: null,
        Location: null,
        AssociateLead: null,
        AssociateData: null,
        AssociateProperty: null,
        PageNumber: 1,
        PageSize: 10
    },
    exportStatus: [],
    isBulkDeleteCPLoading: true,
    isCPUpdateLoading: true,
    isCPAddLoading: true,
    isCPExistLoading: true,
    channelPartnerExist: false,
    isCPExcelUploadedListLoading: true,
};

export function marketingReducer(
    state: MarketingState = initialState,
    action: Action
): MarketingState {
    switch (action.type) {
        // ------------- Agency Part -------------------
        case MarketingAction.FETCH_AGENCY_NAME:
            return {
                ...state,
                isFetchAgencyLoading: true,
            };
        case MarketingAction.FETCH_AGENCY_NAME_SUCCESS:
            return {
                ...state,
                isFetchAgencyLoading: false,
                agencyNameList: (action as FetchAgencyNameSuccess).response,
            };
        case MarketingAction.FETCH_AGENCY_LOCATION:
            return {
                ...state,
                isAgencyLocationLoading: true,
            };
        case MarketingAction.FETCH_AGENCY_LOCATION_SUCCESS:
            return {
                ...state,
                isAgencyLocationLoading: false,
                agencyLocationList: (action as FetchAgencyNameSuccess).response,
            };
        case MarketingAction.UPDATE_AGENCY_FILTERS_PAYLOAD:
            return {
                ...state,
                agencyFiltersPayload: (action as UpdateAgencyFiltersPayload).payload,
            };
        case MarketingAction.MARKETING_EXCEL_UPLOAD_SUCCESS:
            return {
                ...state,
                marketingExcelColumnHeading: (action as MarketingExcelUploadSuccess).resp,
            };
        case MarketingAction.EXPORT_AGENCY_TRACKER:
            return {
                ...state,
                isExportStatusLoading: true
            };
        case MarketingAction.EXPORT_AGENCY_TRACKER_SUCCESS:
            return {
                ...state,
                exportAgencyStatus: (action as FetchAgencyExportTrackerSuccess).response,
                isExportStatusLoading: false
            };
        case MarketingAction.DELETE_AGENCY:
            return {
                ...state,
                isBulkDeleteAgencyLoading: true
            };
        case MarketingAction.DELETE_AGENCY_SUCCESS:
            return {
                ...state,
                isBulkDeleteAgencyLoading: false
            };
        case MarketingAction.FETCH_AGENCY_EXCEL_UPLOADED_LIST:
            return {
                ...state,
                isAgencyExcelUploadedListLoading: true
            };
        case MarketingAction.FETCH_AGENCY_EXCEL_UPLOADED_LIST_SUCCESS:
            return {
                ...state,
                excelUploadedList: (action as FetchAgencyExcelUploadedSuccess)
                    .response,
                isAgencyExcelUploadedListLoading: false
            };
        case MarketingAction.ADD_AGENCY_NAME:
            return {
                ...state,
                isAgencyAddLoading: true
            };
        case MarketingAction.ADD_AGENCY_NAME_SUCCESS:
            return {
                ...state,
                isAgencyAddLoading: false
            };
        case MarketingAction.UPDATE_AGENCY_NAME:
            return {
                ...state,
                isAgencyUpdateLoading: true
            };
        case MarketingAction.UPDATE_AGENCY_NAME_SUCCESS:
            return {
                ...state,
                isAgencyUpdateLoading: false
            };
        case MarketingAction.EXIST_AGENCY:
            return {
                ...state,
                isAgencyExistLoading: true,
            };
        case MarketingAction.EXIST_AGENCY_SUCCESS:
            return {
                ...state,
                agencyExist: (action as ExistAgencySuccess).response,
                isAgencyExistLoading: false,
            };
        //   ----------------- CAMPAIGN PART --------------------
        case MarketingAction.FETCH_CAMPAIGN_NAME:
            return {
                ...state,
                campaignListIsLoading: true,
            };
        case MarketingAction.FETCH_CAMPAIGN_NAME_SUCCESS:
            return {
                ...state,
                campaignNameList: (action as FetchCampaignNameSuccess).response,
                campaignListIsLoading: false,
            };
        case MarketingAction.UPDATE_CAMPAIGN_FILTERS_PAYLOAD:
            return {
                ...state,
                campaignFiltersPayload: (action as UpdateCampaignFiltersPayload).payload,
            };
        case MarketingAction.DELETE_CAMPAIGN:
            return {
                ...state,
                isBulkDeleteCampaignLoading: true
            };
        case MarketingAction.DELETE_CAMPAIGN_SUCCESS:
            return {
                ...state,
                isBulkDeleteCampaignLoading: false
            };
        case MarketingAction.FETCH_CAMPAIGN_EXCEL_UPLOADED_LIST:
            return {
                ...state,
                isCampaignExcelUploadedListLoading: true
            };
        case MarketingAction.FETCH_CAMPAIGN_EXCEL_UPLOADED_LIST_SUCCESS:
            return {
                ...state,
                excelUploadedList: (action as FetchCampaignExcelUploadedSuccess)
                    .response,
                isCampaignExcelUploadedListLoading: false
            };
        case MarketingAction.EXPORT_CAMPAIGN_TRACKER:
            return {
                ...state,
                isExportStatusLoading: true
            }
        case MarketingAction.EXPORT_CAMPAIGN_TRACKER_SUCCESS:
            return {
                ...state,
                exportCampaignStatus: (action as FetchCampaignExportTrackerSuccess).response,
                isExportStatusLoading: false
            }
        case MarketingAction.EXIST_CAMPAIGN:
            return {
                ...state,
                isCampaignExistLoading: true,
            }
        case MarketingAction.EXIST_CAMPAIGN_SUCCESS:
            return {
                ...state,
                campaignExist: (action as ExistCampaignSuccess).response,
                isCampaignExistLoading: false,
            }
        // ----------------- CHANNEL PARTNER PART -----------------------
        case MarketingAction.FETCH_CHANNEL_PARTNER_LOCATION:
            return {
                ...state,
                isCPLocationLoading: true,
            };
        case MarketingAction.FETCH_CHANNEL_PARTNER_LOCATION_SUCCESS:
            return {
                ...state,
                isCPLocationLoading: false,
                channelPartnerLocationList: (action as FetchChannelPartnerLocationsSuccess).response,
            };
        case MarketingAction.FETCH_CHANNEL_PARTNER:
            return {
                ...state,
                channelPartnerListIsLoading: true,
            };
        case MarketingAction.FETCH_CHANNEL_PARTNER_SUCCESS:
            return {
                ...state,
                channelPartnerNameList: (action as FetchChannelPartnerSuccess).response,
                channelPartnerListIsLoading: false,
            };
        case MarketingAction.UPDATE_CHANNEL_PARTNER_FILTERS_PAYLOAD:
            return {
                ...state,
                channelPartnerNameFiltersPayload: (action as UpdateChannelPartnerFiltersPayload).payload,
            };
        case MarketingAction.FETCH_CP_EXCEL_UPLOADED_LIST:
            return {
                ...state,
                isCPExcelUploadedListLoading: true
            };
        case MarketingAction.FETCH_CP_EXCEL_UPLOADED_LIST_SUCCESS:
            return {
                ...state,
                excelUploadedList: (action as FetchChannelPartnerExcelUploadedSuccess)
                    .response,
                isCPExcelUploadedListLoading: false
            };
        case MarketingAction.EXPORT_CHANNEL_PARTNER_TRACKER:
            return {
                ...state,
                isExportStatusLoading: true
            };
        case MarketingAction.EXPORT_CHANNEL_PARTNER_TRACKER_SUCCESS:
            return {
                ...state,
                exportStatus: (action as FetchChannelPartnerExportTrackerSuccess).response,
                isExportStatusLoading: false
            };
        case MarketingAction.DELETE_CHANNEL_PARTNER:
            return {
                ...state,
                isBulkDeleteCPLoading: true
            };
        case MarketingAction.DELETE_CHANNEL_PARTNER_SUCCESS:
            return {
                ...state,
                isBulkDeleteCPLoading: false
            };

        case MarketingAction.ADD_CHANNEL_PARTNER:
            return {
                ...state,
                isCPAddLoading: true
            };
        case MarketingAction.ADD_CHANNEL_PARTNER_SUCCESS:
            return {
                ...state,
                isCPAddLoading: false
            };

        case MarketingAction.UPDATE_CHANNEL_PARTNER:
            return {
                ...state,
                isCPUpdateLoading: true
            };
        case MarketingAction.UPDATE_CHANNEL_PARTNER_SUCCESS:
            return {
                ...state,
                isCPUpdateLoading: false
            };
        case MarketingAction.EXIST_CHANNEL_PARTNER:
            return {
                ...state,
                isCPExistLoading: true,
            };
        case MarketingAction.EXIST_CHANNEL_PARTNER_SUCCESS:
            return {
                ...state,
                channelPartnerExist: (action as ExistChannelPartnerSuccess).response,
                isCPExistLoading: false,
            };
        default:
            return state;
    }
}

export const selectMarketingState = (state: AppState) => state.marketing;

export const getIsFetchAgencyLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isFetchAgencyLoading
);

export const getMarketingAgencyNameList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.agencyNameList
);

export const getAgencyFiltersPayload = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.agencyFiltersPayload
);

export const getMarketingColumnHeadings = createSelector(
    selectMarketingState,
    (state: MarketingState) => {
        return state.marketingExcelColumnHeading;
    }
);

export const getAgencyExcelUploadedList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.excelUploadedList
);

export const getAgencyExcelUploadedListIsLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isAgencyExcelUploadedListLoading
);

export const getAgencyExportStatus = createSelector(
    selectMarketingState,
    (state: MarketingState) => {
        return {
            items: state.exportAgencyStatus.items,
            totalCount: state.exportAgencyStatus.totalCount
        };
    }
);

export const getAgencyExportStatusLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isExportStatusLoading
);

export const getIsBulkAgencyLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isBulkDeleteAgencyLoading
);

export const getIsAgencyUpdateLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isAgencyUpdateLoading
);

export const getIsAgencyAddLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isAgencyAddLoading
);

export const getAgencyExist = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.agencyExist
);

export const getAgencyLoationList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.agencyLocationList
);

export const getIsAgencyLocationLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isAgencyLocationLoading
);



// ----------------------- CAMPAING PART --------------

export const getCampaignNameList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.campaignNameList
);

export const getCampaignNameListIsLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.campaignListIsLoading
);

export const getCampaignFiltersPayload = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.campaignFiltersPayload
);

export const getIsBulkCampaignLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isBulkDeleteCampaignLoading
)

export const getCampaignExcelUploadedList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.excelUploadedList
);

export const getCampaignExcelUploadedListIsLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isCampaignExcelUploadedListLoading
);

export const getCampaignExportStatus = createSelector(
    selectMarketingState,
    (state: MarketingState) => {
        return {
            items: state.exportCampaignStatus.items,
            totalCount: state.exportCampaignStatus.totalCount
        }
    }
)

export const getCampaignExportStatusLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isExportStatusLoading
)

export const getCampaignExist = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.campaignExist
);
// ----------------------- CHANNEL PARTNER ---------------
export const getChannelPartnerList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.channelPartnerNameList
);

export const getChannelPartnerListIsLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.channelPartnerListIsLoading
);

export const getChannelPartnerNameFiltersPayload = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.channelPartnerNameFiltersPayload
);

export const getChannelPartnerExcelUploadedList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.excelUploadedList
);

export const getChannelPartnerExcelUploadedListIsLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isCPExcelUploadedListLoading
);

export const getCPExportStatus = createSelector(
    selectMarketingState,
    (state: MarketingState) => {
        return {
            items: state.exportStatus.items,
            totalCount: state.exportStatus.totalCount
        };
    }
);

export const getCPExportStatusLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isExportStatusLoading
);

export const getIsCPBulkDeleteLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isBulkDeleteCPLoading
);

export const getIsCPUpdateLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isCPUpdateLoading
);

export const getIsCPAddLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isCPAddLoading
);

export const getChannelPartnerExist = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.channelPartnerExist
);

export const getChannelPartnerLoationList = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.channelPartnerLocationList
);

export const getIsChannelPartnerLocationLoading = createSelector(
    selectMarketingState,
    (state: MarketingState) => state.isCPLocationLoading
);