import {
  Component,
  ElementRef,
  EventEmitter,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AnimationOptions } from 'ngx-lottie';
import { Title } from '@angular/platform-browser';

import { AppState } from 'src/app/app.reducer';
import { Profile } from 'src/app/core/interfaces/profile.interface';
import { convertUrlsToLinks, getAWSImagePath } from 'src/app/core/utils/common.util';
import {
  UpdateBannerImg,
  UpdateLogoImg,
} from 'src/app/reducers/profile/profile.actions';
import { getProfile } from 'src/app/reducers/profile/profile.reducers';
import { environment as env } from 'src/environments/environment';
import { FolderNamesS3, ProfileImageType } from 'src/app/app.enum';
import { getDeletePermissions, getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';

@Component({
  selector: 'profile-overview',
  templateUrl: './profile-overview.component.html',
})
export class ProfileOverviewComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('bannerImg', { static: false }) inputBannerImg: ElementRef;
  ProfileImageType: ProfileImageType;
  profile: Profile;
  files: any[] = [];
  profilePic: string = '';
  s3BucketUrl: string = env.s3ImageBucketURL;
  canEditProfile: boolean = false;
  canDelete: boolean = false;
  convertUrlsToLinks = convertUrlsToLinks
  building: AnimationOptions = {
    path: '../../../../assets/animations/building.json',
  };
  get formattedAddress(): string {
    const address: any = this.profile?.address;
    const addressParts = [
      address?.subLocality,
      address?.city,
      address?.state,
      address?.country,
      address?.postalCode,
    ].filter(part => part);
    return addressParts.length > 0 ? addressParts.join(', ') : '';
  }

  constructor(
    private store: Store<AppState>,
    private s3UploadService: BlobStorageService,
    public metaTitle: Title
  ) {
    this.metaTitle.setTitle('CRM | Org Profile');
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit.includes('OrgProfile')) this.canEditProfile = true;
      });

    this.store
      .select(getDeletePermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canDelete: any) => {
        if (canDelete.includes('OrgProfile')) this.canDelete = true;
      });
  }

  ngOnInit(): void {
    this.store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.profile = data;
        this.profilePic = data.bannerImgUrl
          ? getAWSImagePath(data?.bannerImgUrl || '')
          : '';
      });
  }

  onFileSelected(event: any, profileImageType: string) {
    if (event.target.files && event.target.files[0]) {
      for (let i = 0; i < event.target.files.length; i++) {
        this.files.push(event.target.files[i]);
        let reader = new FileReader();
        reader.onload = (eventOnload: any) => {
          const image = new Image();
          image.src = eventOnload.target.result;
          image.onload = (rs: any) => {
            const img_height = rs.currentTarget['height'];
            const img_width = rs.currentTarget['width'];
          };
          this.profilePic = eventOnload.target.result;
          if (profileImageType == 'BannerImage') {
            this.uploadBannerImg(this.profilePic);
            this.profilePic = this.profile.bannerImgUrl
              ? this.s3BucketUrl + this.profile.bannerImgUrl
              : '';
          }
          if (profileImageType == 'LogoImage') {
            this.uploadLogoImg(this.profilePic);
          }
        };
        reader.readAsDataURL(event.target.files[i]);
      }
    }
  }

  uploadBannerImg(bannerImage: string) {
    if (bannerImage == null || bannerImage == '') {
      if (this.profilePic) {
        this.profilePic = '';
        this.inputBannerImg.nativeElement
          ? this.inputBannerImg.nativeElement.value
          : '';
        this.store.dispatch(new UpdateBannerImg(bannerImage));
      }
    } else {
      this.s3UploadService
        .uploadImageBase64([bannerImage], FolderNamesS3.Images)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          if (response.data.length) {
            this.store.dispatch(new UpdateBannerImg(response.data?.[0]));
          }
        });
    }
  }

  uploadLogoImg(logoImg: string) {
    if (!logoImg) {
      this.store.dispatch(new UpdateBannerImg(logoImg));
    } else {
      this.s3UploadService
        .uploadImageBase64([logoImg], FolderNamesS3.Images)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          if (response.data.length) {
            this.store.dispatch(new UpdateLogoImg(response.data?.[0]));
          }
        });
    }
  }
  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
