import {
  Component,
  ElementRef,
  EventEmitter,
  HostL<PERSON>ener,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { AreaUnits } from 'src/app/core/interfaces/property-microsite.interface';
import { formatBudget, getLocationDetailsByObj } from 'src/app/core/utils/common.util';
import { FetchGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { FetchMicrositeAmenities, FetchMicrositeProject, FetchMicrositeUnit, FetchProjectUnit } from 'src/app/reducers/project/project.action';
import { getMicrositeAmentities, getMicrositeProject, getMicrositeUnit } from 'src/app/reducers/project/project.reducer';
import { FetchMicrositeUserDetails } from 'src/app/reducers/property/property.actions';
import { ProjectEnquiryFormComponent } from './project-enquiry-form/project-enquiry-form.component';

@Component({
  selector: 'project-microsite',
  templateUrl: './project-microsite.component.html',
})
export class ProjectMicrositeComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('scrollDiv') scrollDiv!: ElementRef;
  currentActive: any = 0;
  isSticky = false;
  showAllImages: boolean = false;
  projectInfo: any;
  areaSizeUnits: Array<AreaUnits> = [];
  possessionDate: string;
  amenities: any[] = [];
  tick: AnimationOptions = {
    path: 'assets/animations/circle-white-tick.json',
  };
  formatBudget = formatBudget;
  getLocationDetailsByObj = getLocationDetailsByObj;
  projectAmentities: any;
  projectUnit: any;
  sortedUniqueNoOfBHK: number[] = [];
  globalSettingsDetails: any;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private activatedRoute: ActivatedRoute,
    private store: Store<AppState>,
    public metaTitle: Title) {
    this.metaTitle.setTitle('CRM | Project Microsite');
  }

  async ngOnInit() {
    this.activatedRoute.params.subscribe((params: any) => {
      if (params && params.serialNo) {
        this.store.dispatch(new FetchMicrositeProject(params.serialNo));
        this.store.dispatch(new FetchMicrositeUnit(params.serialNo));
        this.store.dispatch(new FetchMicrositeAmenities(params.serialNo));
        this.store.dispatch(new FetchProjectUnit(params.serialNo));
        this.store.dispatch(new FetchGlobalSettingsAnonymous());
      }
    });

    this.store.dispatch(new FetchAreaUnitList());
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
      });

    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this.store
      .select(getMicrositeUnit)
      .pipe(takeUntil(this.stopper))
      .subscribe((unitData: any) => {

        if (Object.values(unitData || {}).length) {
          this.projectUnit = unitData;
          this.projectUnit = Object.assign({}, this.projectUnit);
        }
      });

    this.store
      .select(getMicrositeAmentities)
      .pipe(takeUntil(this.stopper))
      .subscribe((amentitiesData: any) => {

        if (Object.values(amentitiesData || {}).length) {
          this.projectAmentities = amentitiesData;
          this.projectAmentities = Object.assign({}, this.projectAmentities);
        }
      });

    this.store
      .select(getMicrositeProject)
      .pipe(takeUntil(this.stopper))
      .subscribe((projectData: any) => {

        if (Object.values(projectData || {}).length) {
          this.projectInfo = projectData;
          this.projectInfo = Object.assign({}, this.projectInfo);
        }
      });

    if (location.href.split('/').length > 1) {
      const router = location.href.split('/');
      const userName = router[router.length - 2];
      this.store.dispatch(new FetchMicrositeUserDetails(userName));
    }
  };


  showEnquire() {
    this.modalService.show(ProjectEnquiryFormComponent, {
      initialState: {
        projectUnit: this.projectUnit,
        isShowCloseBtn: true,
        projectInfo: this.projectInfo
      },
      class:
        window.innerWidth > 480
          ? 'modal-450 modal-dialog-centered'
          : 'down-modal modal-unset',
    });
  }

  @HostListener('document:scroll', ['$event'])
  onScroll(event: Event) {
    const element = this.scrollDiv.nativeElement as HTMLElement;
    this.isSticky = element.scrollTop > 50;
  }

  scrollTo(section: string, index: number) {
    this.currentActive = index;
    let el = document.getElementById(section) as HTMLElement;

    el.scrollIntoView({ behavior: 'smooth' });
  }

  updateSortedUniqueNoOfBHK(sortedUniqueNoOfBHK: number[]) {
    this.sortedUniqueNoOfBHK = sortedUniqueNoOfBHK;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
