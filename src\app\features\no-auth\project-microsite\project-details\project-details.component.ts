import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { takeUntil } from 'rxjs';
import { Facing, ProjectStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, getAreaUnit, getBHKDisplayString, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'project-details',
  templateUrl: './project-details.component.html',
})
export class ProjectDetailsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  @Input() projectInfo: any;
  @Input() projectUnit: any;
  @Input() areaSizeUnits: any;
  getAreaUnit = getAreaUnit;
  Facing = Facing;
  ProjectStatus = ProjectStatus;
  sortedUniqueNoOfBHK: number[] = [];

  getTimeZoneDate = getTimeZoneDate;
  // currentDate = moment(new Date().toISOString());
  getBHKDisplayString = getBHKDisplayString;
  moment = moment;
  userData: any;
  currentDate: Date = new Date();

  constructor(private _store: Store<AppState>) { }

  ngOnInit(): void {
    this.generateUniqueNoOfBHK();

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });
  }

  generateUniqueNoOfBHK() {
    // without Rk generate unique no of bhk
    const uniqueNoOfBHK = new Set<number>();

    this.projectUnit?.unitTypes.forEach((unit: { noOfBHK: number; }) => {
      if ((unit.noOfBHK !== 0 && unit.noOfBHK !== 0.5)) {
        uniqueNoOfBHK.add(unit.noOfBHK);
      }
    });
    this.sortedUniqueNoOfBHK = Array.from(uniqueNoOfBHK).sort((a, b) => a - b);
  }

  facingList(data: any) {
    return data
      .filter((index: number) => index !== 0)
      .map((index: number) => Facing[index])
      .join(', ');
  }

}