.standard-table {
    &.table {
        @extend .mb-0;

        thead {
            border: 1px solid $dark-400;
            background-color: $primary-black;
            @extend .fv-sm-caps;

            th {
                @extend .border-top-0;
                @extend .border-bottom-0;
                @extend .px-16;
                @extend .py-0;
                height: 30px;
                vertical-align: middle;
                @extend .text-normal;
                font-weight: 400;
                // background-color: $blue-30;
                // @extend .position-sticky;
                top: 0;
                box-shadow: inset 0 -1px 0 $slate-100;
                z-index: 1;
                color: $slate-40;

                &:not(:first-child) {
                    border-left: 1px solid $dark-400;
                }
            }
        }

        td {
            vertical-align: middle;
            @extend .px-16, .text-sm, .fw-semi-bold;
            @extend .border-top-0;
            background-color: $white;

            &:not(:first-child) {
                border-left: 1px solid $dark-400;
                // border-right: 1px solid $dark-400;
                // border-bottom:1px solid $dark-400;
                color: $primary-black;
            }
        }

        tbody tr {
            height: 48px;

            &:hover {
                @extend .bg-pearl;
                transition: 0.2s;
            }
        }

        &.no-hover {
            tbody tr {
                &:hover {
                    background: none !important;
                }
            }
        }

        &.cell-padding-sm {

            thead th,
            tbody td {
                @extend .py-4;
                @extend .px-8;
            }
        }

        &.cell-padding-0 {

            thead th,
            tbody td {
                @extend .p-0;
            }
        }

        &.modal-table {
            thead th {
                top: 110px;
            }
        }

        > :not(:first-child) {
            border-top: none !important;
        }

        &.no-vertical-border {
            tbody {
                tr {
                    td {
                        &:not(:first-child) {
                            border-left: 0;
                        }
                    }
                }
            }
        }
    }

    &.two-line-table {
        tbody tr {
            height: 64px;
        }
    }

    &.three-line-table {
        tbody tr {
            height: 84px;
        }
    }

    &.non-sticky-header {
        &.table {
            thead th {
                top: auto;
            }
        }
    }

    &.rounded-table {
        @extend .rounded;

        thead tr {
            @extend .rounded-top;

            th {
                &:first-of-type {
                    border-top-left-radius: 3px;
                }

                &:last-of-type {
                    border-top-right-radius: 3px;
                }
            }
        }

        tbody {
            tr {
                &:last-child {
                    @extend .rounded-bottom;

                    td {
                        &:first-child {
                            border-bottom-left-radius: 3px;
                        }

                        &:last-child {
                            border-bottom-right-radius: 3px;
                        }
                    }
                }
            }
        }
    }
}

// for me/leave/summary tables scroll
div.table-responsive {
    @extend .border-bottom;
    @extend .border-top;

    .table {
        @extend .border-bottom-0;
        @extend .border-top-0;
    }
}

.table> :not(caption)>*>* {
    background-color: unset !important;
    padding: 0px !important;
    // margin-bottom: 12px !important;
}