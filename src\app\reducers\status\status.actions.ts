import { Action } from '@ngrx/store';

export enum StatusActionTypes {
    FETCH_CUSTOM_STATUS = '[STATUS] Fetch Custom Status',
    FETCH_CUSTOM_STATUS_SUCCESS = '[STATUS] Fetch Custom Status Success',
}
export class FetchCustomStatus implements Action {
    readonly type: string = StatusActionTypes.FETCH_CUSTOM_STATUS;
    constructor() { }
}
export class FetchCustomStatusSuccess implements Action {
    readonly type: string = StatusActionTypes.FETCH_CUSTOM_STATUS_SUCCESS;
    constructor(public response: any = {}) { }
}
