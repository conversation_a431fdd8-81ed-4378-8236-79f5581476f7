import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AutomationActionTypes,
  FetchIntegrationAssignment,
  FetchIntegrationAssignmentSuccess,
  FetchPriorityList,
  FetchPriorityListSuccess,
  FetchUserAssignment,
  FetchUserAssignmentByEntity,
  FetchUserAssignmentByEntitySuccess,
  FetchUserAssignmentById,
  FetchUserAssignmentByIdSuccess,
  FetchUserAssignmentSuccess,
  UpdateMultiUserAssignment,
  UpdateUserAssignment,
  updateIntegrationAssignment,
  updatePriorityList,
} from 'src/app/reducers/automation/automation.actions';
import { FetchProjectList } from 'src/app/reducers/project/project.action';
import {
  FetchCityList,
  FetchLocationList,
  FetchZoneList,
} from 'src/app/reducers/site/site.actions';
import { AutomationService } from 'src/app/services/controllers/automation.service';
import { FetchAllReferenceIds } from '../reference-id-management/reference-id-management.action';

@Injectable()
export class AutomationEffects {
  getPriorityList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AutomationActionTypes.FETCH_PRIORITY_LIST),
      map((action: FetchPriorityList) => action),
      switchMap(() => {
        return this.automationService.getPriorityList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPriorityListSuccess(resp);
            }
            return new FetchPriorityListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updatePriorityList$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AutomationActionTypes.UPDATE_PRIORITY_LIST),
        map((action: updatePriorityList) => action.payload),
        switchMap((data: any) => {
          return this.automationService.updatePriorityList(data).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success(
                  'Priorities updated successfully'
                );
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  getUserAssignment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AutomationActionTypes.FETCH_USER_ASSIGNMENT),
      map((action: FetchUserAssignment) => action),
      switchMap(() => {
        return this.automationService.getUserAssignment().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserAssignmentSuccess(resp);
            }
            return new FetchUserAssignmentSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateUserAssignment$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AutomationActionTypes.UPDATE_USER_ASSIGNMENT),
        map((action: UpdateUserAssignment) => action),
        switchMap((data: any) => {
          const apiCall = data?.isMultiple
            ? this.automationService.updateMultiUserAssignment(
              data?.payload,
              data.moduleName
            )
            : this.automationService.updateUserAssignment(
              data?.payload,
              data.moduleName
            );
          return apiCall.pipe(
            map((resp: any) => {
              if (resp.succeeded && resp.data) {
                this._notificationService.success(resp?.message);
                if (data.moduleName == 'Project') {
                  this.store.dispatch(new FetchProjectList(null, true));
                } else if (data.moduleName == 'Location') {
                  this.store.dispatch(new FetchLocationList());
                } else if (data.moduleName == 'Zone') {
                  this.store.dispatch(new FetchZoneList());
                } else if (data.moduleName == 'City') {
                  this.store.dispatch(new FetchCityList());
                } else if (data.moduleName == 'Reference') {
                  this.store.dispatch(new FetchAllReferenceIds());
                }
              } else {
                this._notificationService.error(resp?.message);
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  updateMultiUserAssignment$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AutomationActionTypes.UPDATE_MULTI_USER_ASSIGNMENT),
        map((action: UpdateMultiUserAssignment) => action),
        switchMap((data: any) => {
          return this.automationService
            .updateMultiUserAssignment(data.payload, data.moduleName)
            .pipe(
              map((resp: any) => {
                if (resp.succeeded) {
                  this._notificationService.success(resp?.message);
                  if (data.moduleName == 'Location') {
                    this.store.dispatch(new FetchLocationList());
                  } else if (data.moduleName == 'Zone') {
                    this.store.dispatch(new FetchZoneList());
                  } else if (data.moduleName == 'City') {
                    this.store.dispatch(new FetchCityList());
                  } else if (data.moduleName == 'Project') {
                    this.store.dispatch(new FetchProjectList(null, true));
                  } else if (data.moduleName == 'Reference') {
                    this.store.dispatch(new FetchAllReferenceIds());
                  }
                }
              }),
              catchError((err) => of(new OnError(err)))
            );
        })
      ),
    { dispatch: false }
  );

  getUserAssignmentById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ID),
      map((action: FetchUserAssignmentById) => action.id),
      switchMap((id) => {
        return this.automationService.getUserAssignmentById(id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserAssignmentByIdSuccess(resp);
            }
            return new FetchUserAssignmentByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUserAssignmentByEntity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ENTITY),
      map((action: FetchUserAssignmentByEntity) => action.entityId),
      switchMap((entityId) => {
        return this.automationService.getUserAssignmentByEntity(entityId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUserAssignmentByEntitySuccess(resp);
            }
            return new FetchUserAssignmentByEntitySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getIntegrationAssignment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AutomationActionTypes.FETCH_INTEGRATION_ASSIGNMENT),
      map((action: FetchIntegrationAssignment) => action.queryParams),
      switchMap((data) => {
        return this.automationService.getIntegrationAssignment(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchIntegrationAssignmentSuccess(resp);
            }
            return new FetchIntegrationAssignmentSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateIntegrationAssignment$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AutomationActionTypes.UPDATE_INTEGRATION_ASSIGNMENT),
        map((action: updateIntegrationAssignment) => action),
        switchMap((data: any) => {
          const apiCall = data?.isMultiple
            ? this.automationService.updateMultipleIntegrationAssignment(
              data?.payload
            )
            : this.automationService.updateIntegrationAssignment(data?.payload);
          return apiCall.pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success('Updated successfully');
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  updateMultiProject$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AutomationActionTypes.UPDATE_PROJECT_ASSIGNMENT),
        map((action: updateIntegrationAssignment) => action),
        switchMap((data: any) => {
          return this.automationService.updateMultiProject(data?.payload).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success('Updated successfully');
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  updateMultiLocation$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AutomationActionTypes.UPDATE_LOCATION_ASSIGNMENT),
        map((action: updateIntegrationAssignment) => action),
        switchMap((data: any) => {
          return this.automationService.updateMultiLocation(data?.payload).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success('Updated successfully');
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  updateMultiCountryCode$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(AutomationActionTypes.UPDATE_COUNTRY_CODE_ASSIGNMENT),
        map((action: updateIntegrationAssignment) => action),
        switchMap((data: any) => {
          return this.automationService
            .updateMultiCountryCode(data?.payload)
            .pipe(
              map((resp: any) => {
                if (resp.succeeded) {
                  this._notificationService.success('Updated successfully');
                }
              }),
              catchError((err) => of(new OnError(err)))
            );
        })
      ),
    { dispatch: false }
  );

  constructor(
    private actions$: Actions,
    private automationService: AutomationService,
    private store: Store<AppState>,
    private _notificationService: NotificationsService
  ) { }
}
