import { Action } from '@ngrx/store';

export enum TagsActionTypes {
  FETCH_ICONS_LIST = "[TAGS] Fetch Icons List",
  FETCH_ICONS_LIST_SUCCESS = "[TAGS] Fetch Icons List Success",

  FETCH_TAGS_LIST = "[TAGS] Fetch Tags List",
  FETCH_TAGS_LIST_SUCCESS = "[TAGS] Fetch Tags List Success",

  FETCH_TAGS_LIST_WITH_COUNT = "[TAGS] Fetch Tags List With Count",
  FETCH_TAGS_LIST_WITH_COUNT_SUCCESS = "[TAGS] Fetch Tags List With Count Success",

  CREATE_TAG = '[TAGS] Create Tag',
  CREATE_TAG_SUCCESS = '[TAGS] Create Tag Success',

  UPDATE_TAGS_LIST = "[TAGS] Update Tags List",
  UPDATE_TAGS_LIST_SUCCESS = "[TAGS] Updatre Tags List Success",

  DELETE_TAG_BY_ID = '[TAGS] Delete Tag By ID',
  DELETE_TAG_BY_ID_SUCCESS = '[TAGS] Delete Tag By ID Success',

  FETCH_TAGS_LIST_BY_MODULE = "[TAGS] Fetch Tags List By Module",
  FETCH_TAGS_LIST_BY_MODULE_SUCCESS = "[TAGS] Fetch Tags List By Module Success",

  UPDATE_TAG ="[TAGS] Update Tag",
  UPDATE_TAG_SUCCESS = "[TAGS] Update Tag Success" 
}

export class FetchIconsList implements Action {
  readonly type: string = TagsActionTypes.FETCH_ICONS_LIST;
  constructor() {
    
  }
}
export class FetchIconsListSuccess implements Action {
  readonly type: string = TagsActionTypes.FETCH_ICONS_LIST_SUCCESS;
  constructor(public response: any = {}) {}
}

export class FetchTagsList implements Action {
  readonly type: string = TagsActionTypes.FETCH_TAGS_LIST;
  constructor() {
    
  }
}
export class FetchTagsListSuccess implements Action {
  readonly type: string = TagsActionTypes.FETCH_TAGS_LIST_SUCCESS;
  constructor(public response: any = {}) {}
}

export class FetchTagsListWithCount implements Action {
  readonly type: string = TagsActionTypes.FETCH_TAGS_LIST_WITH_COUNT;
  constructor() {}
}

export class FetchTagsListWithCountSuccess implements Action {
  readonly type: string = TagsActionTypes.FETCH_TAGS_LIST_WITH_COUNT_SUCCESS;
  constructor(public response: any = {}) {}
}

export class CreateTag implements Action {
  readonly type: string = TagsActionTypes.CREATE_TAG;
  constructor(public payload: any) { }
}

export class CreateTagSuccess implements Action {
  readonly type: string = TagsActionTypes.CREATE_TAG_SUCCESS;
  constructor(public response: any = {}) {}
}

export class UpdateTagsLists implements Action {
  readonly type: string = TagsActionTypes.UPDATE_TAGS_LIST;
  constructor(public payload: any) { }
}

export class UpdateTagsListSuccess implements Action {
  readonly type: string = TagsActionTypes.UPDATE_TAGS_LIST_SUCCESS;
  constructor(public response: any = {}) {}
}

export class DeleteTagById implements Action {
  readonly type: string = TagsActionTypes.DELETE_TAG_BY_ID;
  constructor(public id: string) { }
}

export class DeleteTagByIdSuccess implements Action {
  readonly type: string = TagsActionTypes.DELETE_TAG_BY_ID_SUCCESS;
  constructor(public response: any = {}) {}
}

export class FetchTagsListByModule implements Action {
  readonly type: string = TagsActionTypes.FETCH_TAGS_LIST_BY_MODULE;
  constructor(public module: string) {
    
  }
}
export class FetchTagsListByModuleSuccess implements Action {
  readonly type: string = TagsActionTypes.FETCH_TAGS_LIST_BY_MODULE_SUCCESS;
  constructor(public response: any = {}) {}
}

export class UpdateTag implements Action {
  readonly type: string = TagsActionTypes.UPDATE_TAG;
  constructor(public payload: any) { }
}

export class UpdateTagSuccess implements Action {
  readonly type: string = TagsActionTypes.UPDATE_TAG_SUCCESS;
  constructor(public response: any = {}) { }
}
