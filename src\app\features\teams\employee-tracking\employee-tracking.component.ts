import { Component, EventEmitter, OnInit } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import * as moment from 'moment';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { Store } from '@ngrx/store';
import { getTimeZoneDate } from 'src/app/core/utils/common.util';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'employee-tracking',
  templateUrl: './employee-tracking.component.html',
})
export class EmployeeTrackingComponent implements OnInit {
  userData: any;
  s3BucketUrl: string = env.s3ImageBucketURL;
  clockData: any;
  moment = moment;
  markers: any[] = [];
  center = {
    lat: 12.9106262,
    lng: 77.6405173,
  };
  getTimeZoneDate = getTimeZoneDate
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>

  ) { }

  ngOnInit(): void {
    this.headerTitle.setTitle('Employee Tracking');
    this._store
    .select(getUserBasicDetails)
    .pipe(takeUntil(this.stopper))
    .subscribe((data: any) => {
      this.userData = data;
    });
  }


}
