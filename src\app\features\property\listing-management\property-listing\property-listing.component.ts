import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getLocationDetailsByObj, validateAllFormFields } from 'src/app/core/utils/common.util';
import {
  AddToList,
  DeList
} from 'src/app/reducers/listing-site/listing-site.actions';
import { getListingSources } from 'src/app/reducers/listing-site/listing-site.reducer';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';

@Component({
  selector: 'property-listing',
  templateUrl: './property-listing.component.html',
})
export class PropertyListingComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  data: any[];
  bulkOperation: boolean;
  listForm: FormGroup;
  gridApi: GridApi;
  listingSource: any[];
  action: any;
  noDataFound: AnimationOptions = {
    path: 'assets/animations/empty-notes.json',
  };
  allUsers: any;
  getAssignedToDetails = getAssignedToDetails;
  id: any[];
  selectedListing: any;
  getLocationDetailsByObj = getLocationDetailsByObj

  constructor(
    private _store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.listForm = this.fb.group({
      portal: [null, [Validators.required]],
    });
    if (this.bulkOperation) {
      this.data = this.gridApi?.getSelectedNodes()?.map((prop: any) => prop?.data);
    }

    this.id = this.data?.map((item: any) => item?.assignTo?.map((item: any) => item?.id))
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const sortedUsers = data?.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
      });

    this._store
      .select(getListingSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listingSource = [...data];
      });
  }

  getUser(assignedTo: any[] | undefined): string {
    if (!assignedTo || assignedTo.length === 0) {
      return '--';
    }

    return assignedTo
      .map(userId => this.getAssignedToDetails(userId, this.allUsers, true))
      .filter(Boolean)
      .join(', ');
  }

  getLicense(assignedTo: any[] | undefined): string | string[] {
    if (!assignedTo || assignedTo.length === 0) {
      return 'Required';
    }

    const licenses = assignedTo?.map(userId => {
      const user = this.allUsers.find((user: any) => user.id === userId);
      return user ? user?.licenseNo : null;
    })
      .filter((license): license is string => license !== null);

    return licenses.length ? licenses : 'Required';
  }

  getListingSources(sources: any[]): string {
    if (!sources || sources.length === 0) {
      return '--';
    }
    return sources.map(source => source.displayName).join(', ');
  }

  deselectProperty(id: string) {
    this.data = [];
    this.gridApi?.getSelectedNodes().forEach((node: any) => {
      if (node.data?.id === id) {
        node.setSelected(false);
        if (!this.gridApi?.getSelectedNodes()?.length) {
          this.modalRef.hide();
        }
        return;
      }
      this.data = [...this.data, node.data];
    });
  }

  onPublish() {
    if (!this.listForm.valid) {
      validateAllFormFields(this.listForm);
      return;
    }

    if (!this.data?.length) {
      return;
    }
    const propertiesWithoutData = this.data.filter((item: any) => {
      const license = this.getLicense(item?.assignedTo);
      const hasValidAddress = item?.listingSourceAddresses && item?.listingSourceAddresses.length > 0;

      if (item?.uaeEmirate !== 3) {
        const hasValidLicense = license !== 'Required';
        const hasValidPermit = item?.dldPermitNumber || item?.dtcmPermit;
        return !(hasValidLicense && hasValidPermit && hasValidAddress);
      } else {
        return !hasValidAddress;
      }
    });

    const withoutDataCount = propertiesWithoutData.length;
    const withDataCount = this.data?.length - withoutDataCount;
    if (withDataCount === this.data?.length) {
      this.publishProperties(this.data);
    } else if (withoutDataCount === this.data?.length) {
      const message = `Property(s) does not have a permit number/broker number/address and can't be published.`;
      this.showWarningModal(message, true);
    } else {
      const message = `Some of the properties do not have adequate data to post. Do you want to publish the remaining ${withDataCount} properties?`;
      this.showWarningModal(message, false);
    }
  }

  publishProperties(validPropertyIds: number[]): void {
    const portalValue = this.listForm.get('portal')?.value;
    const ids = validPropertyIds?.map((item: any) => item?.id)
    if (this.action === 'list') {
      this._store.dispatch(new AddToList({ ids: ids, listingSourceIds: portalValue }));
    } else {
      this._store.dispatch(new DeList({ ids: ids, listingSourceIds: portalValue }));
    }
    this.closeModal();
    this.listForm.reset();
  }

  showWarningModal(message: string, allPropertiesWithoutPermits: boolean) {
    const initialState: any = {
      type: 'ListingManagement',
      data: {
        fieldType: 'Warning',
        heading: `Incomplete Data`,
        allPropertiesWithoutPermits: allPropertiesWithoutPermits,
        message: message,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };

    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign({}, { class: 'modal-400 top-modal ph-modal-unset', initialState })
    );

    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason === 'confirmed') {
          const validItems = this.data.filter((item: any) => {
            const license = this.getLicense(item?.assignedTo);
            const hasValidAddress = item?.listingSourceAddresses && item?.listingSourceAddresses.length > 0;

            if (item?.uaeEmirate !== 3) {
              const hasValidLicense = license !== 'Required';
              const hasValidPermit = item?.dldPermitNumber || item?.dtcmPermit;
              return hasValidLicense && hasValidPermit && hasValidAddress;
            } else {
              return hasValidAddress;
            }
          });

          const ids = validItems.map((item: any) => item?.id);
          const portalValue = this.listForm.get('portal')?.value;

          if (this.action === 'list') {
            this._store.dispatch(new AddToList({ ids, listingSourceIds: portalValue }));
          } else {
            this._store.dispatch(new DeList({ ids, listingSourceIds: portalValue }));
          }

          this.closeModal();
          this.listForm.reset();
        }
      });
    }
  }

  openAddPortal(listing: any, addPortal: any) {
    this.selectedListing = listing;
    this.listForm.reset();
    if (this.selectedListing?.shouldVisisbleOnListing) {
      this.listForm.patchValue({
        portal: this.selectedListing.listingSources?.map((source: any) => source.id)
      })
    }
    this.modalRef = this.modalService.show(addPortal, {
      class: 'up-modal modal-350',
      ignoreBackdropClick: false,
    });
  }


  onPortalUpdate() {
    if (!this.listForm.valid) {
      validateAllFormFields(this.listForm);
      return;
    }
    const portalValue = this.listForm.get('portal').value;

    if (portalValue) {
      let payload = { ids: [this.selectedListing?.id], listingSourceIds: portalValue }
      this._store.dispatch(new AddToList(payload));
      this.data = this.data?.map((item: any) => {
        if (this.selectedListing.id === item.id) {
          item = {
            ...item,
            listingSources: this.listingSource.filter((source: any) => portalValue.includes(source.id))
          }
        }
        return item;
      })
    };
    this.modalRef.hide();
    this.listForm.reset();
  }

  closeModal() {
    this.modalService.hide();
  }
}
