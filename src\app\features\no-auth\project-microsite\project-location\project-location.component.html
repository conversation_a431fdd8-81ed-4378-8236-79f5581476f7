<div class="justify-between ip-flex-col w-100 mb-40">
    <div class="w-60pr ip-w-100">
        <div class="position-relative ip-mr-40">
            <img src="../../../../assets/images/trapezoid-shape.svg">
            <div class="border position-absolute top-20 left-20 bottom-20 responsive-map w-100">
                <google-map [center]="{lat: center?.lat, lng: center?.lng}">
                    <map-marker #mapMarker="mapMarker" (mapClick)="openInfoWindow(mapMarker)"
                        *ngFor="let marker of markers" [position]="{lat:marker?.latitude, lng:marker?.longitude}"
                        [label]="marker?.label">
                    </map-marker>
                    <map-info-window>{{selectedAddress}}</map-info-window>
                </google-map>
            </div>
        </div>
    </div>
    <div class="w-33 text-black-200 ip-w-100 ip-mt-10">
        <div class="mx-10 ip-mx-0">
            <h4 class="fw-semi-bold">{{getLocationDetailsByObj(projectInfo?.address)}}</h4>

            <h6 class="mt-10">{{projectInfo?.description}}</h6>
        </div>
    </div>
</div>