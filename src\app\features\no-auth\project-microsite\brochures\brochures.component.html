<div [ngClass]="isUnit ? '' : 'bg-white bg-side-dot-pattern mt-20'" class="brtl-10 brbl-10 pl-16 py-12">
    <h4 class="fw-600 text-black-200 text-decoration-underline">{{isUnit ? '' : 'Brochure'}}</h4>
    <drag-scroll class="d-flex mt-16 scrollbar scroll-hide position-relative">
        <ng-container
            *ngFor="let brochure of isUnit ? projectInfo?.unitInfoGalleries : projectInfo?.brochures; let i = index">
            <div>
                <div class="w-120 mr-6">
                    <a [href]="isUnit ? s3BucketUrl+brochure?.imageFilePath : brochure?.url" target="_blank">
                        <img src="../../../../assets/images/pdf.svg" alt=""
                            class="obj-cover w-100 h-140 cursor-pointer br-4"></a>
                    <div class="p-10 flex-between bg-white brbr-6 brbl-6"
                        (click)="downloadPdf(isUnit ? s3BucketUrl + brochure?.imageFilePath : brochure?.url)">
                        <div class="text-truncate-1 break-all fw-semi-bold">{{brochure?.name}}</div>
                        <span class="dot dot-md bg-accent-green">
                            <div title="download" class="icon ic-xxxs ic-down-to-line cursor-pointer"
                                id="clkDownloadPdf" data-automate-id="clkDownloadPdf"></div>
                        </span>
                    </div>
                </div>
            </div>
        </ng-container>
    </drag-scroll>
</div>


<!-- <div class="brtl-10 brbl-10 bg-white pl-16 py-12 bg-side-dot-pattern mt-20" *ngIf="projectInfo?.brochures?.length">
    <h4 class="fw-600 text-black-200 text-decoration-underline">Brochure</h4>
    <drag-scroll class="scrollbar scroll-hide mt-10">
        <div class="d-flex unitArea">
            <ng-container *ngFor="let brochure of projectInfo?.brochures; let i = index; let last = last">
                <h6 class="activation" [ngClass]="{'active': selectedUrl === i}" (click)="selectName(i)">
                    {{ brochure?.name }}
                </h6>
                <div *ngIf="!last" class="border-left mx-16 h-20px"></div>
            </ng-container>
        </div>
    </drag-scroll>
    <drag-scroll class="mt-16 scrollbar scroll-hide position-relative">
            <ng-container *ngFor="let brochure of projectInfo?.brochures; let i = index">
                <div class="w-100 h-140 scrollbar" *ngIf="selectedUrl === i">
                    <embed [src]="sanitizeUrl(brochure?.url)" type="application/pdf"  class="w-100"/>
                </div>
            </ng-container>
    </drag-scroll>
    <div class="flex-between mr-16 mt-16">
        <div class="fw-semi-bold text-sm text-truncate-1">
            <ng-container *ngFor="let brochure of projectInfo.brochures">
            </ng-container>{{getLocationDetailsByObj(projectInfo?.address)}}
        </div>
        <a *ngIf="selectedUrl !== null" [href]="projectInfo.brochures[selectedUrl].url" target="_blank">
            <button class="btn btn-sm btn-linear-green text-white mr-16 text-nowrap">
                <span class="icon ic-xxs mr-10 ic-down-to-line"></span>Download Brochure
            </button>
        </a>
    </div>
</div> -->