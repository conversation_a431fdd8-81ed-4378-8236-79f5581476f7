import { Component } from '@angular/core';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { FetchAllExistingPermission } from 'src/app/reducers/teams/teams.actions';

@Component({
  selector: 'roles-permission',
  template: `<router-outlet></router-outlet>`,
})
export class TeamsComponent  {
  constructor(private store: Store<AppState>) {
    this.store.dispatch(new FetchAllExistingPermission());
  }

}
