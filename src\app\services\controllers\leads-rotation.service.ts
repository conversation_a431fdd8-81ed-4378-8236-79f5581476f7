import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class LeadsRotationService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'leadrotation';
  }

  createLeadRotationGroup(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/create/group`, payload);
  }
  getLeadRotation(){
    return this.http.get(`${this.serviceBaseUrl}`);
  }
  deleteLeadRotation(id:string){
    return this.http.delete(`${this.serviceBaseUrl}/delete/group?id=${id}`);
  }
  updateLeadRotation(payload:any){
    return this.http.put(`${this.serviceBaseUrl}?id=${payload.id}`,payload);
  }
}
