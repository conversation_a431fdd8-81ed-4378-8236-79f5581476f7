import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class ForgotPasswordService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    let identityUrl = `${env.identityURL}api/`;
    this.serviceBaseUrl = `${identityUrl}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'users';
  }

  verifyUserName(userName: string) {
    return this.http.get(`${this.serviceBaseUrl}/username-verify/${userName}`);
  }

  generateOtp(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/forgot-password`, payload);
  }

  otpVerification(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/otp-verification`, payload);
  }

  resetPassword(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/reset-password`, payload);
  }

  changePassword(payload: any, userId: string) {
    return this.http.post(`${this.serviceBaseUrl}/change-password/${userId}`, payload);
  }
}
