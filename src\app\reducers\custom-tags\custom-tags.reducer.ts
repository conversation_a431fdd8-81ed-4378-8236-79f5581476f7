import { Action, createSelector } from '@ngrx/store';
import { TagsActionTypes, FetchTagsListSuccess, CreateTagSuccess, UpdateTagsListSuccess, DeleteTagByIdSuccess, FetchIconsListSuccess, UpdateTagSuccess, FetchTagsListWithCountSuccess } from './custom-tags.actions';
import { AppState } from 'src/app/app.reducer';
import { Icon } from 'src/app/core/interfaces/common.interface';

export type CustomTagState = {
    lastModifiedOn: string;
    createdOn: string;
    createdBy: string;
    lastModifiedBy: string;
    id: string;
    name: string;
    activeImagePath: string | null;
    inactiveImagePath: string | null;
    activeBackgroundColor: string | null;
    inactiveBackgroundColor: string | null;
    moduleName: number;
    tagsList: CustomTagState[];
    tagsListWithCount: CustomTagState[];
    isCustomTagsLoading: boolean;
    isCustomTagsWithCountLoading: boolean;
    iconsList: Icon[];
    isIconsLoading: boolean;
};

const initialState: CustomTagState = {
    lastModifiedOn: '',
    createdOn: '',
    createdBy: '',
    lastModifiedBy: '',
    id: '',
    name: '',
    activeImagePath: null,
    inactiveImagePath: null,
    activeBackgroundColor: null,
    inactiveBackgroundColor: null,
    moduleName: 0,
    tagsList: [],
    tagsListWithCount: [],
    isCustomTagsLoading: true,
    isCustomTagsWithCountLoading: true,
    iconsList: [],
    isIconsLoading: true,
};

export function customTagsReducer(state: CustomTagState = initialState, action: Action): CustomTagState {
    switch (action.type) {
        case TagsActionTypes.FETCH_ICONS_LIST:
            return {
                ...state,
                isIconsLoading: true
            };
        case TagsActionTypes.FETCH_ICONS_LIST_SUCCESS:
            return {
                ...state,
                iconsList: (action as FetchIconsListSuccess).response,
                isIconsLoading: false
            };
        case TagsActionTypes.FETCH_TAGS_LIST:
            return {
                ...state,
                isCustomTagsLoading: true
            };
        case TagsActionTypes.FETCH_TAGS_LIST_SUCCESS:
            return {
                ...state,
                tagsList: (action as FetchTagsListSuccess).response,
                isCustomTagsLoading: false
            };
        case TagsActionTypes.FETCH_TAGS_LIST_WITH_COUNT:
            return {
                ...state,
                isCustomTagsWithCountLoading: true
            };
        case TagsActionTypes.FETCH_TAGS_LIST_WITH_COUNT_SUCCESS:
            return {
                ...state,
                tagsListWithCount: (action as FetchTagsListWithCountSuccess).response?.items,
                isCustomTagsWithCountLoading: false
            };
        case TagsActionTypes.CREATE_TAG_SUCCESS:
            return {
                ...state,
                tagsList: [...state?.tagsList, (action as CreateTagSuccess).response],
                tagsListWithCount: [...state?.tagsListWithCount, (action as CreateTagSuccess).response]
            };
        case TagsActionTypes.UPDATE_TAGS_LIST_SUCCESS:
            return {
                ...state,
                tagsList: [...state?.tagsList]?.map((tag: any) => {
                    if (tag?.id == (action as UpdateTagsListSuccess).response?.id)
                        return (action as UpdateTagsListSuccess).response;
                    return tag;
                }),
                tagsListWithCount: [...state?.tagsListWithCount]?.map((tag: any) => {
                    if (tag?.id == (action as UpdateTagsListSuccess).response?.id)
                        return (action as UpdateTagsListSuccess).response;
                    return tag;
                }),
            };
        case TagsActionTypes.DELETE_TAG_BY_ID_SUCCESS:
            return {
                ...state,
                tagsList: [...state?.tagsList]?.filter((tag) => tag?.id !== (action as DeleteTagByIdSuccess).response),
                tagsListWithCount: [...state?.tagsListWithCount]?.filter((tag) => tag?.id !== (action as DeleteTagByIdSuccess).response)
            };
        case TagsActionTypes.UPDATE_TAG_SUCCESS:
            return {
                ...state,
                tagsList: state.tagsList.map(tag =>
                    tag.id === (action as UpdateTagSuccess).response.id
                        ? { ...(action as UpdateTagSuccess).response }
                        : tag
                ),
                tagsListWithCount: state.tagsListWithCount.map(tag =>
                    tag.id === (action as UpdateTagSuccess).response.id
                        ? { ...(action as UpdateTagSuccess).response }
                        : tag
                ),
            };

        default:
            return state;
    }
}

export const selectFeature = (state: AppState) => state.customTags;

export const getCustomTags = createSelector(
    selectFeature,
    (state: CustomTagState) => state
);

export const getTagsList = createSelector(
    selectFeature,
    (state: CustomTagState) => state.tagsList
);

export const getCustomTagsIsLoading = createSelector(
    selectFeature,
    (state: CustomTagState) => state.isCustomTagsLoading
);

export const getTagsListWithCount = createSelector(
    selectFeature,
    (state: CustomTagState) => state.tagsListWithCount
);

export const getCustomTagsWithCountIsLoading = createSelector(
    selectFeature,
    (state: CustomTagState) => state.isCustomTagsWithCountLoading
);


export const getIconsList = createSelector(
    selectFeature,
    (state: CustomTagState) => state.iconsList
);

export const getIconsListIsLoading = createSelector(
    selectFeature,
    (state: CustomTagState) => state.isIconsLoading
);

