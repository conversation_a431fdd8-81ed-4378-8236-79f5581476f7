import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { Testimonial } from 'src/app/core/interfaces/profile.interface';
import { AddTestimonialComponent } from 'src/app/features/profile/testimonials/add-testimonial/add-testimonial.component';
import { getDeletePermissions, getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  DeleteTestimonial,
  FetchTestimonial,
} from 'src/app/reducers/profile/profile.actions';
import { getProfileIsLoading, getTestimonials } from 'src/app/reducers/profile/profile.reducers';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'testimonials',
  templateUrl: './testimonials.component.html',
})
export class TestimonialsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  testimonials: Array<any>;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  canEditProfile: boolean = false;
  canDelete: boolean = false;
  isTestimonalLoading: boolean;

  constructor(
    private modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>
  ) {
    this._store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit.includes('OrgProfile')) this.canEditProfile = true;
      });

    this._store
      .select(getDeletePermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canDelete: any) => {
        if (canDelete.includes('OrgProfile')) this.canDelete = true;
      });
    this._store.dispatch(new FetchTestimonial());
  }

  ngOnInit(): void {
    this._store
      .select(getTestimonials)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        let testimonial = [...data];
        this.testimonials = testimonial?.reverse();
      });
    this._store
      .select(getProfileIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isTestimonalLoading = data;
      });
  }

  editTestimonial(testimonial: Testimonial) {
    let initialState = {
      testimonial: testimonial,
    };
    this.modalService.show(AddTestimonialComponent, {
      class: 'right-modal modal-350',
      initialState,
    });
  }

  deleteTestimonial(id: string) {
    this.openConfirmDeleteModal(id)
  }

  openConfirmDeleteModal(id: string): void {
    let initialState: any = {
      message: 'Are you sure you want to delete',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this._store.dispatch(new DeleteTestimonial(id));
        }
      });
    }
  }

  addTestimonial() {
    this.modalService.show(AddTestimonialComponent, {
      class: 'right-modal modal-350',
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
