<form class="d-flex ip-flex-col">
  <div class="position-relative h-150 w-40pr ip-w-100 ip-mb-20">
    <ng-container *ngIf="!profilePic else bannerImg">
      <div class="bg-dark h-100 w-100 br-6">
        <ng-lottie [options]='building' class="position-absolute left-0 h-150"></ng-lottie>
      </div>
    </ng-container>
    <ng-template #bannerImg>
      <img [type]="'leadrat'" [appImage]="s3BucketUrl+profile.bannerImgUrl" alt="Banner" width="100%" height="150" class="br-6 obj-cover">
    </ng-template>
    <div class="position-absolute top-10 left-10 flex-center">
      <div *ngIf="canDelete" class="dot bg-black-50" (click)="uploadBannerImg('')"><span
          class="icon ic-delete ic-xxxs ic-hover-red cursor-pointer"></span></div>
      <label *ngIf="canEditProfile" class="dot bg-black-50 ml-10">
        <span class="icon ic-pen ic-xxxs icon-hover-green cursor-pointer"></span>
        <input #bannerImg type="file" (change)="onFileSelected($event, 'BannerImage')"
          [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'">
      </label>
    </div>
    <label class="logo-container">
      <input *ngIf="canEditProfile" type="file" (change)="onFileSelected($event, 'LogoImage')"
        [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'">
      <img class="position-absolute nright-60 top-15 w-120 ip-right-0" src="assets/images/round-pattern.svg"
        alt="pattern">
      <img class="position-absolute nright-55 top-20 br-50 obj-cover w-110 h-110 ip-right-5"
        [appImage]="profile?.logoImgUrl ? s3BucketUrl+profile.logoImgUrl : ''" [type]="'orgProfileLogo'" alt="logo">
      <div *ngIf="canEditProfile" class="cam-icon position-absolute bg-white w-110 h-110 nright-55 top-20 br-50 flex-center opacity-0
        cursor-pointer ip-right-5">
        <span class="icon ic-camera-solid ic-xxl ic-black"></span>
      </div>
    </label>
  </div>
  <div class="ml-90 text-large fw-semi-bold ip-w-100 ip-ml-10 ip-mb-20">
    <h2 class="fw-600 break-all text-truncate-1">{{profile?.displayName}}</h2>
    <div class="align-center mt-8">
      <div *ngIf="profile?.phoneNumber">
        <span class="icon ic-phone ic-accent-green ic-xxxs mr-4"></span>
        <a [href]="'tel:' + profile?.phoneNumber">
          {{profile?.phoneNumber}}
        </a>
      </div>
      <div *ngIf="profile?.website" class="ml-20 gap-2 align-center">
        <span class="icon ic-globe ic-accent-green ic-xxxs"></span>
        <h5 [innerHTML]="convertUrlsToLinks(profile.website)"></h5>
      </div>
    </div>
    <div *ngIf="profile?.reraNumber" class="align-center mt-8"><span
        class="icon ic-rera ic-coal ic-large mr-8"></span>{{profile.reraNumber}}</div>
    <div class="mt-10">{{ formattedAddress }}</div>
  </div>
</form>