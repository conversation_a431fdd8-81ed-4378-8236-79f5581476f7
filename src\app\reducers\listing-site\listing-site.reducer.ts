import { Action, createSelector } from "@ngrx/store";
import { AppState } from "src/app/app.reducer";
import { ExcelUploadSuccess, FetchAddressSuccess, FetchAllListingSuccess, FetchCommunitiesSuccess, FetchExcelTrackerListSuccess, FetchListingBaseCountSuccess, FetchListingSourceSuccess, FetchListingSourceWithIdSuccess, FetchListingTopCountSuccess, FetchSubCommunitiesSuccess, FetchSyncListingListSuccess, FetchSyncListingSourceSuccess, ListingSiteActionTypes, UpdateListingPayload } from "./listing-site.actions";


export type ListingSiteState = {
    listingSiteSources: any;
    listingSiteSourcesWithId: any;
    filtersPayload: any;
    allListing: any;
    listingTotalCount: number,
    allListingTopCount: any;
    allListingBaseCount: any;
    allAcountsListed: any;
    syncListing: any;
    excelColumnHeading?: any;
    communities: any;
    subCommunities: any;
    trackerList: any;
    addressWithId: any;
    syncListingSources: any;
    loaders: {
        allListing: boolean;
        allListingTopCount: boolean;
        allListingBaseCount: boolean;
        allAcountsListed: boolean;
        listingSiteSources: boolean;
        listingSiteSourcesWithId: boolean;
        syncListing: boolean;
        communities: boolean;
        subCommunities: boolean;
        trackerList: boolean;
        addressWithId: boolean;
        syncListingSources: boolean;
    };
}

const initialState: ListingSiteState = {
    listingSiteSources: [],
    listingSiteSourcesWithId: [],
    filtersPayload: {
        PageNumber: 1,
        PageSize: 10,
        PropertyVisiblity: 0,
        FirstLevelFilter: 0,
        PropertySearch: ''
    },
    allListing: [],
    listingTotalCount: 0,
    allListingTopCount: {},
    allListingBaseCount: {},
    allAcountsListed: {},
    syncListing: {},
    excelColumnHeading: {},
    communities: [],
    subCommunities: [],
    trackerList: {},
    addressWithId: {},
    syncListingSources: [],
    loaders: {
        allListing: false,
        allListingTopCount: false,
        allListingBaseCount: false,
        allAcountsListed: false,
        listingSiteSources: false,
        listingSiteSourcesWithId: false,
        syncListing: false,
        communities: false,
        subCommunities: false,
        trackerList: false,
        addressWithId: false,
        syncListingSources: false,
    },
}

const updateLoader = (state: ListingSiteState, key: keyof ListingSiteState["loaders"], value: boolean) => ({
    ...state.loaders,
    [key]: value,
});

export function listingSiteReducer(
    state: ListingSiteState = initialState,
    action: Action
) {
    switch (action.type) {
        case ListingSiteActionTypes.UPDATE_LISTING_PAYLOAD:
            return {
                ...state,
                filtersPayload: (action as UpdateListingPayload).payload
            }
        case ListingSiteActionTypes.FETCH_LISTING_SOURCE:
            return {
                ...state,
                loaders: updateLoader(state, "listingSiteSources", true),
            }
        case ListingSiteActionTypes.FETCH_LISTING_SOURCE_SUCCESS:
            return {
                ...state,
                listingSiteSources: (action as FetchListingSourceSuccess).resp,
                loaders: updateLoader(state, "listingSiteSources", false),
            }
        case ListingSiteActionTypes.FETCH_LISTING_SOURCE_WITH_ID:
            return {
                ...state,
                loaders: updateLoader(state, "listingSiteSourcesWithId", true),
            }
        case ListingSiteActionTypes.FETCH_LISTING_SOURCE_WITH_ID_SUCCESS:
            return {
                ...state,
                listingSiteSourcesWithId: (action as FetchListingSourceWithIdSuccess).resp,
                loaders: updateLoader(state, "listingSiteSourcesWithId", false),
            }
        case ListingSiteActionTypes.FETCH_ALL_LISTING:
            return {
                ...state,
                loaders: updateLoader(state, "allListing", true),
            }
        case ListingSiteActionTypes.FETCH_ALL_LISTING_SUCCESS:
            return {
                ...state,
                allListing: (action as FetchAllListingSuccess)?.resp?.items,
                listingTotalCount: (action as FetchAllListingSuccess)?.resp?.totalCount,
                loaders: updateLoader(state, "allListing", false),
            }
        case ListingSiteActionTypes.FETCH_LISTING_TOP_COUNT:
            return {
                ...state,
                loaders: updateLoader(state, "allListingTopCount", true),
            };
        case ListingSiteActionTypes.FETCH_LISTING_TOP_COUNT_SUCCESS:
            return {
                ...state,
                allListingTopCount: (action as FetchListingTopCountSuccess).resp,
                loaders: updateLoader(state, "allListingTopCount", false),
            };
        case ListingSiteActionTypes.FETCH_LISTING_BASE_COUNT:
            return {
                ...state,
                loaders: updateLoader(state, "allListingBaseCount", true),
            };
        case ListingSiteActionTypes.FETCH_LISTING_BASE_COUNT_SUCCESS:
            return {
                ...state,
                allListingBaseCount: (action as FetchListingBaseCountSuccess).resp,
                loaders: updateLoader(state, "allListingBaseCount", false),
            };
        case ListingSiteActionTypes.FETCH_LISTING_ACOUNTS:
            return {
                ...state,
                loaders: updateLoader(state, "allAcountsListed", true),
            }
        case ListingSiteActionTypes.FETCH_LISTING_ACOUNTS_SUCCESS:
            return {
                ...state,
                allAcountsListed: (action as FetchAllListingSuccess).resp,
                loaders: updateLoader(state, "allAcountsListed", false),
            }
        case ListingSiteActionTypes.CLEAR_LISTING_SEARCH:
            return {
                ...state,
                filtersPayload: {
                    ...state.filtersPayload,
                    PropertySearch: ''
                }
            }
        case ListingSiteActionTypes.FETCH_SYNC_LISTING_LIST:
            return {
                ...state,
                loaders: updateLoader(state, "syncListing", true),
            };
        case ListingSiteActionTypes.FETCH_SYNC_LISTING_LIST_SUCCESS:
            return {
                ...state,
                syncListing: (action as FetchSyncListingListSuccess).response,
                loaders: updateLoader(state, "syncListing", false),
            };
        case ListingSiteActionTypes.EXCEL_UPLOAD_SUCCESS:
            return {
                ...state,
                excelColumnHeading: (action as ExcelUploadSuccess).resp,
            };
        case ListingSiteActionTypes.FETCH_COMMUNITIES:
            return {
                ...state,
                loaders: updateLoader(state, "communities", true),
            }
        case ListingSiteActionTypes.FETCH_COMMUNITIES_SUCCESS:
            return {
                ...state,
                communities: (action as FetchCommunitiesSuccess).resp,
                loaders: updateLoader(state, "communities", false),
            }
        case ListingSiteActionTypes.FETCH_SUB_COMMUNITIES:
            return {
                ...state,
                loaders: updateLoader(state, "subCommunities", true),
            }
        case ListingSiteActionTypes.FETCH_SUB_COMMUNITIES_SUCCESS:
            return {
                ...state,
                subCommunities: (action as FetchSubCommunitiesSuccess).resp,
                loaders: updateLoader(state, "subCommunities", false),
            }
        case ListingSiteActionTypes.FETCH_EXCEL_TRACKER_LIST:
            return {
                ...state,
                loaders: updateLoader(state, "trackerList", true),
            };
        case ListingSiteActionTypes.FETCH_EXCEL_TRACKER_LIST_SUCCESS:
            return {
                ...state,
                trackerList: (action as FetchExcelTrackerListSuccess).response,
                loaders: updateLoader(state, "trackerList", false),
            };
        case ListingSiteActionTypes.FETCH_ADDRESS_WITH_ID:
            return {
                ...state,
                loaders: updateLoader(state, "addressWithId", true),
            }
        case ListingSiteActionTypes.FETCH_ADDRESS_WITH_ID_SUCCESS:
            return {
                ...state,
                addressWithId: (action as FetchAddressSuccess).resp,
                loaders: updateLoader(state, "addressWithId", false),
            }
        case ListingSiteActionTypes.FETCH_SYNC_LISTING_SOURCE:
            return {
                ...state,
                loaders: updateLoader(state, "syncListingSources", true),
            }
        case ListingSiteActionTypes.FETCH_SYNC_LISTING_SOURCE_SUCCESS:
            return {
                ...state,
                syncListingSources: (action as FetchSyncListingSourceSuccess).resp,
                loaders: updateLoader(state, "syncListingSources", false),
            }
        default:
            return state;
    }
}
export const selectFeature = (state: AppState) => state.listingSite;

export const getListingSources = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.listingSiteSources
);

export const getListingSourcesWithId = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.listingSiteSourcesWithId
);

export const getAllListing = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.allListing
)

export const getListingTopCount = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.allListingTopCount
)

export const getListingBaseCount = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.allListingBaseCount
)

export const getListingTotalCount = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.listingTotalCount
)

export const getListingSiteLoaders = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.loaders
)

export const getListingFilters = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.filtersPayload
)

export const getListingAcount = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.allAcountsListed
)

export const getSyncListing = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.syncListing
)

export const getAddressColumnHeadings = createSelector(
    selectFeature,
    (state: ListingSiteState) => {
        return state.excelColumnHeading;
    }
);

export const getCommunities = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.communities
);

export const getSubCommunities = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.subCommunities
);

export const getAddressExcelList = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.trackerList
);

export const getAddress = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.addressWithId
);

export const getSyncListingSource = createSelector(
    selectFeature,
    (state: ListingSiteState) => state.syncListingSources
);