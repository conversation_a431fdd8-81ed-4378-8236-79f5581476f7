{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"lr-black": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/lr-black", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest", "src/firebase-messaging-sw.js", "src/manifest.json"], "styles": ["node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles/styles.scss", "src/assets/icons/leadRat.css"], "scripts": ["src/js/init.js", "node_modules/jquery/dist/jquery.slim.min.js", "node_modules/@popperjs/core/dist/umd/popper.js", "node_modules/bootstrap/dist/js/bootstrap.min.js"], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "1mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw.prod.js"}], "outputHashing": "all"}, "uat": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "1mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw.uat.js"}], "outputHashing": "all"}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw.qa.js"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw.dev.js"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "lr-black:build:production"}, "development": {"browserTarget": "lr-black:build:development"}, "uat": {"browserTarget": "lr-black:build:uat"}, "qa": {"browserTarget": "lr-black:build:qa"}, "dev": {"browserTarget": "lr-black:build:dev"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "lr-black:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest", "src/firebase-messaging-sw.js", "src/manifest.json"], "styles": ["src/styles/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}