import { Action, createSelector } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import {
  AssignFilterPayload,
  AssignTotalCount,
  EmailSettingsActions,
  FetchEmailSMTPListByUserIdSuccess,
  getEmailSMTPListSuccess,
} from './email-settings.action';

export type EmailSMTPState = {
  emailSMTPList: any;
  emailSMTPListIsLoading: boolean;
  filterPayload: any;
  totalCount: number;
  emailSMTPListByUserId: any;
  addSMTPListIsLoading: boolean;
  editSMTPListIsLoading: boolean;
};

const initialState: EmailSMTPState = {
  emailSMTPList: [],
  emailSMTPListIsLoading: true,
  filterPayload: {
    pageNumber: 1,
    pageSize: 10,
    searchTerm: '',
  },
  totalCount: 0,
  emailSMTPListByUserId: [],
  addSMTPListIsLoading: true,
  editSMTPListIsLoading: true,
};

export function emailSMTPReducer(
  state: EmailSMTPState = initialState,
  action: Action
): EmailSMTPState {
  switch (action.type) {
    case EmailSettingsActions.GET_EMAIL_SMTP:
      return {
        ...state,
        emailSMTPListIsLoading: true,
      };
    case EmailSettingsActions.GET_EMAIL_SMTP_SUCCESS:
      return {
        ...state,
        emailSMTPList: (action as getEmailSMTPListSuccess).resp,
        emailSMTPListIsLoading: false,
      };
    case EmailSettingsActions.ASSIGN_FILTER_PAYLOAD:
      return {
        ...state,
        filterPayload: (action as AssignFilterPayload).payload,
      };
    case EmailSettingsActions.ASSIGN_TOTAL_COUNT:
      return {
        ...state,
        totalCount: (action as AssignTotalCount).TotalCount,
      };
    case EmailSettingsActions.GET_EMAIL_SMTP_BY_USERID_SUCCESS:
      return {
        ...state,
        emailSMTPListByUserId: (action as FetchEmailSMTPListByUserIdSuccess)
          .resp,
      };
    case EmailSettingsActions.CREATE_EMAIL:
      return {
        ...state,
        addSMTPListIsLoading: true,
      };
    case EmailSettingsActions.CREATE_EMAIL_SUCCESS:
      return {
        ...state,
        addSMTPListIsLoading: false,
      };
    case EmailSettingsActions.EDIT_EMAIL_SMTP:
      return {
        ...state,
        editSMTPListIsLoading: true,
      };
    case EmailSettingsActions.EDIT_EMAIL_SMTP_SUCCESS:
      return {
        ...state,
        editSMTPListIsLoading: false,
      };
    default:
      return state;
  }
}

export const selectEmailSMTPFeature = (state: AppState) => state.emailSettings;

export const selectEmailSettings = createSelector(
  selectEmailSMTPFeature,
  (state: EmailSMTPState) => state.emailSMTPList
);

export const selectEmailSettingsIsLoading = createSelector(
  selectEmailSMTPFeature,
  (state: EmailSMTPState) => state.emailSMTPListIsLoading
);

export const getFiltersPayload = createSelector(
  selectEmailSMTPFeature,
  (state: EmailSMTPState) => state.filterPayload
);

export const getTotalCount = createSelector(
  selectEmailSMTPFeature,
  (state: EmailSMTPState) => state.totalCount
);

export const getEmailSMTPByUserId = createSelector(
  selectEmailSMTPFeature,
  (state: EmailSMTPState) => state.emailSMTPListByUserId
);

export const getAddSMTPIsLoading = createSelector(
  selectEmailSMTPFeature,
  (state: EmailSMTPState) => state.addSMTPListIsLoading
);

export const getEditSMTPIsLoading = createSelector(
  selectEmailSMTPFeature,
  (state: EmailSMTPState) => state.editSMTPListIsLoading
);