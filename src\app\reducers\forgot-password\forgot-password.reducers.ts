import { Action, createSelector } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import { ForgotPasswordActions, VerifyOtpSuccess, VerifyUsernameSuccess } from 'src/app/reducers/forgot-password/forgot-password.actions';

export type ForgotPasswordState = {
  user: {},
  otpVerified: boolean,
};

const initialState: ForgotPasswordState = {
  user: {},
  otpVerified: false,
};

export function forgotPasswordReducer(
  state: ForgotPasswordState = initialState,
  action: Action
): ForgotPasswordState {
  switch (action.type) {
    case ForgotPasswordActions.VERIFY_USERNAME_SUCCESS:
      return {
        ...state,
        user: (action as VerifyUsernameSuccess).user
      };
    case ForgotPasswordActions.VERIFY_OTP_SUCCESS:
      return {
        ...state,
        otpVerified: (action as VerifyOtpSuccess).otpVerified
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.forgetPassword;

export const getUser = createSelector(
  selectFeature,
  (state: ForgotPasswordState) => state.user
);

export const getOtpVerified = createSelector(
  selectFeature,
  (state: ForgotPasswordState) => state.otpVerified
);
