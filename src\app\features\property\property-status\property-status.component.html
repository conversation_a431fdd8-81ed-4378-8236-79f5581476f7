<ng-container *ngIf="params.data.isArchived; else activeTemplate">
<div class="align-center h-100">
  <div class="bg-accent-red-40 text-dark-red br-50px text-center p-4 text-xs">
    {{ 'LABEL.inactive' | translate }}
  </div>
</div>
</ng-container>
<ng-template #activeTemplate>
  <div class="align-center h-100" [title]="status ? 'Active' : 'Sold'">
    <input
      type="checkbox"
      [id]="'toggle-' + params.node.id"
      class="toggle-switch toggle-active-sold"
      [(ngModel)]="status"
      (click)="canEditProperty ? openConfirmModal(params.data) : null"
      [ngClass]="{'pe-none': !canEditProperty}"
    />
    <label
      [for]="'toggle-' + params.node.id"
      class="switch-label"
      [ngClass]="{'pe-none': !canEditProperty}"
    ></label>
  </div>
</ng-template>
