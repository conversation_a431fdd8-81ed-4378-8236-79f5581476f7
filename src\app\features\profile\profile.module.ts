import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LottieModule } from 'ngx-lottie';
import { HttpClient } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';

import { ProfileRoutingModule } from 'src/app/features/profile/profile-routing.module';
import { BasicDetailsComponent } from 'src/app/features/profile/basic-details/basic-details.component';
import { AboutUsComponent } from 'src/app/features/profile/about-us/about-us.component';
import { TestimonialsComponent } from 'src/app/features/profile/testimonials/testimonials.component';
import { AwardsComponent } from 'src/app/features/profile/awards/awards.component';
import { ProfileRootComponent } from 'src/app/features/profile/profile-root.component';
import { ProfileDashboardComponent } from 'src/app/features/profile/profile-dashboard/profile-dashboard.component';
import { ProfileOverviewComponent } from 'src/app/features/profile/profile-overview/profile-overview.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AddTestimonialComponent } from 'src/app/features/profile/testimonials/add-testimonial/add-testimonial.component';
import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { SubscriptionComponent } from './subscription/subscription.component';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';

@NgModule({
  declarations: [
    BasicDetailsComponent,
    AboutUsComponent,
    TestimonialsComponent,
    AwardsComponent,
    ProfileRootComponent,
    ProfileDashboardComponent,
    ProfileOverviewComponent,
    AddTestimonialComponent,
    SubscriptionComponent,
  ],
  imports: [
    CommonModule,
    ProfileRoutingModule,
    SharedModule,
    ReactiveFormsModule,
    FormsModule,
    NgxMatIntlTelInputComponent,
    LottieModule.forRoot({ player: playerFactory }),
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
      // to extend the I18n to use global en.json
      extend: true,
    }),
  ],
  providers: [TranslateService, BasicDetailsComponent],
})
export class ProfileModule {}
