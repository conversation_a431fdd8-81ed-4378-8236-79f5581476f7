import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { BLOOD_GROUP, GENDER } from 'src/app/app.constants';

import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getAWSImagePath, validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchTimeZoneInfo } from 'src/app/reducers/profile/profile.actions';
import { getTimeZoneInfo } from 'src/app/reducers/profile/profile.reducers';
import { FetchUserProfile, UpdateImg, UpdateUserProfile } from 'src/app/reducers/teams/teams.actions';
import { getUserProfile } from 'src/app/reducers/teams/teams.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'edit-user',
  templateUrl: './edit-user.component.html',
})
export class EditUserComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl: string = env.s3ImageBucketURL;
  userData: any;
  files: any[] = [];
  profilePic: string = '';
  basicInfoForm: FormGroup;
  showBasicDetails: boolean = true;
  showLocation: boolean = true;
  genderList: Array<any> = GENDER;
  bloodGroup = BLOOD_GROUP;
  timeZoneList: any;
  userId: any;
  isAppTourEnabled: boolean;

  constructor(
    private store: Store<AppState>,
    private s3UploadService: BlobStorageService,
    private formBuilder: FormBuilder,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
  ) {
    this.isAppTourEnabled = JSON.parse(localStorage.getItem('appTour') || 'false');
    this.basicInfoForm = formBuilder.group({
      altEmail: ['', Validators.email],
      altPhoneNumber: ['', Validators.pattern(/^[5-9]\d{9}$/)],
      bloodGroup: [null],
      gender: [null],
      address: [''],
      permanentAddress: [''],
      timeZone: [null],
      shouldShowTimeZone: [false],
    });
  }

  ngOnInit(): void {
    this.store.dispatch(new FetchTimeZoneInfo());
    this.store.dispatch(new FetchUserProfile(this.userId));

    this.store
      .select(getTimeZoneInfo)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.timeZoneList = data;
      });

    this.store
      .select(getUserProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.profilePic = data.imageUrl
          ? getAWSImagePath(data?.imageUrl || '')
          : '';
        this.patchValues(this.userData);
      });
  }

  onFileSelected(event: any) {
    if (event.target.files && event.target.files[0]) {
      for (let i = 0; i < event.target.files.length; i++) {
        this.files.push(event.target.files[i]);
        let reader = new FileReader();
        reader.onload = (eventOnload: any) => {
          const image = new Image();
          image.src = eventOnload.target.result;
          image.onload = (rs: any) => {
            const img_height = rs.currentTarget['height'];
            const img_width = rs.currentTarget['width'];
          };
          this.profilePic = eventOnload.target.result;
          this.s3UploadService
            .uploadImageBase64([this.profilePic], FolderNamesS3.Images)
            .pipe(takeUntil(this.stopper))
            .subscribe((response: any) => {
              if (response.data.length) {
                this.store.dispatch(new UpdateImg(response.data?.[0]));
              }
            });
        };
        reader.readAsDataURL(event.target.files[i]);
      }
    }
  }

  removeProfile() {
    this.store.dispatch(new UpdateImg(''));
  }

  closeModal() {
    this.modalRef.hide();
  }

  patchValues(data: any) {
    this.basicInfoForm.patchValue({
      ...this.userData,
      altPhoneNumber: data?.altPhoneNumber?.substring(
        this.userData?.altPhoneNumber.length,
        3
      ),
      timeZone: data?.timeZoneInfo?.timeZoneDisplay ?
        data?.timeZoneInfo?.timeZoneDisplay
        : null,
      shouldShowTimeZone:
        data?.shouldShowTimeZone || false,
    });
  }

  onSave() {
    if (!this.basicInfoForm.valid) {
      validateAllFormFields(this.basicInfoForm);
      return;
    }
    const userInfo = this.basicInfoForm.value;
    let selectedTZ = this.timeZoneList?.find(
      (item: any) => item?.displayName === userInfo?.timeZone
    );
    let basicDetailsPayload: any = {
      altEmail: userInfo.altEmail,
      altPhoneNumber: userInfo?.altPhoneNumber
        ? `+91${userInfo.altPhoneNumber?.toString()}`
        : '',
      bloodGroup: userInfo?.bloodGroup,
      gender: userInfo?.gender,
      address: userInfo?.address,
      permanentAddress: userInfo?.permanentAddress,
      reportsTo: this.userData?.reportsTo?.id,
      departmentId: this.userData?.department?.id,
      designationId: this.userData?.designation?.id,
      userId: this.userData?.userId,
      firstName: this.userData?.firstName,
      lastName: this.userData?.lastName,
      phoneNumber: this.userData.phoneNumber,
      email: this.userData.email,
      officeName: this.userData.officeName,
      officeAddress: this.userData.officeAddress,
      isAutomationEnabled: this.userData.isAutomationEnabled,
      imageUrl: this.userData.imageUrl,
      description: this.userData.description,
      timeZoneInfo: selectedTZ?.displayName ? {
        timeZoneId: selectedTZ?.ianaZoneId,
        timeZoneDisplay: selectedTZ?.displayName,
        baseUTcOffset: selectedTZ?.baseUtcOffset,
        timeZoneName: selectedTZ?.displayName
      } : null,
      timeZone: selectedTZ?.displayName || null,
      timeZoneId: selectedTZ?.ianaZoneId || null,
      baseUTcOffset: selectedTZ?.baseUtcOffset || null,
      shouldShowTimeZone:
        selectedTZ?.displayName && userInfo.shouldShowTimeZone,
    };
    this.store.dispatch(
      new UpdateUserProfile(this.userData.userId, basicDetailsPayload, true)
    );
    this.modalRef.hide();
  }

  ToggleAppTour(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.isAppTourEnabled = isChecked;
    localStorage.setItem('appTour', JSON.stringify(isChecked));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
