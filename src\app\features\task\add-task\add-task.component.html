<div>
    <div class="flex-between w-100 bg-coal px-24 py-12 text-white">
        <h3 class="fw-700">
            {{ (selectedTodo ? 'TASK.edit-task' : 'TASK.add-task' ) | translate }}</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="px-24 py-10 scrollbar h-100-46">
        <ng-container *ngIf="selectedTodo">
            <div class="border-gray bg-light-pearl p-12 br-5 d-flex fw-semi-bold">
                <div class="w-50 flex-col">
                    <p class="text-mud fw-400 text-sm">{{'LEADS.created-by' | translate}}:</p>
                    <p>{{getAssignedToDetails(selectedTodo?.createdBy, users, true) || '--'}}</p>
                    <p class="text-mud fw-400 mt-10 text-sm">{{'LEADS.modified-by' | translate}}:</p>
                    <p>{{getAssignedToDetails(selectedTodo?.lastModifiedBy, users, true) || '--'}}</p>
                </div>
                <div class="w-50 flex-col ml-10">
                    <p class="text-mud fw-400 text-sm">{{'LEADS.created-date' | translate}}:</p>
                    <p>{{ selectedTodo?.createdOn ?
                        getTimeZoneDate(
                        selectedTodo?.createdOn,userBasicDetails?.timeZoneInfo?.baseUTcOffset):'--'}}</p>
                    <p class="text-mud fw-400 mt-10 text-sm">{{'LEADS.modified-date' | translate}}:</p>
                    <p>{{selectedTodo?.lastModifiedOn ?
                        getTimeZoneDate(selectedTodo?.lastModifiedOn,userBasicDetails?.timeZoneInfo?.baseUTcOffset):
                        '--'}}
                    </p>
                </div>
            </div>
        </ng-container>
        <form [formGroup]="addTaskForm" (ngSubmit)="doSubmit()" autocomplete="off">
            <div>
                <div class="field-label-req">{{ 'TASK.title' | translate }}</div>
                <form-errors-wrapper [control]="addTaskForm.controls['title']" label="{{ 'TASK.title' | translate }}">
                    <input type="text" required id="inpTaskTitle" data-automate-id="inpTaskTitle"
                        formControlName="title" placeholder="ex. Need to call">
                </form-errors-wrapper>
            </div>
            <div class="field-label-req">{{ 'LEADS.assign-to' | translate }}</div>
            <form-errors-wrapper [control]="addTaskForm.controls['assignedUserIds']" label="{{ 'LEADS.assign-to' | translate }}">
                <ng-select [virtualScroll]="true" [items]="canAssignToAny ? allActiveUsers : activeUsers"
                    ResizableDropdown bindLabel="fullName" bindValue="id" name="user" formControlName="assignedUserIds"
                    placeholder="ex. Mounika Pampana" [ngClass]="{'pe-none blinking': isUserListLoading}">
                </ng-select>
            </form-errors-wrapper>
            <div>
                <div class="field-label-req">{{ 'LEAD_FORM.date-time' | translate }}</div>
                <form-errors-wrapper [control]="addTaskForm.controls['scheduledDateTime']"
                    label="{{ 'LEAD_FORM.date-time' | translate }}">
                    <input type="text" [min]="minAllowedDate" [owlDateTimeTrigger]="fromdatepicker" readonly
                        [owlDateTime]="fromdatepicker" required id="inpTaskDate" data-automate-id="inpTaskDate"
                        placeholder="ex. 19/06/2025, 11:49 am" formControlName="scheduledDateTime">
                    <span class="icon ic-calendar ic-sm ic-coal position-absolute right-20 top-12"
                        [owlDateTimeTrigger]="fromdatepicker"></span>
                    <owl-date-time #fromdatepicker [hour12Timer]="'true'" [startAt]="minAllowedDate"
                        (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                </form-errors-wrapper>
            </div>
            <div>
                <div class="field-label-req">{{ 'TASK.task-description' | translate }}</div>
                <form-errors-wrapper [control]="addTaskForm.controls['notes']"
                    label="{{ 'TASK.task-description' | translate }}">
                    <textarea rows="3" id="txtTaskDescription" data-automate-id="txtTaskDescription"
                        formControlName="notes" placeholder="ex. I want to say.... "></textarea>
                </form-errors-wrapper>
            </div>
            <div>
                <div class="field-label-req">{{ 'TASK.priority' | translate }}</div>
                <div class="justify-between">
                    <ng-container *ngFor="let priority of taskPriorities">
                        <input type="radio" class="btn-check" [ngClass]="'btn-check-'+priority.name"
                            [value]="priority.value" autocomplete="off" formControlName="priority"
                            id="inpPriority{{priority.name}}" data-automata-id="inpPriority{{priority.name}}">
                        <label class="btn btn-xxs br-20 flex-center px-12" [ngClass]="'btn-'+priority.name"
                            for="inpPriority{{priority.name}}">{{ priority.name | titlecase }}
                        </label>
                    </ng-container>
                </div>
                <ng-container
                    *ngIf="addTaskForm.controls['priority']?.dirty || addTaskForm.controls['priority']?.touched">
                    <div class="error-message-custom" *ngIf="addTaskForm.controls['priority']?.errors?.required">
                        Priority is a required field.
                    </div>
                </ng-container>
            </div>
        </form>
        <div class="flex-center mt-20">
            <div class="fw-600 px-36 py-10 cursor-pointer rounded bg-coal text-white w-110 text-center"
                (click)="doSubmit()">Save</div>
        </div>
    </div>
</div>