import {
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit
} from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { EMPTY_GUID, INTEGRATION_LIST } from 'src/app/app.constants';
import { IntegrationSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  DeleteUserAssignment
} from 'src/app/reducers/teams/teams.actions';
import { getUserAssignments } from 'src/app/reducers/teams/teams.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'assigned-details',
  templateUrl: './assigned-details.component.html',
})
export class AssignedDetailsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  showIntegration: boolean = true;
  integrationList: Array<any> = INTEGRATION_LIST;
  selectedIntegrationType: any = 'Facebook';
  userAssignmentList: any;
  updatedIntegrationList: any;
  currentUserAssignmentsData: any;
  EMPTY_GUID = EMPTY_GUID;
  isAdsExpanded: boolean = false;
  isFormsExpanded: boolean = false;
  selectedSection: string = 'Integration';
  isEmptyIntegration: boolean = true;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    public trackingService: TrackingService
  ) { }

  ngOnInit() {
    this.fetchIntegrationList();
  }

  fetchIntegrationList() {
    this.store
      .select(getUserAssignments)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.currentUserAssignmentsData = data;
        this.userAssignmentList = data?.entities;
        this.updatedIntegrationList = [
          ...(this.userAssignmentList?.['Integration Accounts'] || []),
          ...(this.userAssignmentList?.['Facebook Accounts'] || [])
        ]
          ?.filter((item: any) => {
            return (
              item.source == IntegrationSource[this.selectedIntegrationType]
            );
          })
          .reverse();
      });
    if (!this.updatedIntegrationList) {
      this.isEmptyIntegration = false;
    }
  }

  openDeleteAssignmentModal(data: any, type: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove assignment for',
      title: data?.name,
      fieldType: type,
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    let payload: any = {
      userId: this.currentUserAssignmentsData?.userId,
      entityIds: [data?.id],
    };
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteUserAssignment(payload));
          let track: any = {
            'integration account': 'Integration',
            project: 'Projects',
            City: 'City',
            Zone: 'Zone'
          };
          let trackingType = track[type] ? track[type] : 'Unknown';
          this.trackingService.trackFeature(`Web.UserDetails.Button.AssignedData${trackingType}Remove.Click`);
        }
      });
    }
  }

  // Inside your component class
  // toggleAdsExpanded(account: any) {
  //   account.isAdsExpanded = !account.isAdsExpanded;
  //   account.isFormsExpanded = false;
  // }

  // toggleFormsExpanded(account: any) {
  //   account.isFormsExpanded = !account.isFormsExpanded;
  //   account.isAdsExpanded = false;
  // }

  integrationType(data: any) {
    this.selectedIntegrationType = data;
    this.fetchIntegrationList();
    return this.updatedIntegrationList;
  }

  isIntegrationType(item: any) {
    if (this.integrationType(item)?.length > 0) {
      return true;
    } else {
      return false;
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
