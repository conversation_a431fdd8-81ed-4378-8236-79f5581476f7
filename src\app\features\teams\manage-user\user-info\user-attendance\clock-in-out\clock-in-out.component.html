<div (click)="openLocationModal(location)" class="text-sm flex-between w-100"
    [ngClass]="clockData?.length ? 'cursor-pointer' : 'pe-none'">
    <ng-container *ngIf="params.value[0]?.clockInOut == 'clockIn'">
        <div>
            {{clockData?.[0]?.clockInTime ?
            getTimeZoneDate(clockData?.[0]?.clockInTime ,userData?.timeZoneInfo?.baseUTcOffset, 'timeWithMeridiem')
            : '--'}}
        </div>
        <div *ngIf="clockData?.length > 1" class="text-accent-green border-accent-green rounded-circle dot dot-md ml-4">
            {{clockData?.length}}
        </div>
    </ng-container>
    <ng-container *ngIf="params.value[0]?.clockInOut == 'clockOut'">
        <div *ngIf="params.value[0]?.clockInOut == 'clockOut'">
            {{clockData?.[clockData.length - 1]?.clockOutTime ?
            getTimeZoneDate((clockData?.[clockData.length -
            1]?.clockOutTime),userData?.timeZoneInfo?.baseUTcOffset,'timeWithMeridiem')
            : '--'}}
        </div>
        <div *ngIf="clockData?.length > 1" class="text-red-350 border-red rounded-circle dot dot-md ml-4">
            {{clockData?.length}}
        </div>
    </ng-container>
</div>
<ng-template #location>
    <div class="bg-white h-100vh tb-w-100-40">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3 class="fw-semi-bold fv-sm-caps">Clock in/out location</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="d-flex">
            <div class="flex-column h-100-80 scrollbar">
                <ng-container *ngFor="let entry of clockData; index as i">
                    <div *ngIf="i != 0" class="border-bottom mx-12 mt-12 bg-light-pearl"></div>
                    <div class="field-label fw-600 text-mud px-12">Clock {{i+1}}</div>
                    <div class="flex-between mt-12">
                        <div [title]="entry?.clockInLocation ? entry?.clockInLocation : 'Location not available'"
                            class="mx-12 p-8 cursor-pointer w-180 bg-light-pearl border-right border-0 br-6"
                            (click)="showLocation(entry.clockInLatitude,entry.clockInLongitude,entry.clockInLocation, i+1)">
                            <div class="flex-between">
                                <div class="flex-column">
                                    <h5 class="fw-600 text-mud">Clock in</h5>
                                    <div class="text-dark-gray text-xxs"><span class="fw-700">{{entry?.clockInTime ?
                                            getTimeZoneDate(entry?.clockInTime,userData?.timeZoneInfo?.baseUTcOffset,
                                            'fullDateTime'): ''}}</span>
                                    </div>
                                </div>
                                <div class="icon ic-location-solid ic-accent-green ic-sm mr-4"></div>
                            </div>
                        </div>
                        <div [ngClass]="entry.clockInImageUrl ? 'cursor-pointer' : 'pe-none'"
                            (click)="openImage(entry.clockInImageUrl, imageView)"><img
                                [appImage]="entry.clockInImageUrl ? s3BucketUrl+entry.clockInImageUrl : ''"
                                [type]="'defaultAvatar'" class="br-50 obj-cover mr-12" width="30" height="30"></div>
                    </div>
                    <div class="flex-between mt-12">
                        <div [title]="entry?.clockOutLocation ? entry?.clockOutLocation : 'Location not available'"
                            class="mx-12 p-8 cursor-pointer w-180 bg-light-pearl border-right border-0 br-6"
                            (click)="showLocation(entry.clockOutLatitude,entry.clockOutLongitude,entry.clockOutLocation, i+1)">
                            <div class="flex-between">
                                <div class="flex-column">
                                    <h5 class="fw-600 text-mud">Clock out</h5>
                                    <div class="text-dark-gray text-xxs"><span class="fw-700">{{entry?.clockOutTime
                                            ?
                                            getTimeZoneDate(entry?.clockOutTime,userData?.timeZoneInfo?.baseUTcOffset,
                                            'fullDateTime')
                                            : ''}}</span>
                                    </div>
                                </div>
                                <div class="icon ic-location-solid ic-accent-green ic-sm mr-4"></div>
                            </div>
                        </div>
                        <div [ngClass]="entry.clockOutImageUrl ? 'cursor-pointer' : 'pe-none'"
                            (click)="openImage(entry.clockOutImageUrl, imageView)"><img
                                [appImage]="entry.clockOutImageUrl ? s3BucketUrl+entry.clockOutImageUrl : ''"
                                [type]="'defaultAvatar'" class="br-50 obj-cover mr-12" width="30" height="30"></div>
                    </div>
                </ng-container>
            </div>
            <div class="w-80pr responsive-map">
                <google-map [center]="{lat: center?.lat, lng: center?.lng}">
                    <map-marker #mapMarker="mapMarker" (mapClick)="openInfoWindow(mapMarker)"
                        *ngFor="let marker of markers" [position]="{lat:marker?.latitude, lng:marker?.longitude}"
                        [label]="marker?.label">
                    </map-marker>
                    <map-info-window>{{selectedAddress}}</map-info-window>
                </google-map>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #imageView>
    <div class="flex-center position-relative">
        <img [appImage]="currentImage ? s3BucketUrl+currentImage : ''" [type]="'defaultAvatar'" alt="image"
            class="w-300 position-relative h-300">
        <div class="w-100 bg-blur position-absolute flex-center">
            <img [appImage]="currentImage ? s3BucketUrl+currentImage : ''" [type]="'defaultAvatar'" alt="image"
                class="max-w-300 h-300">
        </div>
    </div>
</ng-template>