<div>
    <div class="w-100 d-flex ip-flex-col">
        <div class="w-40pr tb-w-50 ip-w-100">
            <div class="p-20 bg-white br-4 text-black-100 mr-16 ip-mr-0">
                <h5 class="fw-600 mb-20 text-coal text-decoration-underline">Active Subscription Info</h5>
                <div class="p-12 flex-between border-bottom mb-10">
                    <h6>Date of Subscription</h6>
                    <h5 class="fw-600">{{ subscription?.dateOfSubscription ?
                        getTimeZoneDate(subscription?.dateOfSubscription, userData?.timeZoneInfo?.baseUTcOffset,
                        'dayMonthYearText') : '--'}}</h5>
                </div>
                <div class="p-12 flex-between border-bottom mb-10">
                    <h6>Total Payment Done <span class="text-light-slate">(inc GST)</span></h6>
                    <h5 class="fw-600 text-accent-green text-nowrap"> <span *ngIf="subscription?.totalAmount"> INR
                        </span>
                        {{subscription?.totalAmount ?
                        (subscription?.totalAmount | number:'1.2-2') :
                        '---'}}</h5>
                </div>
                <div class="p-12 flex-between border-bottom mb-10">
                    <h6>Price Per User<span class="text-light-slate mx-6">x</span> <span>Per Month</span> <span
                            class="text-light-slate"> (inc GST)</span></h6>
                    <h5 class="fw-600 text-nowrap"> <span *ngIf="subscription?.pricePerUserPerMonth"> INR </span> {{
                        subscription?.pricePerUserPerMonth ?
                        (subscription?.pricePerUserPerMonth | number:'1.2-2') :
                        '---' }} </h5>
                </div>
                <div class="p-12 flex-between border-bottom mb-10">
                    <h6>Total Due Amount <span class="text-light-slate"> (inc GST)</span></h6>
                    <div class="align-center">
                        <!-- <div class="bg-accent-green p-6 br-4 text-white mr-6 cursor-pointer"
                            (click)="openPaymentModal(Payment)">Pay now</div> -->
                        <h5 class="fw-600 text-red-350 text-nowrap"> <span *ngIf="subscription?.dueAmount"> INR </span>
                            {{subscription?.dueAmount ?
                            (subscription?.dueAmount |
                            number:'1.2-2'): '---' }}
                        </h5>
                    </div>
                </div>
                <!-- <div class="p-12 flex-between border-bottom mb-10">
                    <h6>Payment Due Date</h6>
                    <h5 class="fw-600">{{subscription?.dueDate
                        ? getTimeZoneDate(subscription?.dueDate, userData?.timeZoneInfo?.baseUTcOffset,
                        'dayMonthYearText')
                        : "---"}}</h5>
                </div> -->
                <div class="p-12 flex-between">
                    <h6>Plan Expiry Date</h6>
                    <h5 class="fw-600 text-red-350">
                        {{subscription?.licenseValidity
                        ? getTimeZoneDate(subscription?.licenseValidity, userData?.timeZoneInfo?.baseUTcOffset,
                        'dayMonthYearText')
                        : "---"
                        }}</h5>
                </div>
            </div>
        </div>
        <div class="w-45 tb-w-50 ip-w-100 ip-mt-10">
            <div class="p-20 bg-white br-4 text-black-100 mr-16 w-100">
                <h5 class="fw-600 mb-20 text-coal text-decoration-underline">Active Plan</h5>
                <div>
                    <div class="align-center ph-flex-wrap">
                        <div class="justify-center-col w-33 ph-w-100">
                            <div class="align-center-col">
                                <h5 class="fw-600 text-nowrap">{{BillingType[subscription?.billingType]}}</h5>
                                <div class="border-bottom w-35px my-4"></div>
                                <div class="text-sm mt-4 text-dark-gray">Plan Validity</div>
                            </div>
                        </div>
                        <div class="mr-20 border-right h-60 ph-d-none"> </div>
                        <div class="d-flex w-100 ph-mt-10">
                            <div class="d-flex w-50 mr-10 border-right h-20"><img alt=""
                                    src="../../../../assets/images/calender-tick-green.svg" class="mr-10">
                                <div>
                                    <h5 class="fw-600">{{subscription?.daysLeft}}</h5>
                                    <div class="text-sm text-dark-gray">Days Left</div>
                                </div>
                            </div>
                            <div class="d-flex w-50"><img src="../../../../assets/dashboard/calendar-cancel.svg" alt=""
                                    class="mr-10">
                                <div>
                                    <h5 class="fw-600">{{subscription?.daysCompleted}}</h5>
                                    <div class="text-sm text-dark-gray">Days Completed</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="border-bottom my-12">
                    </div>
                    <div class="align-center ph-flex-wrap">
                        <div class="justify-center-col w-33 ph-w-100">
                            <div class="align-center-col">
                                <h5 class="fw-600 text-nowrap">{{subscription?.licenseBought}} Users</h5>
                                <div class="border-bottom w-35px my-4"></div>
                                <div class="text-sm mt-4 text-dark-gray">License Bought</div>
                            </div>
                        </div>
                        <div class="mr-20 border-right h-60 ph-d-none"> </div>
                        <div class="d-flex w-100 ph-mt-10">
                            <div class="d-flex w-50 mr-10 border-right h-20"><img alt=""
                                    src="../../../../assets/dashboard/user-tick.svg" class="mr-10">
                                <div>
                                    <h5 class="fw-600">{{subscription?.activeUsers}}</h5>
                                    <div class="text-sm text-dark-gray">Active Users</div>
                                </div>
                            </div>
                            <div class="d-flex w-50"><img src="../../../../assets/dashboard/user-cancel.svg" alt=""
                                    class="mr-10">
                                <div>
                                    <h5 class="fw-600">{{subscription?.inActiveUsers}}</h5>
                                    <div class="text-sm text-dark-gray">Inactive Users</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="border-bottom my-12">
                    </div>
                    <div class="flex-between w-100">
                        <div class="text-sm"><span class="text-dark-gray">Expand your team's capabilities, boost
                                sales with additional</span> <span class="fw-semi-bold mx-2">User Licenses</span>.</div>
                        <div class="btn-coal ml-30 w-110" (click)="openLicenseModal(licenseModal)"><span
                                class="icon ic-xxs ic-add mr-6"></span>Buy Licenses</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-16">
        <div class="flex-between mb-12 bg-white p-12 br-4">
            <h5 class="fw-600 text-coal">Transaction Details</h5>
            <div class="show-dropdown-white align-center position-relative ip-br-0">
                <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                        {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
                <ng-select [items]="showEntriesSize" [formControl]="pageEntry" (change)="assignPageSize()"
                    ResizableDropdown [virtualScroll]="true" [placeholder]="pageSize" class="w-150 tb-w-120px"
                    [searchable]="false">
                </ng-select>
            </div>
        </div>
        <div class="scrollbar scroll-hide tb-w-100-60 table-scrollbar w-100-200 max-h-100-180">
            <table class="table standard-table no-vertical-border">
                <thead>
                    <tr class="w-100 text-nowrap">
                        <th class="w-8"></th>
                        <th class="w-150">Transaction Date</th>
                        <th class="w-90">Plan Status</th>
                        <th class="w-100px">No.of Users</th>
                        <th class="w-130">Plan Net Amount</th>
                        <th class="w-100px">Plan GST</th>
                        <th class="w-100px">Total Amount</th>
                        <th class="w-100px">Paid Amount</th>
                        <th class="w-110">Amount Due</th>
                        <!-- <th class="w-110">Due Date</th> -->
                        <th class="w-130">Payment Details</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold" *ngFor="let data of transaction; let i = index">
                    <tr class="bg-white">
                        <td class="w-8" [ngClass]="data.subscriptionAddOns.length > 0 ? 'cursor-pointer': 'pe-none'">
                            <div class="icon ic-coal ic-triangle-up ic-xxxs" [ngClass]="{
                              'd-none': data.subscriptionAddOns.length === 0,
                              'rotate-90': !(selectedIndex === i && showTransactionData)
                            }" (click)="toggleTransactionData(i)">
                            </div>

                        </td>
                        <td class="w-150">{{ data?.paymentDate ? getTimeZoneDate(data?.paymentDate,
                            userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'): '--' }}</td>
                        <td class="w-90 text-dark-gray border-bottom"> <ng-container
                                *ngIf="data.isActive == true && data.isExpired == false">
                                <div
                                    class="text-green-170 bg-green-180 py-4 px-6 br-12 flex-center fw-semi-bold max-w-60">
                                    <span class="dot dot-xs bg-green-170 mr-4"></span>Active
                                </div>
                            </ng-container>
                            <ng-container *ngIf="data.isExpired == true && data.isActive == false">
                                <div class="text-red-850 bg-red-950 py-4 px-6 br-12 flex-center fw-semi-bold max-w-60">
                                    <span class="dot dot-xs bg-red-850 mr-4"></span>Expired
                                </div>
                            </ng-container>
                            <ng-container *ngIf="data.isExpired == false && data.isActive == false">
                                <div
                                    class="text-aqua-850 bg-aqua-950 py-4 px-6 br-12 flex-center fw-semi-bold max-w-60">
                                    <span class="dot dot-xs bg-aqua-850 mr-4"></span>Awaited
                                </div>
                            </ng-container>
                        </td>
                        <td class="w-100px"> {{data?.soldLicenses}}</td>
                        <td class="w-130"><span *ngIf="data?.netAmount">INR </span>{{data?.netAmount ? data?.netAmount :
                            '--'}}</td>
                        <td class="w-100px"><span *ngIf="data?.gstAmount">INR </span>{{data?.gstAmount ? data?.gstAmount
                            :
                            '--'}}</td>
                        <td class="w-110"><span *ngIf="data?.totalAmount">INR </span>{{data?.totalAmount ?
                            data?.totalAmount : '--'}}</td>
                        <td class="w-100px"><span *ngIf="data?.paidAmount">INR </span>{{data?.paidAmount ?
                            data?.paidAmount : '--'}}</td>
                        <td class="w-110"><span *ngIf="data?.dueAmount">INR </span>{{data?.dueAmount ? data?.dueAmount :
                            '--'}}</td>
                        <!-- <td class="w-110">{{ data?.dueDate ? getTimeZoneDate(data?.dueDate,
                            userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') : '--' }}
                        </td> -->
                        <td class="w-130 cursor-pointer" (click)="openDetailsModal(Details, i)">
                            <span class="text-decoration-underline text-navy-100">Payment Details</span>
                            <span class="ml-6 icon ic-xxxs ic-extend ic-navy-100"></span>
                        </td>
                    </tr>
                    <div *ngIf="showTransactionData && selectedIndex == i && data.subscriptionAddOns.length > 0">
                <tbody class="border-0 fw-semi-bold" *ngFor="let data of transaction ; let i = index">
                    <ng-container *ngIf="i === selectedIndex">
                        <ng-container
                            *ngFor="let subscription of data?.subscriptionAddOns;  let last = last; let first = first; let srno = index">
                            <tr [ngClass]="{'mt-4': first, 'mb-4': last}">
                                <td class="w-8 text-dark-gray bg-unset py-0">
                                    <div class="border-right position-relative"
                                        [ngClass]="last?'h-24 ntop-12':'h-50px'">
                                        <div
                                            [ngClass]="first? 'icon ic-xxs ic-dark-400 ic-circle position-absolute nright-6 ntop-4':''">
                                        </div>
                                        <div class="icon ic-xxs ic-dark-400 position-absolute nright-12"
                                            [ngClass]="last?'ic-turn-right top-22':'ic-arrow-right top-24'">
                                        </div>
                                    </div>
                                </td>
                                <td class="w-150 text-dark-gray border-bottom">{{ subscription?.paymentDate ?
                                    getTimeZoneDate(subscription?.paymentDate,
                                    userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'): '--' }}
                                </td>
                                <td class="w-90 text-dark-gray border-bottom"> <ng-container
                                        *ngIf="subscription.isActive == true && subscription.isExpired == false">
                                        <div
                                            class="text-green-170 bg-green-180 py-4 px-6 br-12 flex-center fw-semi-bold max-w-60">
                                            <span class="dot dot-xs bg-green-170 mr-4"></span>Active
                                        </div>
                                    </ng-container>
                                    <ng-container
                                        *ngIf="subscription.isExpired == true && subscription.isActive == false">
                                        <div
                                            class="text-red-850 bg-red-950 py-4 px-6 br-12 flex-center fw-semi-bold max-w-60">
                                            <span class="dot dot-xs bg-red-850 mr-4"></span>Expired
                                        </div>
                                    </ng-container>
                                    <ng-container
                                        *ngIf="subscription.isExpired == false && subscription.isActive == false">
                                        <div
                                            class="text-aqua-850 bg-aqua-950 py-4 px-6 br-12 flex-center fw-semi-bold max-w-60">
                                            <span class="dot dot-xs bg-aqua-850 mr-4"></span>Awaited
                                        </div>
                                    </ng-container>
                                </td>
                                <td class="w-100px text-dark-gray border-bottom">
                                    <div class="break-all text-truncate-1" [title]="subscription?.soldLicenses">
                                        {{subscription?.soldLicenses ? subscription?.soldLicenses : '--'}}</div>
                                </td>
                                <td class="w-130 text-dark-gray border-bottom">
                                    <div class="break-all text-truncate-1" [title]="subscription?.netAmount"><span
                                            *ngIf="subscription?.netAmount"> INR </span>{{subscription?.netAmount ?
                                        subscription?.netAmount : '--'}}</div>
                                </td>
                                <td class="w-100px text-dark-gray border-bottom">
                                    <div class="break-all text-truncate-1" [title]="subscription?.gstAmount"><span
                                            *ngIf="subscription?.gstAmount"> INR </span>{{subscription?.gstAmount ?
                                        subscription?.gstAmount : '--'}}</div>
                                </td>
                                <td class="w-100px text-dark-gray border-bottom">
                                    <div class="break-all text-truncate-1" [title]="subscription?.totalAmount"> <span
                                            *ngIf="subscription?.totalAmount"> INR </span> {{subscription?.totalAmount ?
                                        subscription?.totalAmount : '--'}}</div>
                                </td>
                                <td class="w-100px text-dark-gray border-bottom">
                                    <div class="break-all text-truncate-1" [title]="subscription?.paidAmount"><span
                                            *ngIf="subscription?.paidAmount"> INR </span> {{subscription?.paidAmount ?
                                        subscription?.paidAmount: '--'}}</div>
                                </td>
                                <td class="w-110 text-dark-gray border-bottom">
                                    <div class="break-all text-truncate-1" [title]="subscription?.dueAmount"><span
                                            *ngIf="subscription?.dueAmount"> INR </span>{{subscription?.dueAmount ?
                                        subscription?.dueAmount : '--'}}</div>
                                </td>
                                <!-- <td class="w-110 text-dark-gray border-bottom">
                                    <div class="break-all text-truncate-1">{{subscription?.dueDate ?
                                        getTimeZoneDate(subscription?.dueDate, userData?.timeZoneInfo?.baseUTcOffset,
                                        'dayMonthYear'): '--'}}</div>
                                </td> -->
                                <td class="w-130 cursor-pointer border-bottom"
                                    (click)="openSubDetailsModal(SubDetails, i, srno)">
                                    <span class="text-decoration-underline text-navy-100">Payment
                                        Details</span>
                                    <span class="ml-6 icon ic-xxxs ic-extend ic-navy-100"></span>
                                </td>
                            </tr>
                        </ng-container>
                    </ng-container>
                </tbody>
        </div>
        </tbody>
        </table>
    </div>
    <div *ngFor="let data of transaction; let first = first">
        <ng-container *ngIf="transaction?.length > 0 && first">
            <div class="mt-16 flex-end">
                <div class="mr-10">
                    {{ 'GLOBAL.showing' | translate }} {{ currOffset * pageSize + 1 }}
                    {{ 'GLOBAL.to-small' | translate }} {{ currOffset * pageSize + transaction?.length }}
                    {{ 'GLOBAL.of-small' | translate }} {{ totalPages }}
                    {{ 'GLOBAL.entries-small' | translate }}
                </div>

                <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(totalPages, pageSize)"
                    (pageChange)="onPageChange($event)">
                </pagination>
            </div>
        </ng-container>
    </div>
</div>
<ng-template #Payment>
    <div class="p-20 bg-white br-4 text-black-100">
        <span class="ic-close-secondary ic-close-modal cursor-pointer" (click)="modalRef.hide()"></span>
        <div class="flex-between">
            <h5 class="fw-600 text-coal">Payment</h5>
            <div class="align-center">
                <h6 class="fw-semi-bold">Amount : </h6>
                <h5 class="fw-600">INR {{subscription?.dueAmount ? subscription?.dueAmount : '--'}}</h5>
            </div>
        </div>
        <div class="align-center payment my-20">
            <div class="activation" [ngClass]="{'active' : selectedSection == 'QRCode'}"
                (click)="selectedSection = 'QRCode'">
                <span class="icon ic-qr-code ic-sm mr-6 ip-mr-4 ic-accent-green"
                    [ngClass]="{'active' : selectedSection !== 'QRCode'}"></span>
                <span class="ph-d-none">QR Code</span>
            </div>
            <div class="activation" [ngClass]="{'active' : selectedSection == 'BankTransfer'}"
                (click)="selectedSection = 'BankTransfer'">
                <span class="icon ic-bank ic-sm mr-6 ip-mr-4 ic-accent-green"
                    [ngClass]="{'active' : selectedSection !== 'BankTransfer'}"></span>
                <span class="ph-d-none">Bank Transfer</span>
            </div>
            <div class="activation" [ngClass]="{'active' : selectedSection == 'CardPayment'}"
                (click)="selectedSection = 'CardPayment'">
                <span class="icon ic-wallet ic-sm mr-6 ip-mr-4 ic-accent-green"
                    [ngClass]="{'active' : selectedSection !== 'CardPayment'}"></span>
                <span class="ph-d-none"> Card Payment</span>
            </div>
        </div>
        <div class="border-bottom"></div>
        <div class="mt-20 mb-20 flex-between ph-flex-between-unset ph-flex-col" *ngIf="selectedSection == 'QRCode'">
            <div class="mr-20">
                <h5 class="fw-600">Dhinwa Solutions Private Limited</h5>
                <h6 class="fw-semi-bold text-accent-green">Leadrat CRM Software</h6>
                <div class="mt-24">
                    <h6 class="fw-semi-bold">Steps to pay</h6>
                    <div class="mt-6 text-wrap text-sm">Open UPI app > Select 'Tap to Pay' >
                        Scan QR Code > Enter Amount</div>
                </div>
            </div>
            <div class="ph-mt-20 justify-center"><img src="../../../../assets/images/payment-qrcode.png" alt="">
            </div>
        </div>
        <div class="mt-20" *ngIf="selectedSection == 'BankTransfer'">
            <h5 class="fw-600">Pay using NEFT/RTGS/IMPS</h5>
            <h6 class="fw-semi-bold mt-10">To complete the transaction, make NEFT / RTGS / IMPS transfer to :</h6>
            <div class="mt-16 mb-56">
                <div class="w-100 d-flex">
                    <div class="text-sm text-gray-850 w-50">Beneficiary Name:</div>
                    <div class="text-sm text-black-200 w-50"><span>DHINWA SOLUTIONS PRIVATE LIMITED</span><span
                            class="icon ic-xxs ic-black-200 ml-6 ic-copy-clipboard cursor-pointer"
                            (click)="copyToClipboard('DHINWA SOLUTIONS PRIVATE LIMITED')"
                            [title]="'Copy Beneficiary Name' | translate"></span></div>
                </div>
                <div class="w-100 d-flex mt-10">
                    <div class="text-sm text-gray-850 w-50">Account:</div>
                    <div class="text-sm text-black-200 w-50"><span>************</span><span
                            class="icon ic-xxs ic-black-200 ml-6 ic-copy-clipboard cursor-pointer"
                            (click)="copyToClipboard('************')"
                            [title]="'Copy Account Number' | translate"></span></div>
                </div>
                <div class="w-100 d-flex mt-10">
                    <div class="text-sm text-gray-850 w-50">IFSC:</div>
                    <div class="text-sm text-black-200 w-50"><span>ICIC0007295</span><span
                            class="icon ic-xxs ic-black-200 ml-6 ic-copy-clipboard cursor-pointer"
                            (click)="copyToClipboard('ICIC0007295')" [title]="'Copy IFSC Code' | translate"></span>
                    </div>
                </div>
            </div>
            <!-- <div class="bg-accent-green py-6 px-16 br-4 text-white mr-6 cursor-pointer mt-16 w-100px">Copy Details</div> -->
        </div>
        <div class="mt-20 mb-10" *ngIf="selectedSection == 'CardPayment'">
            <div class="justify-center">
                <img src="../../../../assets/images/transaction.svg" alt="" width="150">
            </div>
            <h5 class="mt-20 fw-600 justify-center">Please contact our customer care to proceed with card payment.</h5>
            <div class="flex-center">
                <h6 class="text-dark-110 mr-6">Our customer care no.</h6>
                <a [href]="'tel:'+91**********" class="fw-700 text-accent-green cursor-pointer header-3">+91
                    **********</a>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #licenseModal>
    <div class="flex-between bg-coal p-12">
        <h5 class="text-white fw-600">Add License</h5>
        <div class="icon ic-xxs ic-close cursor-pointer" (click)="cancel();modalRef.hide()"></div>
    </div>
    <div class="p-20">
        <div class="d-flex">
            <div class="w-40pr">
                <div class="field-label-req mt-0">Number of License</div>
                <div class="form-group mr-40">
                    <input type="number" placeholder="ex.10" min="1" [(ngModel)]="numberOfLicenses"
                        (input)="calculateAmounts()" (keydown)="onlyNumbers($event)">
                    <div *ngIf="numberOfLicenses == 0" class="position-absolute right-20 text-xxs text-red-350 mt-4">
                        Invalid data</div>
                </div>
            </div>
            <div class="w-60pr">
                <div class="w-100">
                    <div class="d-flex">
                        <h5 class="w-50 fw-400">Net Amount :</h5>
                        <h4 class="text-nowrap"> INR {{netPayableAmount ? (netPayableAmount
                            | number:'1.2-2') : '--' }}
                        </h4>
                    </div>
                    <div class="d-flex mb-10 mt-4">
                        <h5 class="w-50 fw-400">GST / IGST (18%) :</h5>
                        <h4 class="text-nowrap">INR {{ gstAmount ? (gstAmount |
                            number:'1.2-2') : '--'}}</h4>
                    </div>
                    <div class="d-flex border-top pt-10">
                        <h5 class="w-50 fw-400"> Total Amount :</h5>
                        <h4 class="text-nowrap"> INR {{ totalPayableAmount ?
                            (totalPayableAmount | number:'1.2-2'): '--'
                            }}</h4>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-12 fw-semi-bold">Note: The price has been calculated on a pro-rata basis, meaning the number of
            subscription days multiplied by the pricing per day.</div>
        <!-- <h5 class="mt-20">Payment Methods</h5>
        <div class="d-flex mt-6">
            <div class="py-4 px-6 mr-10 align-center"><span class="icon ic-xxs ic-coal ic-upi mr-6"></span>
                <h5 class="text-dark-gray">UPI</h5>
            </div>
            <div class="py-4 px-6 mr-10 align-center"><span class="icon ic-xxs ic-coal ic-wallet mr-6"></span>
                <h5 class="text-dark-gray">Credit / Debit Card</h5>
            </div>
            <div class="py-4 px-6 mr-10 align-center"><span class="icon ic-xxs ic-coal ic-bank mr-6"></span>
                <h5 class="text-dark-gray">Net Banking</h5>
            </div>
        </div> -->
    </div>
    <div class="border-bottom"></div>
    <div class="p-10 flex-end">
        <div class="fw-semi-bold text-sm text-decoration-underline cursor-pointer mr-20"
            (click)="cancel();modalRef.hide()">Cancel</div>
        <button class="btn-coal" (click)="payNow(NoUrl)"
            [ngClass]="{'pe-none opacity-5': !numberOfLicenses || totalPayableAmount <= 0}">
            Pay Now</button>
    </div>
</ng-template>


<ng-template #Details let-modal>
    <div class="flex-between bg-coal p-12">
        <h5 class="text-white fw-600">Payment Details</h5>
        <div class="icon ic-xxs ic-close cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="p-10">
        <ng-container *ngFor="let data of transaction ;  let i = index">
            <ng-container *ngIf="i === selectedIndex">
                <ng-container>
                    <div class="scrollbar scroll-hide tb-w-100-24 table-scrollbar max-h-100-80">
                        <table class="table standard-table no-vertical-border">
                            <thead>
                                <tr class="w-100 text-nowrap">
                                    <th class="w-50px">SL NO</th>
                                    <th class="w-150">Transaction Date</th>
                                    <th class="w-100px">Net Amount</th>
                                    <th class="w-100px">GST (18%)</th>
                                    <th class="w-100px">Total Amount</th>
                                    <th class="w-110">Pending Amount</th>
                                    <th class="w-125">Payment Mode</th>
                                    <th class="w-110">Next Due Date</th>
                                </tr>
                            </thead>
                            <tbody class="text-secondary fw-semi-bold"
                                *ngFor="let payment of data?.payments ; let srno = index">
                                <tr class="bg-white">
                                    <td class="w-50px">{{ srno + 1 }}
                                    </td>
                                    <td class="w-150">{{ payment?.createdOn ? getTimeZoneDate(payment?.createdOn,
                                        userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'): '--' }}</td>
                                    <td class="w-100px">
                                        <div class="break-all text-truncate-1" [title]="payment.netAmount"><span
                                                *ngIf="payment.netAmount">INR </span>{{payment.netAmount ?
                                            payment.netAmount : '--'}}</div>
                                    </td>
                                    <td class="w-100px">
                                        <div class="break-all text-truncate-1" [title]="payment.gstAmount"><span
                                                *ngIf="payment.gstAmount">INR </span> {{payment.gstAmount ?
                                            payment.gstAmount : '--'}}</div>
                                    </td>
                                    <td class="w-100px">
                                        <div class="break-all text-truncate-1" [title]="payment.totalAmount"><span
                                                *ngIf="payment.totalAmount">INR </span> {{payment.totalAmount
                                            ?
                                            payment.totalAmount : '--'}}</div>
                                    </td>
                                    <td class="w-110 text-red-350">
                                        <div class="break-all text-truncate-1" [title]="payment.pendingAmount"><span
                                                *ngIf="payment.pendingAmount">INR </span>{{payment.pendingAmount ?
                                            payment.pendingAmount: '--' }}</div>
                                    </td>
                                    <td class="w-125">{{ payment?.paymentMode || '--'}}</td>
                                    <td class="w-110">{{payment.nextDueDate ? getTimeZoneDate(payment.nextDueDate,
                                        userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'):
                                        'Cleared'}}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </ng-container>
                <div *ngIf="!data?.payments?.length" class="h-200 flex-center-col">
                    <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
                    <div class="fw-semi-bold text-xl text-mud">No Data Found!</div>
                </div>
            </ng-container>
        </ng-container>
    </div>
</ng-template>

<ng-template #SubDetails let-modal>
    <div class="flex-between bg-coal p-12">
        <h5 class="text-white fw-600">Payment Details</h5>
        <div class="icon ic-xxs ic-close cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="p-10">
        <ng-container *ngFor="let data of transaction ; let i = index">
            <ng-container *ngIf="i === selectedIndex">
                <ng-container *ngFor="let subscription of data?.subscriptionAddOns; let srno = index">
                    <ng-container *ngIf="srno === subSelectedIndex">
                        <ng-container>
                            <div class="scrollbar scroll-hide tb-w-100-24 table-scrollbar max-h-100-80">
                                <table class="table standard-table no-vertical-border">
                                    <thead>
                                        <tr class="w-100 text-nowrap">
                                            <th class="w-50px">SL NO</th>
                                            <th class="w-150">Transaction Date</th>
                                            <th class="w-100px">Net Amount</th>
                                            <th class="w-100px">GST (18%)</th>
                                            <th class="w-100px">Total Amount</th>
                                            <th class="w-110">Pending Amount</th>
                                            <th class="w-125">Payment Mode</th>
                                            <th class="w-110">Next Due Date</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-secondary fw-semi-bold"
                                        *ngFor="let payment of subscription?.payments  ; let num = index">
                                        <tr class="bg-white">
                                            <td class="w-50px cursor-pointer">{{ num + 1 }}
                                            </td>
                                            <td class="w-150">{{ payment?.createdOn ?
                                                getTimeZoneDate(payment?.createdOn,userData?.timeZoneInfo?.baseUTcOffset,
                                                'dayMonthYear'): '--' }}</td>
                                            <td class="w-100px">
                                                <div class="break-all text-truncate-1" [title]="payment.netAmount"><span
                                                        *ngIf="payment.netAmount">INR
                                                    </span>{{payment.netAmount
                                                    ?
                                                    payment.netAmount : '--'}}</div>
                                            </td>
                                            <td class="w-100px">
                                                <div class="break-all text-truncate-1" [title]="payment.gstAmount"><span
                                                        *ngIf="payment.gstAmount">INR</span>
                                                    {{payment.gstAmount
                                                    ?
                                                    payment.gstAmount : '--'}}</div>
                                            </td>
                                            <td class="w-100px">
                                                <div class="break-all text-truncate-1" [title]="payment.totalAmount">
                                                    <span *ngIf="payment.totalAmount">INR</span>
                                                    {{payment.totalAmount
                                                    ?
                                                    payment.totalAmount : '--'}}
                                                </div>
                                            </td>
                                            <td class="w-110 text-red-350">
                                                <div class="break-all text-truncate-1" [title]="payment.pendingAmount">
                                                    <span *ngIf="payment.pendingAmount">INR</span>
                                                    {{payment.pendingAmount ?
                                                    payment.pendingAmount: '--' }}
                                                </div>
                                            </td>
                                            <td class="w-125">{{ payment?.paymentMode || '--' }}
                                            </td>
                                            <td class="w-110">{{payment?.nextDueDate ?
                                                getTimeZoneDate(payment?.nextDueDate ,
                                                userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'):
                                                'Cleared'}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </ng-container>
                        <div *ngIf="!subscription?.payments?.length" class="h-200 flex-center-col">
                            <img src="assets/images/layered-cards.svg" alt="No data found" width="160" height="140">
                            <div class="fw-semi-bold text-xl text-mud">No Data Found!</div>
                        </div>
                    </ng-container>
                </ng-container>
            </ng-container>
        </ng-container>

    </div>
</ng-template>


<ng-template #NoUrl>
    <div class="flex-between bg-coal p-12">
        <h5 class="text-white fw-600">Alert!</h5>
        <div class="icon ic-xxs ic-close cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="p-10 h-100px">
        <h5> Please contact support team. **********.</h5>
    </div>

</ng-template>