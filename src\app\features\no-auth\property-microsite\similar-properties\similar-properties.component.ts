import { Component, EventEmitter, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { IMAGES } from 'src/app/app.constants';
import { EnquiryType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  formatBudget,
  getAreaUnit,
  getBHKDisplayString,
  getBRDisplayString,
  getLocationDetailsByObj,
  isEmptyObject,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchMSSimilarProperties } from 'src/app/reducers/property/property.actions';
import { getMSSimilarPropertiesIsLoading, getMicrositeSimilarProperties } from 'src/app/reducers/property/property.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'similar-properties',
  templateUrl: './similar-properties.component.html',
})
export class SimilarPropertiesComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  filterSimilarProp: any = [];
  @Input() areaSizeUnits: any;
  @Input() serialNo: any;
  @Input() isPropertySoldOut: boolean;
  s3BucketUrl = env.s3ImageBucketURL;
  images = IMAGES;
  formatBudget = formatBudget;
  getLocationDetailsByObj = getLocationDetailsByObj;
  isEmptyObject = isEmptyObject;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  getAreaUnit = getAreaUnit;
  defaultCurrency: string = 'INR';
  userName: any = '';
  isMSSimilarPropertiesLoading: boolean = true;
  topView: boolean = true;
  lastView: boolean = false;
  similarPropLength: number = 0;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10
  };
  isListing: boolean = window.location.pathname.includes('listing')

  constructor(private store: Store<AppState>) {
    if (location.href.split('/').length > 1) {
      const router = location.href.split('/');
      this.userName = router[router.length - 2];
    }
  }

  ngOnInit() {
    this.filtersPayload = {
      ...this.filtersPayload,
      serialNo: this.serialNo
    };
    this.store.dispatch(new FetchMSSimilarProperties(this.filtersPayload));

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency = data.countries && data.countries.length > 0 ? data.countries[0].defaultCurrency : null;
      });
    this.store
      .select(getMicrositeSimilarProperties)
      .pipe(takeUntil(this.stopper))
      .subscribe((properties: any) => {
        this.filterSimilarProp.push(...properties?.map((obj: any) => obj.property));
        this.similarPropLength = properties?.length;
      });
    this.store
      .select(getMSSimilarPropertiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isMSSimilarPropertiesLoading = isLoading;
      });
  }

  getEnquiredFor(enquiredFor: number) {
    return EnquiryType[enquiredFor];
  }

  getCoverImage(propertyData: any): string {
    if (propertyData) {
      for (const section of Object.keys(propertyData)) {
        const images = propertyData[section] || [];
        const coverImage = images?.find((image: any) => image.isCoverImage);
        if (coverImage) {
          return this.s3BucketUrl + coverImage.imageFilePath;
        }
      }
    }
    return '';
    // Return an empty string if no cover image is found
  }

  onInView(event: any) {
    if (event && this.similarPropLength === 10) {
      this.filtersPayload = {
        ...this.filtersPayload,
        pageNumber: this.filtersPayload.pageNumber + 1
      };
      this.store.dispatch(new FetchMSSimilarProperties(this.filtersPayload));
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
