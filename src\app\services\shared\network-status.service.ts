import { Injectable } from '@angular/core';
import { BehaviorSubject, fromEvent, map, merge, Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class NetworkStatusService {
    private onlineStatus = new BehaviorSubject<boolean>(navigator.onLine);

    constructor() {
        // Listen for online and offline events
        const online$ = fromEvent(window, 'online').pipe(map(() => true));
        const offline$ = fromEvent(window, 'offline').pipe(map(() => false));

        merge(online$, offline$).subscribe((status) =>
            this.onlineStatus.next(status)
        );
    }

    getNetworkStatus(): Observable<boolean> {
        return this.onlineStatus.asObservable();
    }
}
