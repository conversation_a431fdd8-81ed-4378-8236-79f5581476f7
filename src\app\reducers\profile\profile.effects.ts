import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom } from 'rxjs/operators';

import { Store } from '@ngrx/store';
import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AddRecognition,
  AddTestimonial,
  DeleteRecognition,
  DeleteTestimonial,
  FetchProfile,
  FetchProfileSuccess,
  FetchQRCode,
  FetchQRCodeSuccess,
  FetchRecognition,
  FetchRecognitionSuccess,
  FetchSubscription,
  FetchSubscriptionSuccess,
  FetchTestimonial,
  FetchTestimonialSuccess,
  FetchTimeZoneInfo,
  FetchTimeZoneInfoSuccess,
  FetchTransaction,
  FetchTransactionInfo,
  FetchTransactionInfoSuccess,
  FetchTransactionSuccess,
  profileActionTypes,
  UpdateAboutUs,
  UpdateBannerImg,
  UpdateLogoImg,
  UpdateProfile,
  UpdateSocialMedia,
  UpdateTestimonial
} from 'src/app/reducers/profile/profile.actions';
import { getPageNumber, getPageSize } from './profile.reducers';
import { ProfileService } from 'src/app/services/controllers/profile.service';

@Injectable()
export class ProfileEffects {
  getProfile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_PROFILE),
      map((action: FetchProfile) => action),
      switchMap((action: FetchProfile) => {
        return this.api.getProfile().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProfileSuccess(resp.data);
            }
            return new FetchProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getTestimonials$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_TESTIMONIALS),
      map((action: FetchTestimonial) => action),
      switchMap((action: FetchTestimonial) => {
        return this.api.getTestimonials().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTestimonialSuccess(resp.items);
            }
            return new FetchTestimonialSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getRecognition$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_RECOGNITION),
      map((action: FetchRecognition) => action),
      switchMap((action: FetchRecognition) => {
        return this.api.getAllRecognition().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchRecognitionSuccess(resp.items);
            }
            return new FetchRecognitionSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addTestimonial$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.ADD_TESTIMONIAL),
      map((action: AddTestimonial) => action),
      switchMap((action: AddTestimonial) => {
        return this.api.addTestimonial(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Testimonial Added Successfully'
              );
              return new FetchTestimonial();
            }
            return new FetchTestimonialSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateProfile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.UPDATE_PROFILE),
      map((action: UpdateProfile) => action),
      switchMap((action: UpdateProfile) => {
        return this.api.updateProfile(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'General Info Updated Successfully'
              );
              return new FetchProfile();
            }
            return new FetchProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateBannerImg$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.UPDATE_BANNER_IMG),
      switchMap((action: UpdateBannerImg) => {
        return this.api.updateBannerImg(action.url).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Banner Image Updated Successfully'
              );
              return new FetchProfile();
            }
            return new FetchProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateLogoImg$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.UPDATE_LOGO_IMG),
      switchMap((action: UpdateLogoImg) => {
        return this.api.updateLogoImg(action.url).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Logo Image Updated Successfully'
              );
              return new FetchProfile();
            }
            return new FetchProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateSocialMedia$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.UPDATE_SOCIAL_MEDIA),
      switchMap((action: UpdateSocialMedia) => {
        return this.api.updateSocialMedia(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Social Media Updated Successfully'
              );
              return new FetchProfile();
            }
            return new FetchProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateAboutUs$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.UPDATE_ABOUT_US),
      map((action: UpdateAboutUs) => action),
      switchMap((action: UpdateAboutUs) => {
        return this.api.updateAboutUs(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'About Us Updated Successfully'
              );
              return new FetchProfile();
            }
            return new FetchProfileSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateTestimonial$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.UPDATE_TESTIMONIAL),
      map((action: UpdateTestimonial) => action),
      switchMap((action: UpdateTestimonial) => {
        return this.api.updateTestimonial(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Testimonial Updated Successfully'
              );
              return new FetchTestimonial();
            }
            return new FetchTestimonialSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteTestimonial$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.DELETE_TESTIMONIAL),
      switchMap((action: DeleteTestimonial) => {
        return this.api.deleteTestimonial(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Testimonial deleted successfully.`
              );
              return new FetchTestimonial();
            }
            return new FetchTestimonial();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addRecognition$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.ADD_RECOGNITION),
      map((action: AddRecognition) => action),
      switchMap((action: AddRecognition) => {
        return this.api.addRecognition(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Awards Added Successfully');
              return new FetchRecognition();
            }
            return new FetchRecognitionSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteRecognition$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.DELETE_RECOGNITION),
      switchMap((action: DeleteRecognition) => {
        return this.api.deleteRecognition(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Award deleted successfully.`);
              return new FetchRecognition();
            }
            return new FetchRecognitionSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getQRCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_QR_CODE),
      map((action: FetchQRCode) => action),
      switchMap((data: FetchQRCode) => {
        return this.api.getQRCode(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchQRCodeSuccess(resp.data);
            }
            return new FetchQRCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getTransaction$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_TRANSACTION),
      
      withLatestFrom(
        this.store.select(getPageNumber),
        this.store.select(getPageSize)
      ), switchMap(([action, pageNumber, pageSize]: [FetchTransaction, number, number]) => {
        return this.api.getAllSubscription(pageNumber, pageSize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTransactionSuccess(resp);
            }
            return new FetchTransactionSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getSubscription$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_SUBSCRIPTION),
      map((action: FetchSubscription) => action),
      switchMap((action: FetchSubscription) => {
        return this.api.getSubscription(action.timeZoneInfo).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchSubscriptionSuccess(resp.data);
            }
            return new FetchSubscriptionSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getTransactionInfo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_TRANSACTION_INFO),
      map((action: FetchTransactionInfo) => action),
      switchMap((action: FetchTransactionInfo) => {
        return this.api.transactionInfo().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTransactionInfoSuccess(resp.data);
            }
            return new FetchTransactionInfoSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getTimeZoneInfo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(profileActionTypes.FETCH_TIMEZONE_INFO),
      map((action: FetchTimeZoneInfo) => action),
      switchMap((data: FetchTimeZoneInfo) => {
        return this.api.getTimeZoneInfo().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTimeZoneInfoSuccess(resp.data);
            }
            return new FetchTimeZoneInfoSuccess();
          }
          )
        )
      })
  )
  );


  constructor(
    private actions$: Actions,
    private api: ProfileService,
    private _notificationService: NotificationsService,
    private store: Store<AppState>
  ) { }
}
