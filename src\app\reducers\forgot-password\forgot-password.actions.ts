import { Action } from '@ngrx/store';

export enum ForgotPasswordActions {
  VERIFY_USERNAME = '[FORGOT_PASSWORD] Verify Username',
  VERIFY_USERNAME_SUCCESS = '[FORGOT_PASSWORD] Verify Username Success',
  GET_OTP = '[FORGOT_PASSWORD] Get Otp',
  VERIFY_OTP = '[FORGOT_PASSWORD] Verify Otp',
  VERIFY_OTP_SUCCESS = '[FORGOT_PASSWORD] Verify Otp Success',
  RESET_PASSWORD = '[FORGOT_PASSWORD] Reset Password',
  CHANGE_PASSWORD = '[FORGOT_PASSWORD] Change Password',
}

export class VerifyUsername implements Action {
  readonly type: string = ForgotPasswordActions.VERIFY_USERNAME;
  constructor(public payload: any) { }
}
export class VerifyUsernameSuccess implements Action {
  readonly type: string = ForgotPasswordActions.VERIFY_USERNAME_SUCCESS;
  constructor(public user = {}) { }
}
export class GetOtp implements Action {
  readonly type: string = ForgotPasswordActions.GET_OTP;
  constructor(public payload: any) { }
}
export class VerifyOtp implements Action {
  readonly type: string = ForgotPasswordActions.VERIFY_OTP;
  constructor(public payload: any) { }
}
export class VerifyOtpSuccess implements Action {
  readonly type: string = ForgotPasswordActions.VERIFY_OTP_SUCCESS;
  constructor(public otpVerified = false) { }
}
export class ResetPassword implements Action {
  readonly type: string = ForgotPasswordActions.RESET_PASSWORD;
  constructor(public payload: any) { }
}
export class ChangePassword implements Action {
  readonly type: string = ForgotPasswordActions.CHANGE_PASSWORD;
  constructor(public payload: any, public userId: string) { }
}
