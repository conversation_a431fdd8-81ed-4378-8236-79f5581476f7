import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { UpdateProjectToggleStatus } from 'src/app/reducers/project/project.action';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'project-status',
  templateUrl: './project-status.component.html',
})
export class ProjectStatusComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  status: boolean;
  params: any;
  canEditProperty: boolean = false;

  constructor(
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>,
    public trackingService: TrackingService
  ) { }

  agInit(params: any): void {
    this.params = params;
  }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Projects')) {
          this.canEditProperty = true;
        }
      });
    this.status = this.params.data.currentStatus === 0 ? true : false;
  }

  openConfirmModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'change the availability of',
      title: data?.title,
      fieldType: 'project',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.trackingService.trackFeature(`Web.Project.Button.Availability.Click`)
          this.store.dispatch(
            new UpdateProjectToggleStatus(this.params.data.id)
          );
        } else {
          this.status = this.params.data.currentStatus === 0 ? true : false;
        }
      });
    }
  }
}
