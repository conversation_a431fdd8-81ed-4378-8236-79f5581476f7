<div class="flex-between mt-20 w-100">
    <button [disabled]="index == 0" [class.disabled]="index == 0"
        [ngClass]="index == 0 ? 'bg-gray-darker' : 'bg-accent-green'"
        class="dot dot-x-xxl cursor-pointer mr-20 border-0" (click)="onPreviousClick()">
        <span class="icon ic-arrow-left icon-white icon-lg" id="clkLeftImage" data-automate-id="clkLeftImage"></span>
    </button>
    <div class="position-relative">
        <a class="ic-close-secondary ic-close-modal-coal ip-ic-close-secondary" (click)="onCloseClick()"></a>
        <img [appImage]="s3BucketUrl + imageGallery[index]" [type]="'property'"
            class="object-fit-contain br-10 z-index-2 bg-gray-dark w-100 h-400">
    </div>
    <button [disabled]="index == imageGallery.length - 1" [class.disabled]="index == imageGallery.length - 1"
        [ngClass]="index == imageGallery.length - 1 ? 'bg-gray-darker' : 'bg-accent-green'"
        class="dot dot-x-xxl cursor-pointer ml-20 border-0" (click)="onNextClick()">
        <span class="icon ic-arrow-right icon-white icon-lg" id="clkRightImage" data-automate-id="clkRightImage"></span>
    </button>
</div>
<div class="w-100 flex-end" *ngIf="imageGallery?.length">
    <span class="bg-black-50 text-white br-50px px-6 py-2">{{index+1}}/{{imageGallery.length}}</span>
</div>