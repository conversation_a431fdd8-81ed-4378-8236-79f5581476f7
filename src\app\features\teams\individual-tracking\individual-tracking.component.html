<div class="responsive-map w-100">
    <google-map [center]="{lat: center?.lat, lng: center?.lng}" class="w-unset">
        <map-marker #mapMarker="mapMarker" (mapClick)="openInfoWindow(mapMarker)" *ngFor="let marker of markers"
            [position]="marker?.latitude ? {lat:marker?.latitude, lng:marker?.longitude} : ''" [label]="marker?.label">
        </map-marker>
    </google-map>
</div>
<div class="position-absolute my-10 mx-20 top-50px">
    <div class="p-16 bg-coal w-300 ph-w-230px">
        <div class="fw-semi-bold text-large text-white fv-sm-caps">{{'USER.clock-in' | translate }} /
            {{ 'USER.clock-out' | translate }} </div>
    </div>
    <div class="bg-white p-16">
        <div class="flex-between ph-flex-col ph-flex-start">
            <div>
                <div class="fw-600 header-4 text-black-100">Mounika Pampana</div>
                <div class="fw-semi-bold text-large text-dark-gray mt-4">Software engineer</div>
            </div>
            <button class="btn-green br-20 ph-mt-10" id="liveTracking" data-automate-id="liveTracking">
                {{ 'USER.live-tracking' | translate }}</button>
        </div>
        <div class="mt-16 align-center">
            <span class="fw-600 text-large text-black-100 mr-8 mb-16">{{'USER.attendance-records' | translate }}</span>
            <span class="border-bottom-slate-40 w-130 mb-12 ph-w-50px"></span>
        </div>
        <div class="h-100-210 scrollbar">
            <div class="mb-16 p-10 border flex-between br-4 ph-px-4">
                <div>
                    <!-- <div class="fw-600 text-large text-green-900">{{'USER.clock-in' | translate}}</div> -->
                    <div class="fw-600 text-large text-red-350">{{'USER.clock-out' | translate}}</div>
                    <div class="align-center mt-6">
                        <span class="icon ic-alarm-solid ic-black-200 ic-xxs mr-8"></span>
                        <span class="fw-600 text-xs text-black-200 mr-6">26-05-2025</span>
                        <span class="border-left-black-200 h-10"></span>
                        <span class="fw-600 text-xs text-black-200 ml-6">08:41 PM</span>
                    </div>
                    <span class="mt-6 text-dark-blue-300 fw-semi-bold text-sm border-bottom-blue-300">See location</span>
                </div>
                <div>
                    <img [appImage]="userData?.imageUrl ? s3BucketUrl+userData.imageUrl : ''"
                        [type]="'defaultAvatar'" alt="profile pic" width="48" height="48" class="br-50 obj-cover">
                </div>
            </div>
        </div>
    </div>
</div>
<div class="bg-white py-20 px-32 position-absolute flex-end bottom-10 right-10 br-6 box-shadow-10">
    <div class="w-600 ip-w-200px">
        <div class="align-center">
            <span class="dot bg-green-900 dot-xl br-50 mr-12">
                <div class="icon ic-clock-nine  ic-sm m-4"></div>
            </span>
            <span class="text-green-900 fw-600 header-3 fv-sm-caps">{{'USER.clock-in' | translate }}</span>
        </div>
        <!-- <div class="align-center">
            <span class="dot bg-red-350 dot-xl br-50 mr-12">
                <div class="icon ic-clock-eight  ic-sm m-4"></div>
            </span>
            <span class="text-red-350 fw-600 header-3 fv-sm-caps">{{'USER.clock-out' | translate }}</span>
        </div> -->
        <div class="mt-16">
            <span class="icon ic-alarm-solid ic-black-200 ic-large mr-8"></span>
            <span class="text-black-200 fw-600 header-3 mr-6">26-05-2025</span>
            <span class="border-left-black-200 h-10 mr-6"></span>
            <span class="text-black-200 fw-600 header-3">12:41 PM</span>
        </div>
        <div class="mt-16 d-flex">
            <div class="icon ic-person-circle ic-black-200 ic-large mr-8"></div>
            <div class="text-black-200 fw-600 header-3">1st A, 1st Cross Rd, 5th Block, HBR Layout 4th
                Block,HBR Layout, Bengaluru, Karnataka 560043</div>
        </div>
    </div>
</div>