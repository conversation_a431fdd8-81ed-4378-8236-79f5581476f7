<ng-container *ngIf="!params.data?.logDtos.length else logEntries">
  <div class="bg-gray-dark text-xs text-black-200 br-20 px-20 py-2 text-center fw-600">No entries found</div>
</ng-container>
<ng-template #logEntries>
  <div class="w-100 mt-16 align-center br-20 bg-gray-dark px-24">
    <span style="width: calc(100% / (24*6));"
      *ngFor="let block of blocks; let first = first; let last = last; let i = index">
      <div class="h-16 position-relative"
        [ngClass]="{'brtl-4': first, 'brbl-4': first, 'brtr-4': last, 'brbr-4': last, 'bg-accent-green': minutesClockedIn[i] == 1, 
        'attendance-bar-linear-gradient': minutesClockedIn[i] == 2, 'border-gray-right': (i + 1) % 6 === 0 && !minutesClockedIn[i] == 1 && i != 0 && i != (minutesClockedIn.length -1)}">
        <div *ngIf="i == startIndex"
          class="text-sm text-nowrap position-absolute ntop-24 left-0 flex-col text-accent-green fw-600">
          <span class="nml-46 w-47px text-right">{{ getTimeZoneDate(params?.data?.logDtos?.[params?.data?.logDtos.length
            - 1]?.clockInTime , userData?.timeZoneInfo?.baseUTcOffset, 'timeWithMeridiem') }}
          </span><span class="text-xxxs">|</span>
        </div>
        <div *ngIf="i == endIndex"
          class="text-sm text-nowrap position-absolute ntop-24 left-0 flex-col text-red fw-600">
          <span>{{getTimeZoneDate(params?.data?.logDtos?.[0]?.clockOutTime, userData?.timeZoneInfo?.baseUTcOffset,
            'timeWithMeridiem') }}
          </span>
          <span class="text-xxxs">|</span>
        </div>
      </div>
    </span>
  </div>
</ng-template>