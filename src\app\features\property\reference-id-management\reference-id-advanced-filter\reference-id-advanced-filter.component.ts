import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { assignToSort } from 'src/app/core/utils/common.util';
import { FetchPropertyWithIdNameList } from 'src/app/reducers/property/property.actions';
import { getPropertyWithIdNameList } from 'src/app/reducers/property/property.reducer';
import { FetchAllReferenceIdsList, UpdateReferencePayload } from 'src/app/reducers/reference-id-management/reference-id-management.action';
import { getAllReferenceIdList, getFiltersPayload, getReferenceListingSources } from 'src/app/reducers/reference-id-management/reference-id-management.reducer';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'reference-id-advanced-filter',
  templateUrl: './reference-id-advanced-filter.component.html',
})
export class ReferenceIdAdvancedFilterComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  filtersPayload: any = {}
  userList: any = [];
  propertyList: any = [];
  portals: any = [];
  allReferenceIds: any = [];
  formFields: any = {
    SerialNo: [null],
    PropertyTitle: [null],
    RefrenceIds: [null],
    ListingSourceIds: [null],
    AssignedUserIds: [null],
    AssociatedProperties: [null]
  };
  filtersForm: FormGroup;

  constructor(
    private _store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder,
  ) { }

  ngOnInit(): void {
    this.filtersForm = this.fb.group(this.formFields);
    this.initializeDispatches()
    this.initializeSubscriptions()
  }

  initializeDispatches() {
    this._store.dispatch(new FetchPropertyWithIdNameList());
    this._store.dispatch(new FetchAllReferenceIdsList())
  }

  initializeSubscriptions() {
    this._store.select(getFiltersPayload).pipe(takeUntil(this.stopper)).subscribe((data: any) => {
      this.filtersPayload = data;
      this.filtersForm.patchValue({
        ...data
      })
    })
    this._store
      .select(getPropertyWithIdNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data.slice().sort((a: any, b: any) => {
          const nameA = a.name || '';
          const nameB = b.name || '';
          return nameA.localeCompare(nameB);
        });
      });
    this._store.select(getUsersListForReassignment).pipe(takeUntil(this.stopper)).subscribe((data: any) => {
      this.userList = data?.map((user: any) => ({
        ...user,
        fullName: `${user.firstName} ${user.lastName}`,
      }));
      this.userList = assignToSort(this.userList, '')
    })
    this._store
      .select(getReferenceListingSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.portals = data
      })

    this._store
      .select(getAllReferenceIdList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allReferenceIds = data
      })
  }

  applyAdvancedFilters() {
    const payload = {
      ...this.filtersPayload,
      ...this.filtersForm.value,
      ...(this.filtersForm?.value?.ListingSourceIds?.length ? { ListingSourceId: null } : {})
    }
    this.modalRef.hide()
    this._store.dispatch(new UpdateReferencePayload(payload))
  }
  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
