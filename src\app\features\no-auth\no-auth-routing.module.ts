import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LeadQRFormComponent } from 'src/app/features/no-auth/lead-qr-form/lead-qr-form.component';
import { ProjectMicrositeComponent } from './project-microsite/project-microsite.component';
import { PropertyMicrositeComponent } from './property-microsite/property-microsite.component';
import { ReferEarnComponent } from './refer-earn/refer-earn.component';

export const routes: Routes = [
    { path: '', redirectTo: 'add-lead/qr-code/:templateId', pathMatch: 'full' },
    {
        path: 'add-lead/qr-code/:templateId',
        component: LeadQRFormComponent,
    },
    {
        path: 'property-preview/:userName/:serialNo',
        component: PropertyMicrositeComponent,
    },
    {
        path: 'listing-preview/:userName/:serialNo',
        component: PropertyMicrositeComponent,
    },
    {
        path: 'project-preview/:userName/:serialNo',
        component: ProjectMicrositeComponent,
    },
    {
        path: 'refer-earn',
        component: ReferEarnComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class NoAuthRoutingModule { }
