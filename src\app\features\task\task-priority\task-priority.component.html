<ng-container *ngIf="params.data.priority === 'Low'">
    <p class="text-green">{{ params.data.priority | titlecase}}</p>
</ng-container>
<ng-container *ngIf="params.data.priority === 'Medium'">
    <p class="text-yellow">{{ params.data.priority | titlecase}}</p>
</ng-container>
<ng-container *ngIf="params.data.priority === 'High'">
    <p class="text-light-orange">{{ params.data.priority | titlecase}}</p>
</ng-container>
<ng-container *ngIf="params.data.priority === 'Critical'">
    <p class="text-red">{{ params.data.priority | titlecase}}</p>
</ng-container>
