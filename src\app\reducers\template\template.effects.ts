import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { map, switchMap, catchError,withLatestFrom } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AddTemplate,
  AddTemplateSuccess,
  DeleteTemplate,
  FetchTemplateModule,
  FetchTemplateModuleSuccess,
  TemplateActionTypes,
  UpdateTemplate,
  UpdateTemplateSuccess,
} from 'src/app/reducers/template/template.actions';
import { getTemplatesModule } from './template.reducer';
import { TemplateService } from 'src/app/services/controllers/template.service';

@Injectable()
export class TemplateEffects {
  getModuleTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TemplateActionTypes.FETCH_TEMPLATE_MODULE),
      withLatestFrom(this.store.select(getTemplatesModule)), 
      switchMap(([action, data]) => {
        const { payload, searchTerm } = action;
        const { pageNumber, pageSize } = data;

        return this.api.getModuleTemplate(payload, pageNumber, pageSize, searchTerm).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchTemplateModuleSuccess(resp);
            }
            return new FetchTemplateModuleSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TemplateActionTypes.ADD_TEMPLATE),
      switchMap((action: AddTemplate) => {
        return this.api.add(action.payload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Template added successfully.`);
              return [
                new FetchTemplateModule(action.payload?.moduleName),
                new AddTemplateSuccess(resp)
              ];
            }
            return of(new FetchTemplateModuleSuccess());
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );
  

  updateTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TemplateActionTypes.UPDATE_TEMPLATE),
      switchMap((action: UpdateTemplate) => {
        return this.api.updateTemplate(action.payload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Template updated successfully.`
              );
              return [
                new FetchTemplateModule(action.payload?.moduleName),
                new UpdateTemplateSuccess(resp)
              ];
            }
            return of(new FetchTemplateModuleSuccess());
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteTemplate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TemplateActionTypes.DELETE_TEMPLATE),
      map((action: DeleteTemplate) => action),
      switchMap((action: DeleteTemplate) => {
        return this.api.delete(action.payload?.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Template deleted successfully.`
              );
              return new FetchTemplateModule(action.payload?.moduleName);
            }
            return new FetchTemplateModuleSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );
  constructor(
    private actions$: Actions,
    private api: TemplateService,
    private store: Store<AppState>,
    private _notificationService: NotificationsService
  ) { }
}
