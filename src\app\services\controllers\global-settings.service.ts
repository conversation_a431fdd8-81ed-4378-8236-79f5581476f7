import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
import { Observable } from 'rxjs';
import { CommonService } from '../shared/common.service';

@Injectable({
  providedIn: 'root',
})
export class GlobalSettingsService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(
    private http: HttpClient,
    private commonService: CommonService
  ) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'globalsettings';
  }

  getGlobalSettingsAnonymous() {
    return this.http.get(`${this.serviceBaseUrl}/anonymous`);
  }

  updateGlobalSettings(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}`,payload);
  }

  getDuplicateSettings() {
    return this.http.get(`${this.serviceBaseUrl}/duplicatefeature`);
  }

  updateAllowDuplicatesSettings(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/duplicatefeature`, payload);
  }

  updateMandatoryNotesSettings(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/notes`, payload);
  }

  updateMandatoryProjectsSettings(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/project`, payload);
  }

  updateMandatoryPropertySettings(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/property`, payload);
  }

  getDuplicateSettingsAnonymous() {
    return this.http.get(`${this.serviceBaseUrl}/duplicatefeature/anonymous`);
  }

  getAllCurrencies() {
    return this.http.get(`${this.serviceBaseUrl}/countriesinfo`);
  }

  updateOTP(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/otp`,payload);
  }

  getOTPGlobalSettings(){
    return this.httpClient.get(`${this.serviceBaseUrl}/otp/anonymous`);
  }

  getTempVariables(){
    return this.httpClient.get(`${this.serviceBaseUrl}/tempvariable`);
  }

  getAllSources() {
    return this.http.get(`${env.baseURL}${env.apiURL}/source`);
  }
}
