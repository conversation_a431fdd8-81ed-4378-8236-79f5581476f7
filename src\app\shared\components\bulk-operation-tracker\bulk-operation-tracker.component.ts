import { Component, EventEmitter, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE } from 'src/app/app.constants';
import { FileUploadStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  getTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchBulkOperation } from 'src/app/reducers/lead/lead.actions';
import {
  getBulkOperation,
  getBulkOperationIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { environment as env } from 'src/environments/environment';
@Component({
  selector: 'bulk-operation-tracker',
  templateUrl: './bulk-operation-tracker.component.html',
})
export class BulkOperationTrackerComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  rowData: any = [];
  allUsers: any[] = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  defaultColDef: any;
  totalUploadedCount: number;
  s3BucketUrl: string = env.s3ImageBucketURL;
  getPages = getPages;
  @Input() moduleType: string;
  fieldType: string;
  isBulkOperationLoading: boolean = true;


  userData: any;
  filtersPayload: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    public trackingService: TrackingService
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();
  }

  ngOnInit(): void {
    if (this.moduleType) {
      this.filtersPayload = {
        pageNumber: 1,
        pageSize: this.pageSize,
        path: 'lead',
        Type: this.moduleType
      };
      this.trackingService.trackFeature(`Web.${this.moduleType?.replace(/\s+/g, '').replace(/^./, (match: any) => match.toUpperCase())}.Page.BulkOperationTracker.Visit`)
    }

    this._store.dispatch(new FetchUsersListForReassignment());
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const sortedUsers = data?.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
      });

    this._store
      .select(getBulkOperation)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data?.items;
        this.totalUploadedCount = data?.totalCount;
      });

    this._store
      .select(getBulkOperationIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isBulkOperationLoading = isLoading;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 85,
        valueGetter: (params: any) => [FileUploadStatus[params.data?.status]],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Type',
        field: 'Type',
        minWidth: 110,
        filter: false,
        valueGetter: (params: any) => [params.data?.classType],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`
            ? `<p class="text-truncate-1 break-all">${params.value}</p>`
            : '';
        },
      },
      {
        headerName: 'Done By',
        field: 'Done By',
        minWidth: 180,
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.allUsers, true) ||
          '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap text-truncate-1 break-all">${params.value[0]
            }</p>
            <p class="text-nowrap text-truncate-1 break-all">${params.value[1]
            }</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Total Count',
        field: 'Total Count',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.totalCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Unique Count',
        field: 'Unique Count',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.distinctCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params?.value}</p>`;
        },
      },
      {
        headerName: 'Updated Count',
        field: 'Updated Count',
        minWidth: 110,
        filter: false,
        valueGetter: (params: any) => [params.data?.updatedCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Duplicate Count',
        field: 'Duplicate Count',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.duplicateCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Invalid Count',
        field: 'Invalid Count',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.invalidCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Error Message',
        field: 'Error Message',
        minWidth: 180,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.message ? params.data?.message : '--',
        ],
        cellRenderer: (params: any) => {
          const message = params.value[0];
          const isError = message !== '--';
          const className = isError ? 'text-danger' : '';
          return `<p class="${className} text-truncate-2 break-all text-sm">${message}</p>`;
        },
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.trackingService.trackFeature(`Web.${this.moduleType?.replace(/\s+/g, '').replace(/^./, (match: any) => match.toUpperCase())}.Button.BulkOperationPagination.Click`)
    this.updateTrackerList(this.filtersPayload.Type);
  }

  updateTrackerList(moduleType: any) {
    this._store.dispatch(
      new FetchBulkOperation(
        this.filtersPayload?.pageNumber,
        this.filtersPayload.pageSize,
        moduleType)
    );
    this.trackingService.trackFeature(`Web.${this.moduleType?.replace(/\s+/g, '').replace(/^./, (match: any) => match.toUpperCase())}.Button.BulkOperationRefresh.Click`)
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
