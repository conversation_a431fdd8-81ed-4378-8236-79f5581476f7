import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import {
  FetchPlacesList,
  FetchPlacesListSuccess,
  FetchQRPlacesList,
  FetchQRPlacesListSuccess,
  PlacesActionTypes,
} from './places.actions';
import { OnError } from 'src/app/app.actions';
import { PlacesService } from 'src/app/services/controllers/places.service';

@Injectable()
export class PlacesEffects {
  getPlacesList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PlacesActionTypes.FETCH_PLACES_LIST),
      map((action: FetchPlacesList) => action),
      switchMap((data: any) => {
        return this.api.getPlacesList(data.payload, data.page, data.pagesize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPlacesListSuccess(resp.items || resp.data);
            }
            return new FetchPlacesListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getQRPlacesList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PlacesActionTypes.FETCH_QR_PLACES_LIST),
      map((action: FetchQRPlacesList) => action),
      switchMap((data: any) => {
        return this.api.getQRPlacesList(data.payload, data.page, data.pagesize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchQRPlacesListSuccess(resp.items || resp.data);
            }
            return new FetchQRPlacesListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: PlacesService,
  ) { }
}