import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { FetchInvoiceByIdSuccess, InvoiceActionTypes } from 'src/app/reducers/invoice/invoice.actions';

export type InvoiceState = {
  bookingData: Array<any>;
};

const initialState: InvoiceState = {
  bookingData: [],
};

export function invoiceReducer(
  state: InvoiceState = initialState,
  action: Action
): InvoiceState {
  switch (action.type) {
    case InvoiceActionTypes.FETCH_INVOICE_BY_ID_SUCCESS:
      const fetchInvoiceByIdSuccessAction = action as FetchInvoiceByIdSuccess;
      return {
        ...state,
        bookingData: fetchInvoiceByIdSuccessAction.response,
      };

    default:
      return state;
  }
}

export const selectInvoiceState = (state: AppState) => state.invoice;

export const getBookingData = createSelector(
  selectInvoiceState,
  (state: InvoiceState) => state.bookingData
);
