import {
  Component,
  EventE<PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { MATCHING_RADIUS_LIST, PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { BHKType, EnquiryType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  formatBudget,
  getBHKDisplayString,
  getLocationDetailsByObj,
  getPages,
} from 'src/app/core/utils/common.util';
import { LeadsShareDataComponent } from 'src/app/features/property/matching-leads//leads-share-data/leads-share-data.component';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { UpdateFilterPayload } from 'src/app/reducers/lead/lead.actions';
import { FetchMatchingLeadsList } from 'src/app/reducers/property/property.actions';
import { getMatchingLeads } from 'src/app/reducers/property/property.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';

@Component({
  selector: 'matching-leads',
  templateUrl: './matching-leads.component.html',
})
export class MatchingLeadsComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  params: any;
  gridOptions: any;
  rowData: any = [];
  pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  private filtered_list: Array<any> = [];
  currOffset: number = 0;
  totalMatchCount: number;
  selectedPageSize: number;
  searchTerm: string;
  filtersPayload: any = {
    path: 'property/matchingleads',
    id: null,
    search: null,
  };
  getPages = getPages;
  BHKType = BHKType;
  EnquiryType = EnquiryType;
  defaultColumns: any;
  columns: any;
  defaultCurrency: string = '';
  isProject: boolean;
  isProjectUnit: boolean;
  paramsValue: any;
  matchingRadiusList: any[] = MATCHING_RADIUS_LIST;
  matchingRadius: number;
  isListing: boolean = window.location.pathname.includes('listing');


  constructor(
    private _store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private gridOptionsService: GridOptionsService,
    public router: Router,
    public trackingService: TrackingService
  ) {
    super();
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowData = this.rowData;
  }

  agInit(params: any): void {
    if (params.value?.[0] === "Projects") {
      this.isProject = true;
      this.isProjectUnit = false;
    } else if (params.value?.[0] === "ProjectsUnit") {
      this.isProjectUnit = true;
      this.isProject = false;
    } else {
      this.isProject = false;
      this.isProjectUnit = false;
    }
    this.isListing = window.location.pathname.includes('listing');
    this.paramsValue = params;
    this.params = params;
    this.params = {
      ...this.params?.data,
      budget: this.params?.data?.monetaryInfo?.expectedPrice
        ? formatBudget(
          this.params?.data?.monetaryInfo?.expectedPrice,
          this.params?.data?.monetaryInfo?.currency || this.defaultCurrency
        )
        : null,
      price: this.params?.data?.price
        ? formatBudget(
          this.params?.data?.price,
          this.params?.data?.currency || this.defaultCurrency
        )
        : null,

      area: this.params.data?.dimension
        ? this.params.data?.dimension?.area +
        ' ' +
        this.params.data?.dimension?.unit
        : '',
      location: getLocationDetailsByObj(this.params?.data?.address),
    };
    this.searchTermSubject.subscribe(() => {
      this.filtersPayload = {
        ...this.filtersPayload,
        pageNumber: 1,
      };
      this.filterFunction();
    });
    this.initializeGridSettings();
  }

  get sortOrder(): 'asc' | 'desc' {
    return this.filtersPayload?.['SortingCriteria.IsAscending'] == true
      ? 'asc'
      : this.filtersPayload?.['SortingCriteria.IsAscending'] == false
        ? 'desc'
        : undefined;
  }

  ngOnInit() {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Lead Name',
        field: 'Lead Name',
        valueGetter: (params: any) => [this.isProject ? params?.data?.name : params?.data?.lead?.name],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 text-sm">${params.value}</p>`;
        },
      },
      {
        headerName: this.isProject
          ? 'Project Type'
          : 'Property Type',
        field: this.isProject
          ? 'projectType'
          : 'propertyType',
        valueGetter: (params: any) => {
          return this.isProject ? params?.data?.lead?.enquiry?.propertyType?.displayName : params?.data?.lead?.enquiry?.propertyTypes?.[0]?.displayName || '';
        },
        cellRenderer: (params: any) => {
          let propertyType = params.value;
          if (!this.isProject) {
            return `<p class="text-sm text-truncate-1">${propertyType}</p>`;
          }
          else {
            return `<p class="text-sm text-truncate-1">
            ${[
                params.data?.enquiry?.propertyTypes?.[0]?.displayName,
                params.data?.enquiry?.propertyTypes?.map((item: any) => item?.childType?.displayName),
                params.data?.enquiry?.bhKs ? params.data.enquiry.bhKs.map((index: any) => getBHKDisplayString(index)).join(', ') : '',
                params.data?.enquiry?.bhkTypes ? params.data.enquiry.bhkTypes.map((index: any) => BHKType[index]).join(', ') : ''
              ].filter(item => item).join(', ')
              }</p>`;
          }
        },
      },
      {
        headerName: 'Budget',
        field: 'Budget',
        minWidth: 120,
        valueGetter: (params: any) => [
          params?.data?.enquiry?.lowerBudget
            ? formatBudget(
              params?.data?.enquiry?.lowerBudget,
              params?.data?.enquiry?.currency || this.defaultCurrency
            )
            : params?.data?.lead?.enquiry?.lowerBudget
              ? formatBudget(
                params?.data?.lead?.enquiry?.lowerBudget,
                params?.data?.lead?.enquiry?.currency || this.defaultCurrency
              )
              : '',
          params.data.enquiry?.upperBudget
            ? formatBudget(
              params?.data?.enquiry?.upperBudget,
              params?.data?.enquiry?.currency || this.defaultCurrency
            )
            : params?.data?.lead?.enquiry?.upperBudget
              ? formatBudget(
                params?.data?.lead?.enquiry?.upperBudget,
                params?.data?.lead?.enquiry?.currency || this.defaultCurrency
              )
              : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate">${params.value[0] ? 'Min.: ' + params.value[0] : ''
            }</p> <p class="text-sm text-truncate">${params.value[1] ? 'Max.: ' + params.value[1] : ''
            }</p>`;
        },
      },
      {
        headerName: 'Enquired For',
        field: 'enquiredFor',
        minWidth: 110,
        valueGetter: (params: any) => {
          const enquiryTypes = this.isProject ? params?.data?.enquiry?.enquiryTypes : params?.data?.lead?.enquiry?.enquiryTypes;
          if (!enquiryTypes) return [''];

          return enquiryTypes.map((enquiry: any) => EnquiryType[enquiry] === 'None' ? '' : EnquiryType[enquiry]);
        },
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Enquired Location',
        field: 'Enquired Location',
        valueGetter: (params: any) => {
          const addresses = this.isProject ? params?.data?.enquiry?.addresses : params?.data?.lead?.enquiry?.addresses;
          if (!addresses) return '';

          return addresses.map((address: any) => getLocationDetailsByObj(address)).join('; ');
        },
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate" title='${params?.value}'>${params?.value}</p>`;
        },
        cellClass: 'pe-none',
      },
      {
        headerName: 'Actions',
        menuTabs: [],
        filter: false,
        maxWidth: 110,
        minWidth: 110,
        valueGetter: (params: any) => [
          !this.isProject ? this.params : this.paramsValue?.value?.[1],
          params.data?.contactNo,
          params.data?.email,
          () => {
            this.modalRef?.hide();
          },
          this.paramsValue.value
        ],
        cellRenderer: LeadsShareDataComponent,
      },
    ];

    if (!this.isProject) {
      const numberOfMatchField = this.gridOptions.columnDefs.findIndex((col: any) => col.headerName === 'Enquired Location');
      const newColumnDef = {
        headerName: 'No. Of Match Fields',
        field: 'numberOfMatchField',
        hide: true,
        valueGetter: (params: any) => {
          return `${params.data.noOfFieldsMatched}/${params.data.totalNoOfFields}`;
        },
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate">${params.value}</p>`;
        },
      };

      if (numberOfMatchField !== -1) {
        this.gridOptions.columnDefs.splice(numberOfMatchField + 1, 0, newColumnDef);
      } else {
        this.gridOptions.columnDefs.push(newColumnDef);
      }

      const percentageOfMatchFieldColumn = this.gridOptions.columnDefs.findIndex((col: any) => col.headerName === 'No. Of Match Fields');
      const newColumnDef2 = {
        headerName: 'Percentage Of Match Fields',
        field: 'percentageOfMatchField',
        hide: true,
        valueGetter: (params: any) =>
          params.data.percentageOfFieldsMatched || '',
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate">${params.value}</p>`;
        },
      };
      if (percentageOfMatchFieldColumn !== -1) {
        this.gridOptions.columnDefs.splice(percentageOfMatchFieldColumn + 1, 0, newColumnDef2);
      } else {
        this.gridOptions.columnDefs.push(newColumnDef2);
      }

    }

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.columns = this.gridColumnApi.getColumns();
    this.columns = this.columns.map((column: any) => {
      return {
        label: column.getColDef().headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(2, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));
    this.defaultColumns = this.columns.filter(
      (col: any) => col.value.getColDef().hide !== true
    );

    if (this.onColumnMoved) {
      this.gridOptions.onColumnMoved = this.onColumnMoved;
    }

    let columnData = localStorage
      .getItem('machingPropertyColumn')
      ?.split(',').slice(0, 3);
    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
    this.customizeHeader();
  }

  customizeHeader() {
    const columnDefs = this.gridApi?.getColumnDefs();

    const columnToModify: any = columnDefs.find(
      (column: any) =>
        column.colId === this.filtersPayload?.['SortingCriteria.ColumnName']
    );
    if (columnToModify) {
      columnToModify.sort = this.sortOrder;
      this.gridApi.setColumnDefs(columnDefs);
    }
  }

  onColumnMoved(params: any) {
    var columnState = JSON.stringify(
      params?.columnApi?.getColumnState()?.map((column: any) => ({
        ...column,
        sort: null,
      }))
    );
    localStorage.setItem('machingPropertyColumn', columnState);
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('machingPropertyColumn', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col: any) => col.value),
      false
    );
  }

  getPathBasedOnParamsValue(): string {
    const firstParam = this.paramsValue.value?.[0];

    if (firstParam === "Properties") {
      return this.isListing ? 'property/listing/matchingleads' : 'property/matchingleads';
    } else if (firstParam === "ProjectsUnit") {
      return 'project/matchingleads';
    } else {
      return 'project/matchingleadsbyproject';
    }
  }

  openMatchLeadsModal(matchLeadModal: TemplateRef<any>) {
    if (this.paramsValue.value?.[0] === "Properties") {
      this.trackingService.trackFeature(`Web.Property.Button.MatchingLead.Click`)
    } else {
      this.trackingService.trackFeature(`Web.Project.Matching.Lead.Click`)
    }
    const path = this.getPathBasedOnParamsValue();
    this.filtersPayload = {
      id: this.params?.id,
      path: path,
      pageNumber: 1,
      pageSize: 10,
    };
    this._store.dispatch(new FetchMatchingLeadsList(this.filtersPayload));

    this._store
      .select(getMatchingLeads)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items || [];
        this.totalMatchCount = data.totalCount;
      });
    this.modalRef = this.modalService.show(matchLeadModal, {
      class: 'right-modal modal-1000 tb-modal-unset',
    });
  }

  filterFunction() {
    const path = this.getPathBasedOnParamsValue();
    this.filtersPayload = {
      ...this.filtersPayload,
      path: path,
      search: this.searchTerm,
      RadiusInKms: this.matchingRadius ? this.matchingRadius : null,
    };
    this._store.dispatch(new FetchMatchingLeadsList(this.filtersPayload));
    this._store
      .select(getMatchingLeads)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items || [];
        this.totalMatchCount = data.totalCount;
      });
    this.currOffset = 0;
  }

  assignCount(e: number) {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };

    this.filterFunction();
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    if (this.filtered_list.length > 0) {
      this.gridApi.applyTransaction({ add: this.filtered_list });
    } else {
      this.gridApi.applyTransaction({ add: this.rowData });
    }
    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    const path = this.getPathBasedOnParamsValue();
    this.filtersPayload = {
      ...this.filtersPayload,
      path: path,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this._store.dispatch(new FetchMatchingLeadsList(this.filtersPayload));
    this._store
      .select(getMatchingLeads)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items || [];
        this.totalMatchCount = data.totalCount;
      });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col: any) => col.value.getColDef().hide! == true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  getMatchingLead() {
    const subtractPercentage = (amount: any, percentage: any) =>
      Math.floor(amount - amount * (percentage / 100));
    const addPercentage = (amount: any, percentage: any) =>
      Math.ceil(amount + amount * (percentage / 100));
    const originalMinBudget = this.params?.monetaryInfo?.expectedPrice || 0;
    const originalMaxBudget = this.params?.monetaryInfo?.expectedPrice || 0;
    const adjustedMinBudget = subtractPercentage(originalMinBudget, 20);
    const adjustedMaxBudget = addPercentage(originalMaxBudget, 20);
    const propEnquiredFor = EnquiryType[this.params?.enquiredFor];
    let enquiredFor: any = [];
    if (propEnquiredFor === 'Rent') {
      enquiredFor = ['Rent'];
    } else if (propEnquiredFor === 'Buy') {
      enquiredFor = ['Sale'];
    } else if (propEnquiredFor === 'Sale') {
      enquiredFor = ['Buy'];
    }
    let filterPayload: any = {
      PropertyType: [this.params?.propertyType?.id],
      PropertySubType: [this.params?.propertyType?.childType?.id],
      NoOfBHKs: [this.params?.noOfBHK.toString()],
      States: [this.params?.address?.state],
      enquiredFor: enquiredFor,
      MinBudget: adjustedMinBudget,
      MaxBudget: adjustedMaxBudget,
      pageNumber: 1,
      pageSize: 10,
      Currency: this.params?.monetaryInfo?.currency,
    };
    if (this.params?.address?.latitude && this.params?.address?.longitude) {
      filterPayload.Longitude = this.params?.address?.longitude;
      filterPayload.Latitude = this.params?.address?.latitude;
      filterPayload.RadiusInKm = 10;
    }
    if (this.params?.propertyType?.displayName === 'Commercial' || this.params?.propertyType?.displayName === 'Agricultural') {
      filterPayload.NoOfBHKs = null;
    }
    this._store.dispatch(new UpdateFilterPayload(filterPayload));
    this.router.navigate(['leads/manage-leads']);
    this.modalRef.hide();
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
