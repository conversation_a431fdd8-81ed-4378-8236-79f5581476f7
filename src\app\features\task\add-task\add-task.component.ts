import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { TASK_PRIORITIES } from 'src/app/app.constants';
import { TodoPriority } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getAssignedToDetails,
  getTimeZoneDate,
  onPickerOpened,
  setTimeZoneDateWithTime,
  patchTimeZoneWithTime,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { AddTodo, UpdateTodo } from 'src/app/reducers/todo/todo.actions';

@Component({
  selector: 'add-task',
  templateUrl: './add-task.component.html',
})
export class AddTaskComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  taskPriorities: any = TASK_PRIORITIES;
  addTaskForm: FormGroup;
  selectedTodo: any;
  minAllowedDate: any = new Date();
  currentDate: Date = new Date();
  users: Array<any>;
  activeUsers: Array<any>;
  allActiveUsers: Array<any>;
  getAssignedToDetails = getAssignedToDetails;
  moment = moment;
  userDetails = localStorage.getItem('userDetails');
  canAssignToAny: boolean = false;
  isUserListLoading: boolean;
  userData: any;
  userBasicDetails: any;
  getTimeZoneDate = getTimeZoneDate;
  onPickerOpened = onPickerOpened

  constructor(
    private fb: FormBuilder,
    private _store: Store<AppState>,
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    private _notificationService: NotificationsService
  ) {
    this.addTaskForm = this.fb.group({
      title: ['', Validators.compose([Validators.required])],
      assignedUserIds: [JSON.parse(this.userDetails).sub, Validators.required],
      scheduledDateTime: ['', Validators.compose([Validators.required])],
      notes: ['', Validators.compose([Validators.required])],
      priority: [0, Validators.compose([Validators.required])],
    });
  }

  ngOnInit() {
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this._store.dispatch(new FetchUsersListForReassignment());
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
      });

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isUserListLoading = data));

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
        this.minAllowedDate = this.currentDate
      });

    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.users = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + " " + user.lastName
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.users = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + " " + user.lastName
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });

    if (this.selectedTodo) {
      const todaysDate = moment().format('YYYY-MM-DD');
      const selectedDate = moment(this.selectedTodo.scheduledDateTime).format('YYYY-MM-DD');
      const dayDiff = moment(selectedDate).diff(moment(todaysDate), 'days');
      if (dayDiff < 0) {
        const date = new Date(this.selectedTodo.scheduledDateTime)
        const timezoneOffset = date.getTimezoneOffset() * 60000;
        const localDate = new Date(date.getTime() - timezoneOffset);
        this.minAllowedDate = localDate.toString();
      }
      this.addTaskForm.patchValue({
        title: this.selectedTodo.title,
        scheduledDateTime: patchTimeZoneWithTime(
          this.selectedTodo.scheduledDateTime,
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        ),
        notes: this.selectedTodo.notes,
        priority: TodoPriority[this.selectedTodo.priority],
        assignedUserIds: this.selectedTodo.assignedUsers?.[0]?.id
      });
    }
  }

  doSubmit() {
    if (!this.addTaskForm.valid) {
      validateAllFormFields(this.addTaskForm);
      return;
    }
    const userData = this.addTaskForm.value;
    const scheduledDateTime = new Date(userData.scheduledDateTime);
    const currentDateTime = new Date(this.currentDate);
    if (scheduledDateTime <= currentDateTime) {
      this._notificationService.warn(`Date and time must be in the future.`);
      return;
    }
    const payload = {
      ...userData,
      scheduledDateTime: setTimeZoneDateWithTime(
        userData.scheduledDateTime,
        this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      isMarkedDone: false,
      assignedUserIds: [userData?.assignedUserIds] || [
        this.selectedTodo?.assignedUserIds,
      ],
      ...(this.selectedTodo && {
        id: this.selectedTodo.id,
      }),
    };
    if (this.selectedTodo) {
      this._store.dispatch(new UpdateTodo(this.selectedTodo.id, payload));
    } else {
      this._store.dispatch(new AddTodo(payload));
    }
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
