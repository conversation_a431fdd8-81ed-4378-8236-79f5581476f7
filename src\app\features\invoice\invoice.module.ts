import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';

import { CustomizationAddLeadComponent } from '../leads/customization-add-lead/customization-add-lead.component';
import { ManageLeadsComponent } from '../leads/manage-leads.component';

const routes: Routes = [
  { path: '', component: ManageLeadsComponent },
  { path: 'edit-invoice/:id', component: CustomizationAddLeadComponent },
];

@NgModule({
  declarations: [],
  imports: [CommonModule, RouterModule.forChild(routes)],
  providers: [TranslatePipe],
})
export class InvoiceModule { }
