import { Component, EventEmitter, Input, OnDestroy, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { skipWhile, takeUntil } from 'rxjs/operators';

import { IMAGES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { AddMicrositeLead } from 'src/app/reducers/Integration/integration.actions';
import { getMSleadIsLoading } from 'src/app/reducers/Integration/integration.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchMicrositeUserDetails } from 'src/app/reducers/property/property.actions';
import { getMicrositeUserDetails } from 'src/app/reducers/property/property.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'enquiry-form',
  templateUrl: './enquiry-form.component.html',
})
export class EnquiryFormComponent implements  OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  serialNo: any;
  inquiryForm: FormGroup;
  userName: string;
  s3BucketUrl = env.s3ImageBucketURL;
  images: string = IMAGES.call;
  mobileNumber: string;
  servingCities: any = {};
  cityList: string[] = [];
  userData: any;
  MSleadIsLoading: boolean = false;
  preferredCountries = ['in'];
  hasInternationalSupport: boolean = false;
  @ViewChild('contactNoInput') contactNoInput: any;
  resetNumber: boolean = false;
  @Input() isPropertySoldOut: boolean = false;

  constructor(
    private fb: FormBuilder,
    public router: Router,
    public modalRef: BsModalRef,
    private store: Store<AppState>
  ) {
    if (location.href.split('/').length > 1) {
      const router = location.href.split('/');
      this.userName = router[router.length - 2];
      this.serialNo = router[router.length - 1];
      this.store.dispatch(new FetchMicrositeUserDetails(this.userName));
    }

    this.store
      .select(getMicrositeUserDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.inquiryForm = this.fb.group({
      name: ['', Validators.required],
      contactNo: ['', [this.contactNumberValidator('primary')]],
      agreeTerms: [true],
    });

    this.store
    .select(getGlobalSettingsAnonymous)
    .pipe(takeUntil(this.stopper))
    .subscribe((data: any) => {
      this.hasInternationalSupport = data?.hasInternationalSupport;
      this.preferredCountries = data?.hasInternationalSupport ? data?.countries?.length ? [data.countries[0].code?.toLowerCase()] : ['in'] : ['in'];
    });
  }

  
  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      if (numType == 'primary') {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          numType === 'primary' ? this.contactNoInput?.value : control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  postLead() {
    if (!this.inquiryForm.valid) {
      validateAllFormFields(this.inquiryForm);
      return;
    }
    const payload = {
      name: this.inquiryForm.value.name,
      contactNo:  this.inquiryForm.value.contactNo
      ? `${this.inquiryForm.value.contactNo?.toString()}`
      : null,
      serialNo: this.serialNo,
      userName: this.userName,
    };
    this.MSleadIsLoading = true;
    this.store.dispatch(new AddMicrositeLead(payload));
    this.store
      .select(getMSleadIsLoading)
      .pipe(skipWhile((isLoading) => isLoading))
      .subscribe((isLoading: boolean) => {
        if (!isLoading) {
          this.MSleadIsLoading = isLoading;
        }
      });
    this.inquiryForm?.reset();
    this.resetNumber = true;
    setTimeout(() => { this.resetNumber = false; }, 0);
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
