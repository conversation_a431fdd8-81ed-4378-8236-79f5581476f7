import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE } from 'src/app/app.constants';
import { FileUploadStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  getTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchExportFacebookStatus } from 'src/app/reducers/Integration/integration.actions';
import {
  getExportFacebookStatus,
  getExportFacebookStatusLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { environment as env } from 'src/environments/environment';
@Component({
  selector: 'export-facebook-tracker',
  templateUrl: './export-facebook-tracker.component.html',
})
export class ExportFacebookTrackerComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  rowData: any = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  defaultColDef: any;
  totalReqCount: number;
  s3BucketUrl: string = env.s3ImageBucketURL;
  getPages = getPages;
  isDataExportStatusLoading: boolean;
  filtersPayload = {
    pageNumber: 1,
    pageSize: this.pageSize,
    path: 'global-config/facebook',
  };

  allUserList: any;
  userData: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    public modalService: BsModalService,
    private _store: Store<AppState>
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();

    this._store
      .select(getExportFacebookStatus)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data?.items;
        this.totalReqCount = data?.totalCount;
      });
    this._store
      .select(getExportFacebookStatusLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isDataExportStatusLoading = isLoading;
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allUserList = this.allUserList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => FileUploadStatus[params.data?.status],
        cellRenderer: (params: any) => {
          return `<p>${params.value ? params.value : '--'}</p>`;
        },
      },
      {
        headerName: 'Date',
        field: 'Date',
        minWidth: 200,
        valueGetter: (params: any) => [
          params.data?.fromDate
            ? 'From ' +
            getTimeZoneDate(
              params.data?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
          params.data?.toDate
            ? 'To ' +
            getTimeZoneDate(
              params.data?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all text-nowrap mb-4">${params.value[0]}</p><p class="text-truncate-1 break-all text-nowrap">${params.value[1]}</p>`;
        },
      },
      {
        headerName: 'Requested',
        field: 'Requested',
        minWidth: 175,
        valueGetter: (params: any) => [
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
          getAssignedToDetails(params.data.createdBy, this.allUserList, true) ||
          '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all text-nowrap text-sm">${params.value[1]
            }</p>
          <p class="text-truncate-1 break-all text-nowrap text-sm mt-4">${params.value[0]
            }</p>
          <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Last Modified',
        field: 'Last Modified',
        minWidth: 175,
        valueGetter: (params: any) => [
          params.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.allUserList,
            true
          ) || '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all text-nowrap text-sm">${params.value[1]
            }</p>
          <p class="text-truncate-1 break-all text-nowrap text-sm mt-4">${params.value[0]
            }</p>
          <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      // {
      //   headerName: 'Active Ads Count',
      //   field: 'Active Ads Count',
      //   maxWidth: 140,
      //   filter: false,
      //   valueGetter: (params: any) => [params.data?.activeAdsCount ? params.data?.activeAdsCount : '--'],
      //   cellRenderer: (params: any) => {
      //     return `<p>${params.value}</p>`;
      //   },
      // },
      // {
      //   headerName: 'Active Forms Count',
      //   field: 'Active Forms Count',
      //   maxWidth: 140,
      //   filter: false,
      //   valueGetter: (params: any) => [params.data?.activeFormsCount ? params.data?.activeFormsCount : '--'],
      //   cellRenderer: (params: any) => {
      //     return `<p>${params.value}</p>`;
      //   },
      // },
      // {
      //   headerName: 'Fetched Leads Count',
      //   field: 'Fetched Leads Count',
      //   maxWidth: 150,
      //   filter: false,
      //   valueGetter: (params: any) => [params.data?.fetchedLeadsCount ? params.data?.fetchedLeadsCount : '--'],
      //   cellRenderer: (params: any) => {
      //     return `<p>${params.value}</p>`;
      //   },
      // },
      // {
      //   headerName: 'Unique Leads Count',
      //   field: 'Unique Leads Count',
      //   maxWidth: 140,
      //   filter: false,
      //   valueGetter: (params: any) => [params.data?.uniqueLeadsCount ? params.data?.uniqueLeadsCount : '--'],
      //   cellRenderer: (params: any) => {
      //     return `<p>${params.value}</p>`;
      //   },
      // },
      {
        headerName: 'Stored Leads Count',
        field: 'Stored Leads Count',
        maxWidth: 140,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.storedLeadsCount ? params.data?.storedLeadsCount : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Message',
        field: 'Message',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.error ? params.data?.error : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all text-nowrap">${params.value}</p>`;
        },
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.updateTrackerList();
  }

  updateTrackerList() {
    this._store.dispatch(
      new FetchExportFacebookStatus(
        this.filtersPayload?.pageNumber,
        this.filtersPayload?.pageSize
      )
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
