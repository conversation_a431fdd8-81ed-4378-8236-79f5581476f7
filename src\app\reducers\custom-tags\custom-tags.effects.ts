import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { firstValueFrom, from, of } from 'rxjs';
import { map, catchError, switchMap, skipWhile } from 'rxjs/operators';
import { NotificationsService } from 'angular2-notifications';

import { CreateTag, CreateTagSuccess, DeleteTagById, DeleteTagByIdSuccess, FetchIconsListSuccess, FetchTagsListByModule, FetchTagsListByModuleSuccess, FetchTagsListSuccess, FetchTagsListWithCountSuccess, TagsActionTypes, UpdateTag, UpdateTagsLists, UpdateTagsListSuccess, UpdateTagSuccess } from 'src/app/reducers/custom-tags/custom-tags.actions';
import { OnError } from 'src/app/app.actions';
import { CustomTagService } from 'src/app/services/controllers/custom-tag.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { handleCachedData } from 'src/app/core/utils/common.util';
import { MasterDataService } from 'src/app/services/controllers/master-data.service';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { getFetchModifiedDatesList } from '../master-data/master-data.reducer';

@Injectable()
export class TagEffects {

  constructor(
    private actions$: Actions,
    private customService: CustomTagService,
    private commonService: CommonService,
    private notificationsService: NotificationsService,
    private api: MasterDataService,
    private store: Store<AppState>
  ) { }

  getIconsList$ = createEffect(() => this.actions$.pipe(
    ofType(TagsActionTypes.FETCH_ICONS_LIST),
    switchMap(() => {
      return this.customService.getAllFlagIcons().pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            return new FetchIconsListSuccess(resp?.data);
          }
          this.notificationsService.error('Error', 'Failed to fetch icons list');
          return new FetchIconsListSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    }
    )
  ));

  getTagList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActionTypes.FETCH_TAGS_LIST),
      switchMap(() =>
        from(
          handleCachedData(
            'customTagsData',
            'customTagsList',
            async (): Promise<any> => {
              let value: any = await firstValueFrom(this.store.select(getFetchModifiedDatesList).pipe(skipWhile((value: any) => value.isLoading)));
              return value?.data?.Flag || null;
            },
            async () => {
              const resp: any = await firstValueFrom(this.customService.getAllCustomTag());
              return resp?.items || [];
            },
            (data, lastModified) => ({
              id: 'customTagsList',
              items: data || [],
              lastModified: lastModified,
            }),
            (data: any, isLocalData: boolean) => {
              return isLocalData ? data?.items || [] : data || [];
            }
          )
        ).pipe(
          map((list: any[]) => new FetchTagsListSuccess(list)),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getTagListWithCount$ = createEffect(() => this.actions$.pipe(
    ofType(TagsActionTypes.FETCH_TAGS_LIST_WITH_COUNT),
    switchMap(() => {
      return this.customService.getAllCustomTagWithCount().pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            return new FetchTagsListWithCountSuccess(resp);
          }
          this.notificationsService.error('Error', 'Failed to fetch tags list');
          return new FetchTagsListWithCountSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    }
    )
  ));
  createTag$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActionTypes.CREATE_TAG),
      switchMap((action: CreateTag) =>
        this.customService.createTag(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.notificationsService.success('Tag created successfully');
              return new CreateTagSuccess(resp?.data);
            }
            this.notificationsService.error('Error', 'Failed to create tag');
            return new CreateTagSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  updateTagList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActionTypes.UPDATE_TAGS_LIST),
      switchMap((action: UpdateTagsLists) =>
        this.customService.updateTags(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.notificationsService.success('Tags list updated successfully');
              return new UpdateTagsListSuccess(resp?.data);
            }
            this.notificationsService.error('Error', 'Failed to update tags list');
            return new UpdateTagsListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  deleteTagById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActionTypes.DELETE_TAG_BY_ID),
      switchMap((action: DeleteTagById) =>
        this.customService.deleteTag(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.notificationsService.success('Tag deleted successfully');
              return new DeleteTagByIdSuccess(action.id);
            }
            this.notificationsService.error('Error', 'Failed to delete tag');
            return new DeleteTagByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getTagListByModule$ = createEffect(() => this.actions$.pipe(
    ofType(TagsActionTypes.FETCH_TAGS_LIST_BY_MODULE),
    switchMap((action: FetchTagsListByModule) => {
      const payload: any = {
        Module: action?.module,
        path: '/api/v1/flags/module'
      };
      return this.commonService.getModuleListByAdvFilter(payload).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this.notificationsService.success('Successfully fetched tags list');
            return new FetchTagsListByModuleSuccess(resp?.data);
          }
          this.notificationsService.error('Error', 'Failed to fetch tags list');
          return new FetchTagsListByModuleSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    }
    )
  ));

  updateTag$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActionTypes.UPDATE_TAG),
      switchMap((action: UpdateTag) =>
        this.customService.updateTags(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.notificationsService.success('Tag updated successfully');
              return new UpdateTagSuccess(resp?.data);
            }
            this.notificationsService.error('Error', 'Failed to update tag');
            return new UpdateTagSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

}
