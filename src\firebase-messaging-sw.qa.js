importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-messaging-compat.js");

firebase.initializeApp({
    apiKey: 'AIzaSyBDiowb8mIKKJFv-EWmlztIVaf3-lTlRdg',
    authDomain: 'leadrat-black-web-qa.firebaseapp.com',
    projectId: 'leadrat-black-web-qa',
    storageBucket: 'leadrat-black-web-qa.appspot.com',
    messagingSenderId: '126219827748',
    appId: '1:126219827748:web:269d83407355aa2dc776b1',
    measurementId: 'G-ZWHBK5EEL8',
    vapidKey: 'BNNDgcG-GMR-R6UEFFlGZfUXcBA00lw8dGoZCpXYAI0sQIRrnwgr7F_pIjCi-z9LSZQxjzW5EEhRATcOEu9oewI'
});

const messaging = firebase.messaging();
self.addEventListener('push', function (event) {
    const payload = event.data ? event.data.json() : {};
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png'
        // data: { url: payload.data.click_action},
        // actions: [
        //     {
        //         action: 'reply', // Identifier for the action
        //         title: 'Reply', // Button text
        //         icon: payload.notification.icon // Optional icon for the button,
        //     },
        //     {
        //         action: 'dismiss', // Identifier for the action
        //         title: 'Dismiss', // Button text
        //         icon: payload.notification.icon // Optional icon for the button
        //     }
        // ],
    };

    event.waitUntil(
        // Retrieve all existing notifications and close them
        self.registration.getNotifications().then(function (notifications) {
            return Promise.all(notifications.map(function (notification) {
                return notification.close();
            }));
        })
            .then(function () {
                return self.registration.showNotification(notificationTitle, notificationOptions);
            })
    );
});

// self.addEventListener('notificationclick', function(event) {
//     event.notification.close();
//     console.log(event);

//     // Determine action based on notification data
//     const actionUrl =  event.notification.data.url;

//     event.waitUntil(
//         clients.openWindow(actionUrl)
//     );
// });


messaging.onBackgroundMessage(function (payload) {
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png'
    };

    self.registration.showNotification(notificationTitle, notificationOptions);
});
