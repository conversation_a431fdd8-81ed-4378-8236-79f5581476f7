<ng-container *ngIf="params.data.isArchived">
    <div class="bg-accent-green-40 text-green-900 br-50px text-center p-6 text-xs" *ngIf="params.data.currentStatus===0">
      Available
    </div>
    <div class="bg-accent-red-40 text-dark-red br-50px text-center p-6 text-xs" *ngIf="params.data.currentStatus===1">
      Unavailable
    </div>
  </ng-container>
  <ng-container *ngIf="!params.data.isArchived">
    <div class="text-center align-center" [title]="status ? 'Available' : 'Unavailable'">
      <input type="checkbox" class="toggle-switch toggle-active-sold" [(ngModel)]="status"
        (click)="canEditProperty ? openConfirmModal(params.data) : ''" [ngClass]="{'pe-none' : !canEditProperty}">
      <label for="chkToggle" class="switch-label" [ngClass]="{'pe-none' : !canEditProperty}"></label>
    </div>
  </ng-container>