import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { AppState } from 'src/app/app.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'task',
  template: `<router-outlet></router-outlet>`,
})
export class TaskComponent implements OnInit {
  currentLang: string = localStorage.getItem('locale')
    ? localStorage.getItem('locale')
    : 'en';
  constructor(
    private translate: TranslateService,
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>
  ) {}
  ngOnInit(): void {
    this.headerTitle.setLangTitle('SIDEBAR.tasks');
    this.translate.setDefaultLang('en');
    this.translate.use(this.currentLang);
  }
}
