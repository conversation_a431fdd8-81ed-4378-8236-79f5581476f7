<div class="flex-end" *ngIf="totalPages >= 1 && !isV2Pagination">
  <div class="pagination">
    <ul class="float-right">
      <li (click)="selectPage(1, $event)" [class.disabled]="currentPage == 1" class="prev">
        <a title="Go to first page"><span class="fd-peg" aria-hidden="true">&laquo;</span></a>
      </li>
      <li (click)="selectPage(currentPage - 1, $event)" [class.disabled]="currentPage == 1" class="single-prev">
        <a title="Go to previous page">&lsaquo;</a>
      </li>
      <li class="disabled" (click)="cancelEvent($event)" *ngIf="currentPage - range > 1"><span>...</span></li>
      <li *ngFor="let page of pages | async" [class.active]="page == currentPage" (click)="selectPage(page, $event)">
        <a>{{ page }}</a>
      </li>
      <li class="disabled" (click)="cancelEvent($event)" *ngIf="currentPage + range < totalPages"><span>...</span></li>
      <li *ngIf="totalPages > 1">
        <div class="no-validation px-6">
          <div *ngIf="isShowSearchIcons" (click)="toggleSearch()">
            <div class="icon ic-search ic-gray ic-xxs"></div>
          </div>
          <input *ngIf="!isShowSearchIcons" #searchInput type="number" [min]="1" (keyup.enter)="goToPage($event)"
            [(ngModel)]="inputPageNumber" class="page-search fw-600 w-28 text-center border-0 outline-0"
            autocomplete="off" />
        </div>
      </li>
      <li (click)="selectPage(currentPage + 1, $event)" [class.disabled]="currentPage == totalPages"
        class="single-next"><a title="Go to next page">&rsaquo;</a>
      </li>
      <li (click)="selectPage(totalPages, $event)" [class.disabled]="currentPage == totalPages" class="next">
        <a title="Go to last page"><span class="fd-peg" aria-hidden="true">&raquo;</span></a>
      </li>
    </ul>
  </div>
</div>
<div class="pagination-v2" *ngIf="totalPages >= 1 && isV2Pagination">
  <ul class="float-right">
    <li (click)="selectPage(1, $event)" [class.disabled]="currentPage == 1" class="prev">
      <a title="Go to first page">
        <h2 class="fd-peg" aria-hidden="true">&laquo;</h2>
      </a>
    </li>
    <li (click)="selectPage(currentPage - 1, $event)" [class.disabled]="currentPage == 1" class="single-prev">
      <a title="Go to previous page">
        <h2>&lsaquo;</h2>
      </a>
    </li>
    <li class="disabled" (click)="cancelEvent($event)" *ngIf="currentPage - range > 1"><span>...</span></li>
    <li *ngFor="let page of pages | async" [class.active]="page == currentPage" (click)="selectPage(page, $event)">
      <a>{{ page }}</a>
    </li>
    <li class="disabled" (click)="cancelEvent($event)" *ngIf="currentPage + range < totalPages"><span>...</span></li>
    <li *ngIf="totalPages > 1">
      <div class="no-validation mt-4">
        <div class="icon ic-search ic-coal ic-xxs" *ngIf="isShowSearchIcons" (click)="toggleSearch()"></div>
        <input *ngIf="!isShowSearchIcons" #searchInput type="number" [min]="1" (keyup.enter)="goToPage($event)"
          [(ngModel)]="inputPageNumber" class="fw-600 w-28 text-center border-0 outline-0"
          autocomplete="off" />
      </div>
    </li>
    <li (click)="selectPage(currentPage + 1, $event)" [class.disabled]="currentPage == totalPages" class="single-next">
      <a title="Go to next page">
        <h2>&rsaquo;</h2>
      </a>
    </li>
    <li (click)="selectPage(totalPages, $event)" [class.disabled]="currentPage == totalPages" class="next">
      <a title="Go to last page">
        <h2 class="fd-peg" aria-hidden="true">&raquo;</h2>
      </a>
    </li>
  </ul>
</div>
<ng-template #pageNotFoundTemplate>
  <div class="p-20">
    <h3 class="text-center">{{ pageNotFoundMessage }}</h3>
    <div class="mt-20 flex-end">
      <button type="button" class="btn-green" (click)="modalRef.hide()">OK</button>
    </div>
  </div>
</ng-template>