<div class="d-flex ip-flex-col w-100 mt-10" *ngIf="projectInfo?.videos?.[0]">
    <div class="w-60pr ip-w-100" *ngIf="selectedVideo">
        <div class="mr-10">
            <video class="br-12 w-100 h-450 obj-fill" controls #videoPlayer>
                <source [src]="selectedVideo?.imageFilePath" type="video/mp4">
                {{ selectedVideo?.imageFilePath }}
            </video>
        </div>
    </div>
    <div class="w-40pr ip-w-100 flex-col my-10 scrollbar max-h-430 scroll-hide">
        <ng-container *ngFor="let video of projectInfo?.videos; let i = index">
            <div class="d-flex w-100 cursor-pointer" (click)="selectVideo(i)">
                <div class="position-relative">
                    <video class="mr-24 w-150 h-100px obj-fill mb-10 br-4"
                        [ngClass]="{'border-green-2': video === selectedVideo}">
                        <source [src]="video?.imageFilePath" type="video/mp4">
                    </video>
                    <div class="position-absolute top-45 right-90">
                        <img src="../../../../assets/images/video-btn.svg" class="w-24 h-24">
                    </div>
                </div>
                <div class="text-black-200 mr-20 flex-center">
                    <h5 class="fw-700 text-truncate-1 break-all">{{video.name}}</h5>
                    <!-- <div class="text-sm"> {{getLocationDetailsByObj(projectInfo?.address)}}</div> -->
                </div>
                <!-- <div class="d-flex">
                    <div class="icon ic-secondary-clock ic-gray ic-xxs mr-4"></div>
                    <div class="text-xs">5:20 Mins</div>
                </div> -->
            </div>
        </ng-container>
    </div>
</div>