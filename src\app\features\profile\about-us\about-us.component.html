<ng-container *ngIf="!isProfileLoading;else loader">
  <form [formGroup]="aboutUsForm" class="tb-mb-60">
    <div class="position-absolute right-20 ntop-65 align-center tb-d-none"
      *ngIf="isAboutUsEditable || isVisionEditable || isWhatWeDoEditable">
      <button class="btn-gray" (click)="cancelChanges()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal ml-20" (click)="onSave()">{{ 'BUTTONS.save' | translate }}</button>
    </div>
    <div class="d-flex tb-flex-col">
      <div class="w-40pr p-20 bg-white br-8 tb-w-100 tb-mb-20">
        <div class="flex-between mb-20">
          <h5 class="field-label clear-margin">{{'PROFILE.about-us' | translate}}</h5>
          <div title="Edit" *ngIf="canEditProfile" class="icon ic-pen ic-sm ic-accent-green cursor-pointer"
            (click)="isAboutUsEditable = true"></div>
        </div>
        <div>
          <form-errors-wrapper [ngClass]="{'no-validation' : !isAboutUsEditable}">
            <textarea rows="13" id="txtProfAddr" data-automate-id="txtProfAddr" [readOnly]="!isAboutUsEditable"
              formControlName="aboutUs" class="scrollbar" [ngClass]="isAboutUsEditable ? 'editable' : 'border-white'"
              [ngClass]="isAboutUsEditable ? 'editable' : 'border-white'"
              [placeholder]="isAboutUsEditable ? 'ex. A company known by its legacy. Our company is one of the top real estate developers and builders in Bangalore, having an illustrious past with landmark developments across Residential apartments, state-of-the-art Retail & Commercial spaces. Backed by strong lineage, our company has carved a name for itself, associated with trust, quality, and sophistication.': 'No Data About Your Company'"></textarea>
          </form-errors-wrapper>
        </div>
      </div>
      <div class="w-40pr float-start tb-w-100 tb-mb-20">
        <div class="ml-16 bg-white p-20 br-8  tb-ml-0">
          <div class="flex-between mb-20">
            <h5 class="field-label clear-margin">{{'PROFILE.mission' | translate}}</h5>
            <div title="Edit" *ngIf="canEditProfile" class="icon ic-pen ic-sm ic-accent-green cursor-pointer"
              (click)="isVisionEditable = true"></div>
          </div>
          <div>
            <form-errors-wrapper [ngClass]="{'no-validation' : !isVisionEditable}">
              <textarea rows="3" id="txtProfAddr" data-automate-id="txtProfAddr" [readOnly]="!isVisionEditable"
                formControlName="mission" class="scrollbar" [ngClass]="isVisionEditable ? 'editable' : 'border-white'"
                [placeholder]="isVisionEditable ? 'ex. Our vision is to create homes that personify luxurious living in some of the most prime locations within the city and provide premium amenities and features that can only be marveled. ': 'No Data'"></textarea>
            </form-errors-wrapper>
          </div>
        </div>
        <div class="ml-16 bg-white p-20 br-8 mt-12  tb-ml-0">
          <div class="flex-between mb-20">
            <h5 class="field-label clear-margin">{{'PROFILE.what-we-do'| translate }}</h5>
            <div title="Edit" *ngIf="canEditProfile" class="icon ic-pen ic-sm ic-accent-green cursor-pointer"
              (click)="isWhatWeDoEditable = true"></div>
          </div>
          <div>
            <form-errors-wrapper [ngClass]="{'no-validation' : !isWhatWeDoEditable}">
              <textarea rows="3" id="txtProfAddr" data-automate-id="txtProfAddr" [readOnly]="!isWhatWeDoEditable"
                formControlName="whatWeDo" class="scrollbar"
                [ngClass]="isWhatWeDoEditable ? 'editable' : 'border-white'"
                [placeholder]="isWhatWeDoEditable ? 'ex. Redefining living spaces and creating iconic landmarks through an unswerving passion for quality and the philosophy of self-reliance. ': 'No Data'"></textarea>
            </form-errors-wrapper>
          </div>
        </div>
      </div>
      <div class="w-20 float-start tb-w-100 tb-mb-20">
        <div class="ml-16 bg-white p-20 br-8 tb-ml-0">
          <div class="field-label clear-margin mb-10">{{ 'PROFILE.social-media' | translate }}</div>
          <div *ngFor="let socialMedia of socialMedias" class="flex-between mt-20">
            <div class="align-center">
              <img [type]="'leadrat'" [appImage]='socialMedia.imagePath' width="14" height="14" alt="noimg" class="mr-8 ml-4" />
              <a target="_blank" [ngClass]="{ 'pe-none' : isNotLinked(socialMedia.displayName) }"
                [href]="socialMedia.baseUrl + getIdbyScName(socialMedia.displayName)"
                class="text-large field-label clear-margin fw-semi-bold">{{ "PROFILE."+socialMedia.displayName |
                translate
                }}</a>
            </div>
            <div *ngIf="canEditProfile" (click)="openAddAccountModal(accountAddModal,socialMedia.displayName)">
              <a *ngIf="isNotLinked(socialMedia.displayName)" class="field-label clear-margin fw-600 text-accent-green">
                {{ 'PROFILE.link' | translate }}
              </a>
              <a *ngIf="!isNotLinked(socialMedia.displayName)" class="icon ic-link ic-red ic-xxxs"></a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>

  <ng-template #accountAddModal>
    <a class="ic-close-secondary ic-close-modal ip-ic-close-modal" (click)="closeModal()"></a>
    <div class="p-10">
      <form>
        <div class="mb-20 mx-50">
          <div class="flex-between">
            <div for="inpSocialAccount" class="field-label-req">{{ currentSocialMediaName === 'whatsapp' ?
              'Whatsapp number' : 'INTEGRATION.account-name' | translate }}</div>
            <div *ngIf="currentSocialMediaName === 'whatsapp'" class="mt-16 fw-semi-bold text-xxs text-red">( Include
              Country code)
            </div>
          </div>
          <form-errors-wrapper
            [label]="currentSocialMediaName === 'whatsapp' ? 'Whatsapp number' : 'INTEGRATION.account-name' | translate"
            [control]="socialMediaId">
            <input [type]="currentSocialMediaName === 'whatsapp' ? 'number' : 'text'" required
              [formControl]="socialMediaId" name="inpSocialAccount" id="inpSocialAccount"
              data-automate-id="inpSocialAccount" autocomplete="off"
              [placeholder]="currentSocialMediaName === 'whatsapp' ? 'ex. ************' : 'ex. pampana_mounika'" />
          </form-errors-wrapper>
        </div>
        <div class="flex-center">
          <button class="btn-coal w-130" id="btnSocial" data-automate-id="btnSocial" (click)="saveSocialMediaDetails()">
            {{ 'BUTTONS.save' | translate }}
          </button>
          <button class="btn-gray w-130 ml-20" id="btnSocialCancel" data-automate-id="btnSocialCancel"
            (click)="closeModal()">
            {{ 'BUTTONS.cancel' | translate }}</button>
        </div>
      </form>
    </div>
  </ng-template>

  <ng-template #saveDataModal>
    <save-changes (SaveChanges)="saveInModal()" (DiscardChanges)="discardInModal()"
      (Hide)="closeModal()"></save-changes>
  </ng-template>

  <div class="footer align-center tb-flex-center d-none tb-d-block tb-d-flex tb-py-10"
    *ngIf="isAboutUsEditable || isVisionEditable || isWhatWeDoEditable">
    <button class="btn-gray tb-ml-30" (click)="cancelChanges()">{{ 'BUTTONS.cancel' | translate }}</button>
    <button class="btn-coal ml-20" (click)="onSave()">{{ 'BUTTONS.save' | translate }}</button>
  </div>

</ng-container>
<ng-template #loader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>