<div class="py-10 flex-between property-details" [ngClass]="{'gray-scale': isPropertySoldOut}">
  <div class="mr-20 mt-16">
    <h5 class="text-truncate-1 text-accent-green mb-4 fw-semi-bold" *ngIf="propertyInfo?.title">
      "{{propertyInfo.title}}"</h5>
    <h4 class="fw-semi-bold text-black-100 mt-2 text-truncate-2">
      <span *ngIf="propertyInfo?.noOfBHKs">{{isListing ? getBRDisplayString(propertyInfo.noOfBHKs) :
        getBHKDisplayString(propertyInfo.noOfBHKs)}}
        <span *ngIf="propertyInfo?.bhkType">{{isListing ? '': BHKType[propertyInfo?.bhkType]}}</span>
      </span>
      <span *ngIf="!(isListing && propertyInfo?.propertyType?.displayName === 'Agricultural')">
        <ng-container *ngIf="propertyInfo?.propertyType?.childType?.displayName">
          {{propertyInfo?.propertyType?.childType?.displayName}},</ng-container>
        {{propertyInfo?.propertyType?.displayName}}</span>
    </h4>
    <h5 class="fw-semi-bold text-dark-gray mt-4 text-truncate-2"><span>
        <ng-container
          *ngIf="propertyInfo?.enquiredFor !== 'None' || propertyInfo?.propertyType?.childType?.displayName">"</ng-container>{{propertyInfo?.propertyType?.childType?.displayName}}
        <ng-container *ngIf="propertyInfo?.enquiredFor !== 'None'">{{ 'GLOBAL.for' | translate }}
          {{propertyInfo?.enquiredFor}}</ng-container>
        <ng-container
          *ngIf="propertyInfo?.enquiredFor !== 'None' || propertyInfo?.propertyType?.childType?.displayName">"</ng-container>
      </span>
      <ng-container *ngIf="propertyInfo?.address && !isEmptyObject(propertyInfo?.address)">
        {{ 'GLOBAL.in' | translate}}
        <span class="fw-600 border-bottom">{{getLocationDetailsByObj(propertyInfo?.address)}}</span>
      </ng-container>
    </h5>
  </div>
  <div class="align-end flex-column">
    <div class="flex-center ph-mt-10 ph-flex-col ph-flex-start">
      <div *ngIf="propertyInfo?.monetaryInfo?.expectedPrice">
        <div class="text-xs text-black-100">{{propertyInfo?.enquiredFor === 'Rent' ? 'Rent Amount' : 'Total Price'}}
        </div>
        <h4 class="fw-600 text-nowrap mt-4">
          {{formatBudget(propertyInfo?.monetaryInfo?.expectedPrice,propertyInfo?.monetaryInfo?.currency ||
          defaultCurrency)}}</h4>
      </div>
      <div class="border-left-black-100 h-30px mt-2 mx-12 ph-d-none"
        *ngIf="propertyInfo?.monetaryInfo?.expectedPrice && propertyInfo?.monetaryInfo?.maintenanceCost"></div>
      <div *ngIf="propertyInfo?.monetaryInfo?.maintenanceCost">
        <div class="text-xs text-black-100">Maintenance Cost</div>
        <h4 class="fw-600 text-nowrap mt-4">
          {{formatBudget(propertyInfo?.monetaryInfo?.maintenanceCost,propertyInfo?.monetaryInfo?.currency ||
          defaultCurrency)}}
        </h4>
      </div>
      <div class="border-left-black-100 h-30px mt-2 mx-12 ph-d-none"
        *ngIf="propertyInfo?.monetaryInfo?.maintenanceCost && propertyInfo?.monetaryInfo?.depositAmount"></div>
      <div *ngIf="propertyInfo?.monetaryInfo?.depositAmount">
        <div class="text-xs text-black-100">Deposit/Security Amount</div>
        <h4 class="fw-600 text-nowrap mt-4">
          {{formatBudget(propertyInfo?.monetaryInfo?.depositAmount,propertyInfo?.monetaryInfo?.currency ||
          defaultCurrency)}}
        </h4>
      </div>
    </div>
    <ng-container *ngIf="propertyInfo?.monetaryInfo?.isNegotiable">
      <div class="align-center px-16 py-4 bg-black-100 br-12 w-110">
        <ng-lottie [options]='tick' height='15px' width="15px"></ng-lottie>
        <div class="text-white ml-6 text-xs">Negotiable</div>
      </div>
    </ng-container>
  </div>
</div>
<div class="prop-details d-none">
  <div class="justify-between ph-justify-between-unset text-white">
    <div>
      <h5 class="text-truncate-1 text-accent-green mb-4 fw-semi-bold" *ngIf="propertyInfo?.title">
        "{{propertyInfo.title}}"</h5>
      <h4 class="fw-semi-bold">
        <span *ngIf="propertyInfo?.noOfBHKs">{{isListing ? getBRDisplayString(propertyInfo.noOfBHKs) :
          getBHKDisplayString(propertyInfo.noOfBHKs)}}
          {{BHKType[propertyInfo?.bhkType]}}
        </span>
        <span *ngIf="!(isListing && propertyInfo?.propertyType?.displayName === 'Agricultural')"><ng-container
            *ngIf="propertyInfo?.propertyType?.childType?.displayName">
            {{propertyInfo?.propertyType?.childType?.displayName}},
          </ng-container>{{propertyInfo?.propertyType?.displayName}}</span>
      </h4>
      <h5 class="fw-semi-bold"><span>
          <ng-container
            *ngIf="propertyInfo?.enquiredFor !== 'None' || propertyInfo?.propertyType?.childType?.displayName">"</ng-container>{{propertyInfo?.propertyType?.childType?.displayName}}
          <ng-container *ngIf="propertyInfo?.enquiredFor !== 'None'">{{ 'GLOBAL.for' | translate }}
            {{propertyInfo?.enquiredFor}}</ng-container>
          <ng-container
            *ngIf="propertyInfo?.enquiredFor !== 'None' || propertyInfo?.propertyType?.childType?.displayName">"</ng-container>
        </span>
        <ng-container *ngIf="propertyInfo?.address && !isEmptyObject(propertyInfo?.address)"> {{ 'GLOBAL.in' | translate
          }}
          <span class="fw-600 border-bottom">{{getLocationDetailsByObj(propertyInfo?.address)}}</span>
        </ng-container>
      </h5>
    </div>
    <div class="align-end-col">
      <div class="flex-center gap-2 ph-mt-10">
        <div *ngIf="propertyInfo?.monetaryInfo?.expectedPrice">
          <div class="text-xs">{{propertyInfo?.enquiredFor === 'Rent' ? 'Rent Amount' : 'Total Price'}}</div>
          <h4 class="fw-600 text-nowrap mt-4">{{formatBudget(propertyInfo?.monetaryInfo?.expectedPrice,
            propertyInfo?.monetaryInfo?.currency || defaultCurrency)}}
          </h4>
        </div>
        <div class="border-left-black-100 h-30px mt-2 mx-12"
          *ngIf="propertyInfo?.monetaryInfo?.expectedPrice && propertyInfo?.monetaryInfo?.depositAmount"></div>
        <div *ngIf="propertyInfo?.monetaryInfo?.depositAmount">
          <div class="text-xs">Deposit/Security Amount</div>
          <h4 class="fw-600 text-nowrap mt-4">
            {{formatBudget(propertyInfo?.monetaryInfo?.depositAmount,propertyInfo?.monetaryInfo?.currency ||
            defaultCurrency)}}</h4>
        </div>
        <div class="border-left-black-100 h-30px mt-2 mx-12"
          *ngIf="propertyInfo?.monetaryInfo?.depositAmount && propertyInfo?.monetaryInfo?.maintenanceCost"></div>
        <div *ngIf="propertyInfo?.monetaryInfo?.maintenanceCost">
          <div class="text-xs">Maintenance Cost</div>
          <h4 class="fw-600 text-nowrap mt-4">
            {{formatBudget(propertyInfo?.monetaryInfo?.maintenanceCost, propertyInfo?.monetaryInfo?.currency ||
            defaultCurrency)}}</h4>
        </div>
      </div>
      <ng-container *ngIf="propertyInfo?.monetaryInfo?.isNegotiable">
        <div class="align-center px-16 py-4 br-12 w-110 mt-10 border">
          <ng-lottie [options]='tick' height='15px' width="15px" class="lottie"></ng-lottie>
          <div class="text-white ml-6 text-xs">Negotiable</div>
        </div>
      </ng-container>
    </div>
  </div>
</div>