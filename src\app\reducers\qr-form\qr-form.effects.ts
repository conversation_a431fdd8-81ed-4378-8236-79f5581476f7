import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import {
    catchError,
    map,
    mergeMap,
    switchMap,
    withLatestFrom,
} from 'rxjs/operators';
import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { QrFormService } from 'src/app/services/controllers/qr-form.service';
import {
    AddQrForm,
    AddQrFormSuccess,
    BulkDelete,
    BulkDeleteSuccess,
    BulkRestore,
    BulkRestoreSuccess,
    DeleteQrForm,
    FetchAllQrForms,
    FetchAllQrFormsSuccess,
    FetchQRBySearchSuccess,
    FetchQrFormById,
    FetchQrFormByIdSuccess,
    FetchQrFormsFields,
    FetchQrFormsFieldsSuccess,
    FetchQrFormsList,
    FetchQrFormsListSuccess,
    QRFormActionTypes,
    RestoreTemplate,
    TemplateNameExists,
    TemplateNameExistsSuccess,
    UpdateQrForm,
    UpdateQrFormStatus,
    UpdateQrFormSuccess,
} from './qr-form.action';
import { getPageNumber, getPageSize } from './qr-form.reducer';

@Injectable()
export class QrFormEffects {
    getQRForm$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.FETCH_QR_FORM),
            withLatestFrom(
                this._store.select(getPageNumber),
                this._store.select(getPageSize)
            ),
            switchMap(
                ([action, pageNumber, pageSize]: [FetchAllQrForms, number, number]) => {
                    const { keyword, shouldGetDeletedTemplatesOnly } = action;
                    return this.api
                        .getAllQrForm(
                            pageNumber,
                            pageSize,
                            keyword,
                            shouldGetDeletedTemplatesOnly
                        )
                        .pipe(
                            map((resp: any) => {
                                if (resp.succeeded) {
                                    if (keyword) {
                                        return new FetchQRBySearchSuccess(resp);
                                    } else {
                                        return new FetchAllQrFormsSuccess(resp);
                                    }
                                } else {
                                    return new FetchAllQrFormsSuccess();
                                }
                            }),
                            catchError((err) => of(new OnError(err)))
                        );
                }
            )
        )
    );

    getQRFormList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.FETCH_QR_FORM_LIST),
            map((action: FetchQrFormsList) => action),
            switchMap((action: FetchQrFormsList) => {
                return this.api.getQrFormList().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchQrFormsListSuccess(resp.items);
                        }
                        return new FetchQrFormsListSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getQRFormById$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.FETCH_QR_FORM_BY_ID),
            map((action: FetchQrFormById) => action),
            switchMap((action: FetchQrFormById) => {
                return this.api.getQrFormById(action.id).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchQrFormByIdSuccess(resp);
                        }
                        return new FetchQrFormByIdSuccess({});
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    addQRForm$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.ADD_QR_FORM),
            switchMap((action: AddQrForm) => {
                return this.api.add(action.payload).pipe(
                    mergeMap((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `QR Template added successfully.`
                            );
                            return [new AddQrFormSuccess(), new FetchAllQrForms()]; // Return multiple actions using mergeMap
                        } else {
                            return of(new OnError(resp)); // Handle error if add operation failed
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateQRForm$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.UPDATE_QR_FORM),
            switchMap((action: UpdateQrForm) =>
                this.api.updateQrForm(action.payload, action.id).pipe(
                    switchMap((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `QR Template updated successfully.`
                            );
                            return [
                                new UpdateQrFormSuccess(),
                                new FetchQrFormById(action.id),
                                new FetchAllQrForms(),
                            ];
                        } else {
                            // Handle case when update fails
                            return of(new OnError(resp));
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    deleteQRForm$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.DELETE_QR_FORM),
            map((action: DeleteQrForm) => action),
            switchMap((action: DeleteQrForm) => {
                const shouldDeletePermanently = action.shouldDeletePermanently
                    ? true
                    : false;
                return this.api.deleteQrForm(action.id, shouldDeletePermanently).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `QR Template deleted successfully.`
                            );
                            return new FetchAllQrForms(
                                action.pageNumber,
                                action.pageSize,
                                '',
                                shouldDeletePermanently
                            );
                        }
                        return new FetchAllQrFormsSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateQrFormStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.UPDATE_QR_FORM_STATUS),
            switchMap((action: UpdateQrFormStatus) => {
                return this.api.updateStatus(action.id).pipe(
                    mergeMap((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `QR Template status updated successfully.`
                            );
                            return [new FetchAllQrForms(action.pageNumber, action.pageSize)]; // Return multiple actions using mergeMap
                        } else {
                            return of(new OnError(resp)); // Handle error if update status operation failed
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getQRFormFields$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.FETCH_QR_FORM_FIELDS),
            switchMap((action: FetchQrFormsFields) => {
                return this.api.getQrFormFields().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchQrFormsFieldsSuccess(resp.data);
                        } else {
                            return new FetchQrFormsFieldsSuccess(); // Return a success action even if not succeeded
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );
    isTemplateNameExists$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.TEMPLATE_NAME_EXISTS),
            switchMap((action: TemplateNameExists) =>
                this.api.isTemplateNameExists(action.name).pipe(
                    map((resp: any) => {
                        return new TemplateNameExistsSuccess(resp);
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    restoreTemplate$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.RESTORE_TEMPLATE),
            switchMap((action: RestoreTemplate) => {
                return this.api.restoreTemplate(action.id).pipe(
                    mergeMap((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `QR Template restored successfully.`
                            );
                            return [
                                new FetchAllQrForms(
                                    action.pageNumber,
                                    action.pageSize,
                                    '',
                                    true
                                ),
                            ];
                        } else {
                            return of(new OnError(resp));
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    bulkDelete$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.BULK_DELETE_QR_FORM),
            map((action: BulkDelete) => action),
            switchMap((action: BulkDelete) => {
                const shouldDeletePermanently = action.shouldDeletePermanently
                    ? true
                    : false;
                return this.api
                    .bulkDelete(action.ids, action.shouldDeletePermanently)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                this._notificationService.success('Deleted Successfully');
                                this._store.dispatch(new BulkDeleteSuccess());
                                return new FetchAllQrForms('', '', '', shouldDeletePermanently);
                            }
                            return new FetchAllQrForms();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    bulkRestore$ = createEffect(() =>
        this.actions$.pipe(
            ofType(QRFormActionTypes.BULK_RESTORE_QR_FORM),
            map((action: BulkRestore) => action),
            switchMap((action: BulkRestore) => {
                return this.api.bulkRestore(action.ids).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success('Restore Successfully');
                            this._store.dispatch(new BulkRestoreSuccess());
                            return new FetchAllQrForms('', '', '', true);
                        }
                        return new FetchAllQrForms();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private actions$: Actions,
        private api: QrFormService,
        private _notificationService: NotificationsService,
        private _store: Store<AppState>
    ) { }
}
