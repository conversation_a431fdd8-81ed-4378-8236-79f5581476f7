<div routerLink='/projects/manage-projects' [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
  class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div class="h-60 mt-10 px-40">
  <ul class="flex-center w-100 tb-w-100-80 scrollbar tb-flex-center-unset scroll-hide">
    <li (click)="navigate(nav)" *ngFor="let nav of navTabs; let index = index; let isLast = last"
      class="py-8 align-center" [ngClass]="!isLast ? 'w-20 tb-w-unset':''">
      <div class="br-5 header-4 w-28 h-28 flex-center cursor-pointer"
        [ngClass]="index<currentTab?'bg-green-900 text-white':index===currentTab?'bg-blue-1000 text-white':'bg-white'">
        <div *ngIf="index < currentTab">&check;</div>
        <div *ngIf="index >= currentTab">{{index+1}}</div>
      </div>
      <h5 class="fw-600 ml-10 cursor-pointer mr-30 ip-mr-10 text-nowrap"
        [ngClass]="index<currentTab?'text-green-900':index===currentTab?'text-blue-1000':''">
        {{nav.title}}</h5>
      <div class="mr-30 ip-mr-10 w-50px ip-w-30px flex-grow-1"
        [ngClass]="index<currentTab ? 'border-green-900' : 'border-dashed-bottom-2'" *ngIf="!isLast">
      </div>
    </li>
  </ul>
</div>
<router-outlet></router-outlet>