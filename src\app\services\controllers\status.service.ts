import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class StatusService {
  baseURL: string = `${env.baseURL}`;
  apiURL: string = `${env.apiURL}`;
  resourecURL: string = 'status';

  constructor(private httpClient: HttpClient) { }

  getCustomStatuses() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}`,
    );
  }
}
