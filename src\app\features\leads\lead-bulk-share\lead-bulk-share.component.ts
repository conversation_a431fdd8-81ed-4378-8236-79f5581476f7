import {
  Component,
  EventE<PERSON>ter,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchCampaignList } from 'src/app/reducers/lead/lead.actions';
import { getCampaignList, getCampaignListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import {
  AddWhatsappCloud,
  AddWhatsappCloudTest,
  FetchWhatsappCloud,
} from 'src/app/reducers/whatsapp-cloud/whatsapp-cloud.actions';
import { getWhatsAppCloud } from 'src/app/reducers/whatsapp-cloud/whatsapp-cloud.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'lead-bulk-share',
  templateUrl: './lead-bulk-share.component.html',
})
export class LeadBulkShareComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  data: any;
  shareDetailForm: FormGroup;
  templates: Array<any> = [];
  loggedInPhoneNo: any;
  testTemplateSent: boolean = false;
  bodyValuesCount: boolean;
  fileNameReq: boolean;
  isBodyVarCountValid: boolean = false;
  preferredCountries = ['in'];
  @ViewChild('contactNoInput') contactNoInput: any;
  campaigns: any[] = [];
  isCampaignListLoading: boolean = false;
  selectedNodes: any;

  constructor(
    public modalService: BsModalService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder,
    public trackingService: TrackingService
  ) {
    const userDetails = localStorage.getItem('userDetails');
    this.loggedInPhoneNo = JSON.parse(userDetails)?.phone_number;
    this.shareDetailForm = this.fb.group({
      testContactNo: [
        this.loggedInPhoneNo.toString(),
        this.contactNumberValidator(),
      ],
      template: [null, Validators.required],
      campaignName: [null, Validators.required],
      fileName: [''],
      headerVariable: [''],
      bodyVariables: [''],
    });
  }

  ngOnInit() {
    this.store.dispatch(new FetchWhatsappCloud());
    this.store.dispatch(new FetchCampaignList());
    this.store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.campaigns = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCampaignListLoading = isLoading;
      });
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code.toLowerCase()]
            : ['in']
          : ['in'];
      });
    this.store
      .select(getWhatsAppCloud)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          this.templates = res.templates;
        }
      });

    this.shareDetailForm
      .get('template')
      .valueChanges.subscribe((template: string) => {
        this.bodyValuesCount =
          this.shareDetailForm.controls['template']?.value?.bodyValuesCount >=
          1;
        this.fileNameReq =
          this.shareDetailForm.controls['template']?.value
            ?.whatsAppHeaderTypes === 4;
        this.shareDetailForm.controls['bodyVariables'].setValue('');
        if (this.bodyValuesCount) {
          toggleValidation(
            VALIDATION_SET,
            this.shareDetailForm,
            'bodyVariables',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.shareDetailForm,
            'bodyVariables'
          );
        }
        if (this.fileNameReq) {
          toggleValidation(VALIDATION_SET, this.shareDetailForm, 'fileName', [
            Validators.required,
          ]);
        } else {
          toggleValidation(VALIDATION_CLEAR, this.shareDetailForm, 'fileName');
        }
      });

    this.shareDetailForm
      .get('bodyVariables')
      .valueChanges.subscribe((variable: any) => {
        if (
          this.shareDetailForm?.controls?.['template']?.value
            ?.bodyValuesCount == 0
        ) {
          this.isBodyVarCountValid = true;
          return;
        }
        if (
          variable.split(',')?.length ==
          this.shareDetailForm.controls['template']?.value?.bodyValuesCount &&
          !variable.split(',').some((element: any) => element === '')
        ) {
          this.isBodyVarCountValid = true;
        } else {
          this.isBodyVarCountValid = false;
        }
      });
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  contactNumberValidator(): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      const input = document.querySelector(
        '.contactNoInput > div > input'
      ) as HTMLInputElement;

      if (!input?.value?.length && !control?.value) {
        return { required: true };
      }
      defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
      try {
        const validNumber = isPossiblePhoneNumber(
          this.contactNoInput?.value || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  updateBulkShare(ConfirmationModal: TemplateRef<any>) {
    if (!this.shareDetailForm.valid || !this.isBodyVarCountValid) {
      validateAllFormFields(this.shareDetailForm);
      return;
    }
    const whatsappData = this.shareDetailForm.value;
    const selectedLeads = (
      this.data?.isData ? this.data?.bulkData : this.data
    )?.map((lead: any) => {
      return {
        id: lead?.id,
        contactNo: lead?.contactNo,
        name: lead?.name,
      };
    });

    let payload: any = {
      ...whatsappData?.template,
      headerValue: whatsappData?.template?.headerValuesCount.toString(),
      bodyValues: whatsappData?.bodyVariables.split(','),
      fileName: whatsappData?.fileName,
      campaignName: whatsappData?.campaignName,
      isData: this.data?.isData,
    };

    if (this.testTemplateSent) {
      payload = {
        ...payload,
        leadContactInfoDtos: [...selectedLeads],
      };
      this.store.dispatch(new AddWhatsappCloud(payload));
    } else {
      payload = {
        ...payload,
        leadContactInfoDtos: [
          {
            contactNo: whatsappData?.testContactNo,
          },
        ],
        isTestMessage: true,
      };
      this.store.dispatch(new AddWhatsappCloudTest(payload));
    }
    this.selectedNodes?.forEach((node: any) => {
      node?.setSelected(false);
    });
    if (!this.testTemplateSent) {
      let initialState: any = {
        class: 'modal-300 modal-dialog-centered',
      };
      this.modalRef = this.modalService.show(ConfirmationModal, initialState);
    }
    if (!this.testTemplateSent) {
      this.trackingService.trackFeature('Web.Leads.Button.sendtest.Click');
    } else {
      this.trackingService.trackFeature('Web.Leads.Button.Sendbulk.Click');
    }
  }

  getMessage() {
    return this.shareDetailForm.controls['template']?.value?.message ? this.shareDetailForm.controls['template']?.value?.message.replace(/\\n/g, '\n') : '';
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
