import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { TodoPriority } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { Todo } from 'src/app/core/interfaces/todo.interface';
import { AddTaskComponent } from 'src/app/features/task/add-task/add-task.component';
import { getDeletePermissions, getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { DeleteTodo, UpdateTodo } from 'src/app/reducers/todo/todo.actions';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'todo-actions',
  templateUrl: './task-actions.component.html',
})
export class TaskActionsComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canEditTask: boolean = false;
  canDeleteTask: boolean = false;

  constructor(
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private _store: Store<AppState>,
    public trackingService: TrackingService
  ) {
    this._store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Todos')) {
          this.canEditTask = true;
        }
      });

    this._store
      .select(getDeletePermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canDelete: any) => {
        if (canDelete?.includes('Todos')) {
          this.canDeleteTask = true;
        }
      });
  }


  agInit(params: any): void {
    this.params = params;
  }

  initEditTodo(todo: Todo) {
    this.trackingService.trackFeature(`Web.Task.Button.Edit.Click`)
    const initialState = {
      selectedTodo: todo,
    };
    this.modalRef = this.modalService.show(AddTaskComponent, {
      class: 'right-modal modal-350',
      initialState,
      // ignoreBackdropClick: true,
      // keyboard: false,
    });
  }

  openDeleteToDoModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.title,
      fieldType: 'task',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.trackingService.trackFeature(`Web.Task.Button.Delete.Click`)
          this._store.dispatch(new DeleteTodo(data?.id));
        }
      });
    }
  }

  markAsDone(todo: any) {
    if (todo.isMarkedDone) {
      return;
    }
    const payload = {
      ...todo,
      assignedFrom: todo.assignedFrom?.id,
      assignedToUserId: todo.assignedToUser?.id,
      isMarkedDone: true,
      priority: TodoPriority[todo.priority],
    };
    this.trackingService.trackFeature(`Web.Task.Button.MerkDone.Click`)
    this._store.dispatch(new UpdateTodo(payload.id, payload));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
