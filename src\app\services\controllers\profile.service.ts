import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
import { getSystemTimeOffset, getSystemTimeZoneId } from 'src/app/core/utils/common.util';

@Injectable({
  providedIn: 'root',
})
export class ProfileService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }
  getResourceUrl(): string {
    return 'profile';
  }
  getProfile() {
    return this.http.get(`${this.serviceBaseUrl}`);
  }

  getTestimonials() {
    return this.http.get(`${this.serviceBaseUrl}/testimonial`);
  }

  addTestimonial(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/testimonial`, payload);
  }

  updateProfile(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}`, payload);
  }

  updateTestimonial(payload: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/testimonial/${payload.id}`,
      payload
    );
  }

  updateAboutUs(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/about`, payload);
  }

  getAllRecognition() {
    return this.http.get(`${this.serviceBaseUrl}/recognition`);
  }

  addRecognition(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/recognition`, payload);
  }

  deleteRecognition(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/recognition/${id}`);
  }

  deleteTestimonial(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/testimonial/${id}`);
  }

  updateBannerImg(url: any) {
    return this.http.put(`${this.serviceBaseUrl}/bannerimg`, `"${url}"`);
  }

  updateLogoImg(url: any) {
    return this.http.put(`${this.serviceBaseUrl}/logoimg`, `"${url}"`);
  }

  updateSocialMedia(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/socialmedia`, payload);
  }

  getQRCode(payload: any) {
    let url = `https://${payload?.router}/external/add-lead/qr-code/${payload?.templateId}`;
    let params = new HttpParams().set('url', url);
    return this.http.get(`${this.serviceBaseUrl}/qr-code?${params.toString()}`);
  }

  getSubscription(timeZoneInfo: any) {
    const timeZoneId = timeZoneInfo?.timeZoneId || getSystemTimeZoneId();
    const offset = timeZoneInfo?.baseUTcOffset || getSystemTimeOffset();
    return this.http.get(
      `${this.serviceBaseUrl}/subscription?timeZoneId=${timeZoneId}&baseUTcOffset=${offset}`
    );
  }

  getAllSubscription(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/all/subscription?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  paymentGateway(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/payment-gateway`, payload);
  }

  transactionInfo() {
    return this.http.get(`${this.serviceBaseUrl}/transaction-info`);
  }

  getTimeZoneInfo() {
    return this.http.get(`${this.serviceBaseUrl}/timezone-info`);
  }
}
