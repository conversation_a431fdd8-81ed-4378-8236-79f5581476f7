import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AddNewProjectComponent } from './add-new-project/add-new-project.component';
import { AmenitiesComponent } from './add-new-project/amenities/amenities.component';
import { BasicDetailsComponent } from './add-new-project/basic-details/basic-details.component';
import { AddBlockInfoComponent } from './add-new-project/block-info/add-block-info/add-block-info.component';
import { BlockInfoActionComponent } from './add-new-project/block-info/block-info-action/block-info-action.component';
import { BlocksInfoComponent } from './add-new-project/block-info/block-info.component';
import { GalleryComponent } from './add-new-project/gallery/gallery.component';
import { AddUnitInfoComponent } from './add-new-project/unit-info/add-unit-info/add-unit-info.component';
import { BulkUploadComponent } from './add-new-project/unit-info/bulk-upload/bulk-upload.component';
import { UnitInfoActionsComponent } from './add-new-project/unit-info/unit-info-actions/unit-info-actions.component';
import { UnitInfoComponent } from './add-new-project/unit-info/unit-info.component';
import { ManageProjectsComponent } from './manage-projects/manage-projects.component';
import { ProjectActionComponent } from './manage-projects/project-action/project-action.component';
import { ProjectStatusComponent } from './manage-projects/project-status/project-status.component';
import { ProjectsRootLayoutComponent } from './projects-root.components';
import { UnitInfoAdvanceFiltersComponent } from './add-new-project/unit-info/unit-info-advance-filters/unit-info-advance-filters.component';
import { ProjectBulkUploadComponent } from './bulk-upload/bulk-upload.component';
import { ProjectAdvanceFilterComponent } from './project-advance-filter/project-advance-filter.component';

export const routes: Routes = [
  {
    path: '',
    component: ProjectsRootLayoutComponent,
    children: [
      { path: '', redirectTo: 'manage-projects', pathMatch: 'full' },
      { path: 'manage-projects', component: ManageProjectsComponent },
      { path: 'bulk-upload', component: BulkUploadComponent }, 
      { path: 'project-bulk-upload', component: ProjectBulkUploadComponent },
      {
        path: 'add-project',
        component: AddNewProjectComponent,
        children: [
          { path: '', redirectTo: 'basic-details', pathMatch: 'full' },
          { path: 'blocks-info', component: BlocksInfoComponent },
          { path: 'basic-details', component: BasicDetailsComponent },
          { path: 'unit-info', component: UnitInfoComponent },
          { path: 'amenities', component: AmenitiesComponent },
          { path: 'gallery', component: GalleryComponent },
        ],
      },
      {
        path: 'edit-project',
        component: AddNewProjectComponent,
        children: [
          { path: '', redirectTo: 'basic-details/:id', pathMatch: 'full' },
          { path: 'blocks-info/:id', component: BlocksInfoComponent },
          { path: 'basic-details/:id', component: BasicDetailsComponent },
          { path: 'unit-info/:id', component: UnitInfoComponent },        
          { path: 'amenities/:id', component: AmenitiesComponent },
          { path: 'gallery/:id', component: GalleryComponent },
        ],
      },
    ],
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ProjectsRoutingModule { }

export const PROJECTS_DECLARATIONS = [
  ProjectsRootLayoutComponent,
  ManageProjectsComponent,
  ProjectActionComponent,
  BasicDetailsComponent,
  AmenitiesComponent,
  GalleryComponent,
  BlocksInfoComponent,
  BlockInfoActionComponent,
  AddNewProjectComponent,
  UnitInfoComponent,
  UnitInfoActionsComponent,
  GalleryComponent,
  ProjectStatusComponent,
  AddUnitInfoComponent,
  AddBlockInfoComponent,
  BulkUploadComponent,
  UnitInfoAdvanceFiltersComponent,
  ProjectBulkUploadComponent,
  ProjectAdvanceFilterComponent
];
