import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { getLocationDetailsByObj } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'brochures',
  templateUrl: './brochures.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class BrochuresComponent implements OnInit {
  s3BucketUrl = env.s3ImageBucketURL;
  @Input() projectInfo: any;
  @Input() isUnit: boolean;
  getLocationDetailsByObj = getLocationDetailsByObj;
  selectedUrl: number;

  constructor(private sanitizer: DomSanitizer) { }

  ngOnInit(): void {
    this.selectedUrl = 0;
  }

  previous(): void {
    this.selectedUrl--;
    const brochures = this.isUnit ? this.projectInfo?.unitInfoGalleries : this.projectInfo?.brochures;
    if (this.selectedUrl < 0) {
      this.selectedUrl = this.isUnit ? brochures?.imageFilePath?.length - 1 : brochures?.url?.length - 1;
    }
    this.scroll('previous');
  }

  sanitizeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  next(): void {
    this.selectedUrl++;
    const brochures = this.isUnit ? this.projectInfo?.unitInfoGalleries : this.projectInfo?.brochures;
    if (this.selectedUrl >= (this.isUnit ? brochures?.imageFilePath?.length : brochures?.url?.length)) {
      this.selectedUrl = 0;
    }
    this.scroll('next');
  }

  scroll(direction: 'next' | 'previous'): void {
    const container = document.querySelector('.scroll-behaviour') as HTMLElement;
    let scrollAmount = 120;
    const brochures = this.isUnit ? this.projectInfo?.unitInfoGalleries : this.projectInfo?.brochures;

    if (this.selectedUrl < 0 || this.selectedUrl >= (this.isUnit ? brochures?.imageFilePath?.length : brochures?.url?.length)) {
      return;
    }

    if (direction === 'previous') {
      scrollAmount = -(scrollAmount);
    }

    if (direction === 'next' && this.selectedUrl === 0) {
      scrollAmount *= -1;
    }

    container.scrollTo({
      left: container.scrollLeft + scrollAmount,
      behavior: 'smooth'
    });
  }

  selectName(index: number): void {
    this.selectedUrl = index;
  }

  downloadPdf(url: string): void {
    if (url) {
      window.open(url, '_blank');
    }
  }
}