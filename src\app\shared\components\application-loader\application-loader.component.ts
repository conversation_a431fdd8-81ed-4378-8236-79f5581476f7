import { Component, OnInit } from '@angular/core';
import { AnimationOptions } from 'ngx-lottie';
import { getAppImages } from 'src/app/core/utils/common.util';

@Component({
  selector: 'application-loader',
  templateUrl: './application-loader.component.html'
})
export class ApplicationLoaderComponent implements OnInit {
  loading: AnimationOptions = {
    path: 'assets/animations/loading-text-animation.json',
  };
  appImages: { appLoader: string } = getAppImages();
  constructor() { }

  ngOnInit(): void {
  }

}
