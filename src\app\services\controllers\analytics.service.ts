import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class AnalyticsService extends BaseService<any> {
  analyticsURL: string = `${env.analyticsURL}api`;

  constructor(private http: HttpClient) {
    super(http);
  }
  getResourceUrl(): string {
    return '/tenants';
  }

  getActionsList() {
    return this.http.get(`${this.analyticsURL}/actions`);
  }

  addTracks(payload: any) {
    return this.http.post(`${this.analyticsURL}/tracks`, payload);
  }

  getFeaturesList() {
    return this.http.get(`${this.analyticsURL}/features`);

  }

}