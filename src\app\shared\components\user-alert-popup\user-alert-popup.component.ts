import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { ASSIGNED_FIELDS, DELETED_NOTES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchCampaignName } from 'src/app/reducers/manage-marketing/marketing.action';
import { getCampaignFiltersPayload } from 'src/app/reducers/manage-marketing/marketing.reducer';
import { ManageMarketingService } from 'src/app/services/controllers/marketing.service';

@Component({
  selector: 'user-alert-popup',
  templateUrl: './user-alert-popup.component.html',
})
export class UserAlertPopupComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  data: any;
  type: any;
  count: any;
  deletionMessages = DELETED_NOTES;
  fields = ASSIGNED_FIELDS;
  clickedField: string = '';
  isDualOwnership: boolean = false;
  isDeleting: boolean;
  @Output() navigateToParentLead: EventEmitter<string> = new EventEmitter();
  constructor(
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    public router: Router,
    private _store: Store<AppState>,
    private manageMarketingService: ManageMarketingService
  ) { }

  ngOnInit(): void {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isDualOwnership = data?.isDualOwnershipEnabled;
      });
  }

  deleteConfirm() {
    this.modalService.setDismissReason('confirmed');
    this.modalRef.hide();
  }

  deleteMarketing() {
    this.isDeleting = true
    if (this.data?.buttonContent === 'Delete Agency' || this.data?.buttonContent === 'Delete Channel Partner' || this.data?.buttonContent === 'Delete') {
      this.modalService.setDismissReason('confirmed');
      this.modalRef.hide();
    } else {
      this.manageMarketingService.deleteCampaign([this.data?.agencyData?.id]).subscribe((res: any) => {
        this.modalRef.hide();
        this.isDeleting = false
        this._store.select(getCampaignFiltersPayload).subscribe((payload: any) => {
          this._store.dispatch(new FetchCampaignName(payload));
        })
      })
    }
  }

  onFieldClick(fieldKey: string, type: string) {
    this.clickedField = fieldKey;
    this.handleFieldAction(fieldKey, type);
  }

  handleFieldAction(fieldKey: string, type: string) {
    const userId = this.data?.id;
    let payload: any = {};

    if (fieldKey === 'leads') {
      if (this.isDualOwnership) {
        payload =
          type === 'primary'
            ? { assignTo: [userId], SecondaryUsers: [] }
            : { assignTo: [], SecondaryUsers: [userId] };
      } else {
        payload = { assignTo: [userId] };
      }
      this.router.navigate(['/leads'], { queryParams: payload });
    }

    if (fieldKey === 'prospects') {
      payload = { AssignTo: [userId] };
      this.router.navigate(['/data'], { queryParams: payload });
    }

    this.modalRef.hide();
  }

  truncateText(text: string, limit: number): string {
    if (text.length > limit) {
      return text.substring(0, limit) + '...';
    }
    return text;
  }
}
