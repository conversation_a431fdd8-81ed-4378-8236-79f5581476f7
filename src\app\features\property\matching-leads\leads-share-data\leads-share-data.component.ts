import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { EMPTY_GUID } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  ClickToCall,
  FetchAgents,
  FetchIntegrationCount,
} from 'src/app/reducers/Integration/integration.actions';
import {
  getAgents,
  getIntegrationCount,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  CommunicationCount,
  CommunicationMessage,
} from 'src/app/reducers/lead/lead.actions';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { UnassignedComponent } from 'src/app/shared/components/unassigned/unassigned.component';

@Component({
  selector: 'leads-share-data',
  templateUrl: './leads-share-data.component.html',
})
export class LeadsShareDataComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  userDetails: any;
  agents: boolean = false;
  userPhoneNo: string;
  assignedToUserId: string;
  agentList: Array<Object>;
  agentPhoneNo: Array<any>;
  isIVREnabled: boolean = false;
  warn: AnimationOptions = { path: 'assets/animations/warning.json' };
  userPermissions: string;
  isLoading: boolean = true;
  ivrAccountCount: any;
  globalsettingsIVR: number = 0;
  EMPTY_GUID = EMPTY_GUID;
  projectData: any;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    public router: Router
  ) {
    this.userDetails = localStorage.getItem('userDetails');
    this.userPhoneNo = JSON.parse(this.userDetails)?.phone_number;
    this.userPermissions = localStorage.getItem('userPermissions');

    this.store
      .select(getIntegrationCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.ivrAccountCount = res.IVR;
      });

    if (this.userPermissions?.includes('IVRCall') && this.ivrAccountCount) {
      this.isIVREnabled = true;
    }
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalsettingsIVR = data?.callSettings?.callType;
      });

    this.store
      .select(getAgents)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agentList = item;
      });
  }

  agInit(params: any): void {
    this.params = params;
    this.projectData = params;
  }

  checkToCall(agent: any) {
    this.store.dispatch(new FetchAgents());
    this.store.dispatch(new FetchIntegrationCount());
    if (this.params.data.assignTo !== EMPTY_GUID) {
      if (
        this.isIVREnabled &&
        this.globalsettingsIVR === 2 &&
        this.agentList?.length
      ) {
        this.checkAgent(agent);
      } else {
        this.openDialerPad();
      }
      this.modalService.hide();
    } else {
      this.openUnassignModal();
    }
  }

  openUnassignModal() {
    this.modalRef = this.modalService.show(UnassignedComponent, {
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    });
  }

  checkAgent(agent: any) {
    this.onInitiateCall(1);
    this.store.dispatch(new FetchAgents());
    this.store
      .select(getAgents)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agentList = item;
        this.agentPhoneNo = Object.values(item).filter((agentL: any) => {
          if (agentL.phoneNumber == this.userPhoneNo) {
            this.agents = true;
          }
        });
        if (this.agents) {
          this.ivrClickToCall(this.userPhoneNo);
        } else if (
          this.agentList?.length &&
          this.modalService.getModalsCount() === 0
        ) {
          this.modalRef = this.modalService.show(agent, {
            class: 'modal-400 modal-dialog-centered ph-modal-unset',
          });
        }
      });
  }

  ivrClickToCall(agentNo: any) {
    let payload = {
      destination_number: this.params.data.contactNo,
      agent_number: agentNo,
    };
    this.store.dispatch(new ClickToCall(payload));
    this.store.dispatch(new LoaderHide());
    this.modalRef.hide();
  }

  onInitiateCall(contactType: number) {
    let payload = {
      id: this.params.data?.lead
        ? this.params.data?.lead?.id
        : this.params.data?.id,
      contactType: contactType,
    };
    this.store.dispatch(new CommunicationCount(payload.id, payload));
    const leadPayload: any = {
      contactType: contactType,
      leadId: this.params.data?.lead
        ? this.params.data?.lead?.id
        : this.params?.data?.id,
    };
    if (this.projectData?.value?.[4]?.[1]) {
      this.store.dispatch(new CommunicationMessage(leadPayload));
    }
  }

  openDialerPad() {
    location.href = 'tel:' + this.params?.data?.lead?.contactNo;
    this.onInitiateCall(1);
  }

  closeModal() {
    this.modalRef.hide();
    this.assignedToUserId = null;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
