<div *ngIf="canView && !isTaskLoading; else loader"
  class="card-content border-top coldt-layout h-100 ip-flex-col tb-w-100">
  <div class="h-100 pb-50 p-20 left-container xl ip-w-100 tb-w-25">
    <div class="d-flex flex-wrap">
      <ng-container *ngFor="let taskType of taskFilterTypes">
        <div class="p-10 w-50 tb-w-100 ip-w-50 ph-w-40">
          <div class="card clear-margin br-5 cursor-pointer" id="clk{{taskType.name}}Todos"
            data-automate-id="clk{{taskType.name}}Todos" [ngClass]="taskType.bg"
            [ngClass]="selectedTodoState == taskType.name ? taskType.bg+'-active' : taskType.bg">
            <div class="card-body py-12 px-24 cursor-pointer text-white" (click)="filterTodoList(taskType.name)">
              <div class="align-center mb-12">
                <span class=" icon icon-xs" [ngClass]="taskType.icon"></span>
                <span class="mt-4 ml-8 text-white">{{ 'GLOBAL.'+taskType.name.toLowerCase() | translate
                  }}</span>
              </div>
              <div class="justify-end text-white">
                <h3>{{todoFiltersCount[taskType.name.toLowerCase()]}}</h3>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <ng-container *ngIf="!filteredTodoList?.length; else taskData">
    <div class="flex-grow-1 right-container bg-secondary todo-card-right">
      <div class="p-30 tb-w-75 ip-p-0 ip-w-100">
        <div class="flex-center-col h-100-176 min-h-250 ip-h-auto">
          <div class="d-flex ip-flex-col">
            <ng-lottie [options]='musoTask' height="550px"></ng-lottie>
            <div class="flex-center-col ml-30 ip-ml-0">
              <h4 class="text-gray fw-600 text-center mb-30 w-300 ip-mb-20">{{ 'TASK.empty-message' | translate }}</h4>
              <button *ngIf="canAdd" class="btn-coal btn-large w-120" id="btnToDo" data-automate-id="btToDo"
                (click)="addToDoModal()">
                <span class="ic-add icon icon-xxs mr-8"></span>
                <span class="text-xl fw-700">{{ 'TASK.add-task' | translate }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-template #taskData>
    <div class="flex-grow-1 right-container bg-secondary todo-card-right">
      <div class="flex-end mr-16 mt-10">
        <div class="btn-coal mr-20" (click)="addTaskModal()">
          <span class="ic-add icon ic-xxs"></span>
          <span class="ml-8 ph-d-none">{{ 'TASK.add-task' | translate }}</span>
        </div>
      </div>
      <div class="p-30">
        <div class="flex-between mb-24">
          <h3 class="fw-600 text-gray">{{ selectedTodoState | titlecase }}</h3>
          <div class="align-center show-dropdown">
            <span>{{ 'GLOBAL.show' | translate }}</span>
            <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-70px mx-10"
              [searchable]="false" ResizableDropdown (change)="assignCount()" [(ngModel)]="selectedPageSize">
              <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                {{pageSize}}</ng-option>
            </ng-select>
            <span>{{ 'GLOBAL.entries' | translate }}</span>
          </div>
        </div>
        <div class="manage-user">
          <ag-grid-angular #agGrid class="ag-theme-alpine" [gridOptions]="gridOptions" [pagination]="true"
            [paginationPageSize]="pageSize" [defaultColDef]="gridOptions.defaultColDef" [rowData]="filteredTodoList"
            [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" [icons]="gridOptions.icons"
            [suppressPaginationPanel]="true" (gridReady)="onGridReady($event)" (cellClicked)="onCellClicked($event)">
          </ag-grid-angular>
        </div>
        <div class="flex-end mt-20">
          <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*pageSize + 1}}
            {{ 'GLOBAL.to-small' | translate }} {{currOffset*pageSize + rowData?.length}}
            {{ 'GLOBAL.of-small' | translate }} {{todoTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
          <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(todoTotalCount,pageSize)"
            (pageChange)="onPageChange($event)">
          </pagination>
        </div>
      </div>
    </div>
  </ng-template>
</div>
<ng-template #loader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>