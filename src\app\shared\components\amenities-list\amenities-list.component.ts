import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs/operators';
import { AppState } from 'src/app/app.reducer';
import { getAmenities } from 'src/app/reducers/master-data/master-data.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';
@Component({
  selector: 'amenities-list',
  templateUrl: './amenities-list.component.html',
})
export class AmenitiesListComponent implements OnInit, OnDestroy {
  masterAmenityList: any = {};
  @Input() propertyTypeInput: number = 0;
  @Input() userSelectedAmenities: Array<string> = [];
  @Output() selectedAmenities = new EventEmitter<any>();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  activeAmenitiesType: string = 'Basic';
  filteredAmenityList: any = [];
  searchTerm: any = '';

  constructor(private _store: Store<AppState>, public trackingService: TrackingService) { }

  ngOnInit(): void {
    this._store
      .select(getAmenities)
      .pipe(takeUntil(this.stopper))
      .subscribe((amenityList: any) => {
        this.masterAmenityList = Object.entries(amenityList).reduce(
          (acc: any, [key, amenities]: any) => {
            const enabledAmenity = amenities
              .filter((amenity: any) =>
                amenity.propertyType.includes(this.propertyTypeInput))
              .map((amenity: any) => ({
                ...amenity,
                selected: this.userSelectedAmenities.includes(amenity.id),
              }));
            return { ...acc, [key]: enabledAmenity };
          },
          {}
        );
        this.filteredAmenityList = (this.masterAmenityList[this.activeAmenitiesType] || [])
          .sort((a: any, b: any) => a.amenityDisplayName.localeCompare(b.amenityDisplayName));
        this.emitSelection();
      });
  }

  setActiveAmenityType(amenityType: string) {
    this.activeAmenitiesType = amenityType;
    this.filterAmenities(this.searchTerm);
    this.trackingService.trackFeature(`Web.Property.Amenities.${amenityType}.Click`)
  }

  allAmenitySelectionCheck(aType: string) {
    const filteredAmenities = this.filteredAmenityList;
    if (!filteredAmenities.length) {
      return false;
    }
    return filteredAmenities.every((amenity: any) => amenity.selected);
  }

  changeAllAmenitySelectionForType(amenityType: string, isChecked: boolean) {
    const updatedAmenities = this.masterAmenityList[amenityType].map(
      (amenity: any) => {
        if (
          this.filteredAmenityList.find(
            (filteredAmenity: any) => filteredAmenity.id === amenity.id
          )
        ) {
          return { ...amenity, selected: isChecked };
        }
        return amenity;
      }
    );

    this.masterAmenityList = {
      ...this.masterAmenityList,
      [amenityType]: updatedAmenities,
    };
    this.filteredAmenityList = this.masterAmenityList[amenityType].filter(
      (item: any) =>
        item.amenityDisplayName
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase())
    );
    this.emitSelection();
  }

  emitSelection() {
    const selectedAmenities = Object.keys(this.masterAmenityList).reduce(
      (acc, cur) => {
        const selectedAmenities = this.masterAmenityList[cur]
          .filter((amenity: any) => amenity.selected)
          .map((amenity: any) => amenity.id);
        return [...acc, ...selectedAmenities];
      },
      []
    );
    this.selectedAmenities.emit(selectedAmenities);
  }

  onSearch(event: any) {
    this.searchTerm = event.target.value.toLowerCase().replace(/\s+/g, '');
    this.filterAmenities(this.searchTerm);
  }

  filterAmenities(searchTerm: string) {
    if (this.masterAmenityList[this.activeAmenitiesType]) {
      this.filteredAmenityList = this.masterAmenityList[this.activeAmenitiesType].filter((item: any) =>
        item.amenityDisplayName.toLowerCase().replace(/\s+/g, '').includes(searchTerm)
      ).sort((a: any, b: any) => a.amenityDisplayName.localeCompare(b.amenityDisplayName));
    } else {
      this.filteredAmenityList = [];
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
