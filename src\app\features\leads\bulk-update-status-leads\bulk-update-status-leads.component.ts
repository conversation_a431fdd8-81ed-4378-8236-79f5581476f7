import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { map, skipWhile, take, takeUntil } from 'rxjs';

import { LEAD_STATUS_REASONS, UPDATE_STATUS, UPDATE_STATUS_PAST_TENSE } from 'src/app/app.constants';
import { LeadStatus as LeadStatusEnum } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  setTimeZoneDate,
  patchFormControlValue,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { getGlobalAnonymousIsLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchBulkOperation, UpdateMultipleLead } from 'src/app/reducers/lead/lead.actions';
import { getIsLeadCustomStatusEnabled } from 'src/app/reducers/lead/lead.reducer';
import { getEditPermissions, getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'bulk-update-status-leads',
  templateUrl: './bulk-update-status-leads.component.html',
})
export class BulkUpdateStatusLeadsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('trackerInfoModal') trackerInfoModal: any;

  @Input() leadInput: any[];
  @Input() bulkUpdateModelRefInput: any[];
  @Input() selectedNodes: any[];
  @Input() gridApi: GridApi;
  @Input() modal: BsModalRef;
  @Output() modelOutput = new EventEmitter<string>();
  hideStatus: boolean;
  selectedStatus: string;
  sameStatusRows: any[];
  callBackReason: any[] = [];
  updateLeadStates: any[] = [];
  leadStatus: any = UPDATE_STATUS;
  currentLeadStatus: any = UPDATE_STATUS_PAST_TENSE;
  bulkReassignForm: FormGroup;
  bulkUpdateForm: FormGroup;
  patchFormControlValue = patchFormControlValue;
  allLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));
  conditionalStatus: any[];
  canEditLead: boolean = false;
  canUpdateStatus: boolean = false;
  canUpdateBookedLead: boolean = false;
  isCustomStatusEnabled:boolean;
  userBasicDetails: any;
  isBookedLead(): boolean {
    return !this.canUpdateBookedLead && this.sameStatusRows?.some((lead: { status: { displayName: string; }; }) => lead?.status?.displayName === 'Booked');
  }

  constructor(
    private formBuilder: FormBuilder,
    private _store: Store<AppState>,
    public bsModalRef: BsModalRef,
    public modalService: BsModalService,
    private _notificationsService: NotificationsService,
    private router: Router,
  ) {}

  async ngOnInit() {
    await this._store
    .select(getGlobalAnonymousIsLoading)
    .pipe(
      skipWhile((isLoading: boolean) => {
        return isLoading;
      }),
      take(1)
    )
    .toPromise();
  this.isCustomStatusEnabled = await this._store
    .select(getIsLeadCustomStatusEnabled)
    .pipe(
      map((data: any) => data),
      take(1)
    ).toPromise();
    this.sameStatusRows = this.leadInput;
    
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
      });
    this.bulkUpdateForm = this.formBuilder.group({
      scheduledDate: [''],
      bookedDate: [''],
      note: '',
      status: [''],
      rating: '',
      reason: '',
      leadExpectedBudget: [
        '',
        [ValidationUtil.onlyNumbersValidator('Only Numbers')],
      ],
      revertDate: null,
      purchasedFromWhom: '',
      updatedLocation: '',
      soldPrice: ['', ValidationUtil.onlyNumbersValidator('Only Numbers')],
      chosenProperty: '',
      chosenProject: '',
      bookedUnderName: '',
      meetingLocation: '',
      meetingLocationLatitude: '',
      meetingLocationLongitude: '',
      visitLocation: '',
      visitLocationLatitude: '',
      visitLocationLongitude: '',
      customerDeniedOTP: [false],
      enteredOTP: '',
      shareContent: '',
      shareMedia: '',
    });
    this.bulkReassignForm = this.formBuilder.group({
      assignTo: ['', Validators.required],
      note: '',
    });
    this.bulkUpdateForm.controls['status'].valueChanges.subscribe(
      (value: any) => {
        const [item]: any = this.updateLeadStates.filter(
          (item: any) => item.displayName === value
        );
        if (item && item.childTypes.length) {
          this.callBackReason = item.childTypes;
          this.hideStatus = true;
        }
      }
    );
    this._store
    .select(getEditPermissions)
    .pipe(takeUntil(this.stopper))
    .subscribe((canEdit: any) => {
      if (canEdit.includes('Leads')) this.canEditLead = true;
    });
    this._store
    .select(getPermissions)
    .pipe(takeUntil(this.stopper))
    .subscribe((permissions: any) => {
      if (permissions?.includes('Permissions.Leads.UpdateBookedLead')) {
        this.canUpdateBookedLead = true;
      }
      if (permissions?.includes('Permissions.Leads.UpdateLeadStatus')) 
        this.canUpdateStatus = true;
    });
    this.conditionalStatusRendering();
  }

  emitModalOutput(value: string) {
    this.modelOutput.emit(value);
  }

  conditionalStatusRendering() {
    this.conditionalStatus = this.updateLeadStates.filter((state: any) => {
      switch (
      LeadStatusEnum[this.sameStatusRows[0].leadStatus?.baseStatus as any]
      ) {
        case this.currentLeadStatus['meeting-scheduled']:
        case this.currentLeadStatus['visit-scheduled']:
          return !(state.actionName == UPDATE_STATUS['drop']);
        case UPDATE_STATUS['not-interested']:
          return !(
            state.actionName == UPDATE_STATUS['not-interested'] ||
            state.actionName == UPDATE_STATUS['book']
          );
        case this.currentLeadStatus['dropped']:
          return false;
        case this.currentLeadStatus['booked']:
          return false;
        case UPDATE_STATUS['callback']:
          return !(
            state.actionName == UPDATE_STATUS['drop'] ||
            state.actionName == UPDATE_STATUS['book'] ||
            state.actionName == UPDATE_STATUS['callback']
          );
        default:
          return state.actionName;
      }
    });
    return this.conditionalStatus;
  }

  updateStatus(payload: any) {
    let resource = {
      requests: [] as any[],
    };

    let postPonedStatusId = '';

    this.allLeadStatus?.map((status: any)=>{
      status?.childTypes?.map((child: any)=>{
        if(child?.displayName === LEAD_STATUS_REASONS[UPDATE_STATUS['callback']]['plan-postponed']){
          postPonedStatusId = child?.id;
        }
      });
    });

    if (this.bulkUpdateForm.valid) {
      this.sameStatusRows.forEach((elem: any) => {
        resource.requests.push({
          id: elem.id,
          leadStatusId: payload?.reason || payload.leadStatus,
          rating: payload?.rating,
          notes: payload?.notes,
          scheduledDate: setTimeZoneDate(payload?.scheduledDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || null,
          bookedDate: setTimeZoneDate(payload?.bookedDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || null,
          revertDate: setTimeZoneDate(payload?.revertDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
          unmatchedBudget: payload?.leadExpectedBudget || undefined,
          purchasedFrom: payload?.purchasedFromWhom,
          // preferredLocation: payload?.updatedLocation,
          postponedDate: payload.reason === postPonedStatusId ? setTimeZoneDate(payload?.scheduledDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) : setTimeZoneDate(payload?.revertDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)|| undefined,
          projectsList: payload?.chosenProject?.split(','),
          propertiesList: payload?.chosenProperty?.split(','),
        });
      });
      this._store.dispatch(new UpdateMultipleLead(resource));
      // const numberOfLeads = payload.requests.length;      
      // if (numberOfLeads < 50) {
      //   this._notificationsService.success('Leads updated Successfully');
      // } else {
      //   this.bsModalRef = this.modalService.show(
      //     this.trackerInfoModal,
      //     Object.assign(
      //       {},
      //       {
      //         class: 'modal-400 top-modal ph-modal-unset',
      //         ignoreBackdropClick: true,
      //         keyboard: false,
      //       }
      //     )
      //   );
      // }
      this.emitModalOutput('bulkUpdateModalOutput');
    } else {
      validateAllFormFields(this.bulkUpdateForm);
    }
  }

  openBulkUpdatedStatus() {
    this._store.dispatch(new FetchBulkOperation(1, 10, 'lead'));

    this.bsModalRef = this.modalService.show(BulkOperationTrackerComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState: {
        moduleType: 'lead',
      },
    });
  }


  setActive(e: any) {
    this.bulkUpdateForm.controls['status'].patchValue(e.innerText);
    if (
      [UPDATE_STATUS.callback, UPDATE_STATUS['not-interested']].includes(
        e.innerText
      )
    ) {
      this.hideStatus = true;
      this.bulkUpdateForm.controls['reason'].setValidators(Validators.required);
      this.bulkUpdateForm.controls['reason'].updateValueAndValidity();
    }
    this.selectedStatus = e.innerText;
    this.makeRequired();
  }
  makeRequired() {
    if (
      [
        UPDATE_STATUS.callback,
        UPDATE_STATUS['schedule-meeting'],
        UPDATE_STATUS['schedule-site-visit'],
      ].includes(this.bulkUpdateForm.controls['status'].value)
    ) {
      this.bulkUpdateForm.controls['scheduledDate'].setValidators(
        Validators.required
      );
      this.bulkUpdateForm.controls[
        'scheduledDate'
      ].updateValueAndValidity();
    } else {
      this.bulkUpdateForm.controls['scheduledDate'].clearValidators();
      this.bulkUpdateForm.controls[
        'scheduledDate'
      ].updateValueAndValidity();
      this.bulkUpdateForm.controls['reason'].clearValidators();
      this.bulkUpdateForm.controls['reason'].updateValueAndValidity();
    }
  }

  deselectStatuses() {
    this.bulkUpdateForm.controls['reason'].setValue('');
    this.bulkUpdateForm.controls['status'].setValue('');
    this.selectedStatus = '';
    this.hideStatus = false;
  }
  
  removeLead(id: string): void {        
    const node = this.selectedNodes?.filter(
      (lead: any) => lead?.data?.id === id 
    );
    
    this.gridApi?.deselectNode(node?.[0]);

    this.sameStatusRows = this.sameStatusRows.filter(
      (lead: any) => lead.id !== id
    );
    if (this.sameStatusRows.length <= 0) {      
      this.modal.hide();
    }
  }

  openConfirmDeleteModal(leadName: string, leadId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: leadName,
      fieldType: 'from the selection',
    };
    this.bsModalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.bsModalRef?.onHide) {
      this.bsModalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeLead(leadId);
        }
      });
    }
  }

  toggleStatus() {
    this.bulkUpdateForm.reset();
    this.hideStatus = false;
  }
  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
