<div class="px-30">
    <div class="bg-white border mt-10">
        <div class="w-100 border-bottom">
            <form autocomplete="off" class="align-center w-100 py-10 px-12 no-validation">
                <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
                <input placeholder="type to search" name="search" class="border-0 outline-0 w-100"
                    (input)="search($event)" autocomplete="off" id="inpSearchQrForm">
            </form>
        </div>
        <ng-container *ngIf="noAmenitiesFound; else amenitiesList">
            <div class="flex-center-col h-100-200">
                <img src="assets/images/layered-cards.svg" alt="No amenities found">
                <div class="header-3 fw-600 text-center">No Amenities Found</div>
            </div>
        </ng-container>
        <ng-template #amenitiesList>
            <ng-container *ngIf="!amenitiesLoading; else skeleton">
                <div class="px-20 pb-10 h-100-233 flex-wrap scrollbar">
                    <ng-container *ngFor="let category of filteredAmenities">
                        <ng-container *ngIf="category?.amenities?.length">
                            <div class="flex-between mt-20">
                                <h4 class="fw-semi-bold mb-4 text-decoration-underline">
                                    {{category?.categoryName}}
                                </h4>
                                <label class="checkbox-container ml-10">
                                    <input type="checkbox" (change)="onSelectAllChange(category, $event)"
                                        [checked]="isAllSelected(category)">
                                    <span class="checkmark" [class.select-all]="isAllSelected(category)"></span>
                                    Select All
                                </label>
                            </div>
                            <div class="d-flex flex-wrap flex-grow-1">
                                <ng-container *ngFor="let amenity of category?.amenities">
                                    <div class="w-20 tb-w-25 ip-w-50 ph-w-100 position-relative tag-card">
                                        <div [ngClass]="amenity?.selected ? 'border-bottom-black' : 'border'"
                                            class="box-shadow-40 d-flex bg-white h-80 mt-10 mr-10 br-4 p-8">
                                            <label class="checkbox-container mb-4">
                                                <input type="checkbox" class="mr-10" [value]="amenity.amenityName"
                                                    [checked]="(amenity.selected || selectedAmenities == amenity?.id) ? 'checked' : null"
                                                    (change)="amenity.selected=$event.target.checked;emitSelection();">
                                                <span class="checkmark"></span>
                                            </label>
                                            <div class="flex-center-col w-100 mr-6">
                                                <img *ngIf="amenity?.imageURL && amenity?.inActiveImageURL; else dummy"
                                                    [src]="amenity?.selected ? amenity?.imageURL : amenity?.inActiveImageURL"
                                                    class="obj-fill" width="23px" height="15px">
                                                <h5 [title]="amenity?.amenityDisplayName"
                                                    [ngClass]="amenity?.selected ? 'text-black-100 fw-semi-bold': ' text-light-gray fw-400'"
                                                    class="mt-4 text-center">
                                                    {{amenity?.amenityDisplayName}}
                                                </h5>
                                                <ng-template #dummy>
                                                    <span class="icon ic-black ic-sm">
                                                        {{getFirstCharacter(amenity?.amenityDisplayName)}}</span>
                                                </ng-template>
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                        </ng-container>
                    </ng-container>
                </div>
            </ng-container>
        </ng-template>
    </div>
</div>
<div class="flex-end px-20 py-16 bg-white box-shadow-10">
    <u class="mr-20 text-black-200 text-large fw-bold cursor-pointer mr-10" (click)="manageProject()">Cancel</u>
    <button class="btn-coal" (click)="saveAndNext()">Save & Go To Next</button>
</div>
<ng-template #skeleton>
    <div class="px-20 pb-10 h-100-189 flex-wrap scrollbar pe-none blinking">
        <ng-container *ngFor="let _ of [1, 2, 3]">
            <h4 class="fw-semi-bold pt-20 mb-4">
                <span
                    class="bg-grey">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </h4>
            <div class="d-flex flex-wrap flex-grow-1">
                <ng-container *ngFor="let _ of [1, 2, 3, 4, 5]">
                    <div class="w-20 tb-w-25 ip-w-50 ph-w-100 position-relative">
                        <div class="box-shadow-40 d-flex bg-white h-80 mt-10 mr-10 br-4 p-8">
                            <span
                                class="bg-grey w-100">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        </div>
                        <div
                            class="d-flex position-absolute bg-white top-16 right-12 h-60 w-90 flex-center action-option opacity-0">
                            <span
                                class="bg-grey">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        </div>
                    </div>
                </ng-container>
            </div>
        </ng-container>
    </div>
</ng-template>