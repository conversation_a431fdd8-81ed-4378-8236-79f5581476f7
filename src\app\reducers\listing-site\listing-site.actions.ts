import { Action } from "@ngrx/store";

export enum ListingSiteActionTypes {
    FETCH_LISTING_SOURCE = '[Listing] Fetch Listing Source',
    FETCH_LISTING_SOURCE_SUCCESS = '[Listing] Fetch Listing Source Success',
    FETCH_LISTING_SOURCE_WITH_ID = '[Listing] Fetch Listing Source With Id',
    FETCH_LISTING_SOURCE_WITH_ID_SUCCESS = '[Listing] Fetch Listing Source With Id Success',
    FETCH_ALL_LISTING = '[Listing] Fetch all Listing',
    FETCH_ALL_LISTING_SUCCESS = '[Listing] Fetch all Listing Success',
    UPDATE_LISTING_PAYLOAD = '[Listing] Update Listing Payload',
    FETCH_LISTING_TOP_COUNT = '[Listing] Fetch Listing Top Count',
    FETCH_LISTING_TOP_COUNT_SUCCESS = '[Listing] Fetch Listing Top Count Success',
    FETCH_LISTING_BASE_COUNT = '[Listing] Fetch Listing Base Count',
    FETCH_LISTING_BASE_COUNT_SUCCESS = '[Listing] Fetch Listing Base Count Success',
    FETCH_LISTING_ACOUNTS = '[Listing] Fetch Listing Acounts',
    FETCH_LISTING_ACOUNTS_SUCCESS = '[Listing] Fetch Listing Acounts Success',
    CLEAR_LISTING_SEARCH = '[Listing] Clear Listing Search',
    ADD_LISTING_ACOUNTS = '[Listing] Add Listing Acounts',
    ADD_LISTING_ACOUNTS_SUCCESS = '[Listing] Add Listing Acounts Success',
    UPDATE_LISTING_ACOUNTS = '[Listing] Update Listing Acounts',
    UPDATE_LISTING_ACOUNTS_SUCCESS = '[Listing] Update Listing Acounts Success',
    DELETE_LISTING_ACOUNTS = '[Listing] Delete Listing Acounts',
    DELETE_LISTING_ACOUNTS_SUCCESS = '[Listing] Delete Listing Acounts Success',
    SYNC_LISTING = '[Listing] Sync Listing',
    SYNC_LISTING_SUCCESS = '[Listing] Sync Listing Success',
    FETCH_SYNC_LISTING_LIST = '[Listing] Fetch Sync Listing List',
    FETCH_SYNC_LISTING_LIST_SUCCESS = '[Listing] Fetch Sync Listing List Success',
    ADD_TO_LIST = '[LISTING] ADD TO LIST',
    DELIST = '[LISTING] De-List',
    EXCEL_UPLOAD = '[Listing] Excel Upload',
    EXCEL_UPLOAD_SUCCESS = '[Listing] Excel Upload Success',
    UPLOAD_MAPPED_COLUMNS = '[Listing] Upload Mapped Columns',
    FETCH_COMMUNITIES = '[Listing] Fetch Communities',
    FETCH_COMMUNITIES_SUCCESS = '[Listing] Fetch Communities Success',
    FETCH_SUB_COMMUNITIES = '[Listing] Fetch Sub-Communities',
    FETCH_SUB_COMMUNITIES_SUCCESS = '[Listing] Fetch Sub-Communities Success',
    FETCH_EXCEL_TRACKER_LIST = '[Listing] Fetch Excel Tracker List',
    FETCH_EXCEL_TRACKER_LIST_SUCCESS = '[Listing] Fetch Excel Tracker List Success',
    FETCH_ADDRESS_WITH_ID = '[Listing] Fetch Address With Id',
    FETCH_ADDRESS_WITH_ID_SUCCESS = '[Listing] Fetch Address With Id Success',
    LISTING_EXPORT = '[Listing] Listing Export',
    LISTING_EXPORT_SUCCESS = '[Listing] Listing Export Success',
    FETCH_SYNC_LISTING_SOURCE = '[Listing] Fetch Sync Listing Source',
    FETCH_SYNC_LISTING_SOURCE_SUCCESS = '[Listing] Fetch Sync Listing Source Success',
}

export class FetchListingSource implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_SOURCE;
    constructor() { }
}

export class FetchListingSourceSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_SOURCE_SUCCESS;
    constructor(public resp: any) { }
}

export class FetchListingSourceWithId implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_SOURCE_WITH_ID;
    constructor() { }
}

export class FetchListingSourceWithIdSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_SOURCE_WITH_ID_SUCCESS;
    constructor(public resp: any) { }
}

export class FetchAllListing implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_ALL_LISTING;
    constructor() { }
}

export class FetchAllListingSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_ALL_LISTING_SUCCESS;
    constructor(public resp: any) { }
}

export class FetchListingTopCount implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_TOP_COUNT;
    constructor() { }
}

export class FetchListingTopCountSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_TOP_COUNT_SUCCESS;
    constructor(public resp: any) { }
}

export class FetchListingBaseCount implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_BASE_COUNT;
    constructor() { }
}

export class FetchListingBaseCountSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_BASE_COUNT_SUCCESS;
    constructor(public resp: any) { }
}

export class UpdateListingPayload implements Action {
    readonly type: string = ListingSiteActionTypes.UPDATE_LISTING_PAYLOAD;
    constructor(public payload: any) { }
}

export class FetchListingAcounts implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_ACOUNTS;
    constructor(public id: any) { }
}

export class FetchListingAcountsSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_LISTING_ACOUNTS_SUCCESS;
    constructor(public resp: any) { }
}

export class ClearListingSearch implements Action {
    readonly type: string = ListingSiteActionTypes.CLEAR_LISTING_SEARCH;
    constructor() { }
}

export class AddListingAcounts implements Action {
    readonly type: string = ListingSiteActionTypes.ADD_LISTING_ACOUNTS;
    constructor(public payload: any) { }
}

export class AddListingAcountsSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.ADD_LISTING_ACOUNTS_SUCCESS;
    constructor() { }
}

export class UpdateListingAcounts implements Action {
    readonly type: string = ListingSiteActionTypes.UPDATE_LISTING_ACOUNTS;
    constructor(public payload: any) { }
}

export class UpdateListingAcountsSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.UPDATE_LISTING_ACOUNTS_SUCCESS;
    constructor() { }
}

export class DeleteListingAcounts implements Action {
    readonly type: string = ListingSiteActionTypes.DELETE_LISTING_ACOUNTS;
    constructor(public payload: any) { }
}


export class syncListing implements Action {
    readonly type: string = ListingSiteActionTypes.SYNC_LISTING;
    constructor(public payload: any) { }
}

export class syncListingSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.SYNC_LISTING_SUCCESS;
    constructor() { }
}

export class FetchSyncListingList implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_SYNC_LISTING_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchSyncListingListSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_SYNC_LISTING_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class AddToList implements Action {
    readonly type: string = ListingSiteActionTypes.ADD_TO_LIST;
    constructor(public payload: any) { }
}

export class DeList implements Action {
    readonly type: string = ListingSiteActionTypes.DELIST;
    constructor(public payload: any) { }
}

export class ExcelUpload implements Action {
    readonly type: string = ListingSiteActionTypes.EXCEL_UPLOAD;
    constructor(public file: File) { }
}
export class ExcelUploadSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.EXCEL_UPLOAD_SUCCESS;
    constructor(public resp: any) { }
}
export class UploadAddressMappedColumns implements Action {
    readonly type: string = ListingSiteActionTypes.UPLOAD_MAPPED_COLUMNS;
    constructor(public payload: any) { }
}

export class FetchCommunities implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_COMMUNITIES;
    constructor() { }
}

export class FetchCommunitiesSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_COMMUNITIES_SUCCESS;
    constructor(public resp: any) { }
}

export class FetchSubCommunities implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_SUB_COMMUNITIES;
    constructor() { }
}

export class FetchSubCommunitiesSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_SUB_COMMUNITIES_SUCCESS;
    constructor(public resp: any) { }
}

export class FetchExcelTrackerList implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_EXCEL_TRACKER_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchExcelTrackerListSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_EXCEL_TRACKER_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class FetchAddress implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_ADDRESS_WITH_ID;
    constructor() { }
}

export class FetchAddressSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_ADDRESS_WITH_ID_SUCCESS;
    constructor(public resp: any) { }
}

export class ListingExport implements Action {
    readonly type: string = ListingSiteActionTypes.LISTING_EXPORT;
    constructor(public payload: any) { }
}

export class ListingExportSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.LISTING_EXPORT_SUCCESS;
    constructor(public resp: any = '') { }
}

export class FetchSyncListingSource implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_SYNC_LISTING_SOURCE;
    constructor() { }
}

export class FetchSyncListingSourceSuccess implements Action {
    readonly type: string = ListingSiteActionTypes.FETCH_SYNC_LISTING_SOURCE_SUCCESS;
    constructor(public resp: any) { }
}
