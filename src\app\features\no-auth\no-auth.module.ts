import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { RouterModule } from '@angular/router';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { DragScrollModule } from 'ngx-drag-scroll';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';

import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import {
  NoAuthRoutingModule,
  routes,
} from 'src/app/features/no-auth/no-auth-routing.module';
import { BrochuresComponent } from 'src/app/features/no-auth/project-microsite/brochures/brochures.component';
import { ProjectAmenitiesComponent } from 'src/app/features/no-auth/project-microsite/project-amenities/project-amenities.component';
import { ProjectDetailsComponent } from 'src/app/features/no-auth/project-microsite/project-details/project-details.component';
import { ProjectEnquiryFormComponent } from 'src/app/features/no-auth/project-microsite/project-enquiry-form/project-enquiry-form.component';
import { ProjectImagesComponent } from 'src/app/features/no-auth/project-microsite/project-images/project-images.component';
import { ProjectLocationComponent } from 'src/app/features/no-auth/project-microsite/project-location/project-location.component';
import { ProjectMicrositeComponent } from 'src/app/features/no-auth/project-microsite/project-microsite.component';
import { ProjectVideosComponent } from 'src/app/features/no-auth/project-microsite/project-videos/project-videos.component';
import { UnitRangeComponent } from 'src/app/features/no-auth/project-microsite/unit-range/unit-range.component';
import { AboutPropertyComponent } from 'src/app/features/no-auth/property-microsite/about-property/about-property.component';
import { AmenitiesListComponent } from 'src/app/features/no-auth/property-microsite/amenities-list/amenities-list.component';
import { EnquiryFormComponent } from 'src/app/features/no-auth/property-microsite/enquiry-form/enquiry-form.component';
import { MsSubscriptionComponent } from 'src/app/features/no-auth/property-microsite/ms-subscription/ms-subscription.component';
import { PropertyAttributesComponent } from 'src/app/features/no-auth/property-microsite/property-attributes/property-attributes.component';
import { PropertyDetailsComponent } from 'src/app/features/no-auth/property-microsite/property-details/property-details.component';
import { PropertyMicrositeComponent } from 'src/app/features/no-auth/property-microsite/property-microsite.component';
import { PropertyVideosComponent } from 'src/app/features/no-auth/property-microsite/property-videos/property-videos.component';
import { SimilarPropertiesComponent } from 'src/app/features/no-auth/property-microsite/similar-properties/similar-properties.component';
import { LeadQRFormComponent } from 'src/app/features/no-auth/lead-qr-form/lead-qr-form.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { ReferEarnComponent } from 'src/app/features/no-auth/refer-earn/refer-earn.component';
import { QRConfirmationComponent } from './lead-qr-form/qr-confirmation/qr-confirmation.component';

@NgModule({
  declarations: [
    LeadQRFormComponent,
    PropertyMicrositeComponent,
    PropertyDetailsComponent,
    PropertyAttributesComponent,
    AboutPropertyComponent,
    SimilarPropertiesComponent,
    EnquiryFormComponent,
    AmenitiesListComponent,
    MsSubscriptionComponent,
    ProjectMicrositeComponent,
    ProjectEnquiryFormComponent,
    BrochuresComponent,
    UnitRangeComponent,
    ProjectLocationComponent,
    ProjectVideosComponent,
    ProjectAmenitiesComponent,
    ProjectDetailsComponent,
    ProjectImagesComponent,
    PropertyVideosComponent,
    ReferEarnComponent,
    QRConfirmationComponent,
    // InViewDirective
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    NoAuthRoutingModule,
    SharedModule,
    GoogleMapsModule,
    NgxMatIntlTelInputComponent,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    LottieModule.forRoot({ player: playerFactory }),
    FormsModule,
    ReactiveFormsModule,
    DragScrollModule,
  ],
  exports: [DragScrollModule],
})
export class NoAuthModule { }
