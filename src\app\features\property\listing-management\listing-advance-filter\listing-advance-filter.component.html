<div class="property-adv-filter px-30 bg-white brbl-15 brbr-15">
  <div class="h-100-100 scrollbar position-relative">
    <div class="flex-between ip-flex-start ip-flex-col ip-col-reverse">
      <div class="flex-column">
        <div class="field-label">Listing Status</div>
        <div class="d-flex flex-wrap">
          <div *ngFor="let status of listingStatus">
            <div name="PropertyVisiblity" (click)="filtersForm.get('PropertyVisiblity')?.setValue(status?.enumValue);"
              class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
              [id]="'clkProp' + status?.enumValue" [attr.data-automate-id]="'clkProp' + status.enumValue"
              [ngClass]="getFormValue('PropertyVisiblity') === status?.enumValue ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
              {{ status?.displayName }}
            </div>
          </div>
        </div>
      </div>
      <div class="filters-grid d-flex pl-0">
        <div class="dropdown-date-picker d-flex rounded">
          <div class="bg-white rounded-start manage-select datefilter-scroll">
            <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}" [searchable]="false"
              ResizableDropdown class="lead-date ip-max-w-80px min-w-60" [(ngModel)]="dateType" (change)="dateChange()">
              <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                [value]="dType">{{dType}}</ng-option>
            </ng-select>
          </div>
          <div class="date-picker align-center py-4 rounded-end" id="leadsAppointmentDate"
            data-automate-id="leadsAppointmentDate">
            <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
            <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
              class="pl-20 ph-pl-12 text-large ph-w-150px" placeholder="ex. 5-03-2025 - 14-03-2025"
              (ngModelChange)="filterDate = $event; dateChange()" [ngModel]="filterDate" />
            <owl-date-time [pickerType]="'calendar'" #dt1
              (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
          </div>
        </div>
        <div *ngIf="filterDate[0]" class="bg-coal px-8 cursor-pointer align-center br-6 ml-10"
          (click)="onResetDateFilter()">
          <span class="ic-refresh ic-white"></span>
        </div>
      </div>
    </div>
    <div class="field-label">Offering Type</div>
    <div class="d-flex flex-wrap">
      <div *ngFor="let type of offeringType">
        <div name="FirstLevelFilter" (click)="filtersForm.get('FirstLevelFilter')?.setValue(type?.enumValue);"
          class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer" [id]="'clkProp' + type?.enumValue"
          [attr.data-automate-id]="'clkProp' + type.enumValue"
          [ngClass]="getFormValue('FirstLevelFilter') === type?.enumValue ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
          {{ type?.displayName }}
        </div>
      </div>
    </div>
    <div class="field-label">{{'LEADS.filter-by' | translate}}</div>
    <div class="d-flex flex-wrap">
      <div (click)="filtersForm.get('EnquiredFor')?.setValue(null)"
        class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
        [ngClass]="!getFormValue('EnquiredFor') ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
        {{ 'GLOBAL.all' | translate }}
      </div>
      <div *ngFor="let enquiry of enquiredFor">
        <div name="enquiry" (click)="filtersForm.get('EnquiredFor')?.setValue(getEnquiryType(enquiry.type))"
          class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer" id="clkProp{{enquiry.type}}"
          data-automate-id="clkProp{{enquiry.type}}"
          [ngClass]="getFormValue('EnquiredFor') === getEnquiryType(enquiry.type) ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
          {{ enquiry.type }}
        </div>
      </div>
    </div>
    <form [formGroup]="filtersForm">
      <div class="d-flex w-100 flex-wrap ng-select-sm">
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Listing By</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="allUserList" [multiple]="true" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" formControlName="UserIds" bindValue="id"
              bindLabel="fullName" [ngClass]="{'pe-none blinking': allUserListIsLoading}" ResizableDropdown>
              <ng-template ng-label-tmp let-item="item" let-clear="clear">
                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                <span class="ng-value-label">
                  {{item.firstName + ' ' + item.lastName}}
                </span>
              </ng-template>
              <ng-template ng-label-tmp let-item="item" let-clear="clear">
                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                <span class="ng-value-label">
                  {{item.firstName + ' ' + item.lastName}}
                </span>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="flex-between">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item.firstName}}
                      {{item.lastName}}</span></div> <span class="text-disabled" *ngIf="!item.isActive">( Disabled
                    )</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Listing On Behalf</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="allUserList" [multiple]="true" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" formControlName="ListingOnBehalf" bindValue="id"
              bindLabel="fullName" [ngClass]="{'pe-none blinking': allUserListIsLoading}" ResizableDropdown>
              <ng-template ng-label-tmp let-item="item" let-clear="clear">
                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                <span class="ng-value-label">
                  {{item.firstName + ' ' + item.lastName}}
                </span>
              </ng-template>
              <ng-template ng-label-tmp let-item="item" let-clear="clear">
                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                <span class="ng-value-label">
                  {{item.firstName + ' ' + item.lastName}}
                </span>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="flex-between">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item.firstName}}
                      {{item.lastName}}</span></div> <span class="text-disabled" *ngIf="!item.isActive">( Disabled
                    )</span>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Select Emirates</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="uaeEmiratesList" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="value"
              formControlName="UaeEmirates" ResizableDropdown>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Finishing Type</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="finishingTypeList" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="value"
              formControlName="FinishingType" ResizableDropdown>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Completion Status</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="completionStatusList" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="enumValue"
              formControlName="CompletionStatus" ResizableDropdown>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Listing Level</div>
          <div class="mr-20">
            <ng-select [virtualScroll]="true" [items]="ListingLevel" [closeOnSelect]="false"
              placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="enumValue"
              formControlName="ListingLevel" ResizableDropdown>
            </ng-select>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="canViewOwner">
          <div class="field-label">Owner Name</div>
          <ng-select [virtualScroll]="true" formControlName="OwnerNames" [items]="ownerNames" [multiple]="true"
            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
            class="mr-20" ResizableDropdown>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Property Title</div>
          <div class="no-validation mr-20">
            <form-errors-wrapper label="Property Title">
              <div class="border-gray br-6">
                <input formControlName="PropertyTitle" id="inpPropertyTitle" data-automate-id="inpPropertyTitle"
                  placeholder="enter property title" class="border-0">
              </div>
            </form-errors-wrapper>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Reference No</div>
          <div class="no-validation mr-20">
            <form-errors-wrapper>
              <div class="border-gray br-6">
                <input formControlName="SerialNo" placeholder="enter reference no" class="border-0">
              </div>
            </form-errors-wrapper>
          </div>
        </div>
        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
          <div class="field-label">Available From</div>
          <div class="mr-20">
            <app-possession-filter #possessionFilter [initialPossessionType]="getFormValue('PossesionType')"
              [initialFromPossessionDate]="getFormValue('FromPossesionDate')"
              [initialToPossessionDate]="getFormValue('ToPossesionDate')"
              [userTimeZoneOffset]="userData?.timeZoneInfo?.baseUTcOffset"
              [customDateFilterList]="listingPossessionDateFilterList"
              (possessionFilterChange)="onPossessionFilterChange($event)">
            </app-possession-filter>
          </div>
        </div>

        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 position-relative">
          <div class="field-label">Lead Count</div>
          <div class="mr-20">
            <div class="w-100 align-center">
              <div class="w-100 no-input-validation">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxLeadCheck()"
                        [max]="filtersForm?.value?.MaxLeadCount" placeholder="ex. 10" maxlength="10"
                        formControlName="MinLeadCount">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxLeadCheck()"
                        [min]="filtersForm?.value?.MinLeadCount" placeholder="ex. 1000" maxlength="10"
                        formControlName="MaxLeadCount">
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="text-xs text-red fw-semi-bold position-absolute"
                  *ngIf="(filtersForm?.value?.MinLeadCount && filtersForm?.value?.MaxLeadCount) && !minMaxLeadValidation">
                  {{'Min lead cannot be greater than max lead'}}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 position-relative">
          <div class="field-label">Data Count</div>
          <div class="mr-20">
            <div class="w-100 align-center">
              <div class="w-100 no-input-validation">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxDataCheck()"
                        [max]="filtersForm?.value?.MaxProspectCount" placeholder="ex. 10" maxlength="10"
                        formControlName="MinProspectCount">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="w-50">
                      <input type="number" (keydown)="onlyNumbers($event)" (input)="minMaxDataCheck()"
                        [min]="filtersForm?.value?.MinProspectCount" placeholder="ex. 1000" maxlength="10"
                        formControlName="MaxProspectCount">
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="text-xs text-red fw-semi-bold position-absolute"
                  *ngIf="(filtersForm?.value?.MinProspectCount && filtersForm?.value?.MaxProspectCount) && !minMaxDataValidation">
                  {{'Min data cannot be greater than max lead'}}
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Property Type:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{ 'LABEL.property' | translate }} {{ 'LABEL.type' | translate }}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="propertyTypeList" [closeOnSelect]="true"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
                formControlName="BasePropertyTypeId" ResizableDropdown (change)="updatePropertySubType()">
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LABEL.property'| translate}} {{'LABEL.sub-type'| translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="propertySubTypes" [multiple]="true" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id"
                formControlName="PropertySubTypes" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item.displayName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">BR</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="NoOfBHK" [items]="noOfBhk" [multiple]="true"
                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                  <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                  <span class="ng-value-label"> {{getBRDisplayString(item)}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                      class="checkmark"></span>{{getBRDisplayString(item)}}
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Area:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Property Area</div>
            <div class="w-100 align-center">
              <div class="w-60pr no-input-validation">
                <form-errors-wrapper>
                  <div class="w-100 d-flex">
                    <div class="br-6 w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="sizevalidation()" formControlName="MinPropertySize" id="inpPropSize"
                        data-automate-id="inpPropSize" placeholder="ex. 123">
                    </div>
                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                    <div class="br-6 w-50">
                      <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                        (input)="sizevalidation()" formControlName="MaxPropertySize" id="inpPropSize"
                        data-automate-id="inpPropSize" placeholder="ex. 123">
                    </div>
                  </div>
                </form-errors-wrapper>
              </div>
              <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                *ngIf="( getFormValue('MinPropertySize') && getFormValue('MaxPropertySize')) && !sizevalidations">
                {{'PROPERTY.area-validation' | translate}}</div>
              <div class="w-40pr ml-8 mr-20">
                <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                  <ng-select [virtualScroll]="true" formControlName="PropertySizeUnit" tabindex="4"
                    placeholder="ex. sq. feet." [items]="areaSizeUnits"
                    [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id" bindLabel="unit"
                    ResizableDropdown></ng-select>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Budget:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 field-rupees-tag">
            <div class="field-label">{{'GLOBAL.min' | translate}} {{'LABEL.budget' | translate}}</div>
            <div class="position-relative mt-4 mr-20 lead adv-dropdown">
              <form-errors-wrapper>
                <input type="number" [max]="getFormValue('MaxPrice')" id="inpLeadBudget"
                  data-automate-id="inpLeadBudget" (input)="budgetCheck()" (keydown)="onlyNumbers($event)"
                  placeholder="ex. 10" min="0" formControlName="MinPrice">
                <div class="no-validation">
                  <ng-container *ngIf="propertyCurrency?.length > 1; else showCurrencySymbol">
                    <ng-select formControlName="Currency" ResizableDropdown
                      class="mt-2 ml-2 position-absolute top-0 manage-dropdown">
                      <ng-option *ngFor="let curr of propertyCurrency" [value]="curr">{{curr}}
                      </ng-option>
                    </ng-select>
                  </ng-container>
                  <ng-template #showCurrencySymbol>
                    <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                  </ng-template>
                </div>
              </form-errors-wrapper>
              <div class="text-xs text-red fw-semi-bold position-absolute"
                *ngIf="(getFormValue('MinPrice') && getFormValue('MaxPrice')) && !budgetValidation">
                {{'LEADS.budget-validation' | translate}}</div>
              <div *ngIf="getFormValue('MinPrice')" class="position-absolute right-10 top-15">
                <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                  formatBudget(getFormValue('MinPrice'),getFormValue('Currency') || defaultCurrency
                  )
                  }}</span>
              </div>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100 field-rupees-tag">
            <div class="field-label">{{'GLOBAL.max' | translate}} {{'LABEL.budget' | translate}}</div>
            <div class="position-relative mr-20 lead adv-dropdown">
              <form-errors-wrapper>
                <input type="number" [min]="getFormValue('MinPrice')" id="inpLeadBudget"
                  data-automate-id="inpLeadBudget" placeholder="ex. 1000000" (keydown)="onlyNumbers($event)" min="0"
                  formControlName="MaxPrice" (input)="budgetCheck()">
                <div class="no-validation">
                  <ng-container *ngIf="propertyCurrency?.length > 1; else showCurrencySymbol">
                    <ng-select formControlName="Currency" ResizableDropdown
                      class="mt-2 ml-2 position-absolute top-0 manage-dropdown">
                      <ng-option *ngFor="let curr of propertyCurrency" [value]="curr">{{curr}}
                      </ng-option>
                    </ng-select>
                  </ng-container>
                </div>
              </form-errors-wrapper>
              <div *ngIf="getFormValue('MaxPrice')" class="position-absolute right-10 top-15">
                <span class="text-nowrap text-xs text-accent-green fw-semi-bold">
                  {{ formatBudget(getFormValue('MaxPrice'),getFormValue('Currency') || defaultCurrency)
                  }}</span>
              </div>
            </div>
          </div>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Location:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LOCATION.locality'| translate}} / {{ 'PROJECTS.area' | translate }}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="localityList" formControlName="Locations" [multiple]="true"
                [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isLocationListLoading}"
                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Community</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="Communities" [items]="communityList" [multiple]="true"
                [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isCommunityLoading}"
                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Sub-Community</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="SubCommunities" [items]="subCommunityList"
                [multiple]="true" [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isSubCommunityLoading}"
                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LOCATION.city'| translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="Cities" [items]="cityList" [multiple]="true"
                [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isLocationListLoading}"
                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">{{'LOCATION.state' | translate}}</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="States" [items]="stateList" [multiple]="true"
                [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isLocationListLoading}"
                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Country</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="Countries" [items]="countryList" [multiple]="true"
                [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isLocationListLoading}"
                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
        <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Attributes & Amenities:</legend>
        <div class="d-flex w-100 flex-wrap ng-select-sm">
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Furnish Status</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="FurnishStatuses" [items]="furnishStatus"
                [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                bindLabel="value" bindValue="dispName" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item.dispName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <!-- <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
        <div class="field-label">{{'SIDEBAR.project'| translate}}</div>
        <div class="mr-20">
          <ng-select [virtualScroll]="true" [items]="projectList" [multiple]="true" [closeOnSelect]="false"
            [ngClass]="{'pe-none blinking': isProjectsListLoading}" placeholder="{{'GLOBAL.select' | translate}}"
            bindLabel="id"  ResizableDropdown>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                  [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div> -->
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Facing</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" [items]="facing" [closeOnSelect]="false"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="value"
                formControlName="Facing" ResizableDropdown>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">No. of Bathrooms</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="NoOfBathrooms" [items]="numbers" [multiple]="true"
                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value"
                bindValue="value" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                      class="checkmark"></span>{{item.display}}
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">No. of Bedrooms</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="NoOfBedrooms" [items]="numbers" [multiple]="true"
                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value"
                bindValue="value" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                      class="checkmark"></span>{{item.display}}
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">No. of Parking</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="Parking" [items]="numbers" [multiple]="true"
                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value"
                bindValue="value" ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                      class="checkmark"></span>{{item.display}}
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label">Amenities</div>
            <div class="mr-20">
              <ng-select [virtualScroll]="true" formControlName="Amenities" [items]="amenities" [multiple]="true"
                [closeOnSelect]="false" [ngClass]="{'pe-none blinking': isAmenitiesLoading}"
                placeholder="{{'GLOBAL.select' | translate}}" bindLabel="amenityDisplayName" bindValue="id"
                ResizableDropdown>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                      class="text-truncate-1 break-all">{{item.amenityDisplayName}}</span>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </div>
      </fieldset>
    </form>
  </div>
  <div class="flex-end py-20">
    <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">{{'BUTTONS.cancel' |
      translate }}</u>
    <button class="btn-gray" (click)="onClearAllFilters()">{{ 'GLOBAL.reset' | translate }}</button>
    <button (click)="applyAdvancedFilter()" class="btn-coal ml-20">{{ 'GLOBAL.search' | translate }}</button>
  </div>
</div>