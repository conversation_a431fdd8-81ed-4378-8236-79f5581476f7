<div class="p-20 h-100 bg-light-pearl text-mud">
  <div class="bg-dark br-30 h-100 position-relative">
    <!-- bg-white tb-bg-dark  -->
    <!-- <img src="../../../../assets/images/refer-earn/refer-earn-banner.png" alt=""
      class="h-100 position-absolute bottom-0 left-0 br-30 tb-d-none"> -->
    <ng-lottie [options]='building' class="h-100 position-absolute bottom-0 left-0"></ng-lottie>
    <div class="login-container login-form scrollbar position-absolute top-0 right-0 scroll-hide">
      <img [type]="'leadrat'" [appImage]="appImages?.appFull" alt="App Image">
      <router-outlet></router-outlet>
      <div class="mt-40 justify-between">
        <div class="fw-semi-bold text-sm">© {{currentYear}} {{getAppName()}}</div>
        <div class="d-flex">
          <a [href]="appStoreUrl" target="_blank" class="dot dot-lg-xxl bg-white shadow mr-10 cursor-pointer"
            (click)="trackingService?.trackFeature('Web.Login.Link.IOSApp.Click')">
            <span class="icon ic-sm ic-ios ic-black"></span></a>
          <a [href]="playStorUrl" target="_blank" class="dot dot-lg-xxl bg-accent-green shadow cursor-pointer"
            (click)="trackingService?.trackFeature('Web.Login.Link.AndriodApp.Click')">
            <span class="icon ic-sm ic-playstore"></span></a>
        </div>
      </div>
    </div>
    <div class="login-container support" *ngIf="isShowSupport">
      <h5 class="fw-600 mx-10">{{ 'AUTH.contact-support' | translate }}</h5>
      <a [href]="'tel:'+supportNumber" class="br-10 border-gray w-100 px-24 py-8 mt-16 align-center shadow-hover-sm">
        <span class="icon ic-call-support ic-xl ic-dark"></span>
        <div class="ml-20">
          <h5 class="fw-600">{{ 'AUTH.call-support' | translate }}</h5>
          <div class="text-sm">{{supportNumber}}</div>
        </div>
      </a>
      <a [href]="getWhatsappLink()" target="_blank"
        class="br-10 border-gray w-100 px-24 py-8 mt-16 align-center shadow-hover-sm">
        <span class="icon ic-whatsapp-support ic-xl ic-dark"></span>
        <div class="ml-20">
          <h5 class="fw-600">{{ 'AUTH.whatsapp-support' | translate }}</h5>
          <div class="text-sm">{{supportNumber}}</div>
        </div>
      </a>
    </div>
    <div (click)="isShowSupport = !isShowSupport" class="cursor-pointer">
      <img src="../../../../assets/images/login.svg" alt="chat-bots"
        class="position-absolute left-0 bottom-0 ph-w-100-40">
      <img [type]="'leadrat'" [appImage]="s3BucketUrl + 'logos/single-muso.svg'" alt="muso"
        class="position-absolute muso ph-w-50px">
      <div class="position-absolute bottom-60 left-250 align-center ph-left-150 ph-bottom-50">
        <ng-lottie [options]='online' width="30px"></ng-lottie>
        <div>{{ 'AUTH.online' | translate }}</div>
      </div>
    </div>
  </div>
</div>