import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';
import { TourService } from 'src/app/services/shared/appTour.service';

@Component({
  selector: 'app-tour',
  templateUrl: './app-tour.component.html',
})
export class AppTourComponent implements OnInit {
  currentStep: any = null;
  steps: any[] = [];
  currentRoute: string = '';
  currentStepIndex: number = 0;
  isAppTourEnabled: boolean;
  positionStyle: any = {};
  constructor(
    private router: Router,
    private tourService: TourService,
  ) { }

  ngOnInit(): void {
    this.isAppTourEnabled = JSON.parse(localStorage.getItem('appTour') || 'false');
    this.currentRoute = this.router.url;    
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.currentRoute = this.router.url;
      this.setTourStepsBasedOnRoute();
    this.tourService.getTourStepObservable().subscribe((step) => {
      this.currentStep = step;
      this.currentStepIndex = this.tourService.getCurrentStepIndex();
      this.updateTourPosition();
    });
  });
  }

  setTourStepsBasedOnRoute() {
    if (this.currentRoute.includes('/dashboard/metrics')) {
      const steps = [
        { content: 'This is the sales overview.', element: '#salesoverview' },
        { content: 'You can add new sales data here.', element: '#add-sales' },
        { content: 'This is a sample step.', element: '#ad-sale' },
        { content: 'Hate u', element: '#add' },
        { content: 'Another sample step.', element: '#add-sal' },
        { content: 'Another sample steddddp.', element: '#emo' },
      ];
      this.steps = steps;
      this.tourService.startTour(steps);
      this.currentStep = steps[0];
      this.currentStepIndex = 0;
      setTimeout(() => {
        this.updateTourPosition();
      }, 100);
    } else if (this.currentRoute.includes('/global-config')) {
      const steps = [
        { content: 'View customer data here.', element: '#customerlist' },
        { content: 'Search customers by name or ID.', element: '#search-customers' },
      ];
      this.steps = steps;
      this.tourService.startTour(steps);
      this.currentStep = steps[0];
      this.currentStepIndex = 0;
      setTimeout(() => {
        this.updateTourPosition();
      }, 100);
    }
  }

  updateTourPosition(returnType?: string) {
    if (this.currentStep?.element && this.isAppTourEnabled === true) {
      const existingTooltips = document.querySelectorAll('.app-tour-tooltip');
      existingTooltips.forEach(tooltip => tooltip.remove());

      const parentElement = document.querySelector(this.currentStep.element);
      if (!parentElement) {
        if (returnType === 'next') {
          this.nextStep();
        } else if (returnType === 'prev') {
          this.prevStep();
        }
        return;
      }

      if (parentElement) {
        parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

        parentElement.style.position = 'relative';

        const tourWrapper = document.createElement('div');
        tourWrapper.className = 'app-tour-tooltip';

        const tooltip = document.createElement('div');
        tooltip.className = 'text-gray flex-center';
        tooltip.textContent = this.currentStep?.content || 'No content available';

        const closeIcon = document.createElement('div');
        closeIcon.className = 'icon ic-xxs ic-coal ic-close-secondary position-absolute top-6 right-10 cursor-pointer';
        closeIcon.onclick = () => this.endTour();

        const prevIcon = document.createElement('div');
        prevIcon.className = 'icon ic-large ic-coal ic-circle-chevron-left nleft-6 position-absolute bg-white br-20 cursor-pointer';
        prevIcon.onclick = () => this.prevStep();
        // prevIcon.style.visibility = this.currentStepIndex === 0 ? 'hidden' : 'visible'; // Hide on the first step

        const nextIcon = document.createElement('div');
        nextIcon.className = 'icon ic-large ic-coal ic-circle-chevron-left rotate-180 nright-6 position-absolute bg-white br-20 cursor-pointer';
        nextIcon.onclick = () => this.nextStep();
        // nextIcon.style.visibility = this.currentStepIndex === this.steps.length - 1 ? 'hidden' : 'visible'; // Hide on the last step

        tourWrapper.append(tooltip, closeIcon, prevIcon, nextIcon);

        parentElement.appendChild(tourWrapper);
      }
    }
  }

  nextStep() {
    this.tourService.nextStep();
    this.updateTourPosition('next');
  }

  prevStep() {
    this.tourService.previousStep();
    this.updateTourPosition('prev');
  }

  endTour() {
    this.isAppTourEnabled = false;
    localStorage.setItem('appTour', 'false');
    this.updateTourPosition();
  }
}
