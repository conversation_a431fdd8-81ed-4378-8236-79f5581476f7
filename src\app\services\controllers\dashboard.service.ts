import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
    providedIn: 'root',
})
export class DashboardService extends BaseService<any> {
    serviceBaseUrl: string;
    taskBaseUrl: string;

    constructor(private http: HttpClient) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    }

    getResourceUrl(): string {
        return 'dashboard';
    }

    getDashboardCountByStatus(leadsVisibility: number) {
        return this.http.get(`${this.serviceBaseUrl}/countbystatus?LeadVisibility=${leadsVisibility}`);
    }

    getLeadReport(payload: any) {
        return this.http.get(`${this.serviceBaseUrl}/newleadreport?LeadVisibility=${payload.leadsVisibility}&FromDate=${payload.fromDate}&ToDate=${payload.toDate}`);
    }

    // getVisitAndMeetingCount(leadsVisibility: number) {
    //     let date = getISODateFormat(new Date());
    //     return this.http.get(`${this.serviceBaseUrl}/sitevisitandmeetingstatus?LeadVisibility=${leadsVisibility}&ToDate=${date}`);
    // }

    // getUpcomingEvents(leadsVisibility: number) {
    //     let today = new Date();
    //     today.setHours(0, 0, 0);
    //     let fromDate = getISODateFormat(today);
    //     today.setDate(new Date().getDate() + 7);
    //     today.setHours(23, 59, 0);
    //     let toDate = getISODateFormat(today);
    //     return this.http.get(`${this.serviceBaseUrl}/upcomingevents?LeadVisibility=${leadsVisibility}&FromDate=${fromDate}&ToDate=${toDate}`);
    // }

    getLeadsInContactWith(payload: any) {
        return this.http.get(`${this.serviceBaseUrl}/leadsincontactwith?LeadVisibility=${payload.leadsVisibility}&FromDate=${payload.fromDate}&ToDate=${payload.toDate}`);
    }

    getCountBySource(payload: any) {
        return this.http.get(`${this.serviceBaseUrl}/source?LeadVisibility=${payload.leadsVisibility}&LeadSources=${payload.source}&FromDate=${payload.fromDate}&ToDate=${payload.toDate}`);
    }

    getLeadTracker(payload: any) {
        return this.http.get(`${this.serviceBaseUrl}/newleadtracker?LeadVisibility=${payload.leadsVisibility}&FromDate=${payload.fromDate}&ToDate=${payload.toDate}`);
    }
}
