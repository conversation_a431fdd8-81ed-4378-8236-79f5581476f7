import { Action } from '@ngrx/store';
import { ConversionStatus } from 'src/app/core/interfaces/data-management.interface';
import { LeadExcel } from 'src/app/core/interfaces/leads.interface';

export enum DataManagementActionTypes {
    FETCH_DATA_BY_ID = '[DATA] Fetch Data By Id',
    FETCH_DATA_BY_ID_SUCCESS = '[DATA] Fetch Data By Id Success',
    ADD_NEW_DATA = '[DATA] Add New Data',
    ADD_NEW_DATA_SUCCESS = '[DATA] Add New Data Success',
    UPDATE_DATA = '[DATA] Update Data',
    UPDATE_DATA_SUCCESS = '[DATA] Update Data Success',
    FETCH_DATA_SOURCE_LIST = '[DATA] Fetch Data Source',
    FETCH_DATA_SOURCE_LIST_SUCCESS = '[DATA] Fetch Data Source Success',
    FETCH_DATA_SUB_SOURCE_LIST = '[DATA] Fetch Data Sub Source',
    FETCH_DATA_SUB_SOURCE_LIST_SUCCESS = '[DATA] Fetch Data Sub Source Success',
    FETCH_DATA_ID_WITH_CONTACT_NO = '[DATA] Fetch Data With Contact No',
    FETCH_DATA_ID_WITH_CONTACT_NO_SUCCESS = '[DATA] Fetch Data With Contact No Success',
    FETCH_DATA_ID_WITH_ALT_NO = '[DATA] Fetch Data With Alt No',
    FETCH_DATA_ID_WITH_ALT_NO_SUCCESS = '[DATA] Fetch Data With Alt No Success',
    FETCH_ALL_DATA = '[DATA] Fetch All Data',
    UPDATE_ALL_DATA_IS_LOADING = '[DATA] Update All Data Is Loading',
    FETCH_ALL_DATA_SUCCESS = '[DATA] Fetch All Data Success',
    FETCH_ALL_DATA_COUNT = '[DATA] Fetch All Data Count',
    FETCH_ALL_DATA_COUNT_SUCCESS = '[DATA] Fetch All Data Count Success',
    UPDATE_DATA_FILTER_PAYLOAD = '[DATA] Update Data Filter Payload',
    COMMUNICATION_DATA_COUNT = '[DATA] Communication Data Count',
    COMMUNICATION_DATA_COUNT_SUCCESS = '[DATA] Communication Data Count Success',
    COMMUNICATION_BULK_DATA_COUNT = '[DATA] Communication Bulk Data Count',
    COMMUNICATION_BULK_DATA_COUNT_SUCCESS = '[DATA] Communication Bulk Data Count Success',
    RESTORE_DATA = '[DATA] Restore Data',
    RESTORE_DATA_SUCCESS = '[DATA] Restore Data Success',
    BULK_DELETE_DATA = '[DATA] Delete Bulk Data',
    BULK_DELETE_DATA_SUCCESS = '[DATA] Delete Bulk Data Success',
    BULK_ASSIGN_DATA = '[DATA] Bulk Assign Data',
    BULK_ASSIGN_DATA_SUCCESS = '[DATA] Bulk Assign Data Success',
    BULK_SOURCE = '[DATA] Bulk Source',
    BULK_SOURCE_SUCCESS = '[DATA] Bulk Source Success',
    ASSIGN_DATA = '[DATA] Assign Data',
    FETCH_DATA_STATUS = '[DATA] Fetch Data Status',
    FETCH_DATA_STATUS_SUCCESS = '[DATA] Fetch Data Status Success',
    FETCH_DATA_CUSTOM_STATUS_FILTER = '[DATA] Fetch Data Custom Status Filters',
    FETCH_DATA_CUSTOM_STATUS_FILTER_SUCCESS = '[DATA] Fetch Data Custom Status Filters Success',
    FETCH_DATA_TOP_FILTERS = '[DATA] Fetch Data Top Filters',
    FETCH_DATA_TOP_FILTERS_SUCCESS = '[DATA] Fetch Data Top Filters Success',
    BULK_UPDATE_STATUS = '[DATA] Bulk Update Status',
    BULK_UPDATE_STATUS_SUCCESS = '[DATA] Bulk Update Status Success',
    BULK_CONVERT_TO_LEAD = '[DATA] Bulk Convert To Lead',
    BULK_CONVERT_TO_LEAD_SUCCESS = '[DATA] Bulk Convert To Lead Success',
    CONVERT_TO_LEAD = '[DATA] Convert To Lead',
    CONVERT_TO_LEAD_SUCCESS = '[DATA] Convert To Lead Success',
    CHECK_DUPLICATE_AND_CONVERT_TO_LEAD = '[DATA] Check Duplicate And Convert To Lead',
    UPDATE_STATUS = '[DATA] Update Status',
    FETCH_DATA_QR_CODE = '[DATA] Fetch Data QR Code',
    FETCH_DATA_QR_CODE_SUCCESS = '[DATA] Fetch Data QR Code Success',
    COMMUNICATION_DATA_MESSAGE = '[DATA] Communication Data Message',
    COMMUNICATION_DATA_MESSAGE_SUCCESS = '[DATA] Communication Data Message Success',
    COMMUNICATION_BULK_DATA_MESSAGE = '[DATA] Communication Bulk Data Message',
    COMMUNICATION_BULK_DATA_MESSAGE_SUCCESS = '[DATA] Communication Bulk Data Message Success',
    FETCH_DATA_HISTORY_BY_ID = '[DATA] Fetch Data History By Id',
    FETCH_DATA_HISTORY_BY_ID_SUCCESS = '[DATA] Fetch Data History By Id Success',
    UPDATE_DATA_NOTES = '[DATA] Update Data Notes',
    FETCH_DATA_LOCATIONS = '[DATA] Fetch Data Locations',
    FETCH_DATA_LOCATIONS_SUCCESS = '[DATA] Fetch Data Locations Success',
    FETCH_DATA_CITIES = '[DATA] Fetch Data Cities',
    FETCH_DATA_CITIES_SUCCESS = '[DATA] Fetch Data Cities Success',
    FETCH_DATA_STATES = '[DATA] Fetch Data States',
    FETCH_DATA_STATES_SUCCESS = '[DATA] Fetch Data States Success',
    EXCEL_UPLOAD = '[DATA] Upload Excel',
    EXCEL_UPLOAD_SUCCESS = '[DATA] Upload Excel Success',
    DATA_EXCEL_UPLOAD = '[DATA] Upload Data Excel File',
    DATA_EXCEL_UPLOAD_SUCCESS = '[DATA] Upload Data Excel File Success',
    DATA_UPLOAD_MAPPED_COLUMNS = '[DATA] Upload Mapped Column Data',
    FETCH_EXCEL_UPLOADED_LIST = '[DATA] Fetch Excel Uploaded List',
    FETCH_EXCEL_UPLOADED_LIST_SUCCESS = '[DATA] Fetch Excel Uploaded List Success',
    EXPORT_DATA = '[DATA] Export Data Through Excel',
    EXPORT_DATA_SUCCESS = '[DATA] Export Data Through Excel Success',
    FETCH_DATA_EXPORT_SUCCESS = '[Data] Fetch Data Export Success',
    FETCH_DATA_EXPORT_STATUS = '[DATA] Fetch Data Export Status List',
    FETCH_DATA_EXPORT_STATUS_SUCCESS = '[DATA] Fetch Data Export Status List Success',
    FETCH_DATA_CONVERSION_STATUS = '[DATA] Fetch Data Conversion Status List',
    FETCH_DATA_CONVERSION_STATUS_SUCCESS = '[DATA] Fetch Data Conversion Status List Success',
    UPDATE_DATA_TOP_FILTER_LOADED_ONCE = '[DATA] Update Data Top Filter Loaded Once',
    FETCH_DATA_CURRENCY_LIST = '[DATA] Fetch Currency List',
    FETCH_DATA_CURRENCY_LIST_SUCCESS = '[DATA] Fetch Currency List Success',
    PERMANENT_DELETE_DATA = '[DATA] Permanent Delete Data',
    PERMANENT_DELETE_DATA_SUCCESS = '[DATA] Permanent Delete Data Success',
    DATA_MIGRATE_UPLOAD_MAPPED_COLUMNS = '[DATA] Upload Migrate Mapped Column Data',
    FETCH_MIGRATE_EXCEL_UPLOADED_LIST = '[DATA] Fetch Migrate Excel Uploaded List',
    FETCH_MIGRATE_EXCEL_UPLOADED_LIST_SUCCESS = '[DATA] Fetch Migrate Excel Uploaded List Success',
    CLEAR_DATA = '[DATA] Clear Data',
    PROSPECT_CARD_DATA = '[DATA] Prospect Card Data',
    FETCH_DATA_COUNTRIES = '[DATA] Fetch Data Countries',
    FETCH_DATA_COUNTRIES_SUCCESS = '[DATA] Fetch Data Countries Success',
    FETCH_DATA_SUB_COMMUNITIES = '[DATA] Fetch Data Sub Communities',
    FETCH_DATA_SUB_COMMUNITIES_SUCCESS = '[DATA] Fetch Data Sub Countries Success',
    FETCH_DATA_COMMUNITIES = '[DATA] Fetch Data Communities',
    FETCH_DATA_COMMUNITIES_SUCCESS = '[DATA] Fetch Data Communities Success',
    FETCH_DATA_TOWER = '[DATA] Fetch Data Tower',
    FETCH_DATA_TOWER_SUCCESS = '[DATA] Fetch Data Tower Success',
    FETCH_DATA_ZONES = '[DATA] Fetch Data Zones',
    FETCH_DATA_ZONES_SUCCESS = '[DATA] Fetch Data Zones Success',
    FETCH_DATA_LOCALITIES = '[DATA] Fetch Data Localities',
    FETCH_DATA_LOCALITIES_SUCCESS = '[DATA] Fetch Data Localities Success',
    FETCH_DATA_NATIONALITY = '[DATA] Fetch Data Nationality',
    FETCH_DATA_NATIONALITY_SUCCESS = '[DATA] Fetch Data Nationality Success',
    FETCH_DATA_CLUSTER_NAME = '[DATA] Fetch Data Cluster Name',
    FETCH_DATA_CLUSTER_NAME_SUCCESS = '[DATA] Fetch Data Cluster Name Success',
    FETCH_DATA_UNIT_NAME = '[DATA] Fetch Data Unit Name',
    FETCH_DATA_UNIT_NAME_SUCCESS = '[DATA] Fetch Data Unit Name Success',
    FETCH_DATA_POSTAL_CODE = '[DATA] Fetch Data Postal Code',
    FETCH_DATA_POSTAL_CODE_SUCCESS = '[DATA] Fetch Data Postal Code Success',
    FETCH_DATA_LANDLINE = '[DATA] Fetch Data LandLine',
    FETCH_DATA_LANDLINE_SUCCESS = '[DATA] Fetch Data LandLine Success',
    FETCH_UPLOADTYPENAME_LIST = '[DATA] Fetch Upload Type Name List',
    FETCH_UPLOADTYPENAME_LIST_SUCCESS = '[DATA] Fetch Upload Type Name List Success',
    FETCH_DATA_COMMUNICATION_BY_IDS = '[DATA] Fetch Data Communication By Ids',
    FETCH_DATA_COMMUNICATION_BY_IDS_SUCCESS = '[DATA] Fetch Data Communication By Ids Success',
    FETCH_DATA_COUNTRY_CODE = '[DATA] Fetch Country Code',
    FETCH_DATA_COUNTRY_CODE_SUCCESS = '[DATA] Fetch Country Code Success',
    FETCH_DATA_ALT_COUNTRY_CODE = '[DATA] Fetch Alt Country Code',
    FETCH_DATA_ALT_COUNTRY_CODE_SUCCESS = '[DATA] Fetch Alt Country Code Success',
    BULK_AGENCY = '[DATA] Bulk Data Agency',
    BULK_AGENCY_SUCCESS = '[DATA] Bulk Data Agency Success',
    BULK_CHANNEL_PARTNER = '[DATA] Bulk Data Channel Partner',
    BULK_CHANNEL_PARTNER_SUCCESS = '[DATA] Bulk Data Channel Partner Success',
    BULK_CAMPAIGN = '[DATA] Bulk Data Campaign',
    BULK_CAMPAIGN_SUCCESS = '[DATA] Bulk Data Campaign Success',
}

export class FetchDataCustomStatusFilter implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CUSTOM_STATUS_FILTER;
    constructor(public filtersPayload: any = {}) { }
}

export class FetchDataCustomStatusFilterSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CUSTOM_STATUS_FILTER_SUCCESS;
    constructor(public response: any = {}) { }
}

export class FetchDataTopFilters implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_TOP_FILTERS;
    constructor(public filtersPayload: any = {}) { }
}

export class FetchDataTopFiltersSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_TOP_FILTERS_SUCCESS;
    constructor(public response: any = {}) { }
}

export class FetchAllData implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_ALL_DATA;
    constructor(public filtersPayload: any = {}) { }
}

export class UpdateAllDataIsLoading implements Action {
    readonly type: string = DataManagementActionTypes.UPDATE_ALL_DATA_IS_LOADING;
    constructor(public isLoading: boolean = true) { }
}

export class FetchAllDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_ALL_DATA_SUCCESS;
    constructor(public response: any = {}) { }
}

export class FetchAllDataCount implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_ALL_DATA_COUNT;
    constructor(public filtersPayload: any = {}) { }
}

export class FetchAllDataCountSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_ALL_DATA_COUNT_SUCCESS;
    constructor(public response: any = {}) { }
}

export class FetchDataById implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_BY_ID;
    constructor(public id: string) { }
}

export class FetchDataByIdSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_BY_ID_SUCCESS;
    constructor(public response: any = {}) { }
}

export class AddNewData implements Action {
    readonly type: string = DataManagementActionTypes.ADD_NEW_DATA;
    constructor(public payload: any) { }
}

export class AddNewDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.ADD_NEW_DATA_SUCCESS;
    constructor() { }
}

export class UpdateData implements Action {
    readonly type: string = DataManagementActionTypes.UPDATE_DATA;
    constructor(public id: string, public payload: any) { }
}

export class UpdateDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.UPDATE_DATA_SUCCESS;
    constructor() { }
}

export class FetchDataSourceList implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_SOURCE_LIST;
    constructor() { }
}

export class FetchDataSourceListSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_SOURCE_LIST_SUCCESS;
    constructor(public response: any = []) { }
}

export class FetchDataSubSourceList implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_SUB_SOURCE_LIST;
    constructor() { }
}

export class FetchDataSubSourceListSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_SUB_SOURCE_LIST_SUCCESS;
    constructor(public response: any = []) { }
}

export class FetchDataIdWithContactNo implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_ID_WITH_CONTACT_NO;
    constructor(public contactNo: any) { }
}

export class FetchDataIdWithContactNoSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_ID_WITH_CONTACT_NO_SUCCESS;
    constructor(public response: any = {}) { }
}

export class FetchDataIdWithAltNo implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_ID_WITH_ALT_NO;
    constructor(public contactNo: any) { }
}

export class FetchDataIdWithAltNoSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_ID_WITH_ALT_NO_SUCCESS;
    constructor(public response: any = {}) { }
}

export class UpdateDataFilterPayload implements Action {
    readonly type: string = DataManagementActionTypes.UPDATE_DATA_FILTER_PAYLOAD;
    constructor(public filter: any = {}) { }
}

export class CommunicationDataCount implements Action {
    readonly type: string = DataManagementActionTypes.COMMUNICATION_DATA_COUNT;
    constructor(public id: string, public payload: any) { }
}

export class CommunicationDataCountSuccess implements Action {
    readonly type: string = DataManagementActionTypes.COMMUNICATION_DATA_COUNT_SUCCESS;
    constructor(public id: string, public payload: any) { }
}

export class CommunicationBulkDataCount implements Action {
    readonly type: string = DataManagementActionTypes.COMMUNICATION_BULK_DATA_COUNT;
    constructor(public payload: any) { }
}

export class CommunicationBulkDataCountSuccess implements Action {
    readonly type: string = DataManagementActionTypes.COMMUNICATION_BULK_DATA_COUNT_SUCCESS;
    constructor(public payload: any) { }
}

export class RestoreData implements Action {
    readonly type: string = DataManagementActionTypes.RESTORE_DATA;
    constructor(public ids: string[]) { }
}

export class RestoreDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.RESTORE_DATA_SUCCESS;
    constructor() { }
}

export class BulkDeleteData implements Action {
    readonly type: string = DataManagementActionTypes.BULK_DELETE_DATA;
    constructor(public ids: string[]) { }
}

export class BulkDeleteDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_DELETE_DATA_SUCCESS;
    constructor() { }
}

export class BulkAssignData implements Action {
    readonly type: string = DataManagementActionTypes.BULK_ASSIGN_DATA;
    constructor(public payload: any) { }
}

export class BulkAssignDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_ASSIGN_DATA_SUCCESS;
    constructor() { }
}

export class AssignData implements Action {
    readonly type: string = DataManagementActionTypes.ASSIGN_DATA;
    constructor(public payload: any) { }
}

export class BulkSource implements Action {
    readonly type: string = DataManagementActionTypes.BULK_SOURCE;
    constructor(public payload: any) { }
}

export class BulkSourceSuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_SOURCE_SUCCESS;
    constructor() { }
}

export class FetchDataStatus implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_STATUS;
    constructor() { }
}

export class FetchDataStatusSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_STATUS_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class BulkStatusUpdate implements Action {
    readonly type: string = DataManagementActionTypes.BULK_UPDATE_STATUS;
    constructor(public payload: any) { }
}

export class BulkStatusUpdateSuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_UPDATE_STATUS_SUCCESS;
    constructor() { }
}

export class BulkConvertToLead implements Action {
    readonly type: string = DataManagementActionTypes.BULK_CONVERT_TO_LEAD;
    constructor(public payload: any) { }
}

export class BulkConvertToLeadSuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_CONVERT_TO_LEAD_SUCCESS;
    constructor() { }
}

export class ConvertToLead implements Action {
    readonly type: string = DataManagementActionTypes.CONVERT_TO_LEAD;
    constructor(public payload: any) { }
}

export class CheckDuplicateAndConvertToLead implements Action {
    readonly type: string = DataManagementActionTypes.CHECK_DUPLICATE_AND_CONVERT_TO_LEAD;
    constructor(public payload: any, public number: string) { }
}

export class ConvertToLeadSuccess implements Action {
    readonly type: string = DataManagementActionTypes.CONVERT_TO_LEAD_SUCCESS;
    constructor() { }
}

export class UpdateStatus implements Action {
    readonly type: string = DataManagementActionTypes.UPDATE_STATUS;
    constructor(public payload: any, public isSaveAndClose: boolean = false, public isSaveAndNext: boolean = false) { }
}

export class FetchDataQRCode implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_QR_CODE;
    constructor(public payload?: any) { }
}


export class FetchDataQRCodeSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_QR_CODE_SUCCESS;
    constructor(public response: any = {}) { }
}

export class CommunicationDataMessage implements Action {
    readonly type: string = DataManagementActionTypes.COMMUNICATION_DATA_MESSAGE;
    constructor(public payload: any) { }
}
export class CommunicationDataMessageSuccess implements Action {
    readonly type: string =
        DataManagementActionTypes.COMMUNICATION_DATA_MESSAGE_SUCCESS;
    constructor(public resp: string = '') { }
}

export class CommunicationBulkDataMessage implements Action {
    readonly type: string = DataManagementActionTypes.COMMUNICATION_BULK_DATA_MESSAGE;
    constructor(public payload: any) { }
}
export class CommunicationBulkDataMessageSuccess implements Action {
    readonly type: string =
        DataManagementActionTypes.COMMUNICATION_BULK_DATA_MESSAGE_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchDataHistoryById implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_HISTORY_BY_ID;
    constructor(public id: string) { }
}

export class FetchDataHistoryByIdSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_HISTORY_BY_ID_SUCCESS;
    constructor(public response: any = {}) { }
}

export class UpdateDataNotes implements Action {
    readonly type: string = DataManagementActionTypes.UPDATE_DATA_NOTES;
    constructor(public payload: any, public isSaveAndClose: boolean = false, public isSaveAndNext: boolean = false) { }
}

export class FetchDataLocations implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_LOCATIONS;
    constructor() { }
}

export class FetchDataLocationsSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_LOCATIONS_SUCCESS;
    constructor(public response: string[] = []) { }
}

export class FetchDataCities implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CITIES;
    constructor() { }
}

export class FetchDataCitiesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CITIES_SUCCESS;
    constructor(public response: string[] = []) { }
}

export class FetchDataStates implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_STATES;
    constructor() { }
}

export class FetchDataStatesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_STATES_SUCCESS;
    constructor(public response: string[] = []) { }
}

export class FetchDataCountries implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_COUNTRIES;
    constructor() { }
}

export class FetchDataCountriesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_COUNTRIES_SUCCESS;
    constructor(public response: string[] = []) { }
}

export class FetchDataSubCommunities implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_SUB_COMMUNITIES;
    constructor() { }
}

export class FetchDataSubCommunitiesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_SUB_COMMUNITIES_SUCCESS;
    constructor(public response: string[] = []) { }
}
export class FetchDataCommunities implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_COMMUNITIES;
    constructor() { }
}

export class FetchDataCommunitiesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_COMMUNITIES_SUCCESS;
    constructor(public response: string[] = []) { }
}
export class FetchDataTowerNames implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_TOWER;
    constructor() { }
}

export class FetchDataTowerNamesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_TOWER_SUCCESS;
    constructor(public response: string[] = []) { }
}


export class FetchDataZones implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_ZONES;
    constructor() { }
}

export class FetchDataZonesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_ZONES_SUCCESS;
    constructor(public response: string[] = []) { }
}

export class FetchDataLocalities implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_LOCALITIES;
    constructor() { }
}

export class FetchDataLocalitiesSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_LOCALITIES_SUCCESS;
    constructor(public response: string[] = []) { }
}

export class ExcelUpload implements Action {
    readonly type: string = DataManagementActionTypes.EXCEL_UPLOAD;
    constructor(public file: File) { }
}
export class ExcelUploadSuccess implements Action {
    readonly type: string = DataManagementActionTypes.EXCEL_UPLOAD_SUCCESS;
    constructor(public resp: any) { }
}
export class DataExcelUpload implements Action {
    readonly type: string = DataManagementActionTypes.DATA_EXCEL_UPLOAD;
    constructor(public file: File) { }
}

export class DataExcelUploadSuccess implements Action {
    readonly type: string = DataManagementActionTypes.DATA_EXCEL_UPLOAD_SUCCESS;
    constructor(public resp: LeadExcel) { }
}

export class DataUploadMappedColumns implements Action {
    readonly type: string = DataManagementActionTypes.DATA_UPLOAD_MAPPED_COLUMNS;
    constructor(public payload: any) { }
}

export class FetchDataExcelUploadedList implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchDataExcelUploadedListSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class ExportData implements Action {
    readonly type: string = DataManagementActionTypes.EXPORT_DATA;
    constructor(public payload: any) { }
}
export class ExportDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.EXPORT_DATA_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchDataExportSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_EXPORT_SUCCESS;
    constructor(public response: any = []) { }
}

export class FetchDataExportStatus implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_EXPORT_STATUS;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchDataExportStatusSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_EXPORT_STATUS_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class FetchDataConversionStatus implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CONVERSION_STATUS;
    constructor() { }
}

export class FetchDataConversionStatusSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CONVERSION_STATUS_SUCCESS;
    constructor(public response: ConversionStatus[] = []) { }
}

export class UpdateDataTopFilterLoadedOnce implements Action {
    readonly type: string = DataManagementActionTypes.UPDATE_DATA_TOP_FILTER_LOADED_ONCE;
    constructor(public isLoadedOnce: boolean) { }
}

export class FetchDataCurrency implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CURRENCY_LIST;
    constructor() { }
}
export class FetchDataCurrencySuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CURRENCY_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class DataMigrateUploadMappedColumns implements Action {
    readonly type: string = DataManagementActionTypes.DATA_MIGRATE_UPLOAD_MAPPED_COLUMNS;
    constructor(public payload: any) { }
}

export class FetchDataMigrateExcelUploadedList implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchDataMigrateExcelUploadedListSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}


export class PermanentDeleteData implements Action {
    readonly type: string = DataManagementActionTypes.PERMANENT_DELETE_DATA;
    constructor(public payload: string[]) { }
}

export class PermanentDeleteDataSuccess implements Action {
    readonly type: string = DataManagementActionTypes.PERMANENT_DELETE_DATA_SUCCESS;
    constructor() { }
}

export class ClearData implements Action {
    readonly type: string = DataManagementActionTypes.CLEAR_DATA;
    constructor() { }
}

export class AddCardData implements Action {
    readonly type: string = DataManagementActionTypes.PROSPECT_CARD_DATA;
    constructor(public data: any) { }
}

export class FetchDataNationality implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_NATIONALITY;
    constructor() { }
}

export class FetchDataNationalitySuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_NATIONALITY_SUCCESS;
    constructor(public resp: any = {}) { }
}

export class FetchDataClusterName implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CLUSTER_NAME;
    constructor() { }
}

export class FetchDataClusterNameSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_CLUSTER_NAME_SUCCESS;
    constructor(public resp: any = {}) { }
}

export class FetchDataUnitName implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_UNIT_NAME;
    constructor() { }
}

export class FetchDataUnitNameSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_UNIT_NAME_SUCCESS;
    constructor(public resp: any = {}) { }
}

export class FetchDataPostalCode implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_POSTAL_CODE;
    constructor() { }
}

export class FetchDataPostalCodeSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_POSTAL_CODE_SUCCESS;
    constructor(public resp: any = {}) { }
}

export class FetchDataLandLine implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_LANDLINE;
    constructor() { }
}

export class FetchDataLandLineSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_LANDLINE_SUCCESS;
    constructor(public resp: any = {}) { }
}

export class FetchUploadTypeNameList implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_UPLOADTYPENAME_LIST;
    constructor() { }
}

export class FetchUploadTypeNameListSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_UPLOADTYPENAME_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class FetchDataCommunicationByIds implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_COMMUNICATION_BY_IDS;
    constructor(public payload: { ProspectIds: string[], showCommunicationCount?: boolean }) { }
}

export class FetchDataCommunicationByIdsSuccess implements Action {
    readonly type: string =
        DataManagementActionTypes.FETCH_DATA_COMMUNICATION_BY_IDS_SUCCESS;
    constructor(public resp: any = []) { }
}

export class FetchDataCountryCode implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_COUNTRY_CODE;
    constructor() { }
}

export class FetchDataCountryCodeSuccess implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_COUNTRY_CODE_SUCCESS;
    constructor(public resp: any[] = []) { }
}

export class FetchDataAltCountryCode implements Action {
    readonly type: string = DataManagementActionTypes.FETCH_DATA_ALT_COUNTRY_CODE;
    constructor() { }
}

export class FetchDataAltCountryCodeSuccess implements Action {
    readonly type: string =
        DataManagementActionTypes.FETCH_DATA_ALT_COUNTRY_CODE_SUCCESS;
    constructor(public resp: any[] = []) { }
}

export class BulkAgency implements Action {
    readonly type: string = DataManagementActionTypes.BULK_AGENCY;
    constructor(public payload: any) { }
}

export class BulkAgencySuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_AGENCY_SUCCESS;
    constructor() { }
}

export class BulkChannelPartner implements Action {
    readonly type: string = DataManagementActionTypes.BULK_CHANNEL_PARTNER;
    constructor(public payload: any) { }
}

export class BulkChannelPartnerSuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_CHANNEL_PARTNER_SUCCESS;
    constructor() { }
}

export class BulkCampaign implements Action {
    readonly type: string = DataManagementActionTypes.BULK_CAMPAIGN;
    constructor(public payload: any) { }
}

export class BulkCampaignSuccess implements Action {
    readonly type: string = DataManagementActionTypes.BULK_CAMPAIGN_SUCCESS;
    constructor() { }
}