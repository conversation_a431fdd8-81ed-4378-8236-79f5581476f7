import { Component, Inject, Renderer2 } from '@angular/core';
import { Store } from '@ngrx/store';
import { tap, map } from 'rxjs/operators';
import { AppState } from '../../../app.reducer';
import { DOCUMENT } from '@angular/common';
import { DEFAULT_LOADING_TEXT } from '../../../app.constants';

@Component({
  selector: 'ngw-loader',
  template: `<ng-container *ngIf="loader$ | async as show">
    <div class="cssload-container">
      <img
        class="cssload-zenith"
        height="120px"
        width="120px"
        src="assets/images/loader-rat.svg"
      />
    </div>
  </ng-container>`,
  styleUrls: ['./loader.style.scss'],
})
export class Loader {
  public loader$: any;
  public msg = DEFAULT_LOADING_TEXT;
  constructor(
    private _store: Store<AppState>,
    @Inject(DOCUMENT) private _document: Document,
    private _renderer: Renderer2
  ) {
    this.loader$ = this._store
      .select((state) => state.loader)
      .pipe(
        // .debounceTime(300)
        tap((loader) => (this.msg = DEFAULT_LOADING_TEXT)),
        map((loader) => ((loader || {}).loading || 0) > 0),
        tap((loading) => {
          if (loading) {
            this._renderer.addClass(this._document.body, 'overlay');
            // TODO
          } else {
            // remove body class
            this._renderer.removeClass(this._document.body, 'overlay');
          }
        })
      );
  }
}
