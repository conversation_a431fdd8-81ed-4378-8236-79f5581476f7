import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  FetchWhatsappCloudSuccess,
  WhatsappCloudActionTypes,
} from 'src/app/reducers/whatsapp-cloud/whatsapp-cloud.actions';

export type WhatsappCloudState = {
  templates?: any;
};
const initialState: WhatsappCloudState = {
  templates: [],
};
export function whatsappCloudReducer(
  state: WhatsappCloudState = initialState,
  action: Action
): WhatsappCloudState {
  switch (action.type) {
    case WhatsappCloudActionTypes.FETCH_WHATSAPP_CLOUD_SUCCESS:
      return {
        ...state,
        templates: (action as FetchWhatsappCloudSuccess).resp,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.whatsappCloud;

export const getWhatsAppCloud = createSelector(
  selectFeature,
  (state: WhatsappCloudState) => {
    return state;
  }
);
