import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  LoginActions,
  LoginSuccess,
  StoreUsername,
  TryAnotherWaySuccess,
  TwoFactorOtpGenerationSuccess,
} from 'src/app/reducers/login/login.actions';

export type LoginState = {
  loggedInUser?: any;
  username: any;
  sessionId: null;
  isLoginLoading: boolean;
  login: any;
};

const initialState: LoginState = {
  loggedInUser: {},
  username: null,
  sessionId: null,
  isLoginLoading: false,
  login: null,
};

export function loginReducer(
  state: LoginState = initialState,
  action: Action
): LoginState {
  switch (action.type) {
    case LoginActions.LOGIN:
      return {
        ...state,
        isLoginLoading: true,
      };
    case LoginActions.LOGIN_SUCCESS:
      return {
        ...state,
        isLoginLoading: false,
        login: (action as LoginSuccess).response,
      };
    case LoginActions.STORE_USERNAME:
      return {
        ...state,
        username: (action as StoreUsername).username,
      };
    case LoginActions.OTP_GENERATION_SUCCESS:
      return {
        ...state,
        sessionId: (action as TwoFactorOtpGenerationSuccess).response.SessionId,
      };
    case LoginActions.TRY_ANOTHER_WAY_SUCCESS:
      return {
        ...state,
        sessionId: (action as TryAnotherWaySuccess).resp.data
      };
    case LoginActions.CLEAR_DETAILS:
      return {
        ...state,
        username: null,
        sessionId: null,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.login;
export const getUserName = createSelector(
  selectFeature,
  (state: LoginState) => state.username
);

export const getLogin = createSelector(
  selectFeature,
  (state: LoginState) => state.login
);

export const getIsLoginLoading = createSelector(
  selectFeature,
  (state: LoginState) => state.isLoginLoading
);

export const getSessionId = createSelector(
  selectFeature,
  (state: LoginState) => state.sessionId
);
