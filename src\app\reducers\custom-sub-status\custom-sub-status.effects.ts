import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { FetchLeadStatusList } from 'src/app/reducers/master-data/master-data.actions';
import { CreateSubStatus, CustomSubStatusActionTypes, DeleteSubStatus, ExistSubStatus, ExistSubStatusSuccess, FetchLeadCount, FetchLeadCountSuccess, FetchSubStatus, FetchSubStatusSuccess, UpdateCustomSubStatus } from './custom-sub-status.actions';
import { CustomSubStatusService } from 'src/app/services/controllers/custom-substatus.service';
import { CommonService } from 'src/app/services/shared/common.service';

@Injectable()
export class CustomSubStatusEffects {

    getSubStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(CustomSubStatusActionTypes.FETCH_SUB_STATUS_LIST),
            map((action: FetchSubStatus) => action),
            switchMap((data: any) => {
                return this.api.getCustomSubStatus().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchSubStatusSuccess(resp.data);
                        }
                        return new FetchSubStatusSuccess({});
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );


    getLeadCount$ = createEffect(() =>
        this.actions$.pipe(
            ofType(CustomSubStatusActionTypes.FETCH_LEAD_COUNT),
            map((action: FetchSubStatus) => action),
            switchMap((data: any) => {
                return this.api.getCustomLeadCount().pipe(
                    map((resp: any) => {
                        return new FetchLeadCountSuccess(resp);

                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateCustomSubStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(CustomSubStatusActionTypes.UPDATE_CUSTOM_SUB_STATUS),
            switchMap((action: UpdateCustomSubStatus) => {
                return this.customSubStatusService.updateCustomSubStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Sub-Status Updated Successfully`
                            );
                            return new FetchSubStatusSuccess();
                        }
                        return new FetchSubStatusSuccess({});
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    deleteSubStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(CustomSubStatusActionTypes.DELETE_SUB_STATUS),
            switchMap((action: DeleteSubStatus) => {
                return this.api.deleteSubStatus(action.id).pipe(
                    map((resp: any) => {
                        if (resp) {
                            this._notificationService.success(
                                `Sub-Status Deleted Successfully`
                            );
                            this.store.dispatch(new FetchLeadStatusList());
                            this.store.dispatch(new FetchLeadCount());
                            return new FetchSubStatusSuccess();
                        }
                        return new FetchSubStatusSuccess({});
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    createSubStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(CustomSubStatusActionTypes.CREATE_SUB_STATUS),
            switchMap((action: CreateSubStatus) => {
                return this.api.createCustomSubStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(`Created successfully.`);
                            return new FetchSubStatus();
                        }
                        return new FetchSubStatusSuccess({});
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    existSubStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(CustomSubStatusActionTypes.EXIST_SUB_STATUS),
            map((action: ExistSubStatus) => action.subStatus),
            switchMap((data: any) => {
                return this.api.existSubStatus(data).pipe(
                    map((resp: any) => {
                        return new ExistSubStatusSuccess(resp.data);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private actions$: Actions,
        private customSubStatusService: CustomSubStatusService,
        private api: CustomSubStatusService,
        private _notificationService: NotificationsService,
        private store: Store<AppState>,
        private commonService: CommonService
    ) { }
}