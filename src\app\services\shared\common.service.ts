import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { getIndexes } from 'src/app/core/utils/common.util';

@Injectable({
  providedIn: 'root',
})
export class CommonService {
  serviceBaseUrl: string;
  identityUrl: string;
  isRefreshTokenExpired = new BehaviorSubject(false);
  baseUrlNoVersion: string;
  constructor(private httpClient: HttpClient, private router: Router) {
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}`;
    this.identityUrl = `${env.identityURL}api/`;
    this.baseUrlNoVersion = `${env.baseURL}api/`;
  }

  getModuleList(payload: any, IsNoVersion?: boolean, isIdentityUrl: boolean = false) {
    const controllerPath = payload.path;
    let params: any = new HttpParams();
    Object.entries(payload).map((item: any) => {
      if ((item[1] || item[1] == 0) && item[1] != '') {
        if (Array.isArray(item[1]) && ['project', 'project/count'].includes(controllerPath)) {
          item[1] = getIndexes(item[0], item[1]);
          item[1].forEach((element: any) => {
            params = params.append(item[0].toString(), element);
          });
        } else {
          params = params.set(item[0].toString(), item[1]);
        }
      }
    });
    return this.httpClient.get(
      `${isIdentityUrl ? this.identityUrl : IsNoVersion ? this.baseUrlNoVersion : this.serviceBaseUrl
      }${controllerPath}?${params.toString()}`
    );
  }

  getModuleListByAdvFilter(payload: any, IsNoVersion?: boolean) {
    const controllerPath = payload.path;
    let params: any = new HttpParams();
    Object.entries(payload).map((item: any) => {
      if (item[1] || item[1] == 0) {
        if (Array.isArray(item[1])) {
          item[1] = getIndexes(item[0], item[1]);
          item[1].forEach((element: any) => {
            params = params.append(item[0].toString(), element);
          });
        } else {
          params = params.set(item[0].toString(), item[1]);
        }
      }
    });
    return this.httpClient.get(
      `${IsNoVersion ? this.baseUrlNoVersion : this.serviceBaseUrl}${controllerPath}?${params.toString()}`
    );
  }

  setRefreshTokenCheck(val: boolean) {
    this.isRefreshTokenExpired.next(val);
  }
}
