import {
  Compo<PERSON>,
  <PERSON>E<PERSON>ter,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';

import { EMPTY_GUID, PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getEnvDetails,
  getPages,
  getTenantName,
  getTimeZoneDate,
} from 'src/app/core/utils/common.util';
import {
  FetchPriorityList,
  FetchUserAssignmentByEntity
} from 'src/app/reducers/automation/automation.actions';
import {
  getPriorityList,
  getUserAssignmentByEntity,
} from 'src/app/reducers/automation/automation.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchAssignedProjectsList,
  FetchProjectIdWithName
} from 'src/app/reducers/project/project.action';
import {
  getAssignedProjects,
  getProjectsIDWithName,
} from 'src/app/reducers/project/project.reducer';
import {
  AddQrForm,
  AssignPageSize,
  BulkDelete,
  BulkRestore,
  DeleteQrForm,
  FetchAllQrForms,
  RestoreTemplate,
  UpdateQrFormStatus
} from 'src/app/reducers/qr-form/qr-form.action';
import {
  getAllQRForms,
  getAllQRFormsIsLoading,
  getPageNumber,
  getPageSize,
  getTotalQRCount,
} from 'src/app/reducers/qr-form/qr-form.reducer';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { QrGeneratorComponent } from 'src/app/shared/components/qr-code/qr-generator.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { ConfigurationComponent } from './configuration/configuration.component';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
@Component({
  selector: 'manage-qr-form',
  templateUrl: './manage-qr-form.component.html',
})
export class ManageQrFormComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  project: FormControl = new FormControl(null);
  assignedToUsers: FormControl = new FormControl(null);
  agencyName: FormControl = new FormControl(null);
  allUserList: any[];
  assignedToUsersList: any[];
  qrFormList: any[] = [];
  campaignList: any[] = [];
  currOffset: number = 0;
  selectedCount: number = 0;
  currPageNumber: number = 1;
  pageSize: number = PAGE_SIZE;
  searchTermSubject = new Subject<string>();
  totalPages: number;
  getPages = getPages;
  showEntriesSize: number[] = SHOW_ENTRIES;
  pageEntry: FormControl = new FormControl(this.pageSize);
  params: any;
  moment = moment;
  getAssignedToDetails = getAssignedToDetails;
  getTimeZoneDate = getTimeZoneDate;
  EMPTY_GUID = EMPTY_GUID;
  searchTerm: string;
  status: boolean;
  url: string;
  selectedTemplate: any;
  assignedProjectsList: any[] = [];
  allProjectsList: any[] = [];
  moduleId: string;
  updatedAgent: boolean = false;
  updatedProjects: boolean = false;
  updatedUser: boolean = false;
  showLeftNav: boolean = true;
  selectedTemplates: any;
  isBulkAssignModel: boolean = false;
  allQRFormsIsLoading: boolean = true;
  filter: 'all' | 'deleted' = 'all';
  userData: any;
  canBulkDelete: boolean = false;
  canBulkRestore: boolean = false;
  subDomain = getTenantName();
  expandedSections: { [key: string]: boolean } = {};
  get isLocalhost() {
    return this.subDomain === 'localhost'
  }

  constructor(
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private router: Router,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>,
    private shareDataService: ShareDataService,
    private bulkDeleteModalRef: BsModalRef,
    private fb: FormBuilder
  ) {
    this.metaTitle.setTitle('CRM | Global Config | QR Templates');
    this.headerTitle.setLangTitle('QR Templates');
    this.store.dispatch(new FetchUsersListForReassignment());
    this.store.dispatch(new FetchProjectIdWithName());
    this.store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any[]) => {
        this.allProjectsList = res
          ?.filter((data: any) => data.name)
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getPageSize)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.pageSize = data;
        this.pageEntry.patchValue(data);
      });
    this.store
      .select(getPageNumber)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.currPageNumber = data;
        this.currOffset = data - 1;
      });
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.fetchData();
    this.store.dispatch(
      new FetchAllQrForms(this.currPageNumber, this.pageSize, this.searchTerm)
    );
    const subDomain: string = getTenantName();
    this.url = subDomain + getEnvDetails();
  }

  ngOnInit(): void {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        const permissionsSet = new Set(permissions);
        this.canBulkDelete = permissionsSet.has('Permissions.GlobalSettings.BulkDelete');
        this.canBulkRestore = permissionsSet.has('Permissions.GlobalSettings.BulkRestore');
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.store.dispatch(new FetchUsersListForReassignment());
    this.store.dispatch(new FetchPriorityList());

    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });

    this.store
      .select(getUserAssignmentByEntity)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        if (res?.userIds?.length > 0) {
          this.assignedToUsers.patchValue(res?.userIds);
          this.assignedToUsersList = this.allUserList?.filter((user: any) =>
            res?.userIds?.includes(user?.id)
          );
        }
      });

    this.modalService.onHide.subscribe((value: any) => {
      this.project.reset();
      this.assignedToUsers.reset();
      this.agencyName.reset();
      this.selectedTemplate = null;
      this.assignedProjectsList = [];
      this.assignedToUsersList = [];
      if (this.updatedAgent || this.updatedUser || this.updatedProjects) {
        this.fetchData();
        this.store.dispatch(
          new FetchAllQrForms(
            this.currPageNumber,
            this.pageSize,
            this.searchTerm
          )
        );
        this.store.dispatch(new LoaderHide());
      }
      this.updatedAgent = false;
      this.updatedProjects = false;
      this.updatedUser = false;
    });

    this.store
      .select(getAssignedProjects)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any[]) => {
        this.assignedProjectsList = res;
        let projectIds: any[] = [];
        if (res?.length > 0) {
          for (let project of res) {
            projectIds = [...projectIds, project.id];
          }
        }
        this.project.patchValue(projectIds);
      });

    this.searchTermSubject.subscribe(() => {
      this.currOffset = 0;
      this.currPageNumber = 1;
      this.pageSize = this.pageEntry.value;
      this.fetchData();
      this.store.dispatch(
        new FetchAllQrForms(this.currPageNumber, this.pageSize, this.searchTerm)
      );
      this.moveToFirstPage();
      this.store.dispatch(new LoaderHide());
    });

    this.project.valueChanges
      .pipe(takeUntil(this.stopper))
      .subscribe((value: any[]) => {
        this.assignedProjectsList = this.allProjectsList?.filter((project) =>
          this.project.value?.includes(project.id)
        );
      });

    this.assignedToUsers.valueChanges
      .pipe(takeUntil(this.stopper))
      .subscribe((value: any[]) => {
        this.assignedToUsersList = this.allUserList?.filter((user) =>
          this.assignedToUsers.value?.includes(user.id)
        );
      });
    this.searchTermSubject.subscribe(() => {
      this.currOffset = 0;
      this.currPageNumber = 1;
      this.pageSize = this.pageEntry.value;
      if (this.filter === 'all') {
        this.store.dispatch(
          new FetchAllQrForms(
            this.currPageNumber,
            this.pageSize,
            this.searchTerm,
            false
          )
        );
      } else if (this.filter === 'deleted') {
        this.store.dispatch(
          new FetchAllQrForms(
            this.currPageNumber,
            this.pageSize,
            this.searchTerm,
            true
          )
        );
      }
      this.moveToFirstPage();
    });

    if (this.filter === 'all') {
      this.store.dispatch(
        new FetchAllQrForms(
          this.currPageNumber,
          this.pageSize,
          this.searchTerm,
          false
        )
      );
    } else if (this.filter === 'deleted') {
      this.store.dispatch(
        new FetchAllQrForms(
          this.currPageNumber,
          this.pageSize,
          this.searchTerm,
          true
        )
      );
    }

    this.store
      .select(getAllQRForms)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.qrFormList = data;
        this.qrFormList = this.qrFormList.map((template) => ({
          ...template,
          selected: false,
        }));
      });

    this.store
      .select(getAllQRFormsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.allQRFormsIsLoading = loading;
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data?.filter((user: any) => user.isActive);

        this.allUserList = this.allUserList.map((user) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));

        let userDetails = localStorage.getItem('userDetails');
        let userId = JSON.parse(userDetails).sub;

        const currentUserIndex = this.allUserList.findIndex(
          (user) => user.id === userId
        );
        if (currentUserIndex !== -1) {
          this.allUserList[currentUserIndex].fullName = 'You';

          const currentUser = this.allUserList.splice(currentUserIndex, 1)[0];
          this.allUserList.unshift(currentUser);
        }
      });

    this.store
      .select(getTotalQRCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.totalPages = data;
      });
  }

  fetchData(): void {
    if (this.filter === 'all') {
      this.store.dispatch(
        new FetchAllQrForms(
          this.currPageNumber,
          this.pageSize,
          this.searchTerm,
          false
        )
      );
      this.selectedCountReset();
    } else if (this.filter === 'deleted') {
      this.store.dispatch(
        new FetchAllQrForms(
          this.currPageNumber,
          this.pageSize,
          this.searchTerm,
          true
        )
      );
      this.selectedCountReset();
    }
  }

  navigateToFormCustom() {
    this.router.navigate(['global-config/add-customization-form']);
  }

  editTemplate(template: any) {
    this.router.navigate(
      ['global-config/edit-customization-form/' + template.id],
      {
        state: template,
      }
    );
  }

  cloneTemplate(template: any) {
    let payload = { ...template, isClone: true };
    // const baseName = payload.name;
    // let suffix = 1;

    // while (this.qrFormList.some((form: any) => form.name === payload.name)) {
    //   payload.name = `${baseName}_${suffix}`;
    //   suffix++;
    // }
    payload.id = undefined;
    payload.agency = undefined;
    this.store.dispatch(new AddQrForm(payload));
  }

  assignProjects(template: any, showProjects: TemplateRef<any>) {
    this.selectedTemplate = template;
    this.store.dispatch(new FetchAssignedProjectsList(template.id));
    this.modalRef = this.modalService.show(showProjects, {
      class: 'right-modal modal-350 ip-modal-unset',
    });
  }

  AssignUser(template: any, assignUserModal: TemplateRef<any>) {
    if (template.id) {
      this.store.dispatch(new FetchUserAssignmentByEntity(template.id));
    }
    this.selectedTemplate = template;
    this.modalRef = this.modalService.show(assignUserModal, {
      class: 'right-modal modal-350 ip-modal-unset',
    });
  }

  TrackCampaign(template: any, campaignModal: TemplateRef<any>) {
    this.selectedTemplate = template;
    this.modalRef = this.modalService.show(campaignModal, {
      class: 'right-modal modal-350 ip-modal-unset',
    });
  }

  AssignAgency(template: any, assignAgency: TemplateRef<any>) {
    this.selectedTemplate = template;
    if (template.agencyName) {
      this.agencyName.patchValue(template.agencyName);
    }
    this.modalRef = this.modalService.show(assignAgency, {
      class: 'modal-300 modal-dialog-centered ip-modal-unset',
    });
  }

  openQrCode(template: any) {
    this.modalService.show(QrGeneratorComponent, {
      class: 'modal-450 right-modal ip-modal-unset',
      initialState: {
        data: {
          templateId: template.id,
          templateName: template.name,
        },
      },
    });
  }

  openConfirmModal(data: any, section: string) {
    if (section == 'status') {
      let initialState: any = {
        message: 'GLOBAL.user-confirmation',
        confirmType: 'change the status of',
        title: data?.name,
        fieldType: 'QR Template',
      };
      this.modalRef = this.modalService.show(
        UserConfirmationComponent,
        Object.assign(
          {},
          {
            class: 'modal-400 top-modal ph-modal-unset',
            initialState,
          }
        )
      );
      if (this.modalRef?.onHide) {
        this.modalRef.onHide.subscribe((reason: string) => {
          if (reason == 'confirmed') {
            this.store.dispatch(
              new UpdateQrFormStatus(
                data.id,
                this.currPageNumber,
                this.pageSize
              )
            );
          } else {
            this.store.dispatch(
              new FetchAllQrForms(
                this.currPageNumber,
                this.pageSize,
                this.searchTerm
              )
            );
            this.store.dispatch(new LoaderHide());
          }
        });
      }
    } else if (section == 'delete') {
      let initialState: any = {
        message: 'GLOBAL.user-confirmation',
        confirmType: 'delete',
        title: data?.name,
        fieldType: 'QR Template',
      };
      this.modalRef = this.modalService.show(
        UserConfirmationComponent,
        Object.assign(
          {},
          {
            class: 'modal-400 top-modal ph-modal-unset',
            initialState,
          }
        )
      );
      if (this.modalRef?.onHide) {
        this.modalRef.onHide.subscribe((reason: string) => {
          if (reason == 'confirmed') {
            this.store.dispatch(
              new DeleteQrForm(
                data.id,
                this.currPageNumber,
                this.pageSize,
                false
              )
            );
          }
        });
      }
    }
    this.moveToFirstPage();
  }

  restoreTemplate(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'Restore',
      title: data?.name,
      fieldType: 'QR Template',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-350 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(
            new RestoreTemplate(data.id, this.currPageNumber, this.pageSize)
          );
        }
      });
    }
    this.moveToFirstPage();
  }

  permanentDelete(event: any, data: any) {
    event.stopPropagation();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      type: 'permanentDelete',
      data: {
        fieldType: 'Delete',
        heading: 'Delete QR Template permanently?',
        message: `You are about to delete the QR Template "<b>${data?.name}</b>" permanently. 🚫`,
        description:
          'Delete all information associated with this QR template. Once the QR template is deleted, the action is irreversible, and data recovery will not be possible.',
        title: data?.name,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-450 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(
            new DeleteQrForm(data.id, this.currPageNumber, this.pageSize, true)
          );
        }
      });
    }
    this.moveToFirstPage();
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  isAllSelected() {
    if (this.qrFormList?.length === 0) {
      return false;
    }
    return this.selectedCount === this.qrFormList?.length;
  }

  selectedCountReset() {
    this.selectedCount = 0;
  }

  onCheckboxChange(event: any) {
    const isChecked = event.target.checked;
    if (isChecked) {
      this.selectedCount++;
    } else {
      this.selectedCount--;
    }
  }

  selectAllRows(event: any): void {
    const isChecked = event.target.checked;
    this.qrFormList = this.qrFormList.map((integration) => ({
      ...integration,
      selected: isChecked,
    }));
    this.selectedCount = isChecked ? this.qrFormList?.length : 0;
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    this.selectedTemplates = this.qrFormList.filter((item) => item.selected);
    let initialState: any = {
      data: this.selectedTemplates,
      class: 'right-modal modal-300',
    };
    this.bulkDeleteModalRef = this.modalService.show(
      BulkDeleteModal,
      initialState
    );
  }

  openBulkRestoreModal(BulkRestoreModal: TemplateRef<any>) {
    this.selectedTemplates = this.qrFormList.filter((item) => item.selected);

    let initialState: any = {
      data: this.selectedTemplates,
      class: 'right-modal modal-300',
    };
    this.bulkDeleteModalRef = this.modalService.show(
      BulkRestoreModal,
      initialState
    );
  }

  openConfirmDeleteModal(formName: string, formId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: formName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeSelection(formId);
        }
      });
    }
  }

  removeSelection(id: string): void {
    const formIndex = this.qrFormList.findIndex((form: any) => form.id === id)

    if (formIndex !== -1) {
      this.qrFormList[formIndex].selected = false;
    }
    this.selectedTemplates = this.selectedTemplates.filter(
      (form: any) => form.id !== id
    );
    this.selectedCount = this.selectedTemplates?.length
    if (this.selectedTemplates.length === 0) {
      this.modalService.hide();
    }
  }

  updateBulkDelete(data: any) {
    let ids: any = [];
    this.selectedTemplates?.map((qr: any) => ids.push(qr.id));
    if (this.filter === 'all') {
      this.store.dispatch(new BulkDelete(ids, false));
    } else {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      let initialState: any = {
        type: 'permanentDelete',
        data: {
          fieldType: 'Delete',
          heading: `Delete ${data?.length} QR Template(s) permanently?`,
          message: `You are about to delete ${data?.length} QR Template(s) permanently. 🚫`,
          description:
            'Delete all information associated with this QR Template(s). Once the QR Template(s) is deleted, the action is irreversible, and data recovery will not be possible.',
          title: data?.name,
        },
        class: 'modal-450 modal-dialog-centered ph-modal-unset',
      };
      this.modalRef = this.modalService.show(
        UserAlertPopupComponent,
        Object.assign(
          {},
          {
            class: 'modal-450 modal-dialog-centered ph-modal-unset',
            initialState,
          }
        )
      );
      if (this.modalRef?.onHide) {
        this.modalRef.onHide.subscribe((reason: string) => {
          if (reason == 'confirmed') {
            this.store.dispatch(new BulkDelete(ids, true));
          } else {
            this.store.dispatch(
              new FetchAllQrForms(
                this.currPageNumber,
                this.pageSize,
                this.searchTerm,
                true
              )
            );
            this.store.dispatch(new LoaderHide());
          }
        });
      }
    }
    this.moveToFirstPage();
    this.bulkDeleteModalRef.hide();
    this.selectedCountReset();
  }

  updateBulkRestore() {
    let ids: any = [];
    this.selectedTemplates?.map((qr: any) => ids.push(qr.id));
    this.store.dispatch(new BulkRestore(ids));
    this.moveToFirstPage();
    this.bulkDeleteModalRef.hide();
    this.selectedCountReset();
  }

  onPageChange(offset: number) {
    this.currOffset = offset;
    this.currPageNumber = offset + 1;
    this.store.dispatch(new AssignPageSize(this.pageSize, this.currPageNumber));
    this.filterFormList();
    this.fetchData();
  }

  moveToFirstPage() {
    this.store.dispatch(new AssignPageSize(this.pageSize, 1));
  }

  filterFormList() {
    this.store.dispatch(
      new FetchAllQrForms(this.currPageNumber, this.pageSize, this.searchTerm)
    );
    this.store.dispatch(new LoaderHide());
  }

  assignPageSize() {
    this.pageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    this.store.dispatch(new AssignPageSize(this.pageSize, this.currPageNumber));
    this.filterFormList();
    this.fetchData();
  }

  generateQrUrl(formId: string): string {
    const baseUrl = this.isLocalhost
      ? window.location.origin
      : `https://${this.url}`;
    return `${baseUrl}/external/add-lead/qr-code/${formId}`;
  }

  OpenConfig(template: any, config: TemplateRef<any>) {
    if (template.id) {
      this.store.dispatch(new FetchUserAssignmentByEntity(template.id));
    }
    this.store.dispatch(new FetchAssignedProjectsList(template.id));
    if (template.agencyName) {
      this.agencyName.patchValue(template.agencyName);
    }
    let initialState: any = {
      selectedTemplate: template,
      allUserList: this.allUserList,
      assignedToUsers: this.assignedToUsers,
      moduleId: this.moduleId,
      updatedUser: this.updatedUser,
      project: this.project,
      allProjectsList: this.allProjectsList,
      updatedProjects: this.updatedProjects,
      agencyName: this.agencyName,
      updatedAgent: this.updatedAgent,
    };
    this.modalRef = this.modalService.show(ConfigurationComponent, {
      class: 'right-modal modal-400 ip-modal-unset',
      initialState,
    });

    this.modalRef.onHidden?.subscribe(() => {
      this.expandedSections = {};
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
