import {
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, switchMap, takeUntil } from 'rxjs';

import {
  LEAD_GENERATING_FROM,
  PAGE_SIZE,
  REPORTS_DATE_TYPE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
} from 'src/app/app.constants';
import {
  IntegrationSource,
  LeadGeneratingFrom,
  LeadSource,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchLeadCities,
  FetchLeadCountries,
  FetchLeadStates,
  FetchProjectList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadStates,
  getLeadStatesIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchReceivedDateExportSuccess,
  FetchReportsReceivedDate,
  UpdateReceivedDateFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getReceivedDateFiltersPayload,
  getReportsReceivedDateList,
  getReportsReceivedDateListIsLoading,
} from 'src/app/reducers/reports/reports.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
@Component({
  selector: 'received-source',
  templateUrl: './received-date-source.component.html',
})
export class ReceivedSourceComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  gridOptions: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  rowData: Array<any> = [];
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  getPages = getPages;
  appliedFilter: any;
  filtersPayload: ReportsFilter;
  receivedDateTotalCount: number;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  projectList: any;
  isDateFilter: string;
  subSourceList: any;
  agencyNameList: any;
  allSubSourceList: any;
  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 5);
  leadGeneratingFrom: Array<string> = LEAD_GENERATING_FROM.slice(0, 5);
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  showLeftNav: boolean = true;
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  allSubSourceListIsLoading: boolean = true;
  isProjectListLoading: boolean = true;
  isReceivedDateLoading: boolean = true;
  leadSource = LeadSource;
  showFilters: boolean = false;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  leadSources: any[];


  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  countryList: any[];
  countryIsLoading: boolean = true;

  userData: any;
  currentDate: Date = new Date();
  toDateForLeadReceived: any = new Date();
  fromDateForLeadReceived: any = new Date();
  onPickerOpened = onPickerOpened;
  filteredColumnDefsCache: any[] = [];

  @ViewChild('reportsGraph') reportsGraph: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    public router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef
  ) {
    this.headerTitle.setTitle('Leads - Received Date vs Source Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store.dispatch(new FetchAllSources());
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });
    this._store
      .select(getReceivedDateFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          subSources: this.filtersPayload?.SubSources,
          projects: this.filtersPayload?.Projects,
          dateType: 1,
          generatingFrom:
            LeadGeneratingFrom[Number(this.filtersPayload?.generatingFrom)],
          dateForLeadReceived: [
            patchTimeZoneDate(this.filtersPayload?.fromDateForLeadReceived, this.userData?.timeZoneInfo?.baseUTcOffset),
            patchTimeZoneDate(this.filtersPayload?.toDateForLeadReceived, this.userData?.timeZoneInfo?.baseUTcOffset)
          ],
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States,
          timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
          baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
        };
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          this.users = data;
          this.allUsers = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          this.reportees = data;
          this.onlyReportees = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;

      });
    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });
    this._store
      .select(getReportsReceivedDateList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.items?.length > 0) {
          const fieldNames = Object.keys(data.items[0]);
        }
        this.rowData = getTotalCountForReports(data.items);
        this.receivedDateTotalCount = data?.totalCount;
        if (this.gridApi) {
          this.gridOptions.rowData = this.rowData;
          this.gridApi.setRowData(this.rowData);
        }
      });
    this._store
      .select(getReportsReceivedDateListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isReceivedDateLoading = isLoading;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has('Permissions.Reports.ExportAllUsers');
        this.canViewAllUsers = permissionsSet.has('Permissions.Reports.ViewAllUsers');
        this.canExportReportees = permissionsSet.has('Permissions.Reports.ExportReportees');
        this.canViewReportees = permissionsSet.has('Permissions.Reports.ViewReportees');
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });
    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
        this.updateSubSource();
        this.initializeGridSettings();
        this.initializeGraphData();
        if (this.gridApi) {
          this.gridApi.refreshCells();
        }
      });
    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.updateSubSource()
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.allSubSourceListIsLoading = isLoading;
      });

    this._store
      .select(getLeadCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.citiesIsLoading = data;
      });

    this._store
      .select(getLeadStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getLeadStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getLeadCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });

    this.shareDataService.showLeftNav$.subscribe(show => {
      this.showLeftNav = show;
    });

    this.initializeGridSettings();
    this.initializeGraphData();

    this.filterFunction();
    this.activeDate();
  }

  ngOnInit() {
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });

  }

  initializeGraphData() {
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'Received Date'
    );
  }


  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    const columnDefs: any[] = [
      {
        headerName: 'Received Date',
        field: 'Received Date',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        maxWidth: 150,
        cellClass: 'lock-pinned',
        suppressMovable: true,
        lockPosition: 'left',
        valueGetter: (params: any) => {
          if (params?.data?.projectTitle !== 'Total') {
            return getTimeZoneDate(params.data?.CreatedOn, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
          } else {
            return 'Total';
          }
        },
        cellRenderer: (params: any) => {
          return `<p class="py-16 text-nowrap">${params.value}</p>`;
        },
      }
    ];

    const sourceFieldMapping: { [key: string]: string } = {
      'Roof&Floor': 'RoofandFloor',
      '99Acres': 'NinetyNineAcres',
    };
    if (this.leadSources && this.leadSources.length > 0) {
      this.leadSources.forEach((source) => {
        const sourceName = source?.displayName?.toString();
        let sourceField = source?.displayName?.replace(/\s+/g, '');
        const dataField = sourceFieldMapping[sourceField] || sourceField;
        const columnDef: any = {
          headerName: sourceName,
          field: dataField,
          cellRenderer: (params: any) => {
            const value = params.data?.[dataField] !== undefined ? params.data[dataField] : '0';
            return params?.data?.projectTitle === 'Total' || value === '--' || value === 0
              ? `<p>${value}</p>`
              : `<p><a>${value}</a></p>`;
          },
          cellClass: 'cursor-pointer',
          onCellClicked: (event: any) => {
            if (!event?.data) return;
            const isCtrlClick = event?.event?.ctrlKey;
            const value = event.data[dataField];
            if (
              event.data.projectTitle === 'Total' ||
              value === undefined ||
              value === '--' ||
              value === 0
            ) {
              return;
            } else {
              if (isCtrlClick) {
                this.getDataNewTab(sourceName, event.data.CreatedOn);
                return;
              }
              this.getDataFromCell(sourceName, event.data.CreatedOn);
            }
          }
        };
        columnDefs.push(columnDef);
      });
    }
    this.gridOptions.columnDefs = columnDefs;
    this.columnDropDown = [];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index !== 0 && index !== this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.rowData = this.rowData;
    this.gridOptions.context = {
      componentParent: this,
    };
    this.gridOptions.suppressMovableColumns = false;
    this.gridOptions.onColumnMoved = this.onColumnMoved.bind(this);
    if (this.gridApi) {
      this.gridApi.setColumnDefs(columnDefs);
      this.gridApi.refreshCells();
    }
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    const columnState = JSON.parse(localStorage.getItem('receivedDateSourceColumnState') || 'null');
    if (columnState) {
      if (columnState[0]) {
        columnState[0].pinned = window.innerWidth > 480 ? 'left' : null;
      }
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }
  }

  onColumnMoved(params: any) {
    const columnState = JSON.stringify(
      params?.columnApi?.getColumnState()?.map((column: any) => ({
        ...column,
        sort: null,
      }))
    );
    localStorage.setItem('receivedDateSourceColumnState', columnState);
  }

  getDataFromCell(source: string, receivedDate: Date) {
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.data = { source };
    this.gridOptionsService.dateType = 'Created Date';
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.status = 'All Leads';
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = true;
    this.gridOptionsService.payload = {
      ...filters,
      fromDate: setTimeZoneDate(receivedDate, this.userData?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(receivedDate, this.userData?.timeZoneInfo?.baseUTcOffset),
      Sources: [source],
    };
  }

  getDataNewTab(source: string, receivedDate: Date) {
    const filters = {
      ...this.filtersPayload,
      fromDate: setTimeZoneDate(receivedDate, this.userData?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(receivedDate, this.userData?.timeZoneInfo?.baseUTcOffset),
      Sources: [source],
    };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = true;
    }
    window?.open(
      `leads/manage-leads?leadReportGetData=true&data=${encodeURIComponent(
        JSON.stringify({ source })
      )}&operation=${'All Leads'}&filtersPayload=${encodeURIComponent(
        JSON.stringify({ ...filters })
      )}`,
      '_blank'
    );
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(
      new UpdateReceivedDateFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsReceivedDate());
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(
      new UpdateReceivedDateFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsReceivedDate());
    this.currOffset = 0;
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SubSources: this.appliedFilter.subSources,
      Projects: this.appliedFilter.projects,
      SearchText: this.searchTerm,
      dateType: 1,
      generatingFrom: LeadGeneratingFrom[this.appliedFilter.generatingFrom],
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      fromDateForLeadReceived: setTimeZoneDate(
        this.appliedFilter?.dateForLeadReceived?.[0], this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDateForLeadReceived: setTimeZoneDate(
        this.appliedFilter?.dateForLeadReceived?.[1], this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      Countries: this.appliedFilter?.Countries,
      timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
    };
    this._store.dispatch(
      new UpdateReceivedDateFilterPayload(this.filtersPayload)
    );
    this._store.dispatch(new FetchReportsReceivedDate());
    this.currOffset = 0;
    this.appliedFilter.pageNumber = 1;
    if (
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.dateForLeadReceived?.[0] ||
      this.appliedFilter.generatingFrom?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  resetDate() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateForLeadReceived: [null, null],
    };
    this.isDateFilter = '';
    this.filterFunction();
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
      dateForLeadReceived: [null, null],
    };
    this.isDateFilter = '';

    this.filterFunction();
  }

  filterByDate(type?: string) {
    // let newDate = new Date();
    let date = new Date(this.currentDate.setHours(0, 0, 0, 0));

    switch (type) {
      case 'today':
        this.isDateFilter = 'today';
        this.appliedFilter.dateForLeadReceived[0] = new Date(date);
        this.appliedFilter.dateForLeadReceived[1] = new Date(date);
        break;
      case 'yesterday':
        this.isDateFilter = 'yesterday';
        this.appliedFilter.dateForLeadReceived[0] = new Date().setDate(
          new Date().getDate() - 1
        );
        this.appliedFilter.dateForLeadReceived[1] = new Date(date).setDate(
          new Date(date).getDate() - 1
        );
        break;
      case 'sevenDays':
        this.isDateFilter = 'sevenDays';
        this.appliedFilter.dateForLeadReceived[0] = new Date().setDate(
          new Date().getDate() - 6
        );
        this.appliedFilter.dateForLeadReceived[1] = new Date(date);
        break;
      case 'custom':
        this.isDateFilter = 'custom';
        this.appliedFilter.dateForLeadReceived[0] = null;
        this.appliedFilter.dateForLeadReceived[1] = null;
        break;
    }
  }

  activeDate() {
    const fromDate = new Date(this.appliedFilter.dateForLeadReceived[0]);
    const toDate = new Date(this.appliedFilter.dateForLeadReceived[1]);

    const today = new Date(this.currentDate);
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6);

    if (
      fromDate.toDateString() === today.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'today';
    } else if (
      fromDate.toDateString() === yesterday.toDateString() &&
      toDate.toDateString() === yesterday.toDateString()
    ) {
      this.isDateFilter = 'yesterday';
    } else if (
      fromDate.toDateString() === sevenDaysAgo.toDateString() &&
      toDate.toDateString() === today.toDateString()
    ) {
      this.isDateFilter = 'sevenDays';
    } else if (this.appliedFilter.dateForLeadReceived[0]
    ) {
      this.isDateFilter = 'custom';
    }

  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = ['subSources', 'projects', 'agencyNames', 'cities', 'states'];
    if (['timeZoneId', 'baseUTcOffset'].includes(key)) {
      return [];
    }

    if (
      [
        'pageSize',
        'pageNumber',
        'withTeam',
        'dateType',
        'search'
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'dateForLeadReceived' && values.length === 2) {
      if (key === 'dateForLeadReceived' && values[0] !== null) {
        this.toDateForLeadReceived = setTimeZoneDate(new Date(values[0]), this.userData?.timeZoneInfo?.baseUTcOffset);
        this.fromDateForLeadReceived = setTimeZoneDate(new Date(values[1]), this.userData?.timeZoneInfo?.baseUTcOffset);
        const formattedToDate = getTimeZoneDate(this.toDateForLeadReceived, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
        const formattedFromDate = getTimeZoneDate(this.fromDateForLeadReceived, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  onRemoveFilter(key: string, value: string) {
    if (key === 'dateForLeadReceived') {
      delete this.appliedFilter[key];
      this.isDateFilter = '';
    }
    else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter((item: any, index: number) => {
        const matchIndex = this.appliedFilter[key]?.indexOf(value);
        return index !== matchIndex;
      });
    }
    this.filterFunction();

  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchLeadStates());
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchAllSources());
    this._store.dispatch(new FetchLeadCountries());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  exportMeetingReport() {
    this._store.dispatch(new FetchReceivedDateExportSuccess(''));
    this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.filtersPayload
      }, class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' &&
      this.reportsGraph?.isChartReady &&
      !this.reportsGraph?.showSelectionMessage;
  }

  updateSubSource() {
    let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
      if (lead?.displayName === '99 Acres') {
        return this.allSubSourceList?.['NinetyNineAcres'] || [];
      }
      const formattedKey = lead?.displayName?.replace(/\s+/g, '');
      let match = this.allSubSourceList?.[formattedKey];
      if (!match) {
        match = this.allSubSourceList[lead?.displayName];
      }
      if (!match && formattedKey?.toLowerCase() === '99acres') {
        match = this.allSubSourceList['NinetyNineAcres'];
      }
      return Array.isArray(match) ? match : [];
    }) || [];
    this.subSourceList = subSourceList
  }

  onSelectSource(source: any) {
    if (source) {
      this.updateSubSources(source.displayName);
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSources(sourceName: string | null) {
    if (sourceName) {
      if (sourceName === '99 Acres') {
        this.subSourceList = this.allSubSourceList?.['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        if (Array.isArray(this.allSubSourceList?.[formattedKey])) {
          this.subSourceList = this.allSubSourceList?.[formattedKey] || [];
        } else {
          this.subSourceList = this.allSubSourceList?.[sourceName] || [];
        }
      }
    } else {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList?.[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList?.['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList;
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
