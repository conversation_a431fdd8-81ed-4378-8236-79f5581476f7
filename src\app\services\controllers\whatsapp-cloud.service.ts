import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
    providedIn: 'root'
})
export class WhatsappCloudService extends BaseService<any> {
    serviceBaseUrl: string;
    AddWhatsappCloud: any;

    getResourceUrl(): string {
        return 'whatsappcloudapi';
    }

    constructor(private http: HttpClient) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    }

    getWhatsappCloud() {
        return this.http.get(`${this.serviceBaseUrl}`);
    }

    addWhatsappCloud(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/bulk-batch`, payload);
    }

    addWhatsappCloudTest(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/test`, payload);
    }

    utilitySendEmail(payload: any) {
        const formData: FormData = new FormData();
        formData.append('Sender', payload.Sender);
        formData.append('Subject', payload.Subject);
        formData.append('ContentBody', payload.ContentBody);
        payload.ToRecipients.forEach((recipient: string) => {
            formData.append('ToRecipients', recipient);
        });
        const headers = new HttpHeaders();
        headers.append('Content-Type', 'multipart/form-data');

        return this.http.post(`${env.baseURL}${env.apiURL}utility/sendemail/form`, formData, { headers });
    }
}
