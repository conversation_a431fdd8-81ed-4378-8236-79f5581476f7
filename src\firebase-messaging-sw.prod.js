importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-messaging-compat.js");

firebase.initializeApp({
    apiKey: 'AIzaSyAkHQW-YohRdlSC8mRDMJ5fhrT3eb19dKc',
    authDomain: 'leadrat-black-web-prd.firebaseapp.com',
    projectId: 'leadrat-black-web-prd',
    storageBucket: 'leadrat-black-web-prd.appspot.com',
    messagingSenderId: '100072126185',
    appId: '1:100072126185:web:c0e88ff4e4bacc840ee278',
    measurementId: 'G-38LF6G2BG8',
    vapidKey: 'BMWnbr-GVaSToA3aw9TaIZAefDzSHXU3XGFB2W4M0xG0YyR0sDmS5EymDkcJEwDdcLUMDGW3EzFrFkAMeJ4L2pws'
});

const messaging = firebase.messaging();
self.addEventListener('push', function (event) {
    const payload = event.data ? event.data.json() : {};
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png'
        // data: { url: payload.data.click_action},
        // actions: [
        //     {
        //         action: 'reply', // Identifier for the action
        //         title: 'Reply', // Button text
        //         icon: payload.notification.icon // Optional icon for the button,
        //     },
        //     {
        //         action: 'dismiss', // Identifier for the action
        //         title: 'Dismiss', // Button text
        //         icon: payload.notification.icon // Optional icon for the button
        //     }
        // ],
    };

    event.waitUntil(
        // Retrieve all existing notifications and close them
        self.registration.getNotifications().then(function (notifications) {
            return Promise.all(notifications.map(function (notification) {
                return notification.close();
            }));
        })
            .then(function () {
                return self.registration.showNotification(notificationTitle, notificationOptions);
            })
    );
});

// self.addEventListener('notificationclick', function(event) {
//     event.notification.close();
//     console.log(event);

//     // Determine action based on notification data
//     const actionUrl =  event.notification.data.url;

//     event.waitUntil(
//         clients.openWindow(actionUrl)
//     );
// });


messaging.onBackgroundMessage(function (payload) {
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png'
    };

    self.registration.showNotification(notificationTitle, notificationOptions);
});
