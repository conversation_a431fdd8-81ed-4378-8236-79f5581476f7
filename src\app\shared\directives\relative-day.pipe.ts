import { Pipe, PipeTransform } from '@angular/core';
import { formatDate } from '@angular/common';

@Pipe({
  name: 'relativeDay'
})
export class RelativeDayPipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return '';

    // Convert DD-MM-YYYY to a Date object
    const [day, month, year] = value.split('-').map(Number);
    const inputDate = new Date(year, month - 1, day); // Month is 0-based

    const today = new Date();

    // Normalize times to avoid timezone issues
    inputDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const diffInDays = Math.round((today.getTime() - inputDate.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays <= 6) {
      return inputDate.toLocaleDateString('en-US', { weekday: 'long' }); // Monday, Tuesday, etc.
    }

    return formatDate(inputDate, 'dd-MM-yyyy', 'en-US'); // Show full date after a week
  }
}
