import { Action } from '@ngrx/store';
import { LeadExcel, MapColumnsExcel } from 'src/app/core/interfaces/leads.interface';

export enum MarketingAction {
    // ------------------------- AGENCY PART ------------------------------------
    FETCH_AGENCY_NAME = '[MARKETING] Fetch Agency Name',
    FETCH_AGENCY_NAME_SUCCESS = '[MARKETING] Fetch Agency Name Success',
    ADD_AGENCY_NAME = '[MARKETING] Add Agency Name',
    ADD_AGENCY_NAME_SUCCESS = '[MARKETING] Add Agency Name Success',
    UPDATE_AGENCY_NAME = '[MARKETING] Update Agency Name',
    UPDATE_AGENCY_NAME_SUCCESS = '[MARKETING] Update Agency Name Success',
    DELETE_AGENCY = '[MARKETING] Delete Agency',
    DELETE_AGENCY_SUCCESS = '[MARKETING] Delete Agency Success',
    UPDATE_AGENCY_FILTERS_PAYLOAD = '[AGENCY] Update Agency Filters Payload',
    MARKETING_EXCEL_UPLOAD = '[MARKETING] Upload Agency Excel File',
    MARKETING_EXCEL_UPLOAD_SUCCESS = '[MARKETING] Upload Agency Excel File Success',
    FETCH_AGENCY_EXCEL_UPLOADED_LIST = '[MARKETING] Fetch Agency Excel Uploaded List',
    FETCH_AGENCY_EXCEL_UPLOADED_LIST_SUCCESS = '[AGENCY] Fetch Agency Excel Uploaded List Success',
    UPLOAD_AGENCY_MAPPED_COLUMNS = '[MARKETING] Upload Agency Mapped Column Data',
    EXPORT_AGENCY = '[MARKETING] Export Agency',
    EXPORT_AGENCY_SUCCESS = '[MARKETING] Export Agency Success',
    EXPORT_AGENCY_TRACKER = '[MARKETING] Export Agency Tracker',
    EXPORT_AGENCY_TRACKER_SUCCESS = '[AGENCY] Export Agency Tracker Success',
    EXIST_AGENCY = '[MARKETING] Exists Agency',
    EXIST_AGENCY_SUCCESS = '[MARKETING] Exists Agency Success',
    FETCH_AGENCY_LOCATION = '[MARKETING] Agency LOcation',
    FETCH_AGENCY_LOCATION_SUCCESS = '[MARKETING] Agency Location Success',

    //   -------------------------------- CAMPAIGN PART -------------------------------
    FETCH_CAMPAIGN_NAME = '[MARKETING] Fetch Campaign Name',
    FETCH_CAMPAIGN_NAME_SUCCESS = '[MARKETING] Fetch Campaign Name Success',
    ADD_CAMPAIGN_NAME = '[MARKETING] Add Campaign Name',
    ADD_CAMPAIGN_NAME_SUCCESS = '[MARKETING] Add Campaign Name Success',
    UPDATE_CAMPAIGN_NAME = '[MARKETING] Update Campaign Name',
    UPDATE_CAMPAIGN_NAME_SUCCESS = '[MARKETING] Update Campaign Name Success',
    UPDATE_CAMPAIGN_FILTERS_PAYLOAD = '[MARKETING] Update Campaign Filters Payload',
    DELETE_CAMPAIGN = '[MARKETING] Delete Campaign',
    DELETE_CAMPAIGN_SUCCESS = '[MARKETING] Delete Campaign Success',
    EXPORT_CAMPAIGN = '[MARKETING] Export Campaign',
    EXPORT_CAMPAIGN_SUCCESS = '[MARKETING] Export Campaign Success',
    EXPORT_CAMPAIGN_TRACKER = '[MARKETING] Export Campaign Tracker',
    EXPORT_CAMPAIGN_TRACKER_SUCCESS = '[MARKETING] Export Campaign Tracker Success',
    CAMPAIGN_EXCEL_UPLOAD = '[MARKETING] Upload Campaign Excel File',
    CAMPAIGN_EXCEL_UPLOAD_SUCCESS = '[MARKETING] Upload Campaign Excel File Success',
    FETCH_CAMPAIGN_EXCEL_UPLOADED_LIST = '[MARKETING] Fetch Campaign Excel Uploaded List',
    FETCH_CAMPAIGN_EXCEL_UPLOADED_LIST_SUCCESS = '[MARKETING] Fetch Campaign Excel Uploaded List Success',
    UPLOAD_CAMPAIGN_MAPPED_COLUMNS = '[MARKETING] Upload Campaign Mapped Column Data',
    EXIST_CAMPAIGN = '[MARKETING] Exists Campaign',
    EXIST_CAMPAIGN_SUCCESS = '[MARKETING] Exists Campaign Success',
    // --------------------------------- CHANNEL PARTNER PART ----------------------------------
    FETCH_CHANNEL_PARTNER = '[MARKETING] Fetch Channel Partner',
    FETCH_CHANNEL_PARTNER_SUCCESS = '[MARKETING] Fetch Channel Partner Success',
    ADD_CHANNEL_PARTNER = '[MARKETING] Add Channel Partner',
    ADD_CHANNEL_PARTNER_SUCCESS = '[MARKETING] Add Channel Partner Success',
    DELETE_CHANNEL_PARTNER = '[MARKETING] Delete Channel Partner',
    DELETE_CHANNEL_PARTNER_SUCCESS = '[MARKETING] Delete Channel Partner Success',
    UPDATE_CHANNEL_PARTNER = '[MARKETING] Update Channel Partner Success',
    UPDATE_CHANNEL_PARTNER_SUCCESS = '[MARKETING] Update Channel Partner',
    UPDATE_CHANNEL_PARTNER_FILTERS_PAYLOAD = '[MARKETING] Update Channel Partner Filters Payload',
    CHANNEL_PARTNER_EXCEL_UPLOAD = '[MARKETING] Upload Channel Partner Excel File',
    CHANNEL_PARTNER_EXCEL_UPLOAD_SUCCESS = '[MARKETING] Upload Channel Partner Excel File Success',
    FETCH_CP_EXCEL_UPLOADED_LIST = '[MARKETING] Fetch Channel Partner Excel Uploaded List',
    FETCH_CP_EXCEL_UPLOADED_LIST_SUCCESS = '[MARKETING] Fetch Channel Partner Excel Uploaded List Success',
    UPLOAD_CHANNEL_PARTNER_MAPPED_COLUMNS = '[MARKETING] Upload Channel Partner Mapped Column Data',
    EXPORT_CHANNEL_PARTNER = '[MARKETING] Export Channel Partner',
    EXPORT_CHANNEL_PARTNER_SUCCESS = '[MARKETING] Export Channel Partner Success',
    EXPORT_CHANNEL_PARTNER_TRACKER = '[MARKETING] Export Channel Partner Tracker',
    EXPORT_CHANNEL_PARTNER_TRACKER_SUCCESS = '[MARKETING] Export Channel Partner Tracker Success',
    EXIST_CHANNEL_PARTNER = '[MARKETING] Exists Channel Partner',
    EXIST_CHANNEL_PARTNER_SUCCESS = '[MARKETING] Exists Channel Partner Success',
    FETCH_CHANNEL_PARTNER_LOCATION = '[MARKETING] Channel Partner Location',
    FETCH_CHANNEL_PARTNER_LOCATION_SUCCESS = '[MARKETING] Channel Partner Location Success',
}
// ----------------------- AGENCY PART ----------------------
export class FetchAgencyName implements Action {
    readonly type: string = MarketingAction.FETCH_AGENCY_NAME;
    constructor(public filtersPayload: any) { }
}
export class FetchAgencyNameSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_AGENCY_NAME_SUCCESS;
    constructor(public response?: any) { }
}
export class FetchAgencyLocations implements Action {
    readonly type: string = MarketingAction.FETCH_AGENCY_LOCATION;
    constructor() { }
}
export class FetchAgencyLocationsSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_AGENCY_LOCATION_SUCCESS;
    constructor(public response: string[] = []) { }
}
export class AddAgencyName implements Action {
    readonly type: string = MarketingAction.ADD_AGENCY_NAME;
    constructor(public payload: any) { }
}
export class AddAgencyNameSuccess implements Action {
    readonly type: string = MarketingAction.ADD_AGENCY_NAME_SUCCESS;
    constructor(public response?: any) { }
}
export class DeleteAgency implements Action {
    readonly type: string = MarketingAction.DELETE_AGENCY;
    constructor(public id: any) { }
}
export class DeleteAgencySuccess implements Action {
    readonly type: string = MarketingAction.DELETE_AGENCY_SUCCESS;
    constructor(public response?: any) { }
}
export class UpdateAgencyName implements Action {
    readonly type: string = MarketingAction.UPDATE_AGENCY_NAME;
    constructor(public payload: any) { }
}
export class UpdateAgencyNameSuccess implements Action {
    readonly type: string = MarketingAction.UPDATE_AGENCY_NAME_SUCCESS;
    constructor(public response?: any) { }
}
export class UpdateAgencyFiltersPayload implements Action {
    readonly type: string =
        MarketingAction.UPDATE_AGENCY_FILTERS_PAYLOAD;
    constructor(public payload: any) { }
}
export class MarketingExcelUpload implements Action {
    readonly type: string = MarketingAction.MARKETING_EXCEL_UPLOAD;
    constructor(public file: File) { }
}
export class MarketingExcelUploadSuccess implements Action {
    readonly type: string = MarketingAction.MARKETING_EXCEL_UPLOAD_SUCCESS;
    constructor(public resp: LeadExcel) { }
}
export class FetchAgencyExcelUploadedList implements Action {
    readonly type: string = MarketingAction.FETCH_AGENCY_EXCEL_UPLOADED_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchAgencyExcelUploadedSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_AGENCY_EXCEL_UPLOADED_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}
export class UploadAgencyMappedColumns implements Action {
    readonly type: string = MarketingAction.UPLOAD_AGENCY_MAPPED_COLUMNS;
    constructor(public payload: MapColumnsExcel) { }
}
export class ExportAgency implements Action {
    readonly type: string = MarketingAction.EXPORT_AGENCY;;
    constructor(public payload: any) { }
}
export class ExportAgencySuccess implements Action {
    readonly type: string = MarketingAction.EXPORT_AGENCY_SUCCESS;
    constructor(public resp: string = '') { }
}
export class FetchAgencyExportTracker implements Action {
    readonly type: string = MarketingAction.EXPORT_AGENCY_TRACKER;
    constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchAgencyExportTrackerSuccess implements Action {
    readonly type: string = MarketingAction.EXPORT_AGENCY_TRACKER_SUCCESS;
    constructor(public response: any[] = []) { }
}
export class ExistAgency implements Action {
    readonly type: string = MarketingAction.EXIST_AGENCY;
    constructor(public agencyName: string) { }
}
export class ExistAgencySuccess implements Action {
    readonly type: string = MarketingAction.EXIST_AGENCY_SUCCESS;
    constructor(public response: boolean) { }
}

//   ------------------------- CAMPAIGN PART -------------------------

export class FetchCampaignName implements Action {
    readonly type: string = MarketingAction.FETCH_CAMPAIGN_NAME;
    constructor(public filtersPayload: any) { }
}
export class FetchCampaignNameSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_CAMPAIGN_NAME_SUCCESS;
    constructor(public response: any) { }
}
export class AddCampaignName implements Action {
    readonly type: string = MarketingAction.ADD_CAMPAIGN_NAME;
    constructor(public payload: any) { }
}
export class AddCampaignNameSuccess implements Action {
    readonly type: string = MarketingAction.ADD_CAMPAIGN_NAME_SUCCESS;
    constructor(public response: any) { }
}
export class UpdateCampaignName implements Action {
    readonly type: string = MarketingAction.UPDATE_CAMPAIGN_NAME;
    constructor(public payload: any) { }
}
export class UpdateCampaignNameSuccess implements Action {
    readonly type: string = MarketingAction.UPDATE_CAMPAIGN_NAME_SUCCESS;
    constructor(public response?: any) { }
}
export class UpdateCampaignFiltersPayload implements Action {
    readonly type: string =
        MarketingAction.UPDATE_CAMPAIGN_FILTERS_PAYLOAD;
    constructor(public payload: any) { }
}

export class DeleteCampaign implements Action {
    readonly type: string = MarketingAction.DELETE_CAMPAIGN;
    constructor(public id: any) { }
}

export class DeleteCampaignSuccess implements Action {
    readonly type: string = MarketingAction.DELETE_CAMPAIGN_SUCCESS;
    constructor(public response?: any) { }
}

export class ExportCampaign implements Action {
    readonly type: string = MarketingAction.EXPORT_CAMPAIGN;;
    constructor(public payload: any) { }
}

export class ExportCampaignSuccess implements Action {
    readonly type: string = MarketingAction.EXPORT_CAMPAIGN_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchCampaignExportTracker implements Action {
    readonly type: string = MarketingAction.EXPORT_CAMPAIGN_TRACKER;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchCampaignExportTrackerSuccess implements Action {
    readonly type: string = MarketingAction.EXPORT_CAMPAIGN_TRACKER_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class CampaignExcelUpload implements Action {
    readonly type: string = MarketingAction.CAMPAIGN_EXCEL_UPLOAD;
    constructor(public file: File) { }
}
export class CampaignExcelUploadSuccess implements Action {
    readonly type: string = MarketingAction.CAMPAIGN_EXCEL_UPLOAD_SUCCESS;
    constructor(public resp: LeadExcel) { }
}
export class FetchCampaignExcelUploadedList implements Action {
    readonly type: string = MarketingAction.FETCH_CAMPAIGN_EXCEL_UPLOADED_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchCampaignExcelUploadedSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_CAMPAIGN_EXCEL_UPLOADED_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}
export class UploadCampaignMappedColumns implements Action {
    readonly type: string = MarketingAction.UPLOAD_CAMPAIGN_MAPPED_COLUMNS;
    constructor(public payload: MapColumnsExcel) { }
}

export class ExistCampaign implements Action {
    readonly type: string = MarketingAction.EXIST_CAMPAIGN;
    constructor(public agencyName: string) { }
}

export class ExistCampaignSuccess implements Action {
    readonly type: string = MarketingAction.EXIST_CAMPAIGN_SUCCESS;
    constructor(public response: boolean) { }
}
//   ------------------------ CHANNEL PARTNER -----------------------------

export class FetchChannelPartnerLocations implements Action {
    readonly type: string = MarketingAction.FETCH_CHANNEL_PARTNER_LOCATION;
    constructor() { }
}
export class FetchChannelPartnerLocationsSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_CHANNEL_PARTNER_LOCATION_SUCCESS;
    constructor(public response: string[] = []) { }
}

export class FetchChannelPartner implements Action {
    readonly type: string = MarketingAction.FETCH_CHANNEL_PARTNER;
    constructor(public payload: any) { }
}
export class FetchChannelPartnerSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_CHANNEL_PARTNER_SUCCESS;
    constructor(public response: any) { }
}

export class ExistChannelPartner implements Action {
    readonly type: string = MarketingAction.EXIST_CHANNEL_PARTNER;
    constructor(public CPName: string) { }
}

export class ExistChannelPartnerSuccess implements Action {
    readonly type: string = MarketingAction.EXIST_CHANNEL_PARTNER_SUCCESS;
    constructor(public response: boolean) { }
}
export class AddChannelPartnerName implements Action {
    readonly type: string = MarketingAction.ADD_CHANNEL_PARTNER;
    constructor(public payload: any) { }
}
export class AddChannelPartnerNameSuccess implements Action {
    readonly type: string = MarketingAction.ADD_CHANNEL_PARTNER_SUCCESS;
    constructor(public response?: any) { }
}
export class DeleteChannelPartner implements Action {
    readonly type: string = MarketingAction.DELETE_CHANNEL_PARTNER;
    constructor(public id: any) { }
}

export class DeleteChannelPartnerSuccess implements Action {
    readonly type: string = MarketingAction.DELETE_CHANNEL_PARTNER_SUCCESS;
    constructor(public response?: any) { }
}
export class UpdateChannelPartner implements Action {
    readonly type: string = MarketingAction.UPDATE_CHANNEL_PARTNER;
    constructor(public payload: any) { }
}
export class UpdateChannelPartnerSuccess implements Action {
    readonly type: string = MarketingAction.UPDATE_CHANNEL_PARTNER_SUCCESS;
    constructor(public response?: any) { }
}
export class UpdateChannelPartnerFiltersPayload implements Action {
    readonly type: string =
        MarketingAction.UPDATE_CHANNEL_PARTNER_FILTERS_PAYLOAD;
    constructor(public payload: any) { }
}
export class ChannelPartnerExcelUpload implements Action {
    readonly type: string = MarketingAction.CHANNEL_PARTNER_EXCEL_UPLOAD;
    constructor(public file: File) { }
}

export class ChannelPartnerExcelUploadSuccess implements Action {
    readonly type: string = MarketingAction.CHANNEL_PARTNER_EXCEL_UPLOAD_SUCCESS;
    constructor(public resp: LeadExcel) { }
}

export class FetchChannelPartnerExcelUploadedList implements Action {
    readonly type: string = MarketingAction.FETCH_CP_EXCEL_UPLOADED_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchChannelPartnerExcelUploadedSuccess implements Action {
    readonly type: string = MarketingAction.FETCH_CP_EXCEL_UPLOADED_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class UploadChannelPartnerMappedColumns implements Action {
    readonly type: string = MarketingAction.UPLOAD_CHANNEL_PARTNER_MAPPED_COLUMNS;
    constructor(public payload: MapColumnsExcel) { }
}

export class ExportChannelPartner implements Action {
    readonly type: string = MarketingAction.EXPORT_CHANNEL_PARTNER;
    constructor(public payload: any) { }
}

export class ExportChannelPartnerSuccess implements Action {
    readonly type: string = MarketingAction.EXPORT_CHANNEL_PARTNER_SUCCESS;
    constructor(public resp: string = '') { }
}

export class FetchChannelPartnerExportTracker implements Action {
    readonly type: string = MarketingAction.EXPORT_CHANNEL_PARTNER_TRACKER;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchChannelPartnerExportTrackerSuccess implements Action {
    readonly type: string = MarketingAction.EXPORT_CHANNEL_PARTNER_TRACKER_SUCCESS;
    constructor(public response: any[] = []) { }
}