import { Action } from '@ngrx/store';

export enum TemplateActionTypes {
  FETCH_TEMPLATE_MODULE = '[TEMPLATE] Fetch Template Module',
  FETCH_TEMPLATE_MODULE_SUCCESS = '[TEMPLATE] Fetch Template Module Success',
  ADD_TEMPLATE = '[TEMPLATE] Add Template',
  ADD_TEMPLATE_SUCCESS = '[TEMPLATE] Add Template Success',
  DELETE_TEMPLATE = '[TEMPLATE] Delete Template',
  UPDATE_TEMPLATE = '[TEMPLATE] Update Template',
  UPDATE_TEMPLATE_SUCCESS = '[TEMPLATE] Update Template Success',
  ASSIGN_PAGE_SIZE_NUMBER = '[TEMPLATE] Assign Page Size and Page Number'
}

export class FetchTemplateModule implements Action {
  readonly type: string = TemplateActionTypes.FETCH_TEMPLATE_MODULE;
  constructor(public payload: any = {}, public pageNumber?:any, public pageSize?:any,public searchTerm?:any) { }
}
export class FetchTemplateModuleSuccess implements Action {
  readonly type: string = TemplateActionTypes.FETCH_TEMPLATE_MODULE_SUCCESS;
  constructor(public resp: any = '') { }
}
export class DeleteTemplate implements Action {
  readonly type: string = TemplateActionTypes.DELETE_TEMPLATE;
  constructor(public payload: any) { }
}
export class AddTemplate implements Action {
  readonly type: string = TemplateActionTypes.ADD_TEMPLATE;
  constructor(public payload: any) { }
}
export class AddTemplateSuccess implements Action {
  readonly type: string = TemplateActionTypes.ADD_TEMPLATE_SUCCESS;
  constructor(public resp: any = '') { }
}
export class UpdateTemplate implements Action {
  readonly type: string = TemplateActionTypes.UPDATE_TEMPLATE;
  constructor(public payload: any) { }
}
export class UpdateTemplateSuccess implements Action {
  readonly type: string = TemplateActionTypes.UPDATE_TEMPLATE_SUCCESS;
  constructor(public resp: any = '') { }
}
export class AssignPageSize implements Action {
  readonly type: string = TemplateActionTypes.ASSIGN_PAGE_SIZE_NUMBER;
  constructor(public pageSize:number | null,public pageNumber:number | null) {}
}