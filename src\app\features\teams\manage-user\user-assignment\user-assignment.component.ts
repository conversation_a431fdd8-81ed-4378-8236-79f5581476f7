import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { ToggleAutomation } from 'src/app/reducers/teams/teams.actions';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { ManageUserComponent } from '../manage-user.component';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'user-assignment',
  templateUrl: './user-assignment.component.html',
})
export class UserAssignmentComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  status: boolean;
  params: any;
  canEditUser: boolean = false;

  constructor(
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private store: Store<AppState>,
    private manageUserComponent: ManageUserComponent,
    public trackingService: TrackingService
  ) {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Users')) {
          this.canEditUser = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
    this.status = this.params.data.isAutomationEnabled;
  }


  openConfirmModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'change the assignment of',
      title: data.firstName + ' ' + data.lastName,
      fieldType: 'user',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        this.trackingService.trackFeature(`Web.TeamUser.Button.LeadAssignment.Click`)
        if (reason == 'confirmed') {
          this.manageUserComponent.usersListIsLoading = true;
          this.store.dispatch(new ToggleAutomation([this.params.data.userId]));
        } else {
          this.status = this.params.data.isAutomationEnabled;
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
