import { Action } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { NotificationCallSuccess, NotificationInfoActionTypes } from './notification-info.action';


export type NotificationInfoState = {
  notificationInfoData: any;
};

const initialState: NotificationInfoState = {
  notificationInfoData: {},
};

export function notificationInfoReducer(
  state: NotificationInfoState = initialState,
  action: Action
): NotificationInfoState {
  switch (action.type) {
    
    case NotificationInfoActionTypes.NOTIFICATION_SUCCESS:
      return {
        ...state,
        notificationInfoData: (action as NotificationCallSuccess),
      };
  
    default:
      return state;
  }
}
export const selectFeature = (state: AppState) => state.notificationInfo;
