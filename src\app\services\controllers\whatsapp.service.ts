import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class WhatsappService extends BaseService<any> {
  serviceBaseUrl: string;

  getResourceUrl(): string {
    return 'wa';
  }

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getWAList() {
    return this.http.get(`${this.serviceBaseUrl}/template`);
  }

  updateWhatsappTemplate(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}`, payload);
  }

  get24HrValidation(payload: any) {
    let params = new HttpParams();
    for (let key in payload) {
      if (payload.hasOwnProperty(key)) {
        params = params.append(key, payload[key]);
      }
    }
    return this.http.get(`${this.serviceBaseUrl}/24-hour-validation?${params.toString()}`);
  }

  getConversation(payload: any) {
    let params = new HttpParams();
    for (let key in payload) {
      if (payload.hasOwnProperty(key)) {
        params = params.append(key, payload[key]);
      }
    }
    return this.http.get(`${this.serviceBaseUrl}/message?${params.toString()}`);
  }

  getWAChildList(id: any) {
    return this.http.get(`${this.serviceBaseUrl}/child/leads-with-message?ParentLeadIds=${id}&PageNumber=1&PageSize=10`);
  }
}
