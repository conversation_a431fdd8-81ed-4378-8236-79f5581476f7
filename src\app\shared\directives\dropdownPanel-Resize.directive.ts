import { Directive, HostListener, ElementRef, Renderer2 } from '@angular/core';

@Directive({
  selector: '[ResizableDropdown]'
})
export class ResizableDropdownDirective {
  private isDragging: boolean = false;
  private startX: number;
  private startWidth: number;
  private parentWidth: number;

  constructor(private elementRef: ElementRef, private renderer: Renderer2) { }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent) {
    // Check if mousedown event target is the ng-dropdown-panel
    const dropdownPanel = this.elementRef.nativeElement.querySelector('.ng-dropdown-panel');
    if (dropdownPanel && dropdownPanel.contains(event.target)) {
      this.isDragging = true;
      this.startX = event.clientX;
      this.startWidth = dropdownPanel.offsetWidth;
      this.parentWidth = this.elementRef.nativeElement.offsetWidth;

      // Set cursor style to col-resize
      this.renderer.setStyle(document.body, 'cursor', 'w-resize');
    }
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    if (!this.isDragging) return;

    const deltaX = event.clientX - this.startX;
    let newWidth = this.startWidth + deltaX;
    // Calculate the maximum allowed width with an additional margin of -30px
    const parentRight = this.elementRef.nativeElement.getBoundingClientRect().right;
    const remainingSpace = window.innerWidth - parentRight - 30; // Additional margin
    const maxWidth = this.parentWidth + remainingSpace;

    // Limit the new width to be within the range of the parent width and the remaining space to the right
    newWidth = Math.max(this.parentWidth, Math.min(newWidth, maxWidth));

    // Adjusting the width of the ng-dropdown-panel only
    const dropdownPanel = this.elementRef.nativeElement.querySelector('.ng-dropdown-panel');
    if (dropdownPanel) {
      this.renderer.setStyle(dropdownPanel, 'width', `${newWidth}px`);
    }
  }

  @HostListener('document:mouseup')
  onMouseUp() {
    if (this.isDragging) {
      this.isDragging = false;

      // Revert cursor style to auto
      this.renderer.setStyle(document.body, 'cursor', 'auto');
    }
  }
}
