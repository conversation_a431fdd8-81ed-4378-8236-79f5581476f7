<div>
  <form [formGroup]="testimonialForm">
    <h3 class="bg-coal px-24 py-12 text-white fw-700">{{ (testimonial.id?
      'PROFILE.edit-testimonial' : 'PROFILE.add-testimonial') | translate }}</h3>
    <div class="px-16">
      <div class="justify-center align-items-center mt-30">
        <div class="position-relative">
          <img
            [appImage]="testimonialPic ? s3BucketUrl+testimonialPic : (testimonial?.imageURL ? s3BucketUrl+testimonial?.imageURL : '')"
            [type]="'defaultAvatar'" class="br-50 obj-cover" width="110" height="110" id="clkChangeProfile"
            data-automate-id="clkChangeProfile">
          <label class="icon-badge bg-dark-green w-24 h-24 br-50 position-absolute bottom-0 right-15 border-white-2">
            <span class="icon ic-camera ic-xxs" id="clkCameraProfile" data-automate-id="clkCameraProfile"></span>
            <input type="file" (change)="onFileSelected($event)" formControlName="imageUrl"
              [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'">
          </label>
          <a class="icon-badge bg-light-red w-24 h-24 br-50 position-absolute bottom-25 nright-5 border-white-2"
            (click)="deleteProfilePic()">
            <span class="icon ic-delete ic-xxs" id="clkDeleteProfile" data-automate-id="clkDeleteProfile"></span>
          </a>
        </div>
      </div>
      <div>
        <div class="field-label-req">{{ 'PROFILE.name' | translate }}</div>
        <form-errors-wrapper [control]="testimonialForm.controls['name']" label="Name">
          <input type="text" id="inpName" data-automate-id="inpName" placeholder="ex. Mounika Pampana"
            formControlName="name">
        </form-errors-wrapper>
      </div>
      <div>
        <div class="field-label">{{ 'AUTH.company-name' | translate }}</div>
        <form-errors-wrapper>
          <input type="text" id="inpCompany" data-automate-id="inpCompany" placeholder="ex. ABC Company"
            formControlName="company">
        </form-errors-wrapper>
      </div>
      <div>
        <div class="field-label">{{ 'USER_MANAGEMENT.note' | translate }}</div>
        <form-errors-wrapper>
          <textarea id="inpNote" data-automate-id="inpNote" placeholder="ex. The standard and quality is very good"
            rows="4" formControlName="note" maxlength="300" class="scrollbar"></textarea>
        </form-errors-wrapper>
        <p class="justify-end mt-4 fw-semi-bold">
          {{ testimonialForm.controls['note']?.value?.length }}/300</p>
      </div>
    </div>
    <div class="flex-center pt-20">
      <button class="btn-coal ml-20" id="btnAddRole" data-automate-id="btnAddRole" (click)="onSave()">
        {{ (testimonial.id ? 'BUTTONS.update' : 'BUTTONS.create') | translate }}</button>
    </div>
  </form>
</div>