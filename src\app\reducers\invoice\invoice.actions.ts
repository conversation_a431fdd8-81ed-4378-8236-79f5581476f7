import { Action } from '@ngrx/store';

export enum InvoiceActionTypes {
  FETCH_INVOICE = '[INVOICE] Fetch Invoice List',
  FETCH_INVOICE_SUCCESS = '[INVOICE] Fetch Invoice List Success',
  ADD_INVOICE = '[INVOICE] Add Invoice',
  ADD_INVOICE_SUCCESS = '[INVOICE] Add Invoice Success',
  FETCH_INVOICES_BY_ID = '[INVOICE] Fetch Invoice By Id',
  FETCH_INVOICE_BY_ID_SUCCESS = '[INVOICE] Fetch Invoice By Id Success',
}

export class AddInvoice implements Action {
  readonly type: string = InvoiceActionTypes.ADD_INVOICE;
  constructor(
    public payload: any,
    public leadId: string,
    public isGoToInvoice: boolean = false
  ) { }
}
export class AddInvoiceSuccess implements Action {
  readonly type: string = InvoiceActionTypes.ADD_INVOICE_SUCCESS;
  constructor(public response: any) { }
}

export class FetchInvoiceById implements Action {
  readonly type: string = InvoiceActionTypes.FETCH_INVOICES_BY_ID;
  constructor(public id: string) { }
}
export class FetchInvoiceByIdSuccess implements Action {
  readonly type: string = InvoiceActionTypes.FETCH_INVOICE_BY_ID_SUCCESS;
  constructor(public response: any) { }
}
