<div class="h-100vh text-coal">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
        <h3 class="fw-semi-bold">WhatsApp Chat</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="bg-light-pearl p-20">
        <form [formGroup]="whatsAppForm">
            <div class="bg-white br-10">
                <div class="h-100-150 scrollbar p-10">
                    <div class="flex-between">
                        <div class="field-label mt-0">Send To</div>
                        <div class="text-sm text-accent-green">Total
                            <span class="fw-600">{{data?.length}}</span> lead(s) selected
                        </div>
                    </div>
                    <div class="border br-4 p-10 align-center flex-wrap  max-h-100-433 scrollbar">
                        <div class="border br-25 px-10 py-6 fw-semi-bold flex-center mr-10 mb-10 bg-light-pearl break-all"
                            *ngFor="let lead of selectedLeads">
                            {{lead?.name}} <div class="icon ic-close ic-coal ic-xxs cursor-pointer ml-4"
                                (click)="openConfirmDeleteModal(lead.name, lead.id)"></div>
                        </div>
                    </div>
                    <div class="field-label-req">Choose WhatsApp Template</div>
                    <form-errors-wrapper [control]="whatsAppForm.controls['template']" label="Template">
                        <ng-select [virtualScroll]="true" [items]="whatsAppTemplateList" bindLabel="name"
                            formControlName="template"
                            placeholder="{{ 'GLOBAL.select' | translate }} {{'BULK_LEAD.template' | translate}}"></ng-select>
                    </form-errors-wrapper>
                    <div class="field-label">Content</div>
                    <form-errors-wrapper [control]="whatsAppForm.controls['content']" label="Content">
                        <textarea rows="6" placeholder="select template to start conversation...." class="scrollbar"
                            formControlName="content" readonly></textarea>
                    </form-errors-wrapper>
                </div>
                <div class="flex-end border-top p-10 mt-16">
                    <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">{{'BUTTONS.cancel' |
                        translate }}</u>
                    <button class="btn-coal w-130" (click)="sendMessage()">
                        Send Message <div class="icon ic-xxs ic-navigate ml-10 rotate-45"></div>
                    </button>
                </div>
            </div>
        </form>
    </div>

</div>