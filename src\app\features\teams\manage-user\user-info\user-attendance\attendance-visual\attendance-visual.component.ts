import { Component, EventEmitter } from '@angular/core';
import { Store } from '@ngrx/store';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import * as moment from 'moment';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'attendance-visual',
  templateUrl: './attendance-visual.component.html',
})
export class AttendanceVisualComponent implements ICellRendererAngularComp {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  params: any;
  blocks: number[] = Array(24 * 6).fill(0).map((x, i) => i + 1);
  minutesClockedIn: number[] = [];
  startIndex: number = 145;
  endIndex: number = null;
  moment: any = moment;
  userData: any;
  getTimeZoneDate = getTimeZoneDate;
  constructor( private _store: Store<AppState>) { }


  agInit(params: any): void {
    this.params = params;
    if (params?.data?.logDtos?.length) {
      this.minutesClockedIn = this.getClockStatus([...params?.data?.logDtos].reverse());
    }
    this._store
    .select(getUserBasicDetails)
    .pipe(takeUntil(this.stopper))
    .subscribe((data: any) => {
      this.userData = data;
    });
  }

  getClockStatus(entries: Record<string, any>[]) {
    const tenMinutesStatus = Array(24 * 6).fill(0);
    entries.forEach((entry: Record<string, any>, index: number) => {
      const clockInTime = new Date(entry.clockInTime);
      const clockOutTime = new Date(entry.clockOutTime);
      let clockInMinutes = Math.round(clockInTime.getMinutes() / 10) * 10;
      let clockOutMinutes = Math.round(clockOutTime.getMinutes() / 10) * 10;
      let clockInHours = clockInTime.getHours();
      let clockOutHours = clockOutTime.getHours();
      if (clockInMinutes === 60) {
        clockInMinutes = 0;
        clockInHours += 1;
      }
      if (clockOutMinutes === 60) {
        clockOutMinutes = 0;
        clockOutHours += 1;
      }
      const startIndex = clockInHours * 6 + clockInMinutes / 10;
      const endIndex = entry.clockOutTime ? (clockOutHours * 6 + clockOutMinutes / 10) : null;
      this.startIndex = Math.min(startIndex, this.startIndex);
      this.endIndex = endIndex ? Math.max(endIndex, this.endIndex) : null;
      if (!endIndex) {
        tenMinutesStatus[startIndex] = 2;
      }
      for (let i = startIndex; i <= endIndex; i++) {
        tenMinutesStatus[i] = 1;
      }
    });

    return tenMinutesStatus;
  }


  refresh(): boolean {
    return false;
  }
}
