import { Component } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { SUPP0RT_NO, WHATSAPP_SHARE_API } from 'src/app/app.constants';

@Component({
  selector: 'support',
  templateUrl: './support.component.html',
})
export class SupportComponent {
  supportNumber: string = SUPP0RT_NO;

  constructor(public modalRef: BsModalRef
  ) { }


  
  getWhatsappLink(): string {
    return window.innerWidth <= 600 ? `${WHATSAPP_SHARE_API}?phone=+918050385050` : `${WHATSAPP_SHARE_API}?phone=918050385050`;
  }
}