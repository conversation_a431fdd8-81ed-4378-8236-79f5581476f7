import {
  AfterViewInit,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { Profile } from 'src/app/core/interfaces/profile.interface';
import { BasicDetailsComponent } from 'src/app/features/profile/basic-details/basic-details.component';
import { AddTestimonialComponent } from 'src/app/features/profile/testimonials/add-testimonial/add-testimonial.component';
import {
  getAddPermissions,
  getViewPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProfile } from 'src/app/reducers/profile/profile.actions';
import { getProfile } from 'src/app/reducers/profile/profile.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
@Component({
  selector: 'profile-dashboard',
  templateUrl: './profile-dashboard.component.html',
})
export class ProfileDashboardComponent
  implements OnInit, AfterViewInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  profile: Profile;
  canEditProfile: boolean = false;
  public navigations: Array<object> = [
    { title: 'General Info', link: 'basic-details' },
    { title: 'About us', link: 'about-us' },
    { title: 'Subscription', link: 'subscription' },
    { title: 'Testimonials', link: 'testimonials' },
    { title: 'Awards', link: 'awards' },
  ];
  canAdd: boolean = false;
  canView: boolean = false;

  constructor(
    private modalRef: BsModalRef,
    public modalService: BsModalService,
    private headerTitle: HeaderTitleService,
    public router: Router,
    private _store: Store<AppState>,
    private basicInfoComponent: BasicDetailsComponent
  ) {
    this._store
      .select(getAddPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canAdd: any) => {
        if (canAdd?.includes('OrgProfile')) this.canAdd = true;
      });

    this._store
      .select(getViewPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canView: any) => {
        if (canView?.includes('OrgProfile')) this.canView = true;
      });
  }

  ngAfterViewInit() {
    this.basicInfoComponent.onSave();
  }

  ngOnInit() {
    this.headerTitle.setLangTitle('PROFILE.organization-profile');
    this._store.dispatch(new FetchProfile());
    this._store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.shouldHideSubscription ?? false) {
          this.navigations = this.navigations.filter(
            (nav: any) => nav?.title !== 'Subscription'
          );
        }
      });
  }

  addTestimonial() {
    this.modalService.show(AddTestimonialComponent, {
      class: 'right-modal modal-350',
    });
  }

  saveData() {
    this.basicInfoComponent.onSave();
    this.ngAfterViewInit();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
