import { Action } from '@ngrx/store';
import {
  FetchTasksResponse,
  TasksFilter,
  TodoPayload,
} from 'src/app/core/interfaces/todo.interface';

export enum TaskActionTypes {
  FETCH_TODO_LIST = '[TODO] Fetch Todo List',
  FETCH_TODO_LIST_SUCCESS = '[TODO] Fetch Todo List Success',
  ADD_TODO = '[TODO] Add Todo',
  DELETE_TODO = '[TODO] Delete Todo',
  UPDATE_TODO = '[TODO] Update Todo',
  UPDATE_TODO_FILTER = '[TODO] Update Todo Filter',
}
export class FetchTodoList implements Action {
  readonly type: string = TaskActionTypes.FETCH_TODO_LIST;
  constructor(
    public payload: TasksFilter = {
      pageNumber: 1,
      pageSize: 10,
      path: 'todo',
    }
  ) {}
}
export class FetchTodoListSuccess implements Action {
  readonly type: string = TaskActionTypes.FETCH_TODO_LIST_SUCCESS;
  constructor(public response: FetchTasksResponse = {}) {}
}
export class AddTodo implements Action {
  readonly type: string = TaskActionTypes.ADD_TODO;
  constructor(public payload: TodoPayload) {}
}
export class DeleteTodo implements Action {
  readonly type: string = TaskActionTypes.DELETE_TODO;
  constructor(public id: string) {}
}
export class UpdateTodo implements Action {
  readonly type: string = TaskActionTypes.UPDATE_TODO;
  constructor(public id: string, public payload: TodoPayload) {}
}

export class UpdateTodoFilter implements Action {
  readonly type: string = TaskActionTypes.UPDATE_TODO_FILTER;
  constructor(public payload: TasksFilter) {}
}
