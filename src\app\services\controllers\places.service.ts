import { HttpClient, HttpParams, HttpBackend } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NO_BLOCK_PARAM } from 'src/app/app.constants';
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PlacesService {
  baseURL: string = `${env.baseURL}api`;
  resourecURL: string = '/location';
  private httpClient: HttpClient;

  constructor(private handler: HttpBackend) {
    this.httpClient = new HttpClient(handler);
  }

  getPlacesList(text: string, page: number, pagesize: number) {
    let params = new HttpParams()
      .set('PageNumber', page.toString())
      .set('PageSize', pagesize.toString())
      .set('inputText', text.toString());
    if (localStorage.getItem('idToken') == null || localStorage.getItem('idToken') == undefined) {
      this.baseURL = 'env.authURL';
    }
    return this.httpClient.get(
      `${this.baseURL}${this.resourecURL}?${params.toString()}`,
      {
        params: NO_BLOCK_PARAM['params'],
      }
    );
  }

  getQRPlacesList(text: string, page: number, pagesize: number) {
    let params = new HttpParams()
      .set('PageNumber', page.toString())
      .set('PageSize', pagesize.toString())
      .set('inputText', text.toString());
    return this.httpClient.get(
      `${this.baseURL}${this.resourecURL}/qr?${params.toString()}`,
      {
        params: NO_BLOCK_PARAM['params'],
      }
    );
  }
}
