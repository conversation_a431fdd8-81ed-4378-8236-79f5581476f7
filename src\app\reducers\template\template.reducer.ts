import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  AssignPageSize,
  FetchTemplateModuleSuccess,
  TemplateActionTypes,
} from 'src/app/reducers/template/template.actions';

export type TemplateState = {
  templates?: any;
  isTemplatesLoading: boolean;
  isAddtemplateLoading: boolean;
  isUpdatetemplateLoading: boolean;
  totalCount?: number;
  pageSize: number | null;
  pageNumber: number | null;
};
const initialState: TemplateState = {
  templates: [],
  isTemplatesLoading: true,
  isAddtemplateLoading: false,
  isUpdatetemplateLoading: false,
  totalCount: 0,
  pageSize: null,
  pageNumber: null,
};
export function templateReducer(
  state: TemplateState = initialState,
  action: Action
): TemplateState {
  switch (action.type) {
    case TemplateActionTypes.FETCH_TEMPLATE_MODULE:
      return {
        ...state,
        isTemplatesLoading: true,
      };
    case TemplateActionTypes.FETCH_TEMPLATE_MODULE_SUCCESS:
      return {
        ...state,
        templates: (action as FetchTemplateModuleSuccess).resp.items,
        isTemplatesLoading: false,
        totalCount: (action as FetchTemplateModuleSuccess).resp.totalCount
      };
    case TemplateActionTypes.ADD_TEMPLATE:
      return {
        ...state,
        isAddtemplateLoading: true,
      };
    case TemplateActionTypes.ADD_TEMPLATE_SUCCESS:
      return {
        ...state,
        isAddtemplateLoading: false,
      };
    case TemplateActionTypes.UPDATE_TEMPLATE:
      return {
        ...state,
        isUpdatetemplateLoading: true,
      };
    case TemplateActionTypes.UPDATE_TEMPLATE_SUCCESS:
      return {
        ...state,
        isUpdatetemplateLoading: false,
      };
    case TemplateActionTypes.ASSIGN_PAGE_SIZE_NUMBER:
      return {
        ...state,
        pageSize: (action as AssignPageSize).pageSize,
        pageNumber: (action as AssignPageSize).pageNumber,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.template;

export const getTemplatesModule = createSelector(
  selectFeature,
  (state: TemplateState) => {
    return state;
  }
);

export const getTemplatesModuleIsLoading = createSelector(
  selectFeature,
  (state: TemplateState) => {
    return state.isTemplatesLoading;
  }
);

export const getAddTemplatesIsLoading = createSelector(
  selectFeature,
  (state: TemplateState) => {
    return state.isAddtemplateLoading;
  }
);

export const getUpdateTemplatesIsLoading = createSelector(
  selectFeature,
  (state: TemplateState) => {
    return state.isUpdatetemplateLoading;
  }
);

