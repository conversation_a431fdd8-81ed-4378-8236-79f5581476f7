import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { Recognition } from 'src/app/core/interfaces/profile.interface';
import {
  getAddPermissions,
  getDeletePermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import {
  AddRecognition,
  DeleteRecognition,
  FetchRecognition,
} from 'src/app/reducers/profile/profile.actions';
import { getProfileIsLoading, getRecognition } from 'src/app/reducers/profile/profile.reducers';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'awards',
  templateUrl: './awards.component.html',
})
export class AwardsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  isFileTypeSupported: boolean = true;
  fileFormatToBeUploaded: string =
    'application/pdf,image/x-png,image/gif,image/jpeg,image/tiff';
  awardName: FormControl = new FormControl('', Validators.required);
  selectedFile: any;
  recognitionsList: Recognition[];
  s3BucketPath: string = environment.s3ImageBucketURL;
  validationPerformed = false;
  canAdd: boolean = false;
  canDelete: boolean = false;
  recognition: any;
  isAwardsLoading: boolean;

  constructor(
    private _store: Store<AppState>,
    private s3UploadService: BlobStorageService,
    public modalService: BsModalService,
    private modalRef: BsModalRef
  ) {
    this._store
      .select(getAddPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canAdd: any) => {
        if (canAdd?.includes('OrgProfile')) this.canAdd = true;
      });

    this._store
      .select(getDeletePermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canDelete: any) => {
        if (canDelete?.includes('OrgProfile')) this.canDelete = true;
      });

    this._store.dispatch(new FetchRecognition());
  }

  ngOnInit() {
    this._store
      .select(getRecognition)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        let awards = [...data];
        this.recognitionsList = awards?.reverse();
      });
      this._store
      .select(getProfileIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isAwardsLoading = data;
      });
  }

  isPdf(filePath: string) {
    if (filePath && filePath.substring(filePath.length - 3) === 'pdf')
      return true;
    return false;
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
  }

  fileUploadToS3() {
    if (this.selectedFile?.[0].includes('data:') && this.awardName.valid) {
      this.s3UploadService
        .uploadImageBase64(this.selectedFile, FolderNamesS3.Recognition)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          if (response.data.length) {
            this.onSave(response.data?.[0]);
          }
        });
    } else {
      this.onSave();
    }
  }

  onSave(filePath?: string) {
    this.validationPerformed = true;
    if (this.awardName.invalid || !this.selectedFile) {
      this.awardName.markAsTouched({ onlySelf: true });
      return;
    }
    let payload: Recognition = {
      name: this.awardName.value,
      imageUrl: filePath,
    };
    this._store.dispatch(new AddRecognition(payload));
    this.awardName.reset();
    this.selectedFile = null;
    this.validationPerformed = false;
  }

  initDeleteRecognition(id: string, awardName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: awardName,
      fieldType: 'award',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteRecognition(id);
        }
      });
    }
  }

  deleteRecognition(id: string) {
    this._store.dispatch(new DeleteRecognition(id));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
