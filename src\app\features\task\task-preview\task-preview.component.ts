import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { TodoFilterType, TodoPriority } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { UpdateTodo } from 'src/app/reducers/todo/todo.actions';

@Component({
  selector: 'task-preview',
  templateUrl: './task-preview.component.html',
})
export class TaskPreviewComponent implements  OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  data: any;
  users: any;
  moment = moment;
  getAssignedToDetails = getAssignedToDetails;
  TodoFilterType = TodoFilterType;
  userData: any;
  getTimeZoneDate = getTimeZoneDate;
  constructor(
    private store: Store<AppState>,
    public modalService: BsModalService,
    public modalRef: BsModalRef
  ) {
    this.store.dispatch(new FetchUsersListForReassignment());

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.users = data;
      });

      this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }


  markAsDone(todo: any) {
    if (todo.isMarkedDone) {
      return;
    }
    const payload = {
      ...todo,
      assignedFrom: todo.assignedFrom?.id,
      assignedToUserId: todo.assignedToUser?.id,
      isMarkedDone: true,
      priority: TodoPriority[todo.priority],
    };
    this.store.dispatch(new UpdateTodo(payload.id, payload));
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
