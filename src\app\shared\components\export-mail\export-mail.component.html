<div class="p-20">
    <a class="ic-close-secondary ic-close-modal ip-ic-close-modal" (click)="closePopup()"></a>
    <h4 class="mt-20 text-center fw-600">Are you sure you want to export these {{ getExportLabel(payload?.path) }}?</h4>
    <div class="text-gray fw-600 mt-20 text-center">
        <div>Clicking on <span class="header-5 text-coal">"Confirm"</span> will enable the process of exporting the
            {{ getExportLabel(payload?.path) }} in an excel
            format. You can track the status of the export in <span class="header-5 text-coal">"Export Tracker"</span>
        </div>
    </div>
    <div class="d-flex w-100 flex-nowrap remove-margin-radio">
        <div class="d-flex"
            *ngIf="payload.path == 'lead/new/all' || payload.path == 'prospect/custom-filters' || payload.path == 'prospect'  ">
            <div *ngFor="let notesTypeOption of notesTypeOptions" class="form-check form-check-inline">
                <div class="align-center mt-8">
                    <input type="radio" [id]="notesTypeOption?.value + 'notesTypeOptionRadio'"
                        class="radio-check-input border-remove" [value]="notesTypeOption.value"
                        [formControl]="notesType" name="notesType">
                    <label class="fw-600 text-dark-800 cursor-pointer text-sm ml-8"
                        [for]="notesTypeOption?.value + 'notesTypeOptionRadio'"> {{ notesTypeOption.label }} </label>
                </div>
            </div>
        </div>
        <div class="d-flex" *ngIf="payload.path == 'lead/new/all'">
            <div *ngFor="let facebookOption of facebookOptions" class="form-check form-check-inline">
                <div class="align-center mt-8">
                    <input type="checkbox" [id]="facebookOption?.value + 'facebookOptionRadio'"
                        class="radio-check-input border-remove" [value]="facebookOption.value"
                        [formControl]="facebookOptionType" name="facebookOptionType">
                    <label class="fw-600 text-dark-800 cursor-pointer text-sm ml-8"
                        [for]="facebookOption?.value + 'facebookOptionRadio'"> {{ facebookOption.label }} </label>
                </div>
            </div>
        </div>
    </div>
    <div class="w-100"
        *ngIf="(payload.path == 'lead/new/all' || payload.path == 'prospect/custom-filters' || payload.path == 'prospect') && notesType.value === true">
        <div class="align-end">
            <div class="field-label-req">Notes Count</div>
            <div class="icon ic-circle-exclamation ic-slate-110 cursor-pointer ic-sm ml-20 mb-6"
                title="Between 1 to 100 most recently updated notes can only be exported">
            </div>
        </div>
        <form-errors-wrapper [control]="notesCount" label="Notes Count">
            <input type="number" placeholder="ex 3" min="1" max="100" [formControl]="notesCount">
        </form-errors-wrapper>
    </div>
    <div class="flex-center mt-30">
        <button class="btn-gray" (click)="closePopup()">{{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-coal ml-10" (click)="exportData()"
            [disabled]="notesCount.invalid && notesType.value === true && (payload.path == 'lead/new/all' ||  payload.path == 'prospect/custom-filters' || payload.path == 'prospect')">{{
            'BUTTONS.confirm' | translate }}</button>
    </div>
</div>