<ng-container *ngIf="!whatsAppComp && data?.alternateContactNo else WhatsApp">
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-10">choose which number you want to WhatsApp </h3>
        <div class="align-center">
            <div class="form-check form-check-inline" (click)="selectedWhatsAppNo = 'primary'">
                <input type="radio" id="inpWAPrimary" data-automate-id="inpWAPrimary" class="radio-check-input"
                    name="whatsAppType" checked>
                <label class="fw-600 text-secondary cursor-pointer text-large" for="inpWAPrimary">
                    Primary Number ({{data?.contactNo}})
                </label>
            </div>
        </div>
        <div class="align-center">
            <div class="form-check form-check-inline" (click)="selectedWhatsAppNo = 'alternate'">
                <input type="radio" id="inpAlternateWA" data-automate-id="inpAlternateWA" class="radio-check-input"
                    name="whatsAppType">
                <label class="fw-600 text-secondary cursor-pointer text-large" for="inpAlternateWA">
                    {{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}} ({{data?.alternateContactNo}})
                </label>
            </div>
        </div>
        <div class="flex-end mt-30">
            <button class="btn-gray" (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-green ml-20" (click)="chooseWhatsAppNo(WhatsApp)">WhatsApp</button>
        </div>
    </div>
</ng-container>

<ng-template #WhatsApp>
    <div class="text-coal min-w-350" [ngClass]="whatsAppComp ? 'h-100-144' : 'h-100vh'">
        <ng-container *ngIf="!whatsAppComp">
            <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
                <h3 class="fw-semi-bold">WhatsApp Chat</h3>
                <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
            </div>
        </ng-container>
        <div class="bg-light-pearl" [ngClass]="{'pt-20' : !whatsAppComp}">
            <div class="p-20 bg-white flex-between" [ngClass]="{'mx-20 br-10' : !whatsAppComp}">
                <div class="d-flex">
                    <div>
                        <h4 class="fw-600 text-truncate-1 break-all">{{data?.name}}</h4>
                        <h5 class="text-light-gray" *ngIf="!globalSettingsData?.isMaskedLeadContactNo">
                            {{selectedWhatsAppNo == 'primary' ? data.contactNo : data.alternateContactNo }}
                        </h5>
                    </div>
                    <div *ngIf="whatsAppComp">
                        <span type="button" data-bs-toggle="tooltip" data-bs-html="true" title="Lead Details"
                            class="ml-4 d-none tb-d-block ip-d-none" (click)="openLeadPreviewModal()">
                            <img src="../../../../../assets/images/i-btn.svg" alt="img" width="18" /></span>
                    </div>
                </div>
                <div [ngClass]="{'blinking pe-none': isConversationLoading}" class="flex-center cursor-pointer ml-10"
                    (click)="fetchConversation()">
                    <span class="icon ic-update ic-xxxs ic-coal mr-4 mt-2"></span>
                    <u>refresh</u>
                </div>
                <!-- <div class="icon ic-search-solid ic-black ic-lg cursor-pointer"></div> -->
            </div>
            <!-- conversation -->
            <div class="px-20 mt-10 mb-8 flex-col scrollbar" [ngClass]="whatsAppComp ? 'h-100-388' : 'h-100-298'"
                id="chatContainer" (click)="chatScrolled=true" *ngIf="conversationList?.length else noConversationFound"
                #scrollToBottom (scroll)="onScroll()">
                <!-- <div class="flex-center my-10 fw-300 text-sm">May 09, 2025</div> -->
                <div class="conversation">
                    <div class="conversation-container" *ngFor="let msg of conversationList">
                        <ng-container *ngIf="msg.message == 'None'">
                            <div class="message w-200  h-100 bg-unset"
                                [ngClass]="msg.waEvent == WhatsAppEvents.Receive ? 'received brtl-4' : 'sent brtr-4'"
                                *ngIf="msg.mediaType == 'Image' || msg.mediaType == 'IMAGE' || msg.mediaType == 'image'">
                                <img alt="media" [type]="'leadrat'" [appImage]="msg.mediaUrl" width="200" class="h-100 obj-cover br-4">
                            </div>
                            <div class="message w-200  h-100 bg-unset"
                                [ngClass]="msg.waEvent == WhatsAppEvents.Receive ? 'received brtl-4' : 'sent brtr-4'"
                                *ngIf="msg.mediaType == 'Video' || msg.mediaType == 'VIDEO' || msg.mediaType == 'video'">
                                <video controls alt="video" width="200" class="h-100 obj-cover br-4">
                                    <source [src]="msg.mediaUrl" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            </div>
                            <div class="message pt-10"
                                [ngClass]="msg.waEvent == WhatsAppEvents.Receive ? 'received' : 'sent'"
                                *ngIf="msg.mediaType == 'Document' || msg.mediaType == 'DOCUMENT' || msg.mediaType == 'document'">
                                <a [href]="msg.mediaUrl" target="_blank" class="mx-10">
                                    <img alt="media" src="../../../../assets/images/img-file.svg">
                                </a>

                                <div class="p-10 d-flex bg-white brbr-4 brbl-4" *ngIf="msg?.MediaName">
                                    <div class="text-truncate-1 break-all fw-semi-bold">{{msg?.MediaName}}</div>
                                </div>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="msg.message != 'None'">
                            <div class="message"
                                [ngClass]="msg.waEvent == WhatsAppEvents.Receive ? 'received' : 'sent'">
                                <div class="flex-center obj-cover" *ngIf="msg.mediaType == 'Image'">
                                    <a [href]="msg.mediaUrl" target="_blank" class="w-100 h-100 bg-white">
                                        <img [appImage]="msg.mediaUrl" [type]="'defaultImg'" alt="image"
                                            class="w-100 h-100 bg-white"></a>
                                    <!-- <div class="w-100 bg-blur position-absolute flex-center">
                                        <img [type]="'leadrat'" [appImage]="msg.mediaUrl" alt="image" class="max-w-100 h-100px obj-cover">
                                    </div> -->
                                </div>
                                <div *ngIf="msg.mediaType == 'Video'">
                                    <video controls alt="video" class="w-100 h-100">
                                        <source [src]="msg.mediaUrl" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                </div>
                                <div *ngIf="msg.mediaType == 'Document'">
                                    <a [href]="msg.mediaUrl" target="_blank"
                                        [ngClass]="msg.message ? 'flex-center': ''">
                                        <img alt="media" src="../../../../assets/images/img-file.svg" class="p-10">
                                    </a>
                                </div>
                                <div class="br-4 p-10 text-white break-all pre-whitespace" *ngIf="msg.message"
                                    [innerHTML]="getSanitizedHtml(msg.message)"
                                    [ngClass]="msg.mediaType  == 'Document' ? 'border-top brtl-0 brtr-0' : ''">
                                </div>
                                <div *ngIf="msg.waButtons?.length">
                                    <ng-container *ngFor="let btn of sortButtons(msg.waButtons)">
                                        <div class="border-top p-10 mx-16 text-white flex-center"
                                            [ngClass]="{'cursor-pointer' : btn.type != WAButtonType.QuickReply && btn.type != WAButtonType.None}"
                                            (click)="onButtonClick(btn)">
                                            <span class="icon ic-xxs mr-6" [ngClass]="getButtonIcon(btn.type)"></span>
                                            <span>{{ btn.type === WAButtonType.QuickReply ? btn.value: btn.name}}</span>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </ng-container>

                        <div class="align-center p-10 message h-40" *ngIf="msg?.message !== 'None'"
                            [ngClass]="msg.waEvent == WhatsAppEvents.Receive ? 'received-details flex-row-reverse' : 'sent-details'">
                            <span class="icon ic-light-pale mr-10"
                                [title]="msg.waEvent == WhatsAppEvents.Failed && msg.error ? msg.error : ''" [ngClass]="msg.waEvent == WhatsAppEvents.None ? 'ic-hour-glass ic-xs' :
                                        msg.waEvent == WhatsAppEvents.Sent ? 'ic-tick ic-xxxs' :
                                        msg.waEvent == WhatsAppEvents.Delivered ? 'ic-double-tick ic-xs' :
                                        msg.waEvent == WhatsAppEvents.Read ? 'ic-double-tick ic-xs ic-aqua-750' :
                                        msg.waEvent == WhatsAppEvents.Failed ? 'ic-circle-exclamation rotate-180 ic-red-350 ic-xs' :
                                        msg.waEvent == WhatsAppEvents.Failed && msg.error ? 'cursor-pointer' :
                                        ''">
                            </span>
                            <span class="text-nowrap">{{
                                getTimeZoneDate(msg?.createdOn,userData?.timeZoneInfo?.baseUTcOffset,
                                'timeWithMeridiem')}} |
                                {{getTimeZoneDate(msg?.createdOn, userData?.timeZoneInfo?.baseUTcOffset,
                                'dayMonthYearText')}}</span>
                            <span class="h-10 border mx-4"
                                *ngIf="msg.waEvent == WhatsAppEvents.Receive ? data?.name : msg.userId !== EMPTY_GUID && msg.userId"></span>
                            <span class="text-truncate-1 break-all">{{msg.waEvent == WhatsAppEvents.Receive ?
                                data?.name : getAssignedToDetails(msg?.userId, allUsers, true) || ''}}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- No conversation -->
            <ng-template #noConversationFound>
                <div [ngClass]="whatsAppComp ? 'h-100-370' : 'h-100-280'">
                    <div class="flex-center-col h-100 w-100">
                        <ng-lottie [options]='noConversation' height="150px"></ng-lottie>
                        <h3 class="text-light-gray">no conversation</h3>
                    </div>
                </div>
            </ng-template>
            <div class="box-shadow-3">
                <!-- JUMP TO LATEST -->
                <div class="position-relative">
                    <div *ngIf="!isLastMsgView || isReceiveNewMsg" class="position-absolute ntop-33 right-15"
                        (click)="chatScrolled = false; scrollToBottom()">
                        <div class="dot dot-xl bg-white cursor-pointer shadow-sm position-relative">
                            <span class="icon ic-double-right ic-xxs ic-light-gray rotate-90"></span>
                            <div *ngIf="isReceiveNewMsg"
                                class="dot dot-xsm bg-success cursor-pointer position-absolute ntop-2 nright-2"></div>
                        </div>
                    </div>
                </div>

                <!-- SEND MSG SECTION - START -->
                <ng-container *ngIf="!showTempSelectionSec && !selectedTempVariablesSec && !selectedTempDataSec">
                    <!-- template selection -->
                    <div class="h-100">
                        <div class="flex-center-col p-40 border-top bg-white" *ngIf="!conversationList?.length">
                            <div class="fw-semi-bold">
                                select template to start conversation
                            </div>
                            <button class="btn-coal mx-20 w-130 mt-10" (click)="setState('showTempSelectionSec', true)">
                                <div class="icon ic-folder-solid mr-10"></div>Select Template
                            </button>
                        </div>
                    </div>

                    <!-- 24 Hours -->
                    <div class="flex-center-col p-20 border-top bg-white h-138"
                        *ngIf="conversationList?.length && !isMsgSentIn24Hr">
                        <div class="fw-600 text-red">24 Hours window surpassed</div>
                        <div>The customer has not spoken to you in the last 24 Hours.</div>
                        <div>You can only send pre-approved templates to start conversation again.</div>
                        <button class="btn-coal w-130 mx-20 my-10" (click)="setState('showTempSelectionSec', true)">
                            <div class="icon ic-folder-solid mr-10"></div>Select Template
                        </button>
                    </div>

                    <!-- chatting -->
                    <div *ngIf="conversationList?.length && isMsgSentIn24Hr">
                        <div class="bg-light-pearl px-10 py-6 border-top border-bottom position-relative">
                            <div class="flex-between">
                                <div class="align-center cursor-pointer mt-2">
                                    <!-- <browse-drop-upload [allowedFileType]="'pdf'"6
                                    [allowedFileFormat]="fileFormatToBeUploaded"
                                    (uploadedFile)="onFileSelection($event)"></browse-drop-upload> -->
                                    <label class="cursor-pointer" title="Photo"><span
                                            class="icon ic-xxs ic-degree-camera ic-coal px-20 border-right"></span>
                                        <file-upload (imageFileName)="uploadMedia($event, 'Image')"
                                            [isImageGallery]="true" [allowMultipleFiles]="false"
                                            requiredFileType="image/x-png,image/gif,image/jpeg,image/tiff,image/bmp,image/webp">
                                        </file-upload>
                                    </label>
                                    <label class="cursor-pointer" title="Document"><span
                                            class="icon ic-xxs ic-folder-solid ic-coal px-20 border-right"></span>
                                        <!-- <file-upload (uploadedFile)="uploadMedia($event, 'Document')"
                                            [isExcelUpload]="false" [isImageGallery]="false" [isFormData]="true"
                                            [allowMultipleFiles]="false" (uploadedFileName)="selectedFileName = $event"
                                            requiredFileType="application/pdf,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.spreadsheet,application/vnd.oasis.opendocument.text">
                                        </file-upload> -->
                                        <input id="docUpload" type="file" (change)="handleFileInput($event)"
                                            [accept]="'application/pdf,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.oasis.opendocument.spreadsheet,application/vnd.oasis.opendocument.text'"
                                            [attr.multiple]="false">
                                    </label>
                                    <label class="cursor-pointer" title="Video"><span
                                            class="icon ic-xxs ic-video ic-coal pl-20"></span>
                                        <file-upload (imageFileName)="uploadMedia($event, 'Video')"
                                            (uploadedFileName)="selectedFileName = $event" [isImageGallery]="true"
                                            [allowMultipleFiles]="false"
                                            requiredFileType="video/mp4,video/mpeg,video/quicktime,video/webm,video/x-msvideo,video/x-ms-wmv">
                                        </file-upload>
                                    </label>
                                </div>
                                <div class="align-center" *ngIf="files?.length">
                                    <div class="text-accent-green"><b>{{ files?.length }}</b> Attachments</div>
                                    <div class="d-flex flex-wrap">
                                        <div class="align-center p-10 bg-white br-4 mr-10"
                                            *ngFor="let file of files; let i = index">
                                            <div class="text-truncate-1">{{ file.name }}</div>
                                            <div class="border h-10 mx-10"></div>
                                            <div class="icon ic-delete ic-xxs ic-red-350 cursor-pointer"
                                                (click)="deleteFiles(i, 1)"></div>
                                        </div>
                                    </div>
                                </div>
                                <b class="align-center cursor-pointer" (click)="setState('showTempSelectionSec', true)">
                                    <div class="icon ic-folder-solid ic-xxs ic-black mr-6"></div><u>Choose
                                        Template</u>
                                </b>
                            </div>
                            <div class="bg-white position-absolute z-index-1021 w-360 h-350 bottom-40 shadow ph-min-w-300 ph-w-unset"
                                *ngIf="showImagePreview">
                                <ng-container *ngIf="mediaType === 'Image'; else videoTemplate">
                                    <img [type]="'leadrat'" [appImage]="uploadedImageURL" class="obj-cover h-250 w-100">
                                </ng-container>

                                <ng-template #videoTemplate>
                                    <video controls alt="video" width="200" class="obj-cover h-250 w-100">
                                        <source [src]="uploadedImageURL" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                </ng-template>

                                <div class="p-10">
                                    <div class="form-group no-validation border" [formGroup]="msgForm">
                                        <textarea type="text" formControlName="sendingMsg"
                                            class="border-0 scroll-hide w-100 h-40 textarea-noresize"
                                            placeholder="Caption (optional)"></textarea>
                                    </div>
                                    <div class="flex-end mt-4">
                                        <div class="bg-coal p-10 br-4 flex-center cursor-pointer" (click)="sendImage()">
                                            <span class="icon ic-navigate ic-xxs rotate-45"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dot dot-xl bg-black-500 p-6 cursor-pointer position-absolute top-10 right-10"
                                    (click)="closeImagePreview()">
                                    <div class="icon ic-close-secondary ic-xxxs cursor-pointer">
                                    </div>
                                </div>
                            </div>
                            <div class="bg-pearl position-absolute z-index-1021 w-360 bottom-40 shadow ph-min-w-300 ph-w-unset"
                                *ngIf="showDocPreview">
                                <div class="m-10 py-6 px-4 bg-white br-20">
                                    <div class="flex-between">
                                        <div class="align-center">
                                            <div class="dot dot-md bg-pearl p-6" (click)="closeImagePreview()">
                                                <img alt="media" src="../../../../assets/images/pdf-folder.svg"
                                                    class="h-10 w-10">
                                            </div>
                                            <div class="text-sm text-black-100 text-truncate-1 break-all mx-4">
                                                {{selectedFileName}}</div>
                                            <div class="text-light-slate text-sm">
                                                {{bytesToMegabytes(uploadedDoc.size)}}
                                                mb</div>
                                        </div>
                                        <div class="dot dot-md bg-red-350 p-6 cursor-pointer"
                                            (click)="closeImagePreview()">
                                            <div class="icon ic-close-secondary ic-x-xs"> </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white p-10">
                                    <div class="form-group no-validation border" [formGroup]="msgForm">
                                        <textarea type="text" formControlName="sendingMsg"
                                            class="border-0 scroll-hide w-100 h-40 textarea-noresize"
                                            placeholder="Caption (optional)"></textarea>
                                    </div>
                                    <div class="flex-end mt-4">
                                        <div class="bg-coal p-10 br-4 flex-center cursor-pointer" (click)="sendDoc()">
                                            <span class="icon ic-navigate ic-xxs rotate-45"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <form [formGroup]="msgForm" class="bg-white">
                            <div class="border-top bg-white no-validation form-group">
                                <textarea type="text" placeholder="type here to start conversation..."
                                    formControlName="sendingMsg" class="border-0 scroll-hide textarea-noresize"
                                    (keydown)="onKeyup($event)"></textarea>
                                <div class="flex-end bg-white py-8">
                                    <!-- (click)="sendText()" -->
                                    <button class="btn-coal mx-10 w-130" (click)="sendMessage('text')">
                                        Send Message <div class="icon ic-xxs ic-navigate ml-10 rotate-45"></div>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </ng-container>
                <!-- SEND MSG SECTION - END -->
                <!-- TEMPLATE SECTION - START -->

                <!-- choose/select template -->
                <div class="position-absolute bottom-0 box-shadow-3 w-100" *ngIf="showTempSelectionSec">
                    <div class="bg-light-pearl px-20 py-8 border">
                        <div class="flex-between">
                            <div (click)="setState('showTempSelectionSec', true)" class="flex-center fw-semi-bold">
                                <div class="pr-10 border-right">select a template</div>
                                <div class="cursor-pointer align-center" (click)="fetchTemplateList()">
                                    <div class="icon ic-update ic-xxs ic-black mr-6 ml-10"></div>
                                    <u>refresh</u>
                                </div>
                            </div>
                            <div class="icon ic-close ic-coal ic-xxs cursor-pointer"
                                (click)="showTempSelectionSec = false">
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-10">
                        <div class="flex-between">
                            <div class="align-center bg-white border br-20 h-35px p-4">
                                <ng-container
                                    *ngFor="let type of !isMsgSentIn24Hr ? templateType.slice(0,1) : templateType">
                                    <h6 class="px-8 align-center br-20 cursor-pointer h-28 fw-semi-bold text-light-gray"
                                        (click)="onTemplateTypeChange(type)"
                                        [ngClass]="{'bg-black-100 text-white' : selectedTemplateType == type}">
                                        {{ type }}</h6>
                                </ng-container>
                            </div>
                            <div class="align-center h-35px px-10 py-12 border br-20 no-validation">
                                <span class="icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                                <input name="search" placeholder="search" class="border-0 outline-0 w-100"
                                    (input)="onInputChange($event.target.value)" [value]="searchTerm">
                            </div>
                        </div>
                        <!-- WhatsApp Templates -->
                        <ng-container *ngIf="selectedTemplateType == 'WhatsApp Templates'; else crmTemplates">
                            <div *ngIf="whatsAppTemplateList?.length; else noTemplates" class="scrollbar h-220 mt-6">
                                <ng-container *ngFor="let template of whatsAppTemplateList">
                                    <div class="px-10 py-16 border-bottom cursor-pointer shadow-hover-sm"
                                        (click)="setState('selectedTempVariablesSec', true); chosenTemplate(template)">
                                        <h5 class="fw-semi-bold">{{template?.title}}</h5>
                                        <div class="ml-10 text-xs text-light-gray text-truncate-1">
                                            {{template?.header}} {{template?.message}} {{template?.footer}}
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                        </ng-container>

                        <!-- CRM Templates -->
                        <ng-template #crmTemplates>
                            <div class="d-flex">
                                <div class="align-center border br-20 mt-6 p-6 mb-2">
                                    <div [ngClass]="{'bg-black text-white': selectedCRMTab === 'lead'}"
                                        class="px-10 py-4 br-20 cursor-pointer">
                                        <span (click)="selectCRMTab('lead')">
                                            Lead
                                        </span>
                                    </div>
                                    <div class="px-10 py-4 br-20 cursor-pointer"
                                        [ngClass]="{'bg-black text-white': selectedCRMTab === 'project'}">
                                        <span (click)="selectCRMTab('project')">
                                            Project
                                        </span>
                                    </div>
                                    <div class="px-10 py-4 br-20 cursor-pointer"
                                        [ngClass]="{'bg-black text-white': selectedCRMTab === 'property'}">
                                        <span (click)="selectCRMTab('property')">
                                            Property
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="selectedCRMTab === 'project'">
                                <div for="projectsList" class="field-label">{{ 'Select Project' }}</div>
                                <ng-select [virtualScroll]="true" [items]="projectList" [closeOnSelect]="true"
                                    ResizableDropdown [ngClass]="{'blinking pe-none': projectListIsLoading}"
                                    bindLabel="name" bindValue="id" name="projectsList" placeholder="Select Project"
                                    (change)="onProjectSelect($event)">
                                </ng-select>
                            </div>

                            <div *ngIf="selectedCRMTab === 'property'">
                                <div for="propertiesList" class="field-label">{{ 'Select Property' }}</div>
                                <ng-select [virtualScroll]="true" [items]="propertyList" [closeOnSelect]="true"
                                    ResizableDropdown [ngClass]="{'blinking pe-none': propertyListIsLoading}"
                                    bindLabel="title" bindValue="id" name="propertiesList" placeholder="Select Property"
                                    (change)="onPropertySelect($event)">
                                </ng-select>
                            </div>
                            <div *ngIf="CRMTemplateList?.length; else noTemplates" class="scrollbar h-220 mt-6">
                                <ng-container *ngFor="let template of CRMTemplateList">
                                    <div class="px-10 py-16 border-bottom cursor-pointer shadow-hover-sm"
                                        (click)="setState('selectedTempVariablesSec', true); chosenTemplate(template)">
                                        <h5 class="fw-semi-bold">{{template?.title}}</h5>
                                        <div class="ml-10 text-xs text-light-gray text-truncate-1">
                                            {{template?.header}} {{template?.message}} {{template?.footer}}
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                        </ng-template>

                        <!-- No WhatsApp Templates -->
                        <ng-template #noTemplates>
                            <div class="flex-center-col mt-40">
                                <img src="assets/images/layered-cards.svg" alt="No Data Found" />
                                <h4 class="fw-semi-bold text-center my-20">No Template Found</h4>
                            </div>
                        </ng-template>
                    </div>
                </div>

                <!-- after selected template    -->
                <div class="position-absolute bottom-0 box-shadow-3 w-100" *ngIf="selectedTempVariablesSec">
                    <div class="bg-light-pearl px-20 py-8 border">
                        <div class="flex-between">
                            <div class="flex-center fw-semi-bold cursor-pointer"
                                (click)="setState('showTempSelectionSec', true)">
                                <div class="icon ic-triangle-up rotate-270 ic-x-xs ic-black mr-8"></div>
                                <div>back to templates</div>
                            </div>
                            <!-- don't remove this empty div -->
                            <div>
                                <div *ngIf="selectedTemplateType == 'CRM Templates'" class="align-center cursor-pointer"
                                    (click)="setState('selectedTempDataSec', true)">
                                    <div class="icon ic-pen ic-coal ic-xxs mr-8">
                                    </div>
                                    <u class="fw-semi-bold">Edit Message</u>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white">
                        <div class="form-group no-validation border-bottom">
                            <textarea rows="8" class="h-240 scrollbar border-0"
                                [value]="templateForm.controls['sendingTemplate']?.value" readonly></textarea>
                        </div>
                        <!-- (click)="sendTemplate()" -->
                        <div class="bg-accent-green br-4 py-12 m-10 cursor-pointer" (click)="sendMessage('template')">
                            <div class="flex-center text-white"><span> Send as message</span>
                                <div class="icon ic-xxs ic-navigate ml-10 rotate-45"> </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- click on select & edit Message -->
                <div class="position-absolute bottom-0 box-shadow-3 w-100" *ngIf="selectedTempDataSec">
                    <div class="bg-light-pearl px-20 py-8 border">
                        <div class="flex-between">
                            <div class="align-center">
                                <!-- <span class="icon ic-xxs ic-degree-camera ic-coal pr-20 border-right"></span>
                                <span class="icon ic-xxs ic-folder-solid ic-coal px-20 border-right"></span>
                                <span class="icon ic-xxs ic-playstore ic-coal pl-20"></span> -->
                            </div>
                            <div class="align-center cursor-pointer" (click)="setState('showTempSelectionSec', true)">
                                <div class="icon ic-book ic-coal ic-xxs mr-8"></div>
                                <u class="fw-semi-bold">Choose Template</u>
                            </div>
                        </div>
                    </div>
                    <form [formGroup]="templateForm">
                        <div class="bg-white form-group no-validation">
                            <textarea rows="8" formControlName="sendingTemplate" class="scrollbar border-0 h-240"
                                placeholder="ex. Dear Vikram,
As per Our Telephonic Discussion your Site Visit is Confirm. This is a Reminder Message Please Confirm the time sir.
 
Thankyou
 
Regards,
Ganesh"></textarea>
                            <div class="my-10 border-top"></div>
                            <div class="flex-end mx-10 my-8">
                                <u class="fw-semi-bold mr-20 cursor-pointer"
                                    (click)="setState('selectedTempVariablesSec', true)">Back</u>
                                <u class="fw-semi-bold mr-20 cursor-pointer" (click)="clearTemplate()">Clear</u>
                                <!-- (click)="sendTemplate()" -->
                                <div class="bg-accent-green br-4 py-8 px-10 cursor-pointer"
                                    (click)="sendMessage('template')">
                                    <div class="flex-center text-white">Send as message
                                        <span class="icon ic-xxs ic-navigate ml-10 rotate-45"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- TEMPLATE SECTION - END-->

            </div>
        </div>
    </div>
</ng-template>