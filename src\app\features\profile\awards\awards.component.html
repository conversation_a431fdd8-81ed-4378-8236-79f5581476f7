<ng-container *ngIf="!isAwardsLoading; else loader">
  <div class="d-flex tb-flex-col">
    <div *ngIf="canAdd" class="w-20 tb-w-100">
      <div class="bg-white p-10 br-8 tb-mb-20">
        <div class="fw-600 mb-10">{{ 'PROFILE.awards-certificate' | translate }}</div>
        <div class="br-6 border-green-dashed px-20 py-16 w-100"
          [ngClass]="isFileTypeSupported?'bg-accent-green-light':'bg-accent-red'">
          <div class="fw-semi-bold mb-12 text-center">
            {{ 'GLOBAL.select' | translate }} {{ 'AUTH.and' | translate }} {{ 'LEADS.upload' | translate }}</div>
          <browse-drop-upload [allowedFileType]="'media'" [allowedFileFormat]="fileFormatToBeUploaded"
            (uploadedFile)="onFileSelection($event)"></browse-drop-upload>
          <div class="m-10" *ngIf="(!selectedFile)&&validationPerformed">
            <p class="text-danger text-nowrap">{{'BULK_LEAD.select-file' | translate}}</p>
          </div>
        </div>
        <p class="mt-10 text-xs">{{ 'PROFILE.supported-formats' | translate}}: <span class="fw-600"> Pdf, Jpg, Jepg,
            Png</span></p>
        <div *ngIf="selectedFile" class="flex-between mt-20">
          <span>{{ 'PROFILE.uploaded-award-file' | translate }}</span>
          <div title="Delete" class="bg-light-red icon-badge"><span class="icon ic-delete m-auto ic-xxs"
              id="clkDeleteBrochure" data-automate-id="clkDeleteBrochure" (click)="selectedFile=null"></span></div>
        </div>
        <div>
          <div for="inpAwardName" class="field-label-req">{{ 'PROFILE.award-name' | translate }}</div>
          <div>
            <form-errors-wrapper label="{{ 'PROFILE.award-name' | translate }}" [control]="awardName">
              <input type="text" required id="inpAwardName" [formControl]="awardName" name="awardName" class="editable"
                data-automate-id="inpAwardName" autocomplete="off" placeholder="ex. Smart project of the year" />
            </form-errors-wrapper>
          </div>
        </div>
        <div class="flex-end mt-20">
          <button class="btn-coal" (click)="fileUploadToS3()">{{ 'SIDEBAR.add' | translate }}</button>
        </div>
      </div>
    </div>
    <div class="w-80pr d-flex flex-wrap tb-w-100 ip-flex-center">
      <ng-container *ngIf="recognitionsList?.length">
        <div *ngFor="let recognition of recognitionsList; let i = index" class="ml-20 mb-20 w-130 h-120 flex-col">
          <a [href]="s3BucketPath+recognition?.imageUrl" target="_blank">
            <img *ngIf="isPdf(recognition.imageUrl)" src="../../../../assets/images/pdf.svg">
            <img *ngIf="!isPdf(recognition?.imageUrl)" src="../../../../assets/images/jpg.svg">
          </a>
          <div class="p-10 flex-between bg-white brbr-6 brbl-6">
            <div class="text-truncate-1 break-all fw-semi-bold">{{recognition?.name}}</div>
            <span *ngIf="canDelete" (click)="initDeleteRecognition(recognition?.id, recognition?.name)" class="dot bg-light-red">
              <div title="Delete" class="ic-delete icon ic-xxxs cursor-pointer" id="clkDeleteImage"
                data-automate-id="clkDeleteImage"></div>
            </span>
          </div>
          <br />
        </div>
      </ng-container>
    </div>
  </div>
</ng-container>

<ng-template #loader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>