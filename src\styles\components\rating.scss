//for showing stars rating in grid
// .rating-star {
//   --golden: calc(var(--rating) / 5 * 100%);
//   display: inline-block;
//   font-size: 12px;
//   font-family: Times;
//   line-height: 1;

//   &::before {
//     content: '★★★★★';
//     background: linear-gradient(to right,
//     $dark-yellow 0, 
//     $dark-yellow var(--golden), 
//         $slate-500 var(--golden),
//         $slate-500 100%);
//     -webkit-background-clip: text !important;
//     -webkit-text-fill-color: transparent;
//   }
// }

.rating {
  border: none;
  float: left;

  input {
    display: none;
  }

  label:before {
    margin: 5px;
    font-size: 20px;
    font-family: 'leadRat';
    display: inline-block;
    content: '\ea87';
    @extend .cursor-pointer;
  }

  // .half:before {
  //   content: '\f089';
  //   position: absolute;
  // }

  label {
    color: $dark-400;
    float: right;
  }

  input:checked~label,
  &:not(:checked)>label:hover,
  &:not(:checked)>label:hover~label {
    color: $yellow-300;
  }

  input:checked+label:hover,
  input:checked~label:hover,
  label:hover~input:checked~label,
  input:checked~label:hover~label {
    color: $yellow-400;
  }
}