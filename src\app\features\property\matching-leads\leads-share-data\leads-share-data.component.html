<div *ngIf="!params.data.isArchived" class="align-center mt-4">
    <div *ngIf="params.data.assignTo == EMPTY_GUID" class="bg-dark-blue icon-badge grid-blur cursor-default"
        id="clkMailLead" data-automate-id="clkMailLead" (click)="openUnassignModal()">
        <span class="icon ic-mail m-auto ic-xxs"></span>
    </div>
    <share-external [data]="params?.value?.[0]"
        [key]="params?.value?.[4]?.[0] === 'ProjectsUnit' ? 'share-project-units' : (params?.value?.[4]?.[0] === 'Projects' ? 'share-matching-lead' : 'share-property')"
        [mailCount]="params?.data?.lead ? params?.data?.lead?.contactRecords?.Email : params?.data?.contactRecords?.Email"
        [whatsAppCount]="params?.data?.lead ? params?.data?.lead?.contactRecords?.WhatsApp : params?.data?.contactRecords?.WhatsApp"
        [contactPhone]="params.value[1]" [contactMail]="params.data?.lead?.email" [closeModal]="params?.value?.[3]"
        [leadData]="params?.data?.lead ? params?.data?.lead : params?.data">
    </share-external>
    <div
        [title]="params?.data?.lead?.contactRecords?.Call > 0 ? 'Call: ' + params?.data?.lead?.contactRecords?.Call : 'Call'"
        [ngClass]="{ 'grid-blur cursor-default' : params.data.assignTo == EMPTY_GUID}"
        class="bg-accent-blue icon-badge position-relative" (click)="checkToCall(agent)">
        <span class="icon ic-Call m-auto ic-xxs"></span>
        <span *ngIf="params?.data?.lead ? params?.data?.lead?.contactRecords?.Call : params?.data?.contactRecords?.Call"
            class="position-absolute ntop-14 text-xxs nright-14 dot dot-md bg-red mr-6">
            {{params?.data?.lead?.contactRecords?.Call >= 100 || params?.data?.contactRecords?.Call >= 100 ? '99+' :
            params?.data?.lead ? params?.data?.lead?.contactRecords?.Call : params?.data?.contactRecords?.Call}}</span>
    </div>
</div>

<ng-template #agent>
    <a class="ic-close-secondary ic-close-modal ip-ic-close-modal" (click)="closeModal()"></a>
    <div class="p-16 fw-600 flex-center-col mt-20">
        <h4 class="fw-600 text-orange-800 flex-center">
            <ng-lottie [options]="warn" width="40px" height="40px" class="mr-4"></ng-lottie>
            {{ 'LEADS.agent-not-registered' | translate }}
        </h4>
        <div class="mt-20 text-mud">{{ 'LEADS.register-agent-ivr' | translate }}</div>
        <h4 class="my-10 fw-600">{{ 'GLOBAL.or' | translate }}</h4>
        <div>{{ 'LEADS.select-agent' | translate }}</div>
        <div class="w-100 mt-8">
            <ng-select [virtualScroll]="true" placeholder="Select Agent" name="agent" [(ngModel)]="assignedToUserId"
                ResizableDropdown>
                <ng-option *ngFor="let agent of agentList" [value]="agent.phoneNumber">
                    {{agent.name}}</ng-option>
            </ng-select>
        </div>
        <div class="flex-center mt-20">
            <button class="btn-gray" id="btnAgentCancel" data-automate-id="btnAgentCancel" (click)="closeModal()">
                {{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal ml-8" (click)="ivrClickToCall(assignedToUserId)">
                {{ 'BUTTONS.connect' | translate }}</button>
        </div>
    </div>
</ng-template>