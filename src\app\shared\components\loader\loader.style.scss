//@import "variables";
.cssload-container {
    text-align: center;
    position: fixed;
    z-index: 2000;
    background: rgba(0, 0, 0, 0.8);
    height: 100%;
    width: 100%;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.cssload-zenith {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 0%;

    img {
        width: 20px;
        height: 20px;
    }

    animation: cssload-spin 2s linear infinite;
}

@keyframes cssload-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}

@-o-keyframes cssload-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}

@-ms-keyframes cssload-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}

@-webkit-keyframes cssload-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}

@-moz-keyframes cssload-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}