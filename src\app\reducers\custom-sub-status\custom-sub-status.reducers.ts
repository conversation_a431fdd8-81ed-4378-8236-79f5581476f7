import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { CustomSubStatusActionTypes, ExistSubStatusSuccess, FetchLeadCountSuccess, FetchSubStatusSuccess } from './custom-sub-status.actions';
export type CustomSubStatusState = {
    subStatusExist: boolean;
    LeadCount: any;
    SubStatus: any;
    LeadCountIsLoading: boolean;
};
const initialState: CustomSubStatusState = {
    SubStatus: [],
    LeadCount: [],
    subStatusExist: false,
    LeadCountIsLoading: true,
};
export function customSubStatusReducer(
    state: CustomSubStatusState = initialState,
    action: Action
): CustomSubStatusState {
    switch (action.type) {
        case CustomSubStatusActionTypes.FETCH_SUB_STATUS_SUCCESS_LIST:
            return {
                ...state,
                SubStatus: (action as FetchSubStatusSuccess).response,
            };
        case CustomSubStatusActionTypes.FETCH_LEAD_COUNT:
            return {
                ...state,
                LeadCountIsLoading: true,
            };
        case CustomSubStatusActionTypes.FETCH_LEAD_COUNT_SUCCESS:
            return {
                ...state,
                LeadCount: (action as FetchLeadCountSuccess).response,
                LeadCountIsLoading: false,
            };
        case CustomSubStatusActionTypes.EXIST_SUB_STATUS_SUCCESS:
            return {
                ...state,
                subStatusExist: (action as ExistSubStatusSuccess).response,
            };
        default:
            return state;
    }
}
export const selectFeature = (state: AppState) => state.customSubStatus;

export const getSubStatus = createSelector(
    selectFeature,
    (state: CustomSubStatusState) => state.SubStatus
);

export const getSubStatusLeadCount = createSelector(
    selectFeature,
    (state: CustomSubStatusState) => state.LeadCount
);

export const getSubStatusCountIsLoading = createSelector(
    selectFeature,
    (state: CustomSubStatusState) => {
        return state?.LeadCountIsLoading;
    }
);

export const getSubStatusExist = createSelector(
    selectFeature,
    (state: CustomSubStatusState) => state.subStatusExist
);
