<ng-container
  *ngIf="currentPath === '/invoice' ? (canAssignInvoice && !data?.isArchived) : (data?.status?.actionName !== 'Invoiced' && canAssignLead && !data?.isArchived)">
  <div class="field-label mr-10">{{'BUTTONS.assign' | translate }} {{'GLOBAL.to' | translate }}</div>
  <div class="bg-secondary br-4 mb-10 mr-12 px-12 py-8"
    [ngClass]="{'pe-none': reassignLeadIsLoading && reassignBothIsLoading}">
    <div class="flex-between">
      <div class="d-flex ph-flex-col">
        <div class="align-center">
          <h4 class="dot dot-xl bg-green-50 text-accent-green fw-600 text-uppercase mr-16"
            *ngIf="data.assignTo != EMPTY_GUID">{{getAssignedToDetails(data?.assignTo, users)?.firstName[0]}}</h4>
          <div class="flex-col">
            <h5 class="fw-semi-bold text-dark text-truncate-1 break-all">{{getAssignedToDetails(data?.assignTo, users,
              true) || 'Unassigned lead'}}</h5>
            <div class="text-xs text-dark-gray" *ngIf="globalSettingsData?.isDualOwnershipEnabled">primary</div>
          </div>
        </div>
        <div class="align-center ml-20 ph-ml-0 ph-mt-10"
          *ngIf="globalSettingsData?.isDualOwnershipEnabled && data.secondaryUserId">
          <h4 class="dot dot-xl bg-green-50 text-accent-green fw-600 text-uppercase mr-16"
            *ngIf="data.secondaryUserId != EMPTY_GUID">{{getAssignedToDetails(data?.secondaryUserId,
            users)?.firstName[0]}}</h4>
          <div class="flex-col">
            <h5 class="fw-semi-bold text-dark text-truncate-1 break-all">{{getAssignedToDetails(data?.secondaryUserId,
              users, true) || 'Unassigned lead'}}</h5>
            <div class="text-xs text-dark-gray">secondary</div>
          </div>
        </div>
      </div>
      <button class="btn btn-sm btn-linear-green br-8 text-nowrap" (click)="isShowAssign = !isShowAssign; trackingService.trackFeature('Web.Leads.Menu.ReassignLead.Click', data?.id)">
        {{data.assignTo !== EMPTY_GUID ? 'Re-Assign' : 'Assign'}} {{'GLOBAL.lead' | translate}}</button>
    </div>
    <div *ngIf="isShowAssign">
      <div class="field-label">{{'BUTTONS.assign' | translate }} {{'GLOBAL.to' | translate }}</div>
      <div class="d-flex">
        <div [ngClass]="globalSettingsData?.isDualOwnershipEnabled?'w-50':'w-100'" class="position-relative">
          <div class="text-sm text-black-100 mb-4 mt-0 pt-2"
            [ngClass]="secondaryAssignTo ? 'field-label-req' : 'field-label'"
            *ngIf="globalSettingsData?.isDualOwnershipEnabled">primary
          </div>
          <ng-select [virtualScroll]="true" placeholder="Select User" name="user" [(ngModel)]="assignedToUserId"
            ResizableDropdown [required]="secondaryAssignTo ? true : false" (ngModelChange)="onAssignToChange()"
            [ngClass]="globalSettingsData?.isDualOwnershipEnabled? 'mr-20':''">
            <ng-option *ngIf="data.assignTo !== EMPTY_GUID" [value]="EMPTY_GUID">{{ 'LEADS.unassign-lead' |
              translate}}<span class="text-light-gray">
                ({{'LEADS.mark-unassigned' | translate}})</span></ng-option>
            <ng-option *ngFor="let user of primaryAgentList" [value]="user.id">
              {{user.firstName}} {{user.lastName}} <span class="d-none">{{user.fullName}}</span></ng-option>
            <ng-option *ngFor="let user of deactiveUsers" [value]="user.id" [disabled]="true">
              <div class="dropdown-position"> <span class="text-truncate-1 break-all"> {{ user.firstName }} {{
                  user.lastName }}</span> <span class="d-none">{{user.fullName}}</span><span class="text-disabled"
                  *ngIf="!user.isActive">(Disabled)</span></div>
            </ng-option>
          </ng-select>
          <div *ngIf="secondaryAssignTo && !assignedToUserId"
            class="mt-4 text-xs text-red position-absolute right-20 fw-semi-bold">primary is a required field</div>
        </div>
        <div class="w-50" *ngIf="globalSettingsData?.isDualOwnershipEnabled">
          <div class="text-sm text-black-100 mb-4 pt-2">secondary</div>
          <ng-select [virtualScroll]="true" placeholder="Select User" name="user" [(ngModel)]="secondaryAssignTo"
            ResizableDropdown (ngModelChange)="onSecondaryAssignToChange()">
            <ng-option *ngIf="data.secondaryUserId !== EMPTY_GUID" [value]="EMPTY_GUID">{{ 'LEADS.unassign-lead' |
              translate}}<span class="text-light-gray">
                ({{'LEADS.mark-unassigned' | translate}})</span></ng-option>
            <ng-option *ngFor="let user of secondaryAgentList" [value]="user.id">
              {{user.firstName}} {{user.lastName}} <span class="d-none">{{user.fullName}}</span></ng-option>
            <ng-option *ngFor="let user of deactiveUsers" [value]="user.id" [disabled]="true">
              <div class="dropdown-position">
                <span class="text-truncate-1 break-all"> {{ user.firstName }} {{ user.lastName }} </span><span
                  class="d-none">{{user.fullName}}</span><span class="text-disabled" *ngIf="!user.isActive">
                  (Disabled)</span>
              </div>
            </ng-option>
          </ng-select>
        </div>
      </div>
      <div class="mt-20 flex-end flex-wrap gap-2"
        *ngIf="!reassignLeadIsLoading && !reassignBothIsLoading else ratLoader">
        <button class="btn-gray" (click)="isShowAssign = false">{{'BUTTONS.cancel' | translate}}</button>
        <button class="btn-coal" (click)="userAssign()">{{whatsAppComp ? 'Save' :'BUTTONS.save-and-close' | translate
          }}</button>
        <button class="btn-coal" *ngIf="!isLastLead && !whatsAppComp" (click)="userAssign(true)">
          {{'BUTTONS.save-and-next' | translate}}</button>
      </div>
    </div>
  </div>
</ng-container>
<ng-template #ratLoader>
  <div class="flex-end h-20px mt-20">
    <img src="assets/images/loader-rat.svg" class="rat-loader h-20px w-20px" alt="loader">
  </div>
</ng-template>