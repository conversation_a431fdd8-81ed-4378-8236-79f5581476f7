import { AfterViewInit, Component, OnInit, Renderer2 } from '@angular/core';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'workflow',
  templateUrl: './workflow.component.html'
})
export class WorkflowComponent implements OnInit, AfterViewInit {

  members = [
    {name: '<PERSON>b<PERSON><PERSON><PERSON>'},
    {name: '<PERSON><PERSON><PERSON>'},
    {name: '<PERSON><PERSON><PERSON>'},
    {name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'},
  ];
  openAddGroup: boolean = false;
  public pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  offset: number = 0;
  workFlowView: HTMLElement;
  constructor(private headerTitle: HeaderTitleService, private renderer: Renderer2) { }
  ngAfterViewInit(): void {
  this.workFlowView = this.renderer.selectRootElement('.workflow-bg', true);
  }

  ngOnInit() {
    this.headerTitle.setTitle('Manage Team');
    this.headerTitle.openAddGroup.subscribe(res => {
      this.openAddGroup = res;
    });
  }


  hideAddGroup() {
    this.headerTitle.openAddGroup.next(false);
  }

  zoomIn(){
    this.renderer.setStyle(this.workFlowView, 'scale', 2);
  }

  zoomOut(){
    this.renderer.setStyle(this.workFlowView, 'scale', 1);
  }

}
