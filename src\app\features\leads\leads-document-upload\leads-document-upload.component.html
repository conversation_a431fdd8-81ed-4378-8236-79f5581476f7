<div class="bg-white px-20 py-10">
  <h5 class="fv-sm-caps fw-600 text-coal mb-8">{{ 'BULK_LEAD.uploaded-documents' | translate }}</h5>
  <div class="scrollbar ph-h-100-385" [ngClass]="whatsAppComp ? 'h-100-420' : 'h-100-333'"
    *ngIf="!activeLeadIsLoading else ratLoader">
    <ng-container *ngIf="leadDocList?.length || visitDocList?.length || meetingDocList?.length else noDocuments">
      <div *ngFor="let leadDoc of leadDocList; let i = index" class="flex-col">
        <div class="p-12 flex-between bg-secondary br-6 mb-10 mr-10 w-100">
          <div class="align-center">
            <span *ngIf="isPdf(leadDoc.filePath)" class="icon ic-file ic-sm ic-black"></span>
            <span *ngIf="!isPdf(leadDoc?.filePath)" class="icon ic-image ic-sm ic-black"></span>
            <div class="text-truncate-1 break-all fw-600 ml-8">{{leadDoc?.documentName}}</div>
            <div class="fw-600 text-nowrap">( {{ leadDoc?.uploadedOn ? getTimeZoneDate(leadDoc?.uploadedOn,
              userData?.timeZoneInfo?.baseUTcOffset) : ''}} )</div>
          </div>
          <div class="align-center ml-4">
            <a title="Copy Document Link" class="icon ic-copy-clipboard ic-dark-blue ic-xs cursor-pointer"
              (click)="copyUrl(s3BucketPath+leadDoc?.filePath)"></a>
            <a title="View Document" [href]="s3BucketPath+leadDoc?.filePath" target="_blank"
              class="icon ic-eye-solid ic-accent-green ic-sm cursor-pointer ml-10"></a>
            <a title="Delete Document" class="icon ic-delete ic-red ic-xs cursor-pointer ml-10" id="clkDeleteLeadDoc"
              *ngIf="canEditDoc" data-automate-id="clkDeleteLeadDoc" (click)="initDeleteDocument(leadDoc?.id)"></a>
          </div>
        </div>
      </div>
      <div *ngIf="meetingDocList?.length" class="field-label pb-10">{{ 'DASHBOARD.meetings' | translate }} {{
        'LEAD_FORM.documents' | translate }}</div>
      <div *ngFor="let leadDoc of meetingDocList; let i = index" class="flex-col">
        <div class="p-12 flex-between bg-secondary br-6 mb-10 mr-10 w-100">
          <div class="align-center">
            <span *ngIf="isPdf(leadDoc.filePath)" class="icon ic-file ic-sm ic-black"></span>
            <span *ngIf="!isPdf(leadDoc?.filePath)" class="icon ic-image ic-sm ic-black"></span>
            <div class="text-truncate-1 break-all fw-600 ml-8">{{leadDoc?.documentName}}</div>
            <div class="fw-600 text-nowrap">( {{ leadDoc?.uploadedOn ?
              getTimeZoneDate(leadDoc?.uploadedOn , userData?.timeZoneInfo?.baseUTcOffset)
              : ''}} )</div>
          </div>
          <div class="align-center ml-4">
            <a [href]="s3BucketPath+leadDoc?.filePath" target="_blank"
              class="icon ic-eye-solid ic-accent-green ic-sm cursor-pointer"></a>
            <a class="icon ic-delete ic-red ic-sm cursor-pointer ml-10" id="clkDeleteLeadDoc"
              data-automate-id="clkDeleteLeadDoc" (click)="initDeleteDocument(leadDoc?.id)"></a>
          </div>
        </div>
      </div>
      <div *ngIf="visitDocList?.length" class="field-label pb-10">{{ 'DASHBOARD.site-visits' | translate }} {{
        'LEAD_FORM.documents' | translate }}</div>
      <div *ngFor="let leadDoc of visitDocList; let i = index" class="flex-col">
        <div class="p-12 flex-between bg-secondary br-6 mb-10 w-100">
          <div class="align-center">
            <span *ngIf="isPdf(leadDoc.filePath)" class="icon ic-file ic-sm ic-black"></span>
            <span *ngIf="!isPdf(leadDoc?.filePath)" class="icon ic-image ic-sm ic-black"></span>
            <div class="text-truncate-1 break-all fw-600 ml-8">{{leadDoc?.documentName}}</div>
            <div class="fw-600 text-nowrap">( {{ leadDoc?.uploadedOn ? getTimeZoneDate( leadDoc?.uploadedOn,
              userData?.timeZoneInfo?.baseUTcOffset): ''}} )</div>
          </div>
          <div class="align-center ml-4">
            <a [href]="s3BucketPath+leadDoc?.filePath" target="_blank"
              class="icon ic-eye-solid ic-accent-green ic-sm cursor-pointer"></a>
            <a class="icon ic-delete ic-red ic-sm cursor-pointer ml-10" id="clkDeleteLeadDoc"
              data-automate-id="clkDeleteLeadDoc" (click)="initDeleteDocument(leadDoc?.id)"></a>
          </div>
        </div>
      </div>
      <ng-template #deleteDocumentModal>
        <remove-document (onSaveChanges)="removeDocument(currentDelete)" (onHide)="modalRef.hide()"></remove-document>
      </ng-template>
    </ng-container>
  </div>
  <ng-template #ratLoader>
    <div class="flex-center w-100 h-100-333">
      <img src="assets/images/loader-rat.svg" class="rat-loader" alt="loader">
    </div>
  </ng-template>
  <ng-template #noDocuments>
    <div class="h-100-393 flex-center-col">
      <ng-lottie [options]='noDocument' width="200px" height="200px"></ng-lottie>
      <div class="fw-600 header-4 text-light-slate">{{ 'LEADS.no-documents' | translate }}</div>
    </div>
  </ng-template>
</div>

<div class="z-index-2 position-absolute bottom-0 w-100 p-12 box-shadow-10 bg-white ph-bottom-50"
  *ngIf="!activeLeadIsLoading && canEditDoc">
  <div class="flex-between">
    <div class="d-flex flex-wrap text-mud text-large">
      <div class="mr-6">{{ 'PROFILE.supported-formats' | translate }}.</div>
      <div class="fw-600">JPG,PNG,PDF</div>
    </div>
    <div class="flex-center btn btn-accent-green btn-md w-160 text-sm clear-padding-x" *ngIf="!isUpload"
      (click)="isUpload = true"><span class="icon ic-cloud-upload ic-xxs mr-8"></span>
      {{ 'LEADS.upload-new-document' | translate}}
    </div>
  </div>
  <div *ngIf="isUpload">
    <div for="inpLeadsDoc" class="field-label-req">{{ 'LEADS.document-title' | translate }}</div>
    <form-errors-wrapper label="{{ 'LEADS.document-title' | translate }}" [control]="leadDocName">
      <input type="text" required id="inpLeadsDoc" data-automate-id="inpLeadsDoc" autocomplete="off"
        placeholder="Enter lead document name..." [formControl]="leadDocName" name="leadDocName" />
    </form-errors-wrapper>
    <div class="br-6 p-16 w-100 bg-accent-green-light mt-16 position-relative">
      <div class="flex-center">
        <div class="fw-600 mb-12 text-large w-50">
          {{ 'BULK_LEAD.select-and-upload' | translate }}
          <div class="d-flex text-sm text-slate">
            <div class="mr-6">{{ 'PROFILE.supported-formats' | translate }}. </div>
            <div class="fw-600">JPG,PNG,PDF</div>
          </div>
        </div>
        <browse-drop-upload [allowedFileType]="'imgPdfDoc'" [allowedFileFormat]="fileFormatToBeUploaded"
          (uploadedFile)="selectedFile = $event" class="ml-40 w-50"></browse-drop-upload>
      </div>
      <div class="text-danger position-absolute bottom-3 text-xs" *ngIf="(!selectedFile) && validationPerformed">
        {{'BULK_LEAD.select-file' | translate}}</div>
      <div *ngIf="selectedFile" class="flex-between mt-20">
        <span>{{ 'BULK_LEAD.file-uploaded' | translate }}</span>
        <a class="bg-light-red icon-badge" (click)="selectedFile = null"><span
            class="icon ic-delete m-auto ic-xxs" id="clkLeadDeleteDoc" data-automate-id="clkLeadDeleteDoc"></span></a>
      </div>
    </div>
    <div class="flex-end mt-10">
      <button class="btn-gray mr-10" (click)="cancelUpload()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" (click)="fileUploadToS3()">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</div>