import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { onlyNumbers} from 'src/app/core/utils/common.util';
import { AddBlockInfoComponent } from 'src/app/features/projects/add-new-project/block-info/add-block-info/add-block-info.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  AddProjectBlocks,
  DeleteProjectBlock,
} from 'src/app/reducers/project/project.action';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'block-info-action',
  templateUrl: './block-info-action.component.html',
})
export class BlockInfoActionComponent
  implements OnInit, ICellRendererAngularComp {
  params: any;
  valCount = 0;
  blockId: any;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  areaSizeUnits: Array<any>;
  onlyNumbers = onlyNumbers;
  dataCount: any = [];
  canEdit: boolean;
  canDelete: boolean;

  constructor(
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    private sharedDataService: ShareDataService,
    private modalService: BsModalService,
    public trackingService: TrackingService
  ) { }

  ngOnInit() {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Projects.Update')) {
          this.canEdit = true;
        }
        if (permissions?.includes('Permissions.Projects.Delete')) {
          this.canDelete = true;
        }
      });
  }

  get currentDate(): Date {
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    return currentDate;
  }

  agInit(params: any): void {
    this.params = params;
  }

  refresh(): boolean {
    return false;
  }

  editProject(data: any) {
    this.trackingService.trackFeature(`Web.ProjectBlockInfo.Actions.Edit.Click`)
    let initialState: any = {
      selectedDataId: this.params.value[0],
      selectedBlockData: data,
    };
    this.modalService.show(AddBlockInfoComponent, {
      class: 'ip-modal-unset modal-600',
      initialState,
    });
  }

  openDeleteProjectModal(data: any) {
    this.trackingService.trackFeature(`Web.ProjectBlockInfo.Actions.Delete.Click`)
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'block',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.trackingService.trackFeature(`Web.Project.BlockInfo.Actions.Deleted.Click`)
          this.store.dispatch(
            new DeleteProjectBlock(data?.id, this.params.value[0])
          );
        }
      });
    }
  }

  duplicateBlock(data: any) {
    let payload: any = {
      name: data?.name,
      area: data?.area,
      areaUnitId: data?.areaUnitId,
      startDate: data?.startDate,
      endDate: data?.endDate,
      possessionDate: data?.possessionDate,
      possesionType: data?.possesionType,
      numberOfFloors: data?.numberOfFloors,
      projectId: this.params.value[0],
    };
    this.store.dispatch(new AddProjectBlocks(payload));
    this.trackingService.trackFeature(`Web.ProjectBlockInfo.Actions.Copy.Click`)
  }
}
