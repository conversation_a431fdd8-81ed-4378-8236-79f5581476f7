import {
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, map, skipWhile, switchMap, take, takeUntil } from 'rxjs';

import * as moment from 'moment';
import { AnimationOptions } from 'ngx-lottie';
import {
  PAGE_SIZE,
  REPORTS_DATE_TYPE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import {
  IntegrationSource,
  LeadSource,
  ReportDateType,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalAnonymousIsLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchLeadCities,
  FetchLeadCountries,
  FetchLeadStates,
  FetchProjectList,
  FetchSubSourceList
} from 'src/app/reducers/lead/lead.actions';
import {
  getIsLeadCustomStatusEnabled,
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadStates,
  getLeadStatesIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchReportsSubStatus,
  FetchSubStatusExportSuccess,
  UpdateSubStatusFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getReportsSubStatusList,
  getReportsSubStatusListIsLoading,
  getSubStatusFiltersPayload,
} from 'src/app/reducers/reports/reports.reducer';
import {
  CustomStatus,
  getCustomStatusList,
  getCustomStatusListIsLoading,
} from 'src/app/reducers/status/status.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'sub-status-report',
  templateUrl: './sub-status-report.component.html',
})
export class SubStatusReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  leadSources: Array<any> = [];
  columnDropDown: { field: string; hide: boolean }[] = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  projectList: any;
  rowData: Array<any> = [];
  filtersPayload: ReportsFilter;
  appliedFilter: any;
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  subStatusTotalCount: number;
  getPages = getPages;
  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 4);
  subSourceList: any;
  allSubSourceList: any;
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  allStatusList: Array<any> = [];
  subStatusParentMap: any = {};
  showLeftNav: boolean = true;
  keySeparator: string = ',/_,-,/_,';
  isSubStatusReportLoading: boolean = true;
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  allSubSourceListIsLoading: boolean = true;
  isProjectListLoading: boolean = true;
  customStatusList: CustomStatus[] = [];
  isCustomStatusListLoading: boolean = true;
  showFilters: boolean = false;
  moment = moment;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  isSourcesLoading: boolean = true;
  isGlobalSettingsLoading: boolean = true;
  countryList: any[];
  countryIsLoading: boolean = true;
  loading: AnimationOptions = {
    path: 'assets/animations/loading-text-animation.json',
  };

  isCustomStatusEnabled: boolean = false;
  userData: any;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  onPickerOpened = onPickerOpened;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  filteredColumnDefsCache: any[] = [];

  @ViewChild('reportsGraph') reportsGraph: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    private router: Router,
    private modalService: BsModalService,
    private shareDataService: ShareDataService,
    private modalRef: BsModalRef
  ) { }

  async ngOnInit() {
    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
        this.updateSubSource()
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    await this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    this.isCustomStatusEnabled = await this._store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      )
      .toPromise();

    if (this.isCustomStatusEnabled) this.fetchCustomStatuses();
    this.selectedPageSize = 50;
    if (!this.isCustomStatusEnabled) {
      this.allStatusList = JSON.parse(localStorage.getItem('masterleadstatus'));
    }
    this.headerTitle.setLangTitle('Leads - Sub-Status Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this._store
      .select(getSubStatusFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          userStatus: userStatus,
          visibility: this.filtersPayload?.userStatus,
          dateType: ReportDateType[Number(this.filtersPayload?.dateType)],
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
          IsWithTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          sources: this.filtersPayload?.Sources,
          subSources: this.filtersPayload?.SubSources,
          projects: this.filtersPayload?.Projects,
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States,
        };
        // localStorage.setItem("fromDate", this.filtersPayload.fromDate);
        // localStorage.setItem("toDate", this.filtersPayload.toDate);
        // this.filtersPayload = {
        //   ...this.filtersPayload,
        //   fromDate: null,
        //   toDate: null,
        // }
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });
    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });
    this._store
      .select(getReportsSubStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        (this.isCustomStatusEnabled
          ? this.customStatusList
          : this.allStatusList
        ).forEach((status: any) => {
          if (status?.childTypes?.length) {
            status.childTypes.forEach((subStatus: any) => {
              this.subStatusParentMap[this.getKey(subStatus?.displayName)] =
                status?.status;
            });
          }
        });
        let totalRow: any = {
          projectTitle: 'Total',
          firstName: 'Total',
          lastName: '',
          userName: 'Total',
          subSource: 'Total',
          agencyName: 'Total',
          source: 'Total',
        };
        totalRow.baseStatusWithSubStatusCount = {
          ...totalRow,
          ...this.sumObjectValues(
            data?.items?.map((item: any) => {
              return item.baseStatusWithSubStatusCount;
            })
          ),
        };
        this.rowData = [...data?.items, totalRow]?.map((row: any) => {
          let totalCounts: any = {};
          let flattenedRow = this.flattenObject(
            row.baseStatusWithSubStatusCount
          );
          Object.keys(flattenedRow).forEach((key: string) => {
            const subStatusKey = key?.split(this.keySeparator)?.[1] || key;
            if (this.subStatusParentMap[subStatusKey]) {
              if (!totalCounts[this.subStatusParentMap[subStatusKey] + 'Total'])
                totalCounts[
                  this.subStatusParentMap[subStatusKey] + 'Total'
                ] = 0;
              totalCounts[this.subStatusParentMap[subStatusKey] + 'Total'] +=
                row?.baseStatusWithSubStatusCount?.[
                this.subStatusParentMap[subStatusKey]
                ]?.[subStatusKey] || 0;
            }
          });
          return {
            ...row,
            ...totalCounts,
            all: row?.baseStatusWithSubStatusCount?.All,
            active: row?.baseStatusWithSubStatusCount?.Active,
            overdue: row?.baseStatusWithSubStatusCount?.overdue,
          };
        });
        this.subStatusTotalCount = data.totalCount;
        this.initializeGridSettings();
        this.initializeGraphData();
      });
    this._store
      .select(getReportsSubStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSubStatusReportLoading = isLoading;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        }
        if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });
    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.updateSubSource()
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.allSubSourceListIsLoading = isLoading;
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.filterFunction();

    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });


    this._store
      .select(getLeadCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.citiesIsLoading = data;
      });

    this._store
      .select(getLeadStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getLeadCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });
  }

  fetchCustomStatuses() {
    this._store
      .select(getCustomStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isCustomStatusListLoading = isLoading;
        if (!isLoading) this.initializeGridSettings();
        this.initializeGraphData();
      });

    this._store
      .select(getCustomStatusList)
      .pipe(
        skipWhile(() => this.isCustomStatusListLoading),
        take(1)
      )
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus;
      });
  }

  initializeGraphData() {
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'User Name'
    ).map((col: any) => {
      if (col.children && col.children.length > 0) {
        return {
          ...col,
          isParent: true,
          children: col.children.filter((child: any) => child.headerName !== 'Total')
        };
      } else {
        return {
          ...col,
          isParent: true,
          children: [{
            ...col,
            parentName: col.headerName
          }]
        };
      }
    });
  }

  sumObjectValues(objects: any) {
    const result: any = {};
    objects.forEach((obj: any) => {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (
            typeof obj[key] === 'object' &&
            Object.keys(obj[key]).length > 0
          ) {
            result[key] = result[key] || {};
            result[key] = this.sumObjectValues([result[key], obj[key]]);
          } else {
            result[key] = (result[key] || 0) + obj[key];
          }
        }
      }
    });
    return result;
  }

  flattenObject(obj: any, parentKey = '') {
    let result: any = {};
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        let newKey = parentKey ? `${parentKey}${this.keySeparator}${key}` : key;

        if (typeof obj[key] === 'object' && obj[key] !== null) {
          Object.assign(result, this.flattenObject(obj[key], newKey));
        } else {
          result[newKey] = obj[key];
        }
      }
    }
    return result;
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [
          params.data?.userName,
          params?.data?.projectTitle,
        ],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center text-truncate"><p>${params?.value?.[1] == 'Total'
            ? 'Total'
            : params.value[0]
            }
            </p></div>`;
        },
      },
      {
        headerName: 'All Leads',
        field: 'All Leads',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.all,
          params.data?.active,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 120,
        valueLabels: ['All Leads', 'Active Leads'],
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return `${params?.value?.[3] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`
            }
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != 'Total'
              ? `<a>${params.value[1]}</a>`
              : params.value[1]
                ? params.value[1]
                : '--'
            }<span>
          </p>`;
        },
        cellStyle: { cursor: 'pointer' },
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.event.target.innerText == event.value[0]) {
            if (isCtrlClick) {
              window?.open(
                `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[2]
                }&data=${encodeURIComponent(
                  JSON.stringify(params?.data)
                )}&operation=All Leads&filtersPayload=${encodeURIComponent(
                  JSON.stringify(filters)
                )}`,
                '_blank'
              );
              return;
            }
            this.getData('All Leads', event);
          } else if (event.event.target.innerText == event.value[1]) {
            if (isCtrlClick) {
              window?.open(
                `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[2]
                }&data=${encodeURIComponent(
                  JSON.stringify(params?.data)
                )}&operation=Active Leads&filtersPayload=${encodeURIComponent(
                  JSON.stringify(filters)
                )}`,
                '_blank'
              );
              return;
            }
            this.getData('Active Leads', event);
          }
        },
      },
      {
        headerName: 'Overdue',
        field: 'Overdue',
        filter: false,
        valueGetter: (params: any) => [
          params.data?.overdue,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        minWidth: 80,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
        },
        cellStyle: { cursor: 'pointer' },
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              const filters = { ...this.filtersPayload };
              if (filters?.IsWithTeam) filters.IsWithTeam = false;
              window?.open(
                `leads/manage-leads?leadSubStatusGetData=true&assignTo=${params?.value?.[1]
                }&data=${encodeURIComponent(
                  JSON.stringify(params?.data)
                )}&operation=Overdue&filtersPayload=${encodeURIComponent(
                  JSON.stringify(filters)
                )}`,
                '_blank'
              );
              return;
            }
            this.getData('Overdue', event);
          }
        },
      },
    ];
    for (const status of this.isCustomStatusEnabled
      ? this.customStatusList
      : this.allStatusList) {
      let col: any;
      if (!status?.childTypes?.length) {
        col = {
          headerName: status.displayName,
          field: this.getKey(status.displayName),
          filter: false,
          valueGetter: (params: any) => [
            params.data?.baseStatusWithSubStatusCount?.[status.status],
            params?.data?.userId,
            params?.data?.projectTitle,
          ],
          minWidth: 90,
          cellRenderer: (params: any) => {
            const filters = { ...this.filtersPayload };
            if (filters?.IsWithTeam) filters.IsWithTeam = false;
            return params?.value?.[2] == 'Total' || !params.value[0]
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
          },
          cellStyle: { cursor: 'pointer' },
          onCellClicked: (event: any) => {
            const isCtrlClick = event?.event?.ctrlKey;
            const params = { value: event?.value, data: event?.data };
            if (event.data.projectTitle == 'Total') {
              return;
            } else if (event.value[0]) {
              if (isCtrlClick) {
                const filters = { ...this.filtersPayload };
                if (filters?.IsWithTeam) filters.IsWithTeam = false;
                window?.open(
                  `leads/manage-leads?leadSubStatusGetData=true&assignTo=${params?.value?.[1]
                  }&data=${encodeURIComponent(
                    JSON.stringify(params?.data)
                  )}&operation=${status.displayName
                  }&filtersPayload=${encodeURIComponent(
                    JSON.stringify(filters)
                  )}`,
                  '_blank'
                );
                return;
              }
              this.getData(status.displayName, event);
            }
          },
        };
      } else {
        col = {
          headerName: status.displayName,
          children: [],
        };
        for (const subStatus of status.childTypes) {
          col.children.push({
            headerName: subStatus.displayName,
            field: this.getKey(subStatus?.displayName) +  status.displayName,
            filter: false,
            valueGetter: (params: any) => [
              params.data?.baseStatusWithSubStatusCount?.[status?.status]?.[
              this.getKey(subStatus?.displayName)
              ],
              params?.data?.userId,
              params?.data?.projectTitle,
            ],
            minWidth: 160,
            cellRenderer: (params: any) => {
              const filters = { ...this.filtersPayload };
              if (filters?.IsWithTeam) filters.IsWithTeam = false;
              return params?.value?.[2] == 'Total' || !params.value[0]
                ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
                : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
            },
            cellStyle: { cursor: 'pointer' },
            onCellClicked: (event: any) => {
              const isCtrlClick = event?.event?.ctrlKey;
              const params = { value: event?.value, data: event?.data };
              if (event.data.projectTitle == 'Total') {
                return;
              } else if (event.value[0]) {
                if (isCtrlClick) {
                  const filters = { ...this.filtersPayload };
                  if (filters?.IsWithTeam) filters.IsWithTeam = false;
                  window?.open(
                    `leads/manage-leads?leadSubStatusGetData=true&assignTo=${params?.value?.[1]
                    }&data=${encodeURIComponent(
                      JSON.stringify(params?.data)
                    )}&operation=All Leads&subStatus=${subStatus.displayName
                    }&filtersPayload=${encodeURIComponent(
                      JSON.stringify(filters)
                    )}`,
                    '_blank'
                  );
                  return;
                }
                this.getData('All Leads', event, subStatus.displayName);
              }
            },
          });
        }
        col.children.push({
          headerName: 'Total',
          field: this.getKey(status.displayName) + 'Total',
          filter: false,
          valueGetter: (params: any) => [
            params.data?.[status?.status + 'Total'],
            params?.data?.userId,
            params?.data?.projectTitle,
          ],
          minWidth: 160,
          cellRenderer: (params: any) => {
            const filters = { ...this.filtersPayload };
            if (filters?.IsWithTeam) filters.IsWithTeam = false;
            return params?.value?.[2] == 'Total' || !params.value[0]
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`;
          },
          cellStyle: { cursor: 'pointer' },
          onCellClicked: (event: any) => {
            const isCtrlClick = event?.event?.ctrlKey;
            const params = { value: event?.value, data: event?.data };
            if (event.data.projectTitle == 'Total') {
              return;
            } else if (event.value[0]) {
              if (isCtrlClick) {
                const filters = { ...this.filtersPayload };
                if (filters?.IsWithTeam) filters.IsWithTeam = false;
                window?.open(
                  `leads/manage-leads?leadSubStatusGetData=true&assignTo=${params?.value?.[1]
                  }&data=${encodeURIComponent(
                    JSON.stringify(params?.data)
                  )}&operation=${status.displayName
                  }&filtersPayload=${encodeURIComponent(
                    JSON.stringify(filters)
                  )}`,
                  '_blank'
                );
                return;
              }
              this.getData(status.displayName, event);
            }
          },
        });
      }

      this.gridOptions.columnDefs.push(col);
    }
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  getKey(displayName: string): string {
    let keyWithoutSpace = displayName?.replace(/[\s]+/g, '');
    return keyWithoutSpace?.toLowerCase();
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  getData(operation: string, event: any, subStatus?: string) {
    // if (this.isCustomStatusEnabled) {
    //   return
    // }
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.subStatus = subStatus;
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    this.gridOptionsService.payload = filters;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(new UpdateSubStatusFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchReportsSubStatus());
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = [
      'subSources',
      'projects',
      'agencyNames',
      'cities',
      'states',
    ];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'userStatus',
        'search',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  onRemoveFilter(key: string, value: string) {
    if (['dateType', 'date'].includes(key)) {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any, index: number) => {
          const matchIndex = this.appliedFilter[key]?.indexOf(value);
          return index !== matchIndex;
        }
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchLeadStates());
    this._store.dispatch(new FetchLeadCountries());
    this._store.dispatch(new FetchAllSources());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }
  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateSubStatusFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchReportsSubStatus());
    this.currOffset = 0;
  }

  currentVisibility(visibility: any, isTopLevelFilter: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    if (isTopLevelFilter) {
      this.appliedFilter.users = null;
    }
    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    if (
      this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.sources?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      dateType: ReportDateType[this.appliedFilter.dateType],
      userStatus: this.appliedFilter.userStatus,
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      UserIds: this.appliedFilter.users?.length
        ? this.appliedFilter.users
        : null,
      SearchText: this.searchTerm,
      Sources: this.appliedFilter.sources,
      SubSources: this.appliedFilter.subSources,
      Projects: this.appliedFilter.projects,
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      Countries: this.appliedFilter?.Countries,
      IsWithTeam: this.appliedFilter.IsWithTeam,
    };
    this._store.dispatch(new UpdateSubStatusFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchReportsSubStatus());
    this.currOffset = 0;
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterFunction();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  updateSubSource() {
    if (this.appliedFilter?.sources?.length) {
      this.subSourceList = [];
      this.appliedFilter?.sources.forEach((i: any) => {
        const source: any = LeadSource[i];
        const leadSource = IntegrationSource[source];
        if (leadSource === '99 Acres') {
          this.subSourceList.push.apply(
            this.subSourceList,
            this.allSubSourceList['NinetyNineAcres'] || []
          );
        } else {
          const formattedKey = leadSource?.replace(/\s+/g, '');
          if (Array.isArray(this.allSubSourceList?.[formattedKey])) {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList?.[formattedKey] || []
            );
          } else {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList[leadSource] || []
            );
          }
        }
      });
    } else {
      if (this.allSubSourceList) {
        let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
          if (lead?.displayName === '99 Acres') {
            return this.allSubSourceList?.['NinetyNineAcres'] || [];
          }
          const formattedKey = lead?.displayName?.replace(/\s+/g, '');
          let match = this.allSubSourceList?.[formattedKey];
          if (!match) {
            match = this.allSubSourceList[lead?.displayName];
          }
          if (!match && formattedKey?.toLowerCase() === '99acres') {
            match = this.allSubSourceList['NinetyNineAcres'];
          }
          return Array.isArray(match) ? match : [];
        }) || [];
        this.subSourceList = subSourceList;
      }
    }
  }

  onSelectSource(source: any) {
    if (source) {
      this.updateSubSources(source.displayName);
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSources(sourceName: string | null) {
    if (sourceName && this.allSubSourceList) {
      if (sourceName === '99 Acres') {
        this.subSourceList = this.allSubSourceList['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        if (Array.isArray(this.allSubSourceList?.[formattedKey])) {
          this.subSourceList = this.allSubSourceList?.[formattedKey] || [];
        } else {
          this.subSourceList = this.allSubSourceList[sourceName] || [];
        }
      }
    } else if (this.allSubSourceList) {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList?.[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList;
    }
  }

  exportSubStatusReport() {
    this._store.dispatch(new FetchSubStatusExportSuccess(''));
    this.filterFunction();

    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' && 
           this.reportsGraph?.isChartReady && 
           !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
