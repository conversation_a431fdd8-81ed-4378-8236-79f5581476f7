import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class ListingSiteService extends BaseService<any> {
  serviceBaseUrl: string;

  getResourceUrl(): string {
    return 'listingsite';
  }

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getListingSources() {
    return this.http.get(`${this.serviceBaseUrl}/listing-source`);
  }

  getListingSourcesWithId() {
    return this.http.get(`${this.serviceBaseUrl}/listing-source/namewithid`);
  }

  getAllListingAcounts(id: string) {
    return this.http.get(`${this.serviceBaseUrl}?ListingSourceId=${id}`);
  }

  createListingAcounts(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}`, payload);
  }

  updateListingAcounts(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}`, payload);
  }

  deleteListingAcounts(id: any) {
    const ids = {
      body: {
        id: id,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}`, ids);
  }

  syncListing(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/sync`, payload);
  }

  syncListingTracker(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  uploadExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/excel`, formData);
  }

  uploadMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/batch`, payload);
  }

  getCommunities() {
    return this.http.get(`${this.serviceBaseUrl}/community`);
  }

  getSubCommunities() {
    return this.http.get(`${this.serviceBaseUrl}/sub-community`);
  }

  getTracker(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/address/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  getAddress() {
    return this.http.get(`${this.serviceBaseUrl}/addresses`);
  }

  getSyncListingSources() {
    return this.http.get(`${this.serviceBaseUrl}/listed-source`);
  }
}
