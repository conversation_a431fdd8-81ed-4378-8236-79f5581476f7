<div class="h-100vh d-flex flex-column">
    <div class="flex-between fw-400 bg-coal px-24 py-12 text-white">
        <h5 class="fw-semi-bold">Add Reference ID</h5>
        <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="px-24 py-10 flex-between-col flex-grow-1 bg-light-pearl">
        <div class="w-100">
            <form [formGroup]="addReferenceForm">
                <div>
                    <div class="label-req">Portal name</div>
                    <form-errors-wrapper [control]="addReferenceForm.controls['listingSourceId']" label="portal name">
                        <ng-select [virtualScroll]="true" class="bg-white" bindValue="id" bindLabel="userName"
                            placeholder="ex. Bayut" formControlName="listingSourceId"
                            [ngClass]="{'pe-none bg-secondary hide-arrow': !portals?.length}" ResizableDropdown>
                            <ng-option *ngFor="let portal of portals" [value]="portal.id" class="flex-center">
                                {{ portal?.displayName }}
                            </ng-option>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
                <div>
                    <div class="label-req">Reference ID</div>
                    <form-errors-wrapper [control]="addReferenceForm.controls['referenceId']" label="reference id">
                        <input type="text" autocomplete="off" required id="inpRef" data-automate-id="inpRef"
                            formControlName="referenceId" placeholder="ex. TXN-20230815-12345">
                        <div class="error-message"
                            *ngIf="doesRefIdExist && addReferenceForm.controls.referenceId.status==='VALID'">
                            Reference id already exists</div>
                    </form-errors-wrapper>
                </div>
                <div class="form-group">
                    <div class="label">Property title</div>
                    <input type="text" id="inpTitle" data-automate-id="inpTitle" formControlName="propertyTitle"
                        placeholder="ex. Rohit house">
                </div>
                <div class="form-group">
                    <div class="label">Associated Property</div>
                    <ng-select [virtualScroll]="true" class="bg-white" bindValue="id" bindLabel="title"
                        placeholder="ex. Bayut" formControlName="propertyId"
                        [ngClass]="{'pe-none bg-secondary hide-arrow': !propertyList?.length}" ResizableDropdown>
                        <ng-option *ngFor="let property of propertyList" [value]="property.id" class="flex-center">
                            {{ property.title }}
                        </ng-option>
                    </ng-select>
                </div>
                <!-- <div class="form-group">
                    <div class="label">Serial number</div>
                    <input type="text" id="inpSerial" autocomplete="off" data-automate-id="inpSerial"
                        formControlName="serialNo" placeholder="ex. P-987654">
                </div> -->
            </form>
        </div>
    </div>
    <div class="flex-end modal-footer bg-white box-shadow-3">
        <h6 class="text-black-10 fw-semi-bold text-decoration-underline cursor-pointer" (click)="modalService.hide()">{{
            'BUTTONS.cancel' | translate }}</h6>
        <button (click)="onSave()" class="btn-coal ml-20">{{referenceId ? 'Update' : 'Save'}}</button>
    </div>
</div>