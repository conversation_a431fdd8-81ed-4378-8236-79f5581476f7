import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { getTenantName } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class MasterDataService {
  baseURL: string = `${env.baseURL}`;
  apiURL: string = `${env.apiURL}`;
  resourecURL: string = 'masterdata';

  constructor(private httpClient: HttpClient) { }

  fetchAmenityList(type?: string) {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/masterpropertyamenities${type ? '/listing' : ''}`
    );
  }

  fetchAttributeList() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/masterpropertyattributes`
    );
  }

  fetchPropertyTypes(type?: string) {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/propertytypes${type ? '/listing' : ''}`
    );
  }

  fetchAnonymousAttributeList() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/masterpropertyattributes/anonymous`
    );
  }

  fetchQRPropertyTypes(type?: string) {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/qr/propertytypes${type ? '/listing' : ''}`
    );
  }

  fetchLeadSourceList() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/leadsources`
    );
  }

  fetchLastModifiedList() {
    let tenant = getTenantName();
    const headers = new HttpHeaders().set('tenant', tenant);
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/lastmodifieddates`, { headers }
    );
  }

  fetchModifiedDate() {
    return `${this.baseURL}${this.apiURL}${this.resourecURL}/modifieddates`
  }

  fetchModifiedDatesList() {
    let tenant = getTenantName();
    const headers = new HttpHeaders().set('tenant', tenant);
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/modifieddates`, { headers }
    );
  }

  fetchLeadStatusList() {
    let tenant = getTenantName();
    const headers = new HttpHeaders().set('tenant', tenant);
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/custom/masterleadstatuses`, { headers }
    );
  }

  fetchAreaUnitList() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/masterareaunits`
    );
  }

  fetchProjectType() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/masterprojecttypes`
    );
  }

  fetchQRAreaUnitList() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/qr/masterareaunits`
    );
  }

  fetchUserServicesList() {
    return this.httpClient.get(`${this.baseURL}${this.resourecURL}/userServices`);
  }

  fetchBrandsList() {
    return this.httpClient.get(`${this.baseURL}${this.resourecURL}/builderInfo`);
  }

  fetchProjectAttributes() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/projectattributes`
    );
  }

  fetchProjectAmenities() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/projectamenities`
    );
  }

  fetchAssociateBanks() {
    return this.httpClient.get(
      `${this.baseURL}${this.apiURL}${this.resourecURL}/associatedbank`
    );
  }
}
