import { Component, EventEmitter, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { skipWhile, Subject, Subscription, take, takeUntil } from 'rxjs';

import {
  DATE_TYPE,
  EMPTY_GUID,
  LEAD_FILTERS_KEY_LABEL,
  LEAD_VISIBILITY_IMAGE,
  SOURCE_IMAGE,
} from 'src/app/app.constants';
import { LeadDateType, LeadSource, LeadVisibility } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getAssignedToDetails,
  isEmptyObject,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { WhatsAppInboxAdvFiltersComponent } from 'src/app/features/whatsApp/whatsApp-inbox/whatsApp-inbox-adv-filters/whatsApp-inbox-adv-filters.component';
import { FetchLeadById, FetchLeadIdSuccess } from 'src/app/reducers/lead/lead.actions';
import {
  getActiveLead,
  getActiveLeadIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import {
  FetchWhatsappChildList,
  FetchWhatsappList,
  UpdateFilterPayload,
} from 'src/app/reducers/whatsapp/whatsapp.actions';
import {
  getFiltersPayload,
  getWhatsAppChildList,
  getWhatsAppList,
  getWhatsAppListIsLoading,
  initialState as WAInitialFilterState,
} from 'src/app/reducers/whatsapp/whatsapp.reducer';
import { environment as env } from 'src/environments/environment';
import { WhatsappChatComponent } from '../../leads/whatsapp-chat/whatsapp-chat.component';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'whatsApp-inbox',
  templateUrl: './whatsApp-inbox.component.html',
})
export class WhatsAppInboxComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canUserView: boolean;
  whatsAppListIsLoading: boolean;
  visibilityList: Array<Object> = LEAD_VISIBILITY_IMAGE.slice(0, 3);
  showLeftNav: boolean = true;
  noDataFound: AnimationOptions = {
    path: 'assets/animations/empty-notes.json',
  };

  noConversation: AnimationOptions = {
    path: 'assets/animations/whatsApp-chat.json',
  };
  searchTerm: string;
  private searchTermSubject = new Subject<string>();
  getAssignedToDetails = getAssignedToDetails;
  EMPTY_GUID = EMPTY_GUID;
  allUserList: Array<any> = [];
  canShowChatScreen: boolean = false;
  clickedData: any;
  isEmptyObject = isEmptyObject;
  canViewLeadSource: boolean;
  LeadSource = LeadSource;
  clickedDataIsLoading: boolean;
  whatsAppData: any;
  filtersPayload: any = {
    path: 'wa/leads-with-message',
    PageNumber: 1,
    PageSize: 10,
  };
  dateTypeList: Array<string> = DATE_TYPE;
  dateType: string;
  filterDate: any;
  LeadVisibility = LeadVisibility;
  whatsAppDataItems: any[] = [];
  whatsAppChildData: any;
  expandedItemId: any;
  private subscriptions: Subscription[] = [];
  userBasicDetails: any;
  currentDate: Date = new Date();
  get showFilters(): boolean {
    return this.filtersPayload?.Sources?.length ||
      this.filtersPayload?.SubSources?.length ||
      this.filtersPayload?.Projects?.length ||
      this.filtersPayload?.Properties?.length ||
      this.filtersPayload?.UserIds?.length ||
      this.filtersPayload?.TextByIds?.length ||
      this.filtersPayload?.StatusesIds?.length ||
      this.filtersPayload?.SubStatusesIds?.length
      ? true
      : false;
  }
  filtersKeyLabel = LEAD_FILTERS_KEY_LABEL;
  masterLeadStatus: Array<any> = JSON.parse(
    localStorage.getItem('masterleadstatus') || '[]'
  );
  s3BucketUrl: string = env.s3ImageBucketURL;
  currentId: string;
  onPickerOpened = onPickerOpened;

  constructor(
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    private shareDataService: ShareDataService,
    public metaTitle: Title,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) {
    this.headerTitle.setTitle('WhatsApp Inbox');
    this.metaTitle.setTitle('CRM | WhatsApp');
    this.store.dispatch(new FetchUsersListForReassignment());

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        //change permissions
        this.canUserView = permissionsSet.has('Permissions.Leads.View');
        this.canViewLeadSource = permissionsSet.has(
          'Permissions.Leads.ViewLeadSource'
        );
        // this.visibilityList =
        //   this.canManagerView || this.canAdminView
        //     ? LEAD_VISIBILITY_IMAGE.slice(0, 3)
        //     : LEAD_VISIBILITY_IMAGE.slice(1, 2);
      });

    this.store
      .select(getWhatsAppChildList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.whatsAppChildData = data;
      });
  }

  ngOnInit() {
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
      });

    this.store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        this.filtersPayload = {
          ...this.filtersPayload,
          ...filters,
          PageNumber: filters?.PageNumber,
          PageSize: filters?.PageSize,
          SearchText: filters?.SearchText ? filters?.SearchText : null,
          DateType: filters?.DateType || 0,
          FromDate: filters?.FromDate,
          ToDate: filters?.ToDate,
        };

        this.searchTerm = filters?.SearchText ? filters?.SearchText : null;
        this.dateType = LeadDateType[this.filtersPayload.DateType];
        this.filterDate = [patchTimeZoneDate(filters?.FromDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset), patchTimeZoneDate(filters?.ToDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)];
      });
    this.store
      .select(getWhatsAppListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.whatsAppListIsLoading = isLoading;
      });
    this.store
      .select(getWhatsAppList)
      .pipe(skipWhile(() => this.whatsAppListIsLoading), takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.whatsAppData = data;
        this.whatsAppDataItems = [...this.whatsAppDataItems, ...(data?.items || [])];
      });

    this.searchTermSubject.subscribe(() => {
      this.filtersPayload = {
        ...this.filtersPayload,
        PageNumber: 1,
        SearchText: this.searchTerm ? this.searchTerm : null,
      };
      this.filterFunction();
    });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allUserList = this.allUserList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
      });

    this.store.select(getWhatsAppListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.whatsAppListIsLoading = isLoading;
      });
    this.filterFunction();
  }

  currentVisibility(visibility: any) {
    if (LeadVisibility[visibility] === this.filtersPayload?.Visibility) return;
    this.filtersPayload = {
      ...this.filtersPayload,
      Visibility: LeadVisibility[visibility],
      PageNumber: 1,
    };
    this.filterFunction();
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm?.trim() === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  dateChange() {
    if (this.dateType && this.filterDate?.[0]) {
      this.filtersPayload = {
        ...this.filtersPayload,
        DateType: LeadDateType[this.dateType as keyof typeof LeadDateType],
        FromDate: setTimeZoneDate(this.filterDate?.[0], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
        ToDate: setTimeZoneDate(this.filterDate?.[1], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
      };
      this.filterFunction();
    }
  }

  onResetDateFilter() {
    this.filtersPayload = {
      ...this.filtersPayload,
      DateType: 0,
      FromDate: null,
      ToDate: null,
    };
    this.filterDate = [null, null];
    this.filterFunction();
  }

  fetchConversation(data: any) {
    const width = window.innerWidth;
    if (width > 768) {
      this.fetchConversation1(data);
    } else {
      this.fetchConversation2(data);
    }
  }

  fetchConversation1(data: any) {
    this.clickedData = null;
    const width = window.innerWidth;
    if (width > 480) {
      this.canShowChatScreen = true;
    } else {
      this.canShowChatScreen = false;
    }
    this.currentId = data?.id;
    this.store.dispatch(new FetchLeadById(data?.id));
    this.store
      .select(getActiveLead)
      .pipe(takeUntil(this.stopper))
      .subscribe((activeLead: any) => {
        if (!isEmptyObject(activeLead)) {
          this.clickedData = activeLead;
        }
      });
    this.store
      .select(getActiveLeadIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.clickedDataIsLoading = isLoading;
      });
  }

  fetchConversation2(data: any) {
    this.clickedData = null;

    this.currentId = data?.id;
    this.store.dispatch(new FetchLeadById(data?.id));

    // Unsubscribe from previous subscriptions
    this.subscriptions?.forEach((sub: any) => sub.unsubscribe());
    this.subscriptions = [];

    // Subscribe to getActiveLead
    const activeLeadSub = this.store
      .select(getActiveLead)
      .pipe(takeUntil(this.stopper))
      .subscribe((activeLead: any) => {
        if (!isEmptyObject(activeLead)) {
          this.clickedData = activeLead;
          if (this.clickedData) {
            let initialState: any = {
              data: this.clickedData,
            };
            if (this.modalRef) {
              this.modalRef.hide();
            }
            this.modalRef = this.modalService.show(
              WhatsappChatComponent,
              Object.assign(
                {},
                {
                  class: "modal-550 right-modal ip-modal-unset",
                  initialState,
                }
              )
            );
          }
        }
      });

    // Subscribe to getActiveLeadIsLoading
    const isLoadingSub = this.store
      .select(getActiveLeadIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.clickedDataIsLoading = isLoading;
      });

    // Store the subscriptions
    this.subscriptions.push(activeLeadSub, isLoadingSub);
  }

  fetchChildData(data: any) {
    this.store.dispatch(new FetchWhatsappChildList(data?.id));
    this.fetchConversation(data);
  }

  clickedItem(data: any) {
    if (data?.childLeadsCount) {
      // this.canShowChatScreen = true;
      // this.currentId = data?.id;
      // this.store.dispatch(new FetchLeadById(data?.id));
      // this.store
      //   .select(getActiveLead)
      //   .pipe(takeUntil(this.stopper))
      //   .subscribe((activeLead: any) => {
      //     if (!isEmptyObject(activeLead))
      //       this.clickedData = activeLead;
      //   });
      // this.store
      //   .select(getActiveLeadIsLoading)
      //   .pipe(takeUntil(this.stopper))
      //   .subscribe((isLoading: any) => {
      //     this.clickedDataIsLoading = isLoading;
      //   });
    } else {
      this.canShowChatScreen = true;
      this.currentId = data?.id;
      this.store.dispatch(new FetchLeadById(data?.id));
      this.store
        .select(getActiveLead)
        .pipe(take(1))
        .subscribe((activeLead: any) => {
          if (!isEmptyObject(activeLead))
            this.clickedData = activeLead;
        });
      this.store
        .select(getActiveLeadIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.clickedDataIsLoading = isLoading;
        });
    }
  }

  toggleChildData(item: any) {
    if (this.expandedItemId === item.id) {
      this.expandedItemId = null; // Collapse if already expanded
    } else {
      this.expandedItemId = item.id; // Expand new item
      // Fetch child data if necessary
      this.fetchChildData(item);
    }
  }

  onInView(isVisible: boolean) {
    if (isVisible &&
      !this.whatsAppListIsLoading &&
      this.whatsAppDataItems?.length < this.whatsAppData?.totalCount
    ) {
      this.loadMore();
    }
  }

  loadMore() {
    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: this.filtersPayload.PageNumber + 1,
      PageSize: 10,
    };
    this.filterAndUpdate();
  }

  filterAndUpdate() {
    this.clickedData = null;
    this.store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this.store.dispatch(new FetchWhatsappList(this.filtersPayload));
  }

  getImageUrl(leadSource: number): string {
    const source = SOURCE_IMAGE.find(item => item.leadSource === leadSource);
    //need to change this
    return source ? this.s3BucketUrl + source.imageURL : 'https://dleadrat-black.s3.ap-south-1.amazonaws.com/logos/IVR.svg';
  }

  openAdvFiltersModal() {
    let initialState: any = {
      clearDataAndFilter: () => {
        this.filterFunction();
      }
    };
    this.modalService.show(WhatsAppInboxAdvFiltersComponent, {
      class: 'ip-modal-unset  top-full-modal',
      initialState
    });
  }

  filterFunction() {
    this.whatsAppDataItems = [];
    this.filterAndUpdate();
  }

  getUserName(id: string) {
    let userName = '';
    this.allUserList?.forEach((user: any) => {
      if (id === user.id) userName = `${user.firstName} ${user.lastName}`;
    });
    return userName;
  }

  getStatusName(id: string) {
    let status = '';
    this.masterLeadStatus?.forEach((type: any) => {
      if (type.id === id) status = type.displayName;
    });
    return status;
  }

  getSubStatusName(id: string) {
    let status = '';
    let allSubStatusList = this.masterLeadStatus?.map(
      (status: any) => status.childTypes
    );
    if (allSubStatusList) {
      allSubStatusList = Object.values(allSubStatusList).flat();
    }
    allSubStatusList?.forEach((type: any) => {
      if (type.id === id) status = type.displayName;
    });
    return status;
  }

  getArrayOfFilters(key: string, values: string) {
    if (
      key === 'DateType' ||
      key === 'Visibility' ||
      key === 'PageSize' ||
      key === 'PageNumber' ||
      key === 'FromDate' ||
      key === 'ToDate' ||
      key === 'SearchText' ||
      key === 'path' ||
      key === 'IsWithTeam' ||
      values?.length === 0
    )
      return [];
    return values?.toString()?.split(',');
  }

  onRemoveFilter(key: string, value: string) {
    if (
      [
        'Sources',
        'SubSources',
        'Projects',
        'Properties',
        'UserIds',
        'TextByIds',
        'StatusesIds',
        'SubStatusesIds',
      ].includes(key)
    ) {
      if (this.filtersPayload.hasOwnProperty(key)) {
        const filteredValue: any = (
          this.filtersPayload[key] as string[]
        ).filter((item: any) => item !== value);

        this.filtersPayload = {
          ...this.filtersPayload,
          [key]: filteredValue,
        };
      }
    } else {
      this.filtersPayload = {
        ...this.filtersPayload,
        [key]: null,
      };
    }

    this.filterFunction();
  }

  onClearAllFilters() {
    const Visibility = this.filtersPayload?.Visibility;
    const SearchText = this.filtersPayload?.SearchText || null;
    const DateType = this.filtersPayload?.DateType || 0;
    const FromDate = this.filtersPayload?.FromDate;
    const ToDate = this.filtersPayload?.ToDate;
    this.filtersPayload = WAInitialFilterState.filtersPayload;
    this.filtersPayload = {
      ...this.filtersPayload,
      SearchText,
      Visibility,
      DateType,
      FromDate,
      ToDate,
      PageNumber: 1,
      PageSize: 10,
    };
    this.filterFunction();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    this.subscriptions?.forEach((sub: any) => sub.unsubscribe());
  }
}
