import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { debounceTime, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import { FetchPropertyWithIdNameList } from 'src/app/reducers/property/property.actions';
import { getPropertyWithIdLoading, getPropertyWithIdNameList } from 'src/app/reducers/property/property.reducer';
import { AddReferenceId, DoesRefIdExist, UpdateReferenceId } from 'src/app/reducers/reference-id-management/reference-id-management.action';
import { DoesRefNameExist, getReferenceListingSources } from 'src/app/reducers/reference-id-management/reference-id-management.reducer';

@Component({
  selector: 'add-reference-id',
  templateUrl: './add-reference-id.component.html',
})
export class AddReferenceIdComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() referenceId: any;

  addReferenceForm: FormGroup;
  portals: any[] = [];
  propertyList: any[] = [];
  propertyListIsLoading: boolean;
  doesRefIdExist: boolean = false;

  constructor(private fb: FormBuilder,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    public modalService: BsModalService,
  ) { }

  ngOnInit(): void {
    this.addReferenceForm = this.fb.group({
      referenceId: [this.referenceId?.referenceId ?? null, Validators.required],
      listingSourceId: [this.referenceId?.listingSource?.id ?? null, Validators.required],
      propertyTitle: [this.referenceId?.propertyTitle ?? null],
      serialNo: [this.referenceId?.serialNo ?? null],
      propertyId: [this.referenceId?.associatedProperty?.id ?? null],
    });

    this.store.dispatch(new FetchPropertyWithIdNameList());
    this.store
      .select(getPropertyWithIdNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data.slice().sort((a: any, b: any) => {
          const nameA = a.name || '';
          const nameB = b.name || '';
          return nameA.localeCompare(nameB);
        });
      });
    this.store
      .select(getReferenceListingSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.portals = data
      })

    this.store
      .select(getPropertyWithIdLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.propertyListIsLoading = data;
      });

    this.addReferenceForm
      .get('referenceId')
      .valueChanges.pipe(debounceTime(300))
      .subscribe((value: any) => {
        if (
          value &&
          this.addReferenceForm.controls.referenceId.status === 'VALID'
        ) {
          this.doesRefNameExists();
        }
      });
  }


  onSave() {
    if (!this.addReferenceForm.valid || this.doesRefIdExist) {
      validateAllFormFields(this.addReferenceForm);
      return;
    }
    const refData = this.addReferenceForm.value;

    let payload = {
      referenceId: refData?.referenceId,
      listingSourceId: refData?.listingSourceId,
      propertyTitle: refData?.propertyTitle,
      serialNo: refData?.serialNo,
      propertyId: refData?.propertyId,
      ...(this.referenceId ? { id: this.referenceId?.id } : {})
    }

    this.referenceId ? this.store.dispatch(new UpdateReferenceId(payload)) : this.store.dispatch(new AddReferenceId(payload))
  }

  doesRefNameExists() {
    let refName = this.addReferenceForm.value.referenceId;
    let sourceId = this.addReferenceForm.value.listingSourceId;
    this.store.dispatch(new DoesRefIdExist(refName, sourceId));
    this.store.dispatch(new LoaderHide());
    this.store
      .select(DoesRefNameExist)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.doesRefIdExist = data;
      });
  }
}
