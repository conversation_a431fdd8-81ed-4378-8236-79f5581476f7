import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { AgGridModule } from 'ag-grid-angular';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import {
  PROPERTY_DECLARATIONS,
  PropertyRoutingModule,
} from 'src/app/features/property/property-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { AddReferenceIdComponent } from './reference-id-management/add-reference-id/add-reference-id.component';
import { ReferenceIdActionsComponent } from './reference-id-management/reference-id-actions/reference-id-actions.component';
import { ReferenceIdAdvancedFilterComponent } from './reference-id-management/reference-id-advanced-filter/reference-id-advanced-filter.component';
import { ReferenceIdManagementComponent } from './reference-id-management/reference-id-management.component';

@NgModule({
  declarations: [...PROPERTY_DECLARATIONS, ReferenceIdManagementComponent, AddReferenceIdComponent, ReferenceIdActionsComponent, ReferenceIdAdvancedFilterComponent],
  imports: [
    NgxSliderModule,
    CommonModule,
    PropertyRoutingModule,
    SharedModule,
    AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    NgxMatIntlTelInputComponent,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
      // to extend the I18n to use global en.json
      extend: true,
    }),
    LottieModule.forRoot({ player: playerFactory }),
  ],
  providers: [TranslateService],
})
export class PropertyModule { }
