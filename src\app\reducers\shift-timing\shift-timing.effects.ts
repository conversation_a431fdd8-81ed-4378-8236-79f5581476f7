import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { AddShiftTiming, AddShiftTimingSuccess, FetchBulkShiftTiming, FetchBulkShiftTimingSuccess,ShiftTimingActionTypes } from "./shift-timing.actions";
import { catchError, map, mergeMap, of, switchMap } from "rxjs";
import { OnError } from "src/app/app.actions";
import { NotificationsService } from "angular2-notifications";
import { AppState } from "src/app/app.reducer";
import { Store } from "@ngrx/store";
import { ShifttimingService } from "src/app/services/controllers/shift-timing.service";


@Injectable()
export class ShiftTimingEffects {

    addShiftTiming$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ShiftTimingActionTypes.ADD_SHIFT_TIMING),
            switchMap((action: AddShiftTiming) => {
                return this.api.addShiftTiming(action.payload).pipe(
                    mergeMap((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(`Shift Timing added successfully.`);
                            return [new AddShiftTimingSuccess(), new FetchBulkShiftTiming()]; 
                        } else {
                            return of(new OnError(resp)); 
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );
    
    getShiftTimingBulk$ = createEffect(() =>
        this.actions$.pipe(
            ofType(ShiftTimingActionTypes.FETCH_BULK_SHIFT_TIMING),
            map((action: FetchBulkShiftTiming) => action),
            switchMap((action: FetchBulkShiftTiming) => {
                return this.api.getShiftTiming().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchBulkShiftTimingSuccess(resp);
                        }
                        return new FetchBulkShiftTimingSuccess([]);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private actions$: Actions,
        private api: ShifttimingService,
        private _notificationService: NotificationsService,
        private _store: Store<AppState>
    ) { }
}