import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AnimationOptions } from 'ngx-lottie';
import { IMAGES } from 'src/app/app.constants';

@Component({
  selector: 'project-amenities',
  templateUrl: './project-amenities.component.html',
})
export class ProjectAmenitiesComponent implements OnInit, OnDestroy {

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  images: string = IMAGES.noAmenitiesFound;
  @Input() projectAmentities: any;
  nearby: any[] = [];
  basic: any[] = [];
  featured: any[] = [];
  amenitiesArr: any[] = [];
  activeAmenitiesType: string = 'Basic';
  isView: boolean = false;
  mobileView: boolean = false;

  chevron: AnimationOptions = {
    path: '../../../assets/animations/chevron-down.json',
  };

  constructor() {
    if (window.screen?.width < 600) {
      this.mobileView = true;
    }
  }
  ngOnInit(): void {
    this.projectAmentities?.amenitiesDto?.forEach((item: any) => {
      let categoryExists = this.amenitiesArr.find(amenity => amenity[0] === item.category);

      if (categoryExists) {
        categoryExists[1].push(item);
        categoryExists[1].sort((a: any, b: any) => a.amenityDisplayName.localeCompare(b.amenityDisplayName));
      } else {
        this.amenitiesArr.push([item.category, [item]]);
      }
    });
  }

  getFirstCharacter(name: string) {
    return name?.charAt(0).toUpperCase();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
