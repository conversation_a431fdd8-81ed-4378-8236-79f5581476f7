import { Component, EventEmitter, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import {
  OTP_RECEIVER,
  OTP_TYPE,
  OTP_USER_OPTIONS,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  UpdateGlobalSettings,
  UpdateOTPSettings
} from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous, getGlobalAnonymousIsLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchOnlyAdmins,
  FetchUsersListForReassignment
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyAdmins,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'security-settings',
  templateUrl: './security-settings.component.html',
})
export class SecuritySettingsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canView: boolean = false;
  canUpdate: boolean = false;
  securityForm: FormGroup;
  otpReceiver = OTP_RECEIVER;
  userTypeOptions = OTP_USER_OPTIONS;
  otpChannels = OTP_TYPE;
  message: string;
  notes: string;
  allUserList: any;
  allActiveUsers: any;
  adminsList: Array<any>;
  getAssignedToDetails = getAssignedToDetails;
  settingData: any;
  globalSettings: any;
  settingType: string;
  isGlobalSettingLoading: boolean = true;

  constructor(
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    private fb: FormBuilder,
    public metaTitle: Title,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private router: Router
  ) {
    this.securityForm = fb.group({
      otpEnabled: [null],
      otpChannels: fb.array([], Validators.required),
      otpReceiver: [null, Validators.required],
      receiverUserIds: [null],
      userOption: [null],
      userList: [null],
      copyPaste: [null],
      screenshot: [null],
    });
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.GlobalSettings.View'))
          this.canView = true;
        if (permissions?.includes('Permissions.GlobalSettings.Update'))
          this.canUpdate = true;
      });
  }

  ngOnInit(): void {
    this.store.dispatch(new FetchOnlyAdmins());
    this.store.dispatch(new FetchUsersListForReassignment());
    this.patchValues();

    this.store
      .select(getOnlyAdmins)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.adminsList = data?.items?.admins?.filter(
          (user: any) => user.isActive
        );
        this.adminsList = this.adminsList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.adminsList = assignToSort(this.adminsList, '');
        const mfaEnabledUsers = data?.items?.mfaEnabledUsers?.map(
          (user: any) => user.id
        );
        this.securityForm.patchValue({
          ...this.securityForm.value,
          userList: mfaEnabledUsers,
        });
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });

    this.securityForm
      .get('otpReceiver')
      .valueChanges.subscribe((value: Number) => {
        if (value == 1) {
          toggleValidation(
            VALIDATION_SET,
            this.securityForm,
            'receiverUserIds',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.securityForm,
            'receiverUserIds'
          );
        }
      });

    this.securityForm
      .get('userOption')
      .valueChanges.subscribe((value: boolean) => {
        if (value) {
          toggleValidation(VALIDATION_CLEAR, this.securityForm, 'userList');
        } else {
          toggleValidation(VALIDATION_SET, this.securityForm, 'userList', [
            Validators.required,
          ]);
        }
      });

    this.modalRef.onHide?.subscribe(() => {
      this.settingType = '';
      this.notes = '';
      this.message = '';
    });

    this.store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isGlobalSettingLoading = loading;
      });
  }

  patchValues() {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettings = data;
        this.settingData = data?.otpSettings;
        const selectedChannels = data?.otpSettings?.channels || [];
        let formArray;
        if (selectedChannels.length > 0) {
          formArray = this.fb.array(
            this.createChannelControls(selectedChannels)
          );
        } else {
          formArray = this.fb.array(this.createChannelControls([2]));
        }

        this.securityForm.setControl('otpChannels', formArray);
        this.securityForm.patchValue({
          otpEnabled: data?.otpSettings?.isEnabled,
          otpReceiver:
            data?.otpSettings?.receiver == null
              ? 1
              : data?.otpSettings?.receiver,
          receiverUserIds: data?.otpSettings?.receiverUserIds,
          userOption:
            data?.otpSettings?.isEnabledForAll == null
              ? true
              : data?.otpSettings?.isEnabledForAll,
          copyPaste: data?.isCopyPasteEnabled,
          screenshot: data?.isScreenshotEnabled,
        });
      });
  }

  createChannelControls(selectedChannels: number[]): FormGroup[] {
    return this.otpChannels.map((channel) => {
      return this.fb.group({
        displayName: channel.displayName,
        value: channel.value,
        selected: selectedChannels.includes(channel.value),
      });
    });
  }

  handleCheckboxClick(event: MouseEvent, value: number): void {
    const selectedChannels = this.securityForm
      .get('otpChannels')
      .value.filter((channel: any) => channel.selected);
    if (selectedChannels.length === 1 && selectedChannels[0].value === value) {
      event.preventDefault();
    }
  }

  onCancel() {
    this.router.navigate(['global-config']);
  }

  openConfirmModal(changePopup: any, settingType: string) {
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    this.settingType = settingType;
    switch (settingType) {
      case '2FA':
        if (this.securityForm.value.otpEnabled) {
          this.message =
            'Are you sure you want to disable the “Two Factor Authentication” option?';
          this.notes =
            'this will make the password as the sole authentication mode.';
        } else {
          this.message =
            'Are you sure you want to enable the “Two Factor Authentication” option?';
          this.notes =
            'this will make selected users to authenticate Via OTP post entering the password.';
        }
        break;
      case 'copyPaste':
        if (this.securityForm.get('copyPaste').value) {
          this.message =
            'Are you sure you want to disable the “Copy-Paste Functionality” option?';
          this.notes =
            'This will bar the users from Copy and Pasting the contents from CRM';
        } else {
          this.message =
            'Are you sure you want to enable the “Copy-Paste Functionality” option?';
          this.notes =
            'This will allow users to Copy and Paste the contents of the CRM via the Clipboard';
        }
        break;
      case 'screenshot':
        if (this.securityForm.get('screenshot').value) {
          this.message =
            'Are you sure you want to enable the “Screenshot Functionality” option?';
          this.notes = 'This will allow users to take screenshots in the CRM.';
        } else {
          this.message =
            'Are you sure you want to disable "Screenshot functionality" inside the CRM?';
          this.notes =
            'This Option will Stop users taking screenshots inside the CRM (Currently Available only on Mobile Application).';
        }
    }
  }

  TwoFactorAuthenticationSave(type?: string) {
    const settingsData: any = this.securityForm.value;
    let selectedChannels = settingsData.otpChannels.filter(
      (channel: boolean, index: number) => channel
    );
    selectedChannels = selectedChannels
      .filter((channel: any) => channel.selected)
      .map((channel: any) => channel.value);
    if (type !== 'modal') {
      if (!this.securityForm.valid || selectedChannels.length < 1) {
        validateAllFormFields(this.securityForm);
        return;
      }
    }

    let payload = {
      ...this.settingData,
      isEnabled: settingsData.otpEnabled,
      channels: selectedChannels.length > 0 ? selectedChannels : [2],
      receiver: settingsData.otpReceiver,
      receiverUserIds: settingsData.receiverUserIds,
      isEnabledForAll: settingsData.userOption,
      userIdsToEnableMFA: settingsData.userList,
    };
    this.modalRef.hide();
    this.store.dispatch(new UpdateOTPSettings(payload));
  }

  onSave() {
    let payload: any;

    switch (this.settingType) {
      case 'copyPaste':
        payload = {
          ...this.globalSettings,
          isCopyPasteEnabled: this.securityForm.get('copyPaste').value,
        };
        this.store.dispatch(new UpdateGlobalSettings(payload));
        break;
      case 'screenshot':
        payload = {
          ...this.globalSettings,
          isScreenshotEnabled: this.securityForm.get('screenshot').value,
        };
        this.store.dispatch(new UpdateGlobalSettings(payload));
        break;

      default:
        break;
    }
    this.modalRef.hide();
  }

  closePopup() {
    this.patchValues();
    this.modalRef.hide();
  }
}
