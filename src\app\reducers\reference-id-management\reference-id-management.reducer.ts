import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { DoesRefIdExistSuccess, FetchAllReferenceIdsListSuccess, FetchReferenceExcelUploadedSuccess, FetchReferenceIdCountsSuccess, FetchReferenceListingSourceSuccess, ReferenceExcelUploadSuccess, ReferenceIdManagementActionTypes, UpdateReferencePayload } from './reference-id-management.action';

export type ReferenceIdState = {
    filtersPayload: any,
    referenceIdList: any,
    totalCount: number,
    excelColumnHeading?: any;
    excelUploadedList?: any;
    loaders: {
        excelUploadedList: boolean;
        referenceIdListIsLoading: boolean,
        isCountLoading: boolean,
        isBulkDeleteLoading: boolean,
    }
    listingSources: any;
    counts: any;
    allReferenceIdList: any;
    doesRefIdExists: boolean;
};

const initialState: ReferenceIdState = {
    filtersPayload: {},
    referenceIdList: [],
    totalCount: 0,
    excelColumnHeading: {},
    excelUploadedList: [],
    loaders: {
        excelUploadedList: true,
        referenceIdListIsLoading: true,
        isCountLoading: true,
        isBulkDeleteLoading: true
    },
    listingSources: [],
    counts: {},
    allReferenceIdList: [],
    doesRefIdExists: false
};

const updateLoader = (state: ReferenceIdState, key: keyof ReferenceIdState["loaders"], value: boolean) => ({
    ...state.loaders,
    [key]: value,
});
export function referenceIdReducer(
    state: ReferenceIdState = initialState,
    action: Action
): ReferenceIdState {
    switch (action.type) {
        case ReferenceIdManagementActionTypes.UPDATE_FILTERS_PAYLOAD:
            return {
                ...state,
                filtersPayload: (action as UpdateReferencePayload).payload
            };
        case ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID:
            return {
                ...state,
                loaders: updateLoader(state, "referenceIdListIsLoading", true)
            }
        case ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_SUCCESS:
            return {
                ...state,
                referenceIdList: (action as any).response?.items,
                loaders: updateLoader(state, "referenceIdListIsLoading", false),
                totalCount: (action as any).response?.totalCount
            };
        case ReferenceIdManagementActionTypes.REFERENCE_EXCEL_UPLOAD_SUCCESS:
            return {
                ...state,
                excelColumnHeading: (action as ReferenceExcelUploadSuccess).resp,
            };
        case ReferenceIdManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST:
            return {
                ...state,
                loaders: updateLoader(state, "excelUploadedList", true),
            };
        case ReferenceIdManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS:
            return {
                ...state,
                excelUploadedList: (action as FetchReferenceExcelUploadedSuccess)
                    .response,
                loaders: updateLoader(state, "excelUploadedList", false),
            };
        case ReferenceIdManagementActionTypes.FETCH_LISTING_SOURCE_SUCCESS:
            return {
                ...state,
                listingSources: (action as FetchReferenceListingSourceSuccess).response
            }
        case ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_COUNT:
            return {
                ...state,
                loaders: updateLoader(state, "isCountLoading", true)
            }
        case ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_COUNT_SUCCESS:
            return {
                ...state,
                counts: (action as FetchReferenceIdCountsSuccess).response,
                loaders: updateLoader(state, "isCountLoading", false)
            }
        case ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_LIST_SUCCESS:
            return {
                ...state,
                allReferenceIdList: (action as FetchAllReferenceIdsListSuccess).response
            }
        case ReferenceIdManagementActionTypes.DOES_REFERENCE_ID_EXIST_SUCCESS:
            return {
                ...state,
                doesRefIdExists: (action as DoesRefIdExistSuccess).response,
            };
        case ReferenceIdManagementActionTypes.REFERENCE_BULK_DELETE:
            return {
                ...state,
                loaders: updateLoader(state, "isBulkDeleteLoading", true)
            };
        case ReferenceIdManagementActionTypes.REFERENCE_BULK_DELETE_SUCCESS:
            return {
                ...state,
                loaders: updateLoader(state, "isBulkDeleteLoading", false)
            };
        default:
            return state;
    }
}

export const selectFeature = (state: AppState) => state.referenceIdManagement;

export const getReferenceIdloaders = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.loaders
);

export const getFiltersPayload = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.filtersPayload
);

export const getReferenceColumnHeadings = createSelector(
    selectFeature,
    (state: ReferenceIdState) => {
        return state.excelColumnHeading;
    }
);

export const getReferenceExcelUploadedList = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.excelUploadedList
);

export const getReferenceExcelListLoading = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.excelUploadedList
);

export const getReferenceIds = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.referenceIdList
);


export const getReferenceListingSources = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.listingSources
)

export const getReferenceIdLoaders = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.loaders
)

export const getReferenceIdCounts = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.counts
)

export const getReferenceTotalCounts = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.totalCount
)

export const getAllReferenceIdList = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.allReferenceIdList
)

export const DoesRefNameExist = createSelector(
    selectFeature,
    (state: ReferenceIdState) => state.doesRefIdExists
)
