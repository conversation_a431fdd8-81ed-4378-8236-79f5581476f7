import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE } from 'src/app/app.constants';
import { FileUploadStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  getAssignedToDetails,
  getPages,
  getTimeZoneDate,
} from 'src/app/core/utils/common.util';
import {
  FetchDataExcelUploadedList,
  FetchDataMigrateExcelUploadedList,
} from 'src/app/reducers/data/data-management.actions';
import {
  getDataExcelUploadedList,
  getDataExcelUploadedListIsLoading,
  getDataMigrateExcelUploadedList,
  getDataMigrateExcelUploadedListIsLoading,
} from 'src/app/reducers/data/data-management.reducer';
import {
  FetchExcelUploadedList,
  FetchMigrateExcelUploadedList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getExcelMigrateUploadedListIsLoading,
  getExcelUploadedList,
  getExcelUploadedListIsLoading,
  getMigrateExcelUploadedList,
} from 'src/app/reducers/lead/lead.reducer';
import { FetchExcelTrackerList } from 'src/app/reducers/listing-site/listing-site.actions';
import {
  getAddressExcelList,
  getListingSiteLoaders,
} from 'src/app/reducers/listing-site/listing-site.reducer';
import {
  FetchAgencyExcelUploadedList,
  FetchCampaignExcelUploadedList,
  FetchChannelPartnerExcelUploadedList,
} from 'src/app/reducers/manage-marketing/marketing.action';
import {
  getAgencyExcelUploadedList,
  getAgencyExcelUploadedListIsLoading,
  getCampaignExcelUploadedList,
  getCampaignExcelUploadedListIsLoading,
  getChannelPartnerExcelUploadedList,
  getChannelPartnerExcelUploadedListIsLoading,
} from 'src/app/reducers/manage-marketing/marketing.reducer';
import {
  FetchProjectExcelUploadedList,
  FetchProjectUnitExcelUploadedList,
} from 'src/app/reducers/project/project.action';
import {
  getProjectExcelUploadedList,
  getProjectExcelUploadedListIsLoading,
  getProjectUnitExcelUploadedList,
  getProjectUnitExcelUploadedLoader,
} from 'src/app/reducers/project/project.reducer';
import { FetchPropertyExcelUploadedList } from 'src/app/reducers/property/property.actions';
import {
  getPropertyExcelUploadedList,
  getPropertyExcelUploadedListIsLoading,
} from 'src/app/reducers/property/property.reducer';
import { FetchReferenceExcelUploadedList } from 'src/app/reducers/reference-id-management/reference-id-management.action';
import {
  getReferenceExcelUploadedList,
  getReferenceIdloaders,
} from 'src/app/reducers/reference-id-management/reference-id-management.reducer';
import {
  FetchUserExcelUploadedList,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getUserBasicDetails,
  getUserExcelUploadedList,
  getUserExcelUploadedListIsLoading,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'excel-uploaded-status',
  templateUrl: './excel-uploaded-status.component.html',
})
export class ExcelUploadedStatusComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  rowData: any = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  defaultColDef: any;
  totalUploadedCount: number;
  s3BucketUrl: string = env.s3ImageBucketURL;
  getPages = getPages;
  filtersPayload = {
    pageNumber: 1,
    pageSize: this.pageSize,
    path: 'lead',
  };
  fieldType: string;
  isDataExcelListLoading: boolean = true;
  userData: any;
  allUsers: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private _notificationService: NotificationsService,
    private _translateService: TranslateService,
    public trackingService: TrackingService
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();
  }

  ngOnInit(): void {
    (window as any).copyFileName = this.copyFileName.bind(this);
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.trackingService.trackFeature(
      `Web.${this.fieldType
        ?.replace(/\s+/g, '')
        ?.replace(/^./, (match: any) =>
          match?.toUpperCase()
        )}.Page.ExcelUploadTracker.Visit`
    );

    this._store.dispatch(new FetchUsersListForReassignment());
    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const sortedUsers = data?.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
      });

    if (this.fieldType == 'leads') {
      this._store
        .select(getExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {
          this.isDataExcelListLoading = isLoading;
        });
    }

    if (this.fieldType == 'data') {
      this._store
        .select(getDataExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getDataExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.isDataExcelListLoading = isLoading;
        });
    }

    if (this.fieldType == 'property') {
      this._store
        .select(getPropertyExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {
          this.isDataExcelListLoading = isLoading;
        });
      this._store
        .select(getPropertyExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });
    }

    if (this.fieldType == 'project') {
      this._store
        .select(getProjectExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {
          this.isDataExcelListLoading = isLoading;
        });
      this._store
        .select(getProjectExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });
    }

    if (this.fieldType == 'project-unit') {
      this._store
        .select(getProjectUnitExcelUploadedLoader)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {
          this.isDataExcelListLoading = isLoading;
        });

      this._store
        .select(getProjectUnitExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });
    }

    if (this.fieldType == 'Data Migration') {
      this._store
        .select(getDataMigrateExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getDataMigrateExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.isDataExcelListLoading = isLoading;
        });
    }

    if (this.fieldType == 'Lead Migration') {
      this._store
        .select(getMigrateExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getExcelMigrateUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.isDataExcelListLoading = isLoading;
        });
    }
    if (this.fieldType == 'Agency Name') {
      this._store
        .select(getAgencyExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getAgencyExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.isDataExcelListLoading = isLoading;
        });
    }

    if (this.fieldType == 'Channel partner') {
      this._store
        .select(getChannelPartnerExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getChannelPartnerExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.isDataExcelListLoading = isLoading;
        });
    }

    if (this.fieldType == 'Campaign name') {
      this._store
        .select(getCampaignExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getCampaignExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.isDataExcelListLoading = isLoading;
        });
    }

    if (this.fieldType == 'user') {
      this._store
        .select(getUserExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getUserExcelUploadedListIsLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: any) => {
          this.isDataExcelListLoading = isLoading;
        });
    }

    if (this.fieldType == 'address') {
      this._store
        .select(getAddressExcelList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getListingSiteLoaders)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.isDataExcelListLoading = data?.trackerList;
        });
    }
    if (this.fieldType == 'reference') {
      this._store
        .select(getReferenceExcelUploadedList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalUploadedCount = data?.totalCount;
        });

      this._store
        .select(getReferenceIdloaders)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.isDataExcelListLoading = data?.excelUploadedList;
        });
    }
    this.initializeGridSettings();
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 85,
        valueGetter: (params: any) => [FileUploadStatus[params.data?.status]],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Done By',
        field: 'Done By',
        minWidth: 180,
        valueGetter: (params: any) => [
          getAssignedToDetails(params?.data?.createdBy, this.allUsers, true) ||
          '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap text-truncate-1 break-all">${params.value[0]
            }</p>
            <p class="text-nowrap text-truncate-1 break-all">${params.value[1]
            }</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Total Count',
        field: 'Total Count',
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [params.data?.totalCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Total Uploaded Count',
        field: 'Total Uploaded Count',
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [params.data?.totalUploadedCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Duplicate Count',
        field: 'Duplicate Count',
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [params.data?.duplicateCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Invalid Count',
        field: 'Invalid Count',
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [params.data?.invalidCount],
        cellRenderer: (params: any) => {
          return params.data?.status == 0 || params.data?.status == 1
            ? '--'
            : `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'File Name',
        field: 'File Name',
        minWidth: 200,
        filter: false,
        cellRenderer: this.fileNameCellRenderer.bind(this),
        valueGetter: (params: any) => [params.data?.sheetName],
      },
      {
        headerName: 'Invalid Report/ Error Message',
        field: 'Invalid Report/ Error Message',
        minWidth: 180,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.invalidDataS3BucketKey
            ? this.s3BucketUrl + params.data?.invalidDataS3BucketKey
            : '',
          params.data?.message,
          params.data?.status,
          params.data?.invalidProspectS3BucketKey
            ? this.s3BucketUrl + params.data?.invalidProspectS3BucketKey
            : '',
          params.data?.invalidS3BucketKey
            ? this.s3BucketUrl + params.data?.invalidS3BucketKey
            : '',
        ],
        cellRenderer: (params: any) => {
          let href = '';
          if (this.fieldType === 'Data Migration') {
            href = params.value[3];
          } else if (
            this.fieldType === 'Agency Name' ||
            this.fieldType === 'Channel partner' ||
            this.fieldType == 'Campaign name'
          ) {
            href = params.value[4];
          } else {
            href = params.value[0];
          }
          return href
            ? `<a href="${href}" class="btn btn-xxs btn-linear-green text-nowrap flex-center w-150">
                <span class="icon ic-xxs ic-download"></span>
                <span class="text-white ml-8">Download Report</span></a>`
            : params.value[2] === 4 && params.value[1]
              ? `<p class="text-danger text-truncate-2 text-sm">${params.value[1]}</p>`
              : '--';
        },
      },
    ];
    this.gridOptions.context = {
      componentParent: this,
    };

    if (this.fieldType !== 'project') {
      const uniqueCountColumn = {
        headerName: 'Unique Count',
        field: 'Unique Count',
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.distinctLeadCount,
          params.data?.distinctProspectCount,
          params.data?.distinctUnitCount,
          params?.data?.distinctCount,
        ],
        cellRenderer: (params: any) => {
          if (params.data?.status == 0 || params.data?.status == 1) {
            return '--';
          }
          let valueToShow = '';
          if (
            this.fieldType == 'property' ||
            this.fieldType == 'leads' ||
            this.fieldType == 'Lead Migration' ||
            this.fieldType == 'address' ||
            this.fieldType == 'reference'
          ) {
            valueToShow = params.value[0];
          } else if (this.fieldType == 'project-unit') {
            valueToShow = params.value[2];
          } else if (
            this.fieldType == 'Agency Name' ||
            this.fieldType == 'Channel partner' ||
            this.fieldType == 'Campaign name' ||
            this.fieldType == 'user'
          ) {
            valueToShow = params.value[3];
          } else {
            valueToShow = params.value[1];
          }
          return `<p>${valueToShow}</p>`;
        },
      };
      if (this.fieldType !== 'user') {
        this.gridOptions.columnDefs.splice(3, 0, uniqueCountColumn);
      }
    }
  }

  fileNameCellRenderer(params: any) {
    const fileName = params.value[0] || '';
    if (this.fieldType == 'leads' || this.fieldType == 'Lead Migration' || this.fieldType == 'data' || this.fieldType == 'Data Migration') {
      return `
      <div class="flex-between">
        <p class="text-truncate-1 break-all">${fileName}</p>
        <div title="Copy File Name" class="bg-brown icon-badge" onclick="copyFileName('${params?.data?.s3BucketKey}')">
          <span class="icon ic-copy-clipboard m-auto ic-xxxs"></span>
        </div>
      </div>
    `;
    } else {
      return `<p class="text-truncate-1 break-all">${fileName}</p>`;
    }
  }

  copyFileName(fileName: string) {
    navigator.clipboard?.writeText(fileName);
    this._notificationService.success(
      this._translateService.instant('GLOBAL.link-copied')
    );
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.trackingService.trackFeature(
      `Web.${this.fieldType
        ?.replace(/\s+/g, '')
        .replace(/^./, (match: any) =>
          match.toUpperCase()
        )}.Button.ExcelUploadTrackerpagination.Click`
    );
    this.updateTrackerList();
  }

  updateTrackerList() {
    this.trackingService.trackFeature(
      `Web.${this.fieldType
        ?.replace(/\s+/g, '')
        .replace(/^./, (match: any) =>
          match.toUpperCase()
        )}.Button.ExcelUploadTrackerRefresh.Click`
    );

    if (this.fieldType == 'property') {
      this._store.dispatch(
        new FetchPropertyExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'project') {
      this._store.dispatch(
        new FetchProjectExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'leads') {
      this._store.dispatch(
        new FetchExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'project-unit') {
      this._store.dispatch(
        new FetchProjectUnitExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'Data Migration') {
      this._store.dispatch(
        new FetchDataMigrateExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'Lead Migration') {
      this._store.dispatch(
        new FetchMigrateExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'Agency Name') {
      this._store.dispatch(
        new FetchAgencyExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'Channel partner') {
      this._store.dispatch(
        new FetchChannelPartnerExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'Campaign name') {
      this._store.dispatch(
        new FetchCampaignExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'user') {
      this._store.dispatch(
        new FetchUserExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'address') {
      this._store.dispatch(
        new FetchExcelTrackerList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else if (this.fieldType == 'reference') {
      this._store.dispatch(
        new FetchReferenceExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    } else {
      this._store.dispatch(
        new FetchDataExcelUploadedList(
          this.filtersPayload?.pageNumber,
          this.filtersPayload.pageSize
        )
      );
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
