import { Action } from '@ngrx/store';
import {
  AboutUs,
  Profile,
  Recognition,
  Subscription,
  Testimonial,
} from 'src/app/core/interfaces/profile.interface';

export enum profileActionTypes {
  FETCH_PROFILE = '[PROFILE] Fetch Profile',
  FETCH_PROFILE_SUCCESS = '[PROFILE] Fetch Profile Success',
  FETCH_RECOGNITION = '[PROFILE] Fetch Recognition',
  FETCH_RECOGNITION_SUCCESS = '[PROFILE] Fetch Recognition Success',
  FETCH_TESTIMONIALS = '[TESTIMONIAL] Fetch Testimonials',
  FETCH_TESTIMONIALS_SUCCESS = '[TESTIMONIAL] Fetch Testimonials Success',
  ADD_TESTIMONIAL = '[TESTIMONIAL] Add Testimonial',
  ADD_RECOGNITION = '[RECOGNITION] Add Recognition',
  UPDATE_PROFILE = '[PROFILE] Update Profile',
  UPDATE_TESTIMONIAL = '[TESTIMONIAL] Update Testimonial',
  UPDATE_ABOUT_US = '[PROFILE] Update About Us',
  DELETE_TESTIMONIAL = '[TESTIMONIAL] Delete Testimonial',
  DELETE_RECOGNITION = '[RECOGNITION] Delete Recognition',
  UPDATE_BANNER_IMG = '[PROFILE] Update Banner Image',
  UPDATE_LOGO_IMG = '[PROFILE] Update Logo Image',
  UPDATE_SOCIAL_MEDIA = '[PROFILE] Update Social Media Accounts',
  FETCH_QR_CODE = '[PROFILE] Fetch QR Code',
  FETCH_QR_CODE_SUCCESS = '[PROFILE] Fetch QR Code Success',
  FETCH_TRANSACTION = '[PROFILE] Fetch Transaction',
  FETCH_TRANSACTION_SUCCESS = '[PROFILE] Fetch Transaction Success',
  FETCH_SUBSCRIPTION = '[PROFILE] Fetch subscription',
  FETCH_SUBSCRIPTION_SUCCESS = '[PROFILE] Fetch subscription Success',
  FETCH_TIMEZONE_INFO = '[PROFILE] Fetch Time Zone Info',
  FETCH_TIMEZONE_INFO_SUCCESS = '[PROFILE] Fetch Time Zone Info Success',
  PAYMENT_GATEWAY = '[PROFILE] Payment Gateway',
  PAYMENT_GATEWAY_SUCCESS = '[PROFILE] Payment Gateway Success',
  FETCH_TRANSACTION_INFO = '[PROFILE] Fetch  Transaction Info',
  FETCH_TRANSACTION_INFO_SUCCESS = '[PROFILE] Fetch Transaction Info Success',
  ASSIGN_PAGE_SIZE = '[PROFILE]  Assign PAGE SIZE',
}
export class FetchProfile implements Action {
  readonly type: string = profileActionTypes.FETCH_PROFILE;
  constructor() { }
}

export class FetchProfileSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_PROFILE_SUCCESS;
  constructor(public profile: Profile = {} as Profile) { }
}

export class FetchTestimonial implements Action {
  readonly type: string = profileActionTypes.FETCH_TESTIMONIALS;
  constructor() { }
}

export class FetchTestimonialSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_TESTIMONIALS_SUCCESS;
  constructor(public testimonials: Testimonial[] = [] as Testimonial[]) { }
}
export class FetchRecognition implements Action {
  readonly type: string = profileActionTypes.FETCH_RECOGNITION;
  constructor() { }
}

export class FetchRecognitionSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_RECOGNITION_SUCCESS;
  constructor(public recognitionList: Recognition[] = [] as Recognition[]) { }
}

export class AddTestimonial implements Action {
  readonly type: string = profileActionTypes.ADD_TESTIMONIAL;
  constructor(public payload: Testimonial) { }
}
export class UpdateProfile implements Action {
  readonly type: string = profileActionTypes.UPDATE_PROFILE;
  constructor(public payload: Profile) { }
}

export class UpdateAboutUs implements Action {
  readonly type: string = profileActionTypes.UPDATE_ABOUT_US;
  constructor(public payload: AboutUs) { }
}

export class UpdateTestimonial implements Action {
  readonly type: string = profileActionTypes.UPDATE_TESTIMONIAL;
  constructor(public payload: Testimonial) { }
}

export class DeleteTestimonial implements Action {
  readonly type: string = profileActionTypes.DELETE_TESTIMONIAL;
  constructor(public id: string) { }
}
export class AddRecognition implements Action {
  readonly type: string = profileActionTypes.ADD_RECOGNITION;
  constructor(public payload: Recognition) { }
}
export class DeleteRecognition implements Action {
  readonly type: string = profileActionTypes.DELETE_RECOGNITION;
  constructor(public id: string) { }
}

export class UpdateBannerImg implements Action {
  readonly type: string = profileActionTypes.UPDATE_BANNER_IMG;
  constructor(public url: any) { }
}

export class UpdateLogoImg implements Action {
  readonly type: string = profileActionTypes.UPDATE_LOGO_IMG;
  constructor(public url: string) { }
}

export class UpdateSocialMedia implements Action {
  readonly type: string = profileActionTypes.UPDATE_SOCIAL_MEDIA;
  constructor(public payload: any) { }
}

export class FetchQRCode implements Action {
  readonly type: string = profileActionTypes.FETCH_QR_CODE;
  constructor(public payload?: any) { }
}

export class FetchQRCodeSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_QR_CODE_SUCCESS;
  constructor(public response: any = {}) { }
}

export class FetchTransaction implements Action {
  readonly type: string = profileActionTypes.FETCH_TRANSACTION;
  constructor(public pageNumber?: any, public pageSize?: any) { }
}

export class FetchTransactionSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_TRANSACTION_SUCCESS;
  constructor(public response: any = {}) { }
}

export class FetchSubscription implements Action {
  readonly type: string = profileActionTypes.FETCH_SUBSCRIPTION;
  constructor(public timeZoneInfo: any) { }
}

export class FetchSubscriptionSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_SUBSCRIPTION_SUCCESS;
  constructor(public subscription: Subscription = {} as Subscription) { }
}

export class FetchTransactionInfo implements Action {
  readonly type: string = profileActionTypes.FETCH_TRANSACTION_INFO;
  constructor() { }
}

export class FetchTransactionInfoSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_TRANSACTION_INFO_SUCCESS;
  constructor(public response: any = {}) { }
}

export class AssignPageSize implements Action {
  readonly type: string = profileActionTypes.ASSIGN_PAGE_SIZE;
  constructor(public pageSize: number, public pageNumber: number) { }
}

export class FetchTimeZoneInfo implements Action {
  readonly type: string = profileActionTypes.FETCH_TIMEZONE_INFO;
  constructor() { }
}

export class FetchTimeZoneInfoSuccess implements Action {
  readonly type: string = profileActionTypes.FETCH_TIMEZONE_INFO_SUCCESS;
  constructor(public response: any = {}) { }
}
