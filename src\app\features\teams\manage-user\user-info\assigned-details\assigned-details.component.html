<div class="align-center user-info">
    <div class="activation align-center-col" [ngClass]="{'active' : selectedSection == 'Integration'}"
        (click)="selectedSection = 'Integration';trackingService.trackFeature('Web.UserDetails.Button.AssignedDataIntegration.Click')">
        <span class="ic-connection-solid ic-sm mr-8 ip-mr-4"
            [ngClass]="{'active' : selectedSection !== 'Integration'}"></span>
        <span class="ph-d-none mt-6">Integration</span>
    </div>
    <div class="border-left h-16 mx-20"></div>
    <div class="activation align-center-col" [ngClass]="{'active' : selectedSection == 'Projects'}"
        (click)="userAssignmentList?.['Project']?.length?selectedSection = 'Projects':selectedSection = 'Projects';trackingService.trackFeature('Web.UserDetails.Button.AssignedDataProjects.Click')">
        <span class="ic-buliding ic-sm mr-8 ip-mr-4" [ngClass]="{'active' : selectedSection !== 'Projects'}"></span>
        <span class="ph-d-none mt-6">Projects</span>
    </div>
    <div class="border-left h-16 mx-20"></div>
    <div class="activation align-center-col" [ngClass]="{'active' : selectedSection == 'City'}"
        (click)="selectedSection = 'City';trackingService.trackFeature('Web.UserDetails.Button.AssignedDataCity.Click')">
        <span class="ic-location-map ic-sm mr-8 ip-mr-4" [ngClass]="{'active' : selectedSection !== 'City'}"></span>
        <span class="ph-d-none mt-6"> City</span>
    </div>
    <div class="border-left h-16 mx-20"></div>
    <div class="activation align-center-col" [ngClass]="{'active' : selectedSection == 'Zone'}"
        (click)="selectedSection = 'Zone';trackingService.trackFeature('Web.UserDetails.Button.AssignedDataZone.Click')">
        <span class="ic-compass ic-xxs mr-8 ip-mr-4" [ngClass]="{'active' : selectedSection !== 'Zone'}"></span>
        <span class="ph-d-none mt-6">Zone</span>
    </div>
    <!-- <div class="border-left h-16 mx-20"></div>
    <div class="activation align-center-col" [ngClass]="{'active' : selectedSection == 'Locality'}"
        (click)="selectedSection = 'Locality'">
        <span class="ic-location-solid ic-xxs mr-8 ip-mr-4"
            [ngClass]="{'active' : selectedSection !== 'Locality'}"></span>
        <span class="ph-d-none mt-6">Locality</span>
    </div> -->
</div>
<ng-container *ngIf="selectedSection == 'Integration' && isEmptyIntegration">
    <div class="scrollbar scroll-hide mt-10 h-100-200">
        <ng-container *ngFor="let integration of integrationList">
            <div class="m-12 br-4 border bg-white" *ngIf="isIntegrationType(integration?.name)">
                <div class="p-16 position-relative">
                    <span class="fw-600 text-black-200">{{integration?.displayName}}</span>
                    <div class="border-bottom mt-12"></div>
                    <div class="d-flex flex-wrap fw-600 text-black-200 overflow-auto">
                        <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                            *ngFor="let childType of integrationType(integration?.name)">
                            {{childType?.name}}
                            <span class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"
                                (click)="openDeleteAssignmentModal(childType, 'integration account')"></span>
                        </div>
                    </div>
                </div>
            </div>
        </ng-container>
    </div>
</ng-container>
<ng-container *ngIf="selectedSection == 'Projects' && userAssignmentList?.['Project']?.length">
    <div class="scrollbar scroll-hide mt-10 h-100-200">
        <div class="m-12 br-4 border bg-white">
            <div class="p-16 position-relative">
                <span class="fw-600 text-black-200">Projects</span>
                <div class="border-bottom mt-12"></div>
                <div class="d-flex flex-wrap fw-600 text-black-200 overflow-auto">
                    <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                        *ngFor="let project of userAssignmentList?.['Project']">
                        {{project.name}}
                        <span class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"
                            (click)="openDeleteAssignmentModal(project, 'project')"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="selectedSection == 'City' && userAssignmentList?.['City']?.length">
    <div class="scrollbar scroll-hide mt-10 h-100-200">
        <div class="m-12 br-4 border bg-white">
            <div class="p-16 position-relative">
                <span class="fw-600 text-black-200">City</span>
                <div class="border-bottom mt-12"></div>
                <div class="d-flex flex-wrap fw-600 text-black-200 overflow-auto">
                    <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                        *ngFor="let city of userAssignmentList?.['City']">
                        {{city.name}}
                        <span class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"
                            (click)="openDeleteAssignmentModal(city, 'City')"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="selectedSection == 'Zone' && userAssignmentList?.['Zone']?.length">
    <div class="scrollbar scroll-hide mt-10 h-100-200">
        <div class="m-12 br-4 border bg-white">
            <div class="p-16 position-relative">
                <span class="fw-600 text-black-200">City</span>
                <div class="border-bottom mt-12"></div>
                <div class="d-flex flex-wrap fw-600 text-black-200 overflow-auto">
                    <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                        *ngFor="let zone of userAssignmentList?.['Zone']">
                        {{zone.name}}
                        <span class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"
                            (click)="openDeleteAssignmentModal(zone, 'Zone')"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="selectedSection == 'Locality'">
    <ng-container *ngIf="userAssignmentList?.['Locality']?.length else noData">
        <div class="fw-semi-bold text-normal text-black-100 mt-40">This user is associated with these localities </div>
        <div class="scrollbar scroll-hide table-scrollbar">
            <table class="table standard-table no-vertical-border my-30">
                <thead>
                    <tr class="w-100">
                        <th>Locality Title</th>
                        <th class="w-100px">Action</th>
                    </tr>
                </thead>
                <tbody class="text-secondary fw-semi-bold h-100-315 scrollbar">
                    <ng-container *ngFor="let locality of userAssignmentList?.['Locality']">
                        <tr class="w-100">
                            <td>
                                <p>{{ locality.name }}</p>
                            </td>
                            <td class="w-100px">
                                <div title="Remove Assignment" (click)="openDeleteAssignmentModal(locality, 'Locality')"
                                    class="bg-red-350 br-4 flex-center w-24 h-24 cursor-pointer">
                                    <span class="dot dot-sm bg-white">
                                        <span class="icon ic-cancel ic-red-350 ic-xx-xs"></span></span>
                                </div>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </ng-container>
</ng-container>
<div *ngIf="selectedSection == 'Projects' && !userAssignmentList?.['Project']?.length
      || selectedSection == 'City' && !userAssignmentList?.['City']?.length
      || selectedSection == 'Zone' && !userAssignmentList?.['Zone']?.length
      || selectedSection == 'Integration' && !isEmptyIntegration">
    <div class="flex-col flex-center h-100-260 min-h-250">
        <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">{{'PROFILE.no-data-found' | translate}}</div>
    </div>
</div>