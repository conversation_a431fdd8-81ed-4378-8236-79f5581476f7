import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  matchValidator,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  GetOtp,
  ResetPassword,
  VerifyOtp,
  VerifyUsername,
} from 'src/app/reducers/forgot-password/forgot-password.actions';
import {
  getOtpVerified,
  getUser,
} from 'src/app/reducers/forgot-password/forgot-password.reducers';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'login-forget-password',
  templateUrl: './forgot-password.component.html',
})
export class ForgotPasswordComponent implements On<PERSON><PERSON>roy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  resetPasswordForm: FormGroup;
  showOtp: boolean = false;
  showAccount: boolean = false;
  showForgetPassword: boolean = true;
  otpVerified: boolean = false;
  userData: any;
  userName: string = '';
  isShowPassword: boolean = false;
  otp: any = [null, null, null, null, null, null];
  validateUserName: boolean = false;
  showNewPassword: boolean = false;
  shoConfirmPassword: boolean = false;
  isExpired: boolean = false;
  // minutes: number = 1;
  // seconds: number = 0;
  seconds: number = 30;
  building: AnimationOptions = {
    path: 'assets/animations/building.json',
  };
  online: AnimationOptions = {
    path: 'assets/animations/circle-green.json',
  };
  frontLaptop: AnimationOptions = {
    path: 'assets/animations/front-laptop.json',
  };
  formattedSeconds: string = '30';

  constructor(
    public router: Router,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    public trackingService: TrackingService
  ) {
    const navigation = this.router.getCurrentNavigation();
    this.userName = navigation?.extras.state?.['userName'];

    this.resetPasswordForm = this.fb.group({
      newPassword: ['', [Validators.required]],
      confirmPassword: ['', Validators.required],
    });
    if (this.showForgetPassword) {
      this.trackingService?.trackFeature('Web.Login.Page.ForgotPassword.Visit')
    }
    else if (this.showAccount && this.userData) {
      this.trackingService?.trackFeature('Web.Login.Page.ConfirmDetails.Visit')

    } else if (this.showOtp) {
      this.trackingService?.trackFeature('Web.Login.Page.EnterOTP.Visit')

    } else if (this.otpVerified) {
      this.trackingService?.trackFeature('Web.Login.Page.ChangePassword.Visit')

    }
    this.resetPasswordForm.addValidators(
      matchValidator(
        this.resetPasswordForm.get('newPassword'),
        this.resetPasswordForm.get('confirmPassword')
      )
    );
  }

  navigateToLogin() {
    this.router.navigate(['login']);
  }

  otpClickEvent(e: any, next: string, prev: string) {
    if (e.key === 'Backspace') {
      document.getElementById(prev).innerHTML = null;
      document.getElementById(prev)?.focus();
    } else {
      document.getElementById(next).innerHTML = null;
      document.getElementById(next)?.focus();
    }
  }

  verifyUsername() {
    this._store.dispatch(new VerifyUsername(this.userName));

    this._store
      .select(getUser)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        if (this.userData?.id) {
          this.showAccount = true;
          this.showForgetPassword = false;
        }
      });
  }

  getOtp() {
    let payload = {
      userId: this.userData?.id,
      phoneNumber: this.userData?.phoneNumber,
      email: this.userData?.email,
    };

    this._store.dispatch(new GetOtp(payload));
    this.startTimer();
    this.showOtp = true;
    this.showAccount = false;
    document.getElementById('digit-1')?.focus();
  }

  startTimer() {
    //30 Seconds timer
    const interval = setInterval(() => {
      if (this.seconds === 0) {
        this.isExpired = true;
        clearInterval(interval);
      } else {
        this.formattedSeconds = this.seconds.toString().padStart(2, '0');
        this.seconds--;
      }
    }, 1000);

    //1 Minute timer
    // const interval = setInterval(() => {
    //   if (this.seconds === 0) {
    //     if (this.minutes === 0) {
    //       this.expired = true;
    //       clearInterval(interval);
    //     } else {
    //       this.seconds = 59;
    //       this.minutes--;
    //     }
    //   } else {
    //     this.seconds--;
    //   }
    // }, 1000);
  }

  resendOtp() {
    let payload = {
      userId: this.userData?.id,
      phoneNumber: this.userData?.phoneNumber,
      email: this.userData?.email,
    };
    this.startTimer();
    this.isExpired = false;
    this.seconds = 30;
    this._store.dispatch(new GetOtp(payload));
  }

  verifyOtp() {
    let otp = '';

    this.otp?.forEach((digit: string) => {
      otp += digit;
    });
    let payload = {
      userId: this.userData?.id,
      otp: otp,
    };

    this._store.dispatch(new VerifyOtp(payload));

    this._store
      .select(getOtpVerified)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.otpVerified = data;
        if (data) {
          this.showOtp = false;
        } else {
          document.getElementById('digit-1')?.focus();
        }
      });
  }

  resetPassword() {
    if (!this.resetPasswordForm.valid) {
      validateAllFormFields(this.resetPasswordForm);
      return;
    }

    let otp = '';
    this.otp?.forEach((digit: string) => {
      otp += digit;
    });

    let payload = {
      userId: this.userData?.id,
      password: this.resetPasswordForm.value.newPassword,
      otp: otp,
    };

    this._store.dispatch(new ResetPassword(payload));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
