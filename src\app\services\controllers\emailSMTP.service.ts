import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class EmailSMTPService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'customemail';
  }

  createEmailSMTP(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}`, payload);
  }

  getEmailSMTP(payload?: any) {
    return this.http.get(
      `${this.serviceBaseUrl}?Search=${payload.searchTerm}&PageNumber=${payload.pageNumber}&PageSize=${payload.pageSize}&`
    );
  }

  sendTestEmail(payload: any) {
    const formData: FormData = new FormData();
    formData.append('CurrentUserId', payload.CurrentUserId);
    formData.append('From', payload.From);
    formData.append('ServerName', payload.ServerName);
    formData.append('Port', payload.Port);
    formData.append('UserName', payload.UserName);
    formData.append('Password', payload.Password);
    formData.append('Priority', payload.Priority);
    formData.append('To', payload.To.join(','));
    formData.append('Body', payload.Body);
    formData.append('Subject', payload.Subject);
    if (payload.CC) {
      formData.append('CC', payload.CC);
    }
    if (payload.BCC) {
      formData.append('BCC', payload.BCC);
    }
    const headers = new HttpHeaders();
    headers.append('Content-Type', 'multipart/form-data');

    return this.http.post(`${this.serviceBaseUrl}/send`, formData, { headers });
  }

  sendTestEmailBulk(req: any, Files: any) {
    let formData = new FormData();
    formData.append('RequestInJsonFormatted', JSON.stringify(req));
    const headers = new HttpHeaders();
    for (let file of Files) {
      formData.append('FileAttachments', file);
    }
    headers.append('Content-Type', 'multipart/form-data');
    return this.http.post(`${this.serviceBaseUrl}/send/bulk`, formData, {
      headers,
    });
  }

  editEmailSMTP(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}`, payload);
  }

  deleteEmailSMTP(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/${id}`);
  }

  getEmailSMTPByUserId(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/userids?UserIds=${id}`);
  }
}
