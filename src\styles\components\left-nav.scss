.left-nav-sticky {
  transition: 0.2s ease-in;

  a {
    @extend .d-flex, .cursor-pointer, .text-dark-gray;

    .icon {
      @extend .ml-4;
      opacity: 0.5;

      &__skeleton {
        @extend .br-5;
        height: 18px;
        width: 18px;
      }
    }

    &.active {
      @extend .bg-accent-green-300;

      .icon {
        opacity: 1;
      }
    }

    .nav-text {
      @extend .text-nowrap, .ml-16, .br-5;

      &__skeleton {
        width: 94px;
        height: 18px;
      }
    }

    &.nav-item {
      @extend .px-4, .py-8, .position-relative;

      div:first-child {
        @extend .align-center;
      }

      .sub-navbar {
        @extend .d-none, .brtr-5, .brbr-5;

        ul {
          @extend .brtr-5, .brbr-5;
        }

        .sup-sub-nav {
          @extend .d-none, .brtr-5, .brbr-5;
        }

        a {
          &:hover {
            .sup-sub-nav {
              @extend .bg-coal, .position-absolute, .w-150, .ip-w-80px, .text-white, .d-block;
              // margin-top: -20px;
              top: unset;
              text-align: left;
              box-sizing: border-box;
              z-index: 5;
            }
          }
        }
      }

      .on-hover-label {
        @extend .bg-coal, .p-8, .text-white, .position-fixed, .text-nowrap, .br-8, .left-48;
        z-index: 2;
        margin-top: -2px;
        display: none;

        &::before {
          content: "";
          @extend .d-block, .position-fixed;
          border-top: 8px solid transparent;
          border-bottom: 8px solid transparent;
          border-right: 9px solid $primary-black;
          left: 39px;
        }
      }

      &.active {
        @extend .bg-accent-green-300;

        .nav-text {
          @extend .text-white;
        }

        // .pointer {
        //   &::before {
        //     content: "";
        //     @extend .d-block, .position-fixed, .left-40;
        //     border-top: 8px solid transparent;
        //     border-bottom: 8px solid transparent;
        //     border-left: 9px solid $accent-green-300;
        //   }
        // }
      }

      &:hover {
        @extend .bg-accent-green-200;

        .icon {
          opacity: 1;
        }

        .nav-text {
          @extend .text-white;
        }

        .sub-navbar {
          @extend .bg-coal, .position-fixed, .w-150, .ip-w-100px, .text-white, .d-block;
          margin-top: -8px;
          top: unset;
          text-align: left;
          box-sizing: border-box;
          z-index: 5;

          .sidebar-label {
            @extend .fw-semi-bold, .text-large, .p-8;
          }

          ul {
            @extend .py-4, .bg-black-10;

            a {
              @extend .m-0, .cursor-pointer, .p-8;

              &:hover {
                @extend .bg-accent-green-200, .text-white;

                .icon {
                  opacity: 1;
                }
              }

              &.active {
                @extend .bg-accent-green-300, .text-white;

                .icon {
                  opacity: 1;
                }
              }
            }
          }
        }

        .on-hover-label {
          @extend .d-block;
        }
      }
    }
  }
}