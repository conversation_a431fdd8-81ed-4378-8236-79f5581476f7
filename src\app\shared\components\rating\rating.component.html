<div class="d-flex flex-column">
  <p class="field-label">{{'PROPERTY.ATTRIBUTE.rating' | translate}}</p>
  <form>
    <fieldset class="rating">
      <ng-container *ngFor="let rating of ratings">
        <input type="radio" id="star{{rating.value}}" name="rating" [formControl]="userInputRating" [value]="rating.value"  />
        <label class="full" for="star{{rating.value}}" [title]="rating.dispValue" (click)="emitRating(rating.value)"></label>
      </ng-container>
    </fieldset>
  </form>
</div>