import { Attribute, Component, EventEmitter, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AnimationOptions } from 'ngx-lottie';

import { IMAGES } from 'src/app/app.constants';

@Component({
  selector: 'selected-amenities-list',
  templateUrl: './amenities-list.component.html',
})
export class AmenitiesListComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() presentAmenities: Array<string>;
  @Input() propertyInfo: any;
  @Input() selectedSection: string;
  @Input() isPropertySoldOut: boolean;
  nearby: any[] = [];
  basic: any[] = [];
  featured: any[] = [];
  amenitiesArr: any[] = [];
  AdditionalAttrData: Attribute[];
  activeAmenitiesType: string = 'Basic';
  isView: boolean = false;
  image = IMAGES;
  chevron: AnimationOptions = {
    path: '../../../assets/animations/chevron-down.json',
  };
  mobileView: boolean = false;
  constructor() {
    if (window.screen?.width < 600) {
      this.mobileView = true;
    }
  }
  ngOnInit(): void {
    this.AdditionalAttrData = this.propertyInfo?.attributes?.filter(
      (item: any) => item.attributeType === 'Additional'
    ).sort((a: any, b: any) =>
      a.attributeDisplayName.localeCompare(b.attributeDisplayName)
    );

    this.amenitiesArr = [];

    this.presentAmenities?.forEach((item: any) => {
      let categoryExists = this.amenitiesArr.find(amenity => amenity[0] === item.category);
      if (categoryExists) {
        categoryExists[1].push(item);
        categoryExists[1].sort((a: any, b: any) => a.amenityDisplayName.localeCompare(b.amenityDisplayName));
      } else {
        this.amenitiesArr.push([item.category, [item]]);
      }
    });
  }

  getFirstCharacter(name: string) {
    return name?.charAt(0).toUpperCase();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
