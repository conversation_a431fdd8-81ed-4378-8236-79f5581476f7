import { Action } from '@ngrx/store';
import { FetchResponse } from 'src/app/reducers/lead/lead.reducer';

export enum WhatsappActionTypes {
  FETCH_WHATSAPP_TEMPLATE = '[WHATSAPP] Fetch Whatsapp Template',
  FETCH_WHATSAPP_TEMPLATE_SUCCESS = '[WHATSAPP] Fetch Whatsapp Template Success',
  ADD_WHATSAPP_TEMPLATE = '[WHATSAPP] Add Whatsapp Template',
  DELETE_WHATSAPP_TEMPLATE = '[WHATSAPP] Delete Whatsapp Template',
  UPDATE_WHATSAPP_TEMPLATE = '[WHATSAPP] Update Whatsapp Template',
  FETCH_24HR_VALIDATION = '[WHATSAPP] Fetch 24 Hours Validation',
  FETCH_24HR_VALIDATION_SUCCESS = '[WHATSAPP] Fetch 24 Hours Validation Success',
  FETCH_CONVERSATION = '[WHATSAPP] Fetch Conversation',
  FETCH_CONVERSATION_SUCCESS = '[WHATSAPP] Fetch Conversation Success',
  FETCH_WHATSAPP_LIST = '[WHATSAPP] Fetch WhatsApp List',
  FETCH_WHATSAPP_LIST_SUCCESS = '[WHATSAPP] Fetch WhatsApp List Success',
  FETCH_WHATSAPP_CHILD_LIST = '[WHATSAPP] Fetch WhatsApp Child List',
  FETCH_WHATSAPP_CHILD_LIST_SUCCESS = '[WHATSAPP] Fetch WhatsApp Child List Success',
  UPDATE_FILTER_PAYLOAD = '[WHATSAPP] Update Filter Payload',
}

export class FetchWhatsappTemplate implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_WHATSAPP_TEMPLATE;
  constructor() { }
}
export class FetchWhatsappTemplateSuccess implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_WHATSAPP_TEMPLATE_SUCCESS;
  constructor(public resp: string = '') { }
}
export class DeleteWhatsappTemplate implements Action {
  readonly type: string = WhatsappActionTypes.DELETE_WHATSAPP_TEMPLATE;
  constructor(public id: string) { }
}
export class AddWhatsappTemplate implements Action {
  readonly type: string = WhatsappActionTypes.ADD_WHATSAPP_TEMPLATE;
  constructor(public payload: any) { }
}
export class UpdateWhatsappTemplate implements Action {
  readonly type: string = WhatsappActionTypes.UPDATE_WHATSAPP_TEMPLATE;
  constructor(public payload: any) { }
}

export class Fetch24HrValidation implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_24HR_VALIDATION;
  constructor(public payload: any) { }
}
export class Fetch24HrValidationSuccess implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_24HR_VALIDATION_SUCCESS;
  constructor(public resp: boolean = false) { }
}

export class FetchConversation implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_CONVERSATION;
  constructor(public payload: {}) { }
}
export class FetchConversationSuccess implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_CONVERSATION_SUCCESS;
  constructor(public resp: any = []) { }
}

export class FetchWhatsappList implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_WHATSAPP_LIST;
  constructor(public filtersPayload: any = {}) { }
}
export class FetchWhatsappListSuccess implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_WHATSAPP_LIST_SUCCESS;
  constructor(public response: FetchResponse = {}) { }
}

export class UpdateFilterPayload implements Action {
  readonly type: string = WhatsappActionTypes.UPDATE_FILTER_PAYLOAD;
  constructor(public filter: any) { }
}

export class FetchWhatsappChildList implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_WHATSAPP_CHILD_LIST;
  constructor(public payload: any = {}) { }
}
export class FetchWhatsappChildListSuccess implements Action {
  readonly type: string = WhatsappActionTypes.FETCH_WHATSAPP_CHILD_LIST_SUCCESS;
  constructor(public response: FetchResponse = {}) { }
}