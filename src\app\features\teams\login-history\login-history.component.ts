import { Component, EventEmitter, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getPages,
  getTenantName,
  getTimeZoneDate,
  onPickerOpened
} from 'src/app/core/utils/common.util';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'teams-login-history',
  templateUrl: './login-history.component.html',
})
export class LoginHistoryComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  rowData: any = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  defaultColDef: any;
  users: any[] = [];
  records: any;
  getPages = getPages;
  date: any;
  payload: any = {
    path: 'analytics',
    pageNumber: 1,
    pageSize: 10,
    fromDate: null,
    toDate: null,
  };
  userData: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;

  constructor(
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>
  ) { }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Login History');
    this.headerTitle.setLangTitle('SIDEBAR.login-history');

    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();

    this._store.dispatch(new FetchUsersListForReassignment());
    this.payload = {
      ...this.payload,
      tenantId: getTenantName(),
    };
    // this._store.dispatch(new FetchAnalyticsRecord(this.payload));

    this._store.select(getUsersListForReassignment).subscribe((data: any) => {
      this.users = data;
      this.users = this.users?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        };
        return user;
      });
    });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    // this._store.select(getRecords).subscribe((data: any) => {
    //   this.rowData = data;
    // });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'User Name',
        field: 'User Name',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data.username],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Time',
        field: 'Time',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [
          getTimeZoneDate(
            params.data.createdOn,
            this.userData?.timeZoneInfo?.baseUTcOffset,
            'fullDateTime'
          ),
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Device',
        field: 'Device',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [
          params.data.deviceType,
          params.data.device,
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0]} (${params.value[1]})</p>`;
        },
      },
      {
        headerName: 'OS',
        field: 'OS',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data.osVersion, params.data.os],
        cellRenderer: (params: any) => {
          return `<p>${params.value[1]} v${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'IP Address',
        field: 'IP Address',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data.ipAddress],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Browser',
        field: 'Browser',
        minWidth: 150,
        filter: false,
        valueGetter: (params: any) => [
          params.data.browser,
          params.data.browserVersion,
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value[0] ? params.value[0] : ''}${params.value[1] ? ' v' + params.value[1] : ''
            }</p>`;
        },
      },
      {
        headerName: 'Platform',
        field: 'Platform',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data.platform],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.gridApi.paginationGoToPage(e);
    this.payload = {
      ...this.payload,
      pageNumber: e + 1,
    };
    // this._store.dispatch(new FetchAnalyticsRecord(this.payload));
  }
}
