import { Action } from '@ngrx/store';

export enum CustomSubStatusActionTypes {

    UPDATE_CUSTOM_SUB_STATUS = '[SUB STATUS] Update Custom Sub Status',
    FETCH_SUB_STATUS_LIST = '[SUB STATUS] Fetch Sub Status List',
    FETCH_SUB_STATUS_SUCCESS_LIST = '[SUB STATUS] Fetch Sub Status List Success',
    DELETE_SUB_STATUS = '[SUB STATUS] Delete Sub Status',
    CREATE_SUB_STATUS = '[SUB STATUS] Create Sub Status',
    FETCH_LEAD_COUNT = '[SUB STATUS] Fetch Leads Count',
    FETCH_LEAD_COUNT_SUCCESS = '[SUB STATUS] Fetch Leads Count Success',
    EXIST_SUB_STATUS = '[SUB STATUS] Exists Sub Status',
    EXIST_SUB_STATUS_SUCCESS = '[SUB STATUS] Exists Sub Status Success'
}

export class UpdateCustomSubStatus implements Action {
    readonly type: string = CustomSubStatusActionTypes.UPDATE_CUSTOM_SUB_STATUS;
    constructor(public payload: any) { }
}


export class FetchSubStatus implements Action {
    readonly type: string = CustomSubStatusActionTypes.FETCH_SUB_STATUS_LIST;
    constructor() { }
}

export class FetchSubStatusSuccess implements Action {
    readonly type: string = CustomSubStatusActionTypes.FETCH_SUB_STATUS_SUCCESS_LIST;
    constructor(public response: any = {}) { }
  }

  export class DeleteSubStatus implements Action {
    readonly type: string = CustomSubStatusActionTypes.DELETE_SUB_STATUS;
    constructor(public id: string) { }
  }

  export class CreateSubStatus implements Action {
    readonly type: string = CustomSubStatusActionTypes.CREATE_SUB_STATUS;
    constructor(public payload: any) { }
  }

  export class ExistSubStatus implements Action {
    readonly type: string = CustomSubStatusActionTypes.EXIST_SUB_STATUS;
    constructor(public subStatus: string) { }
  }

  export class ExistSubStatusSuccess implements Action {
    readonly type: string = CustomSubStatusActionTypes.EXIST_SUB_STATUS_SUCCESS;
    constructor(public response: boolean) { }
  }

  export class FetchLeadCount implements Action {
    readonly type: string = CustomSubStatusActionTypes.FETCH_LEAD_COUNT;
    constructor() { }
}

export class FetchLeadCountSuccess implements Action {
    readonly type: string = CustomSubStatusActionTypes.FETCH_LEAD_COUNT_SUCCESS;
    constructor(public response: any = {}) { }
  }
