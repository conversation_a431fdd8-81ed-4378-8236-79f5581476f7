<div class="flex-col">
  <webcam [trigger]="triggerObservable" (imageCapture)="handleImage($event)" (initError)="errors.push($event)"></webcam>
  <div
    *ngIf="(isClockInMode && !isMandatorySelfieClockin) || (!isClockInMode && !isMandatorySelfieClockout); else onlyCapturePhoto"
    class="w-100 flex-center gap-4 pt-4 pb-6">
    <div class="btn-gray" (click)="bsModalRef.hide()">{{ 'BUTTONS.skip' | translate }}</div>
    <div class="btn btn-sm btn-linear-green" (click)="triggerSnapshot()">Capture Photo</div>
  </div>
  <ng-template #onlyCapturePhoto>
    <div class="flex-center btn btn-sm btn-linear-green br-0" (click)="triggerSnapshot()">Capture Photo</div>
  </ng-template>
</div>