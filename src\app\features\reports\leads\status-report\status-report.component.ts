import {
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, firstValueFrom, map, skipWhile, switchMap, take, takeUntil } from 'rxjs';

import * as moment from 'moment';
import { AnimationOptions } from 'ngx-lottie';
import {
  PAGE_SIZE,
  REPORTS_DATE_TYPE,
  REPORT_FILTERS_KEY_LABEL,
  SHOW_ENTRIES,
  USER_VISIBILITY,
} from 'src/app/app.constants';
import {
  IntegrationSource,
  LeadSource,
  ReportDateType,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ReportsFilter } from 'src/app/core/interfaces/reports.interface';
import {
  assignToSort,
  changeCalendar,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  getTotalCountForReports,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
  snakeToCamel,
} from 'src/app/core/utils/common.util';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchLeadCities,
  FetchLeadCountries,
  FetchLeadStates,
  FetchProjectList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getIsLeadCustomStatusEnabled,
  getLeadCities,
  getLeadCitiesIsLoading,
  getLeadCountries,
  getLeadCountriesIsLoading,
  getLeadStates,
  getLeadStatesIsLoading,
  getProjectList,
  getProjectListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchReportsCustomUser,
  FetchReportsUser,
  FetchUserExportSuccess,
  UpdateUserFilterPayload,
} from 'src/app/reducers/reports/reports.actions';
import {
  getReportsCustomUsersList,
  getReportsCustomUsersListIsLoading,
  getReportsCustomUsersListTotalCount,
  getReportsUsersList,
  getReportsUsersListIsLoading,
  getUserFiltersPayload,
} from 'src/app/reducers/reports/reports.reducer';
import {
  CustomStatus,
  getCustomStatusList,
  getCustomStatusListIsLoading,
} from 'src/app/reducers/status/status.reducer';
import {
  FetchOnlyReporteesWithInactive,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getOnlyReporteesWithInactive,
  getOnlyReporteesWithInactiveIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment } from 'src/environments/environment';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'status-report',
  templateUrl: './status-report.component.html',
})
export class StatusReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  leadSources: Array<any> = [];
  columnDropDown: { field: string; hide: boolean }[] = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  pageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  currentView: 'table' | 'graph' = 'table';
  projectList: any;
  rowData: Array<any> = [];
  filtersPayload: ReportsFilter;
  appliedFilter: any;
  filteredColumnDefsCache: any[] = [];
  canExportAllUsers: boolean = false;
  canViewAllUsers: boolean = false;
  canViewReportees: boolean = false;
  canExportReportees: boolean = false;
  userTotalCount: number;
  getPages = getPages;
  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 4);
  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);
  subSourceList: any;
  allSubSourceList: any;
  allUsers: Array<any> = [];
  onlyReportees: Array<any> = [];
  users: Array<any> = [];
  reportees: Array<any> = [];
  showLeftNav: boolean = true;
  isStatusReportLoading: boolean = true;
  isAllUsersLoading: boolean = true;
  isOnlyReporteesLoading: boolean = true;
  allSubSourceListIsLoading: boolean = true;
  isProjectListLoading: boolean = true;
  customStatusList: CustomStatus[] = [];
  isCustomStatusListLoading: boolean = true;
  isSourcesLoading: boolean = true;
  showFilters: boolean = false;
  moment = moment;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;
  cities: string[];
  citiesIsLoading: boolean = true;
  states: string[];
  statesIsLoading: boolean = true;
  columns: any[];
  defaultColumns: any[];
  canShowPercentage: boolean;
  countryList: any[];
  countryIsLoading: boolean = true;


  isCustomStatusEnabled: boolean = false;
  isGlobalSettingsLoading: boolean = true;
  isCustomStatusReportLoading: boolean = true;
  snakeToCamel = snakeToCamel;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  onPickerOpened = onPickerOpened;
  userData: any;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  globalSettingsData: any;

  @ViewChild('reportsGraph') reportsGraph: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    private router: Router,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private shareDataService: ShareDataService
  ) { }
  async ngOnInit() {
    this.globalSettingsData = await firstValueFrom(
      this._store
        .select(getGlobalSettingsAnonymous)
        .pipe(skipWhile((data) => !Object.keys(data).length))
    );
    this.headerTitle.setTitle('Leads - Status Report');
    this.metaTitle.setTitle('CRM | Reports');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
        this.updateSubSource()
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    await this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    this.isCustomStatusEnabled = await this._store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      )
      .toPromise();

    this._store
      .select(getUserFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = { ...data, isNavigatedFromReports: true };
        this.pageSize = this.filtersPayload?.pageSize;
        const userStatus =
          this.filtersPayload?.userStatus === undefined
            ? 1
            : this.filtersPayload?.userStatus;
        this.appliedFilter = {
          ...this.appliedFilter,
          pageNumber: this.filtersPayload?.pageNumber,
          pageSize: this.filtersPayload?.pageSize,
          userStatus: userStatus,
          visibility: this.filtersPayload?.userStatus,
          dateType: ReportDateType[Number(this.filtersPayload?.dateType)],
          date: [
            patchTimeZoneDate(
              this.filtersPayload?.fromDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
            patchTimeZoneDate(
              this.filtersPayload?.toDate,
              this.userData?.timeZoneInfo?.baseUTcOffset
            ),
          ],
          withTeam: this.filtersPayload?.IsWithTeam,
          users: this.filtersPayload?.UserIds,
          search: this.filtersPayload?.SearchText,
          sources: this.filtersPayload?.Sources,
          subSources: this.filtersPayload?.SubSources,
          projects: this.filtersPayload?.Projects,
          cities: this.filtersPayload?.Cities,
          states: this.filtersPayload?.States,
          ShouldShowAll: this.filtersPayload?.ShouldShowAll ?? true,
          shouldShowPercentage: this.filtersPayload?.ShouldShowPercentage,
        };
      });
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.users = usersData;
          this.allUsers = usersData;
          this.allUsers = assignToSort(this.allUsers, '');
          return this._store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(
        takeUntil(this.stopper),
        switchMap((data: any) => {
          const usersData = data?.map((user: any) => {
            user = {
              ...user,
              fullName: user.firstName + ' ' + user.lastName,
            };
            return user;
          });
          this.reportees = usersData;
          this.onlyReportees = usersData;
          this.onlyReportees = assignToSort(this.onlyReportees, '');
          return this._store
            .select(getOnlyReporteesWithInactiveIsLoading)
            .pipe(takeUntil(this.stopper));
        })
      )
      .subscribe((isLoading: boolean) => {
        this.isOnlyReporteesLoading = isLoading;
        if (!isLoading) {
          this.currentVisibility(1, false);
        }
      });
    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProjectListLoading = isLoading;
      });
    this._store
      .select(getReportsUsersListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isStatusReportLoading = isLoading;
      });
    this._store
      .select(getReportsCustomUsersListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isCustomStatusReportLoading = isLoading;
      });
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExportAllUsers = permissionsSet.has(
          'Permissions.Reports.ExportAllUsers'
        );
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Reports.ViewAllUsers'
        );
        this.canExportReportees = permissionsSet.has(
          'Permissions.Reports.ExportReportees'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Reports.ViewReportees'
        );
        if (this.canViewAllUsers) {
          this._store.dispatch(new FetchUsersListForReassignment());
        } else if (this.canViewReportees) {
          this._store.dispatch(new FetchOnlyReporteesWithInactive());
        }
      });
    this._store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
        this.updateSubSource()
      });
    this._store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.allSubSourceListIsLoading = isLoading;
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.initializeGridSettings();
    this.initializeGraphData();

    if (this.isCustomStatusEnabled) {
      this._store
        .select(getReportsCustomUsersList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data.map((row: any) => {
            let statuses: any = {};
            row?.status?.forEach((status: any) => {
              statuses[status?.statusDisplayName] = status?.count || 0;
              statuses[status?.statusDisplayName + '__percentage__'] =
                `${status?.percentage}` || '';
            });
            return {
              ...row,
              statuses,
            };
          });
          let totalRow: any = {
            userName: 'Total',
            projectTitle: 'Total',
            statuses: {},
          };
          this.rowData.forEach((row: any) => {
            for (let key in row?.statuses) {
              if (!totalRow?.statuses?.[key]) {
                totalRow.statuses[key] = 0;
              }
              if (!key.includes('__percentage__'))
                totalRow.statuses[key] += row?.statuses?.[key] || 0;
            }
          });
          if (this.rowData?.length > 1) {
            this.rowData?.push(totalRow);
          }
        });
      this._store
        .select(getReportsCustomUsersListTotalCount)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.userTotalCount = data;
        });
    } else
      this._store
        .select(getReportsUsersList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = getTotalCountForReports(data.items);
          this.userTotalCount = data.totalCount;
        });

    if (this.isCustomStatusEnabled) this.fetchCustomStatuses();
    this.selectedPageSize = 50;
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterFunction();
    });

    this._store
      .select(getLeadCities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.cities = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCitiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.citiesIsLoading = data;
      });
    this._store
      .select(getLeadStates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.states = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadStatesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.statesIsLoading = data;
      });

    this._store
      .select(getLeadCountries)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this._store
      .select(getLeadCountriesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.countryIsLoading = data;
      });
  }

  fetchCustomStatuses() {
    this._store
      .select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus;
      });

    this._store
      .select(getCustomStatusListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isCustomStatusListLoading = isLoading;
        if (!isLoading) this.initializeGridSettings();
        this.initializeGraphData();
      });
  }

  initializeGraphData() {
    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(
      (col: any) => col.field !== 'User Name' && col.field !== 'General Manager' && col.field != 'Reporting Manager'
    );
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    const nameAndLeads = [
      {
        headerName: 'User Name',
        field: 'User Name',
        pinned: window.innerWidth > 480 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [
          params.data?.userName
        ],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center text-truncate"><p>${params.value[0]}
            </p></div>`;
        },
      },
      {
        headerName: 'General Manager',
        field: 'General Manager',
        hide: false,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [params.data?.generalManager],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center text-truncate"><p>${params.value[0] || ''
            }
            </p></div>`;
        },
      },
      {
        headerName: 'Reporting Manager',
        field: 'Reporting Manager',
        hide: false,
        cellClass: 'lock-pinned',
        valueGetter: (params: any) => [params.data?.reportingManager],
        minWidth: 180,
        cellRenderer: (params: any) => {
          return `<div class="py-16 align-center text-truncate"><p>${params.value[0] || ''
            }
            </p></div>`;
        },
      },
      {
        headerName: 'All Leads',
        field: 'All Leads',
        filter: false,
        valueGetter: (params: any) => [
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.AllCount
            : params.data?.allCount,
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.ActiveCount
            : params.data?.activeCount,
          params?.data?.userId,
          params?.data?.projectTitle,
        ],
        valueLabels: ['All Leads', 'Active Leads'],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `${params?.value?.[3] == 'Total' || params.value[0] == 0
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`
            }
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != 'Total'
              ? `<a>${params.value[1]}</a>`
              : params.value[1]
                ? params.value[1]
                : '--'
            }<span>
          </p>`
            : `${params?.value?.[3] == 'Total' || params.value[0] == 0
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : `<p><span >${params.value[0] ? params.value[0] : '--'
              }</span></p>`
            }
          <p class="text-truncate"><span class="text-dark-gray">active: </span>
          <span class="fw-600">${params.value[1] && params?.value?.[3] != 'Total'
              ? `<span>${params.value[1]}</span>`
              : params.value[1]
                ? params.value[1]
                : '--'
            }<span>
          </p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.event.target.innerText == event.value[0]) {
            if (isCtrlClick) {
              this.getDataInNewTab('All Leads', params);
              return;
            }
            this.getDataFromCell('All Leads', event);
          } else if (event.event.target.innerText == event.value[1]) {
            if (isCtrlClick) {
              this.getDataInNewTab('Active Leads', params);
              return;
            }
            this.getDataFromCell('Active Leads', event);
          }
        },
      },
    ];
    const newAndPending: any = [
      {
        headerName: 'New',
        field: 'New',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.newCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage ? params.data?.newCountPercentage : '',
        ],
        minWidth: 70,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('New', params);
              return;
            }
            this.getDataFromCell('New', event);
          }
        },
      },
      {
        headerName: 'Pending',
        field: 'Pending',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.pendingCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage ? params.data?.pendingCountPercentage : '',
        ],
        minWidth: 80,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Pending', params);
              return;
            }
            this.getDataFromCell('Pending', event);
          }
        },
      },
    ];
    const overdue: any = [
      {
        headerName: 'Overdue',
        field: 'Overdue',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.OverdueCount
            : params.data?.overdueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? this.isCustomStatusEnabled
              ? params?.data?.statuses?.OverdueCount__percentage__
              : params.data?.overdueCount__percentage__
            : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Overdue', params);
              return;
            }
            this.getDataFromCell('Overdue', event);
          }
        },
      },
    ];
    const callbackAndMS: any = [
      {
        headerName: 'Callback',
        field: 'Callback',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.callbackCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage ? params.data?.callbackCountPercentage : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`
              : `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Callback', params);
              return;
            }
            this.getDataFromCell('Callback', event);
          }
        },
      },
      {
        headerName: 'Meeting Scheduled',
        field: 'Meeting Scheduled',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.meetingScheduledCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.meetingScheduledCountPercentage
            : '',
        ],
        minWidth: 140,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a ><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></a>`
            : `<span><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Meeting Scheduled', params);
              return;
            }
            this.getDataFromCell('Meeting Scheduled', event);
          }
        },
      },
    ];
    const meetingDoneAndNotDone: any = [
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.MeetingDoneCount
            : params.data?.meetingDoneCount,
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.MeetingDoneUniqueCount
            : params.data?.meetingDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? this.isCustomStatusEnabled
              ? params?.data?.statuses?.MeetingDoneUniqueCount__percentage__
              : params.data?.meetingDoneUniqueCount__percentage__
            : '',
        ],
        minWidth: 120,
        valueLabels: ['Meeting Done', 'Meeting Done (Unquie Count)'],
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a ><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab('All Leads', params, 'Meeting Done');
              return;
            }
            this.getMeetingCount('All Leads', event, 'Meeting Done');
          }
        },
      },
      {
        headerName: 'Meeting Not Done',
        field: 'Meeting Not Done',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.MeetingNotDoneCount
            : params.data?.meetingNotDoneCount,
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.MeetingNotDoneUniqueCount
            : params.data?.meetingNotDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? this.isCustomStatusEnabled
              ? params?.data?.statuses?.MeetingNotDoneUniqueCount__percentage__
              : params.data?.meetingNotDoneUniqueCount__percentage__
            : '',
        ],
        minWidth: 150,
        valueLabels: ['Meeting Not Done', 'Meeting Not Done (Unquie Count)'],
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Meeting Not Done'
              );
              return;
            }
            this.getMeetingCount('All Leads', event, 'Meeting Not Done');
          }
        },
      },
    ];
    const svs: any = [
      {
        headerName: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        field: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.siteVisitScheduledCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.siteVisitScheduledCountPercentage
            : '',
        ],
        valueLabels: [
          this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',
          (this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled') + ' (unique count)'
        ], 
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a ><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></a>`
            : `<span><p>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', params);
              return;
            }
            this.getDataFromCell(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', event);
          }
        },
      },
    ];
    const siteVisitDoneAndNotDone: any = [
      {
        headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.SiteVisitDoneCount
            : params.data?.siteVisitDoneCount,
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.SiteVisitDoneUniqueCount
            : params.data?.siteVisitDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? this.isCustomStatusEnabled
              ? params?.data?.statuses?.SiteVisitDoneUniqueCount__percentage__
              : params.data?.siteVisitDoneUniqueCount__percentage__
            : '',
        ],
        minWidth: 120,
        valueLabels: [
          !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
          (!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken') + ' (unique count)'
        ],
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Site Visit Done'
              );
              return;
            }
            this.getMeetingCount('All Leads', event, 'Site Visit Done');
          }
        },
      },
      {
        headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',
        field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.SiteVisitNotDoneCount
            : params.data?.siteVisitNotDoneCount,
          this.isCustomStatusEnabled
            ? params?.data?.statuses?.SiteVisitNotDoneUniqueCount
            : params.data?.siteVisitNotDoneUniqueCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? this.isCustomStatusEnabled
              ? params?.data?.statuses
                ?.SiteVisitNotDoneUniqueCount__percentage__
              : params.data?.siteVisitNotDoneUniqueCount__percentage__
            : '',
        ],
        valueLabels: [
          !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',
          (!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken') + ' (unique count)'
        ],
        minWidth: 150,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return !this.isCustomStatusEnabled
            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></a>`
            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>
            <p class="text-truncate"><span class="text-dark-gray">unique: </span><span class="fw-600">${params.value[1]
              ? params.value[1] +
              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')
              : '--'
            }<span></p></span>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getMeetingCountnewTab(
                'All Leads',
                params,
                'Site Visit Not Done'
              );
              return;
            }
            this.getMeetingCount('All Leads', event, 'Site Visit Not Done');
          }
        },
      },
    ];
    const others: any = [
      {
        headerName: 'Booked',
        field: 'Booked',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.bookedCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage ? params.data?.bookedCountPercentage : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total' || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Booked', params);
              return;
            }
            this.getDataFromCell('Booked', event);
          }
        },
      },
      {
        headerName: 'Invoiced',
        field: 'Invoiced',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.invoicedLeadsCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.invoicedLeadsCountPercentage
            : '',
        ],
        minWidth: 110,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : `<p><a>${params.value[0]
              ? params.value[0] +
              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
              : '--'
            }</a></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total' || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Invoiced', params);
              return;
            }
            this.getDataFromCell('Invoiced', event);
          }
        },
      },
      {
        headerName: 'Booking Cancel',
        field: 'Booking Cancel',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.bookingCancelCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params?.data?.bookingCancelCountPercentage
            : '',
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total' || event.value[0] == 0) {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Booking Cancel', params);
              return;
            }
            this.getDataFromCell('Booking Cancel', event);
          }
        },
      },
      {
        headerName: 'Not Interested',
        field: 'Not Interested',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.notInterestedCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.notInterestedCountPercentage
            : '',
        ],
        minWidth: 120,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Not Interested', params);
              return;
            }
            this.getDataFromCell('Not Interested', event);
          }
        },
      },
      {
        headerName: 'Dropped',
        field: 'Dropped',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.droppedCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage ? params.data?.droppedCountPercentage : '',
        ],
        minWidth: 80,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return params?.value?.[2] == 'Total' || params.value[0] == 0
            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
            : !this.isCustomStatusEnabled
              ? `<p><a>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</a></p>`
              : `<p><span>${params.value[0]
                ? params.value[0] +
                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                : '--'
              }</span></p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              this.getDataInNewTab('Dropped', params);
              return;
            }
            this.getDataFromCell('Dropped', event);
          }
        },
      },
      {
        headerName: 'Expression Of Interest',
        field: 'Expression Of Interest',
        filter: false,
        hide: false,
        valueGetter: (params: any) => [
          params.data?.expressionOfInterestLeadCount,
          params?.data?.userId,
          params?.data?.projectTitle,
          this.canShowPercentage
            ? params.data?.expressionOfInterestLeadCountPercentage
            : '',
        ],
        minWidth: 160,
        cellRenderer: (params: any) => {
          const filters = { ...this.filtersPayload };
          if (filters?.IsWithTeam) filters.IsWithTeam = false;
          return `<p>${params.value[0]
            ? params.value[0] +
            (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
            : '--'
            }</p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const isCtrlClick = event?.event?.ctrlKey;
          const params = { value: event?.value, data: event?.data };
          if (event.data.projectTitle == 'Total') {
            return;
          } else if (event.value[0] != 0) {
            if (isCtrlClick) {
              const filters = { ...this.filtersPayload };
              if (filters?.IsWithTeam) filters.IsWithTeam = false;
              window?.open(
                `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[0]
                }&data=${encodeURIComponent(
                  JSON.stringify(params?.data)
                )}&operation=Dropped&filtersPayload=${encodeURIComponent(
                  JSON.stringify(filters)
                )}`,
                '_blank'
              );
              return;
            }
            this.getDataFromCell('Expression Of Interest', event);
          }
        },
      },
    ];
    this.gridOptions.columnDefs = this.isCustomStatusEnabled
      ? [
        ...nameAndLeads,
        // ...newAndPending,
        ...overdue,
        // ...callbackAndMS,
        ...meetingDoneAndNotDone,
        // ...svs,
        ...siteVisitDoneAndNotDone,
        // ...others
      ]
      : [
        ...nameAndLeads,
        ...newAndPending,
        ...overdue,
        ...callbackAndMS,
        ...meetingDoneAndNotDone,
        ...svs,
        ...siteVisitDoneAndNotDone,
        ...others,
      ];
    if (this.isCustomStatusEnabled)
      this.customStatusList.forEach((customStatus: CustomStatus) => {
        let col: any = {
          headerName: customStatus?.displayName,
          field: customStatus?.displayName,
          filter: false,
          hide: false,
          valueGetter: (params: any) => [
            params?.data?.statuses?.[customStatus?.displayName],
            params?.data?.userId,
            params?.data?.projectTitle,
            this.canShowPercentage
              ? params?.data?.statuses?.[
              customStatus?.displayName + '__percentage__'
              ]
              : '',
          ],
          minWidth: 110,
          cellRenderer: (params: any) => {
            const filters = { ...this.filtersPayload };
            if (filters?.IsWithTeam) filters.IsWithTeam = false;
            return params?.value?.[2] == 'Total' || params.value[0] == 0
              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`
              : !this.isCustomStatusEnabled
                ? `<p><a>${params.value[0]
                  ? params.value[0] +
                  (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                  : '--'
                }</a></p>`
                : `<p><span>${params.value[0]
                  ? params.value[0] +
                  (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')
                  : '--'
                }</span></p>`;
          },
          cellClass: 'cursor-pointer',
          onCellClicked: (event: any) => {
            const isCtrlClick = event?.event?.ctrlKey;
            const params = { value: event?.value, data: event?.data };
            if (event.data.projectTitle == 'Total') {
              return;
            } else if (event.value[0] != 0) {
              if (isCtrlClick) {
                this.getDataInNewTab(customStatus?.displayName, params);
                return;
              }
              this.getDataFromCell(customStatus?.displayName, event);
            }
          },
        };
        this.gridOptions?.columnDefs?.push(col);
      });

    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  getMeetingCount(operation: string, event: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    // if (this.isCustomStatusEnabled) {
    //   return
    // }
    this.router.navigate(['leads/manage-leads']);
    let visitMeeting: any = [];
    visitMeeting.push(meetingStatus);
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.status = operation;
    this.gridOptionsService.payload = this.filtersPayload;
    this.gridOptionsService.meetingStatus = visitMeeting;
  }

  getMeetingCountnewTab(operation: string, params: any, meetingStatus: string) {
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
    this.gridOptions.api = params.api;
  }

  getDataFromCell(operation: string, event: any) {
    // if (this.isCustomStatusEnabled) {
    //   return
    // }
    this.router.navigate(['leads/manage-leads']);
    this.gridOptionsService.meetingStatus = undefined;
    this.gridOptionsService.dateType = this.appliedFilter.dateType;
    this.gridOptionsService.data = event.data;
    this.gridOptionsService.status = operation;
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) filters.IsWithTeam = false;
    this.gridOptionsService.payload = filters;
  }

  getDataInNewTab(operation: string, params: any) {
    //     if (this.isCustomStatusEnabled) {
    //   return
    // }
    const filters = { ...this.filtersPayload };
    if (filters?.IsWithTeam) {
      filters.IsWithTeam = false;
    }
    window?.open(
      `leads/manage-leads?leadReportGetData=true&data=${encodeURIComponent(
        JSON.stringify(params?.data)
      )}&operation=${operation}&filtersPayload=${encodeURIComponent(
        JSON.stringify(filters)
      )}`,
      '_blank'
    );
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.gridApi.paginationGoToPage(e);
    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));
    this.isCustomStatusEnabled
      ? this._store.dispatch(new FetchReportsCustomUser())
      : this._store.dispatch(new FetchReportsUser());
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
    this.filterFunction();
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = [
      'subSources',
      'projects',
      'agencyNames',
      'cities',
      'states',
    ];

    if (
      [
        'pageSize',
        'pageNumber',
        'visibility',
        'withTeam',
        'isGM',
        'userStatus',
        'search',
        'ShouldShowAll',
      ].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'date' && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    } else if (allowedKeys.includes(key)) {
      return values;
    }
    return values?.toString()?.split(',');
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  onRemoveFilter(key: string, value: string) {
    if (['dateType', 'date'].includes(key)) {
      delete this.appliedFilter[key];
      const dependentKey = key === 'date' ? 'dateType' : 'date';
      if (this.appliedFilter[dependentKey]) {
        delete this.appliedFilter[dependentKey];
      }
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any, index: number) => {
          const matchIndex = this.appliedFilter[key]?.indexOf(value);
          return index !== matchIndex;
        }
      );
    }
    this.filterFunction();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchSubSourceList());
    this._store.dispatch(new FetchLeadCities());
    this._store.dispatch(new FetchLeadStates());
    this._store.dispatch(new FetchLeadCountries)
    this._store.dispatch(new FetchAllSources());
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
    };
    this.modalService.show(advFilters, initialState);
  }

  getUserName(id: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (id === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }
  toggleColumns(params: any): void {
    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {
      return {
        label: column?.getColDef()?.headerName,
        value: column,
      };
    });

    // .slice(4, this.columns?.length), this.columns[1], this.columns[2]]
    this.columns = [...this.columns.slice(1, this.columns?.length)].sort(
      (a: any, b: any) => a?.label?.localeCompare(b?.label)
    );
    this.defaultColumns = this.columns?.filter(
      (col) => col?.value?.getColDef()?.hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myDataColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('status-reports-columns')?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnsSelected(columns: any): void {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('status-reports-columns', colData?.toString());
    const cols = columns?.map((col: any) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    if (columnState && columnState[0]) columnState[0].pinned = 'left';
    localStorage.setItem('myDataColumnState', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  currentVisibility(visibility: any, isTopLevelFilter: any) {
    this.appliedFilter.userStatus = visibility;
    this.appliedFilter.pageNumber = 1;
    if (isTopLevelFilter) {
      this.appliedFilter.users = null;
    }
    this.filterFunction();

    if (this.canViewAllUsers) {
      switch (visibility) {
        case 1:
          this.allUsers = this.users?.filter((user: any) => user.isActive);
          break;
        case 2:
          this.allUsers = this.users?.filter((user: any) => !user.isActive);
          break;
        case null:
          this.allUsers = this.users;
          break;
      }
      this.allUsers = assignToSort(this.allUsers, '');
    } else {
      switch (visibility) {
        case 1:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => user.isActive
          );
          break;
        case 2:
          this.onlyReportees = this.reportees?.filter(
            (user: any) => !user.isActive
          );
          break;
        case null:
          this.onlyReportees = this.reportees;
          break;
      }
      this.onlyReportees = assignToSort(this.onlyReportees, '');
    }
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));
    this.isCustomStatusEnabled
      ? this._store.dispatch(new FetchReportsCustomUser())
      : this._store.dispatch(new FetchReportsUser());
    this.currOffset = 0;
  }

  filterFunction() {
    this.appliedFilter.pageNumber = 1;
    if (
      this.appliedFilter?.dateType?.length ||
      this.appliedFilter?.date?.[0]?.length ||
      this.appliedFilter.users?.length ||
      this.appliedFilter.projects?.length ||
      this.appliedFilter.subSources?.length ||
      this.appliedFilter.sources?.length ||
      this.appliedFilter.cities?.length ||
      this.appliedFilter.Countries?.length ||
      this.appliedFilter.states?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      userStatus: this.appliedFilter.userStatus,
      dateType: ReportDateType[this.appliedFilter.dateType],
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      IsWithTeam: this.appliedFilter.withTeam,
      UserIds: this.appliedFilter.users,
      SearchText: this.searchTerm,
      Sources: this.appliedFilter.sources,
      SubSources: this.appliedFilter.subSources,
      Projects: this.appliedFilter.projects,
      ReportPermission: this.canViewAllUsers ? 0 : 1,
      ExportPermission: this.canExportAllUsers ? 0 : 1,
      Cities: this.appliedFilter?.cities,
      States: this.appliedFilter?.states,
      Countries: this.appliedFilter?.Countries,
      ShouldShowAll: this.appliedFilter?.ShouldShowAll ?? true,
      ShouldShowPercentage: this.canShowPercentage,
    };
    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));
    this.isCustomStatusEnabled
      ? this._store.dispatch(new FetchReportsCustomUser())
      : this._store.dispatch(new FetchReportsUser());
    this.currOffset = 0;
  }

  reset() {
    this.appliedFilter = {
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.filterFunction();
  }

  updateSubSource() {
    if (this.appliedFilter?.sources?.length) {
      this.subSourceList = [];
      this.appliedFilter?.sources.forEach((i: any) => {
        const source: any = LeadSource[i];
        const leadSource = IntegrationSource[source];
        if (leadSource === '99 Acres') {
          this.subSourceList.push.apply(
            this.subSourceList,
            this.allSubSourceList['NinetyNineAcres'] || []
          );
        } else {
          const formattedKey = leadSource?.replace(/\s+/g, '');
          if (Array.isArray(this.allSubSourceList?.[formattedKey])) {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList?.[formattedKey] || []
            );
          } else {
            this.subSourceList.push.apply(
              this.subSourceList,
              this.allSubSourceList[leadSource] || []
            );
          }
        }
      });
    } else {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList?.[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  onSelectSource(source: any) {
    if (source) {
      this.updateSubSources(source.displayName);
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSources(sourceName: string | null) {
    if (sourceName) {
      if (sourceName === '99 Acres') {
        this.subSourceList = this.allSubSourceList['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        if (Array.isArray(this.allSubSourceList?.[formattedKey])) {
          this.subSourceList = this.allSubSourceList?.[formattedKey] || [];
        } else {
          this.subSourceList = this.allSubSourceList[sourceName] || [];
        }
      }
    } else {
      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {
        if (lead?.displayName === '99 Acres') {
          return this.allSubSourceList?.['NinetyNineAcres'] || [];
        }
        const formattedKey = lead?.displayName?.replace(/\s+/g, '');
        let match = this.allSubSourceList?.[formattedKey];
        if (!match) {
          match = this.allSubSourceList[lead?.displayName];
        }
        if (!match && formattedKey?.toLowerCase() === '99acres') {
          match = this.allSubSourceList['NinetyNineAcres'];
        }
        return Array.isArray(match) ? match : [];
      }) || [];
      this.subSourceList = subSourceList
    }
  }

  refresh() {
    this.gridOptions.api?.refreshCells();
  }

  exportLeadReport() {
    this._store.dispatch(new FetchUserExportSuccess(''));
    this.filterFunction();
    let initialState: any = {
      payload: {
        ...this.filtersPayload,
        selectedColumns: this.gridColumnApi
          .getColumnState()
          .filter((col: any) => !col?.hide)
          .map((col: any) => col?.colId)?.map((col: any) => {
            if (col === 'Referral Taken') {
              return 'Site Visit Done'
            }
            else if (col === 'Referral Not Taken') {
              return 'Site Visit Not Done'
            }
            else if (col === 'Referral Scheduled') {
              return 'Site Visit Scheduled'
            }
            return col
          }),
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  toggleView() {
    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';
  }

  exportGraphAsPDF() {
    if (this.reportsGraph && this.isGraphExportEnabled()) {
      this.reportsGraph.exportGraph();
    }
  }

  isGraphExportEnabled(): boolean {
    return this.currentView === 'graph' &&
      this.reportsGraph?.isChartReady &&
      !this.reportsGraph?.showSelectionMessage;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
