<!-- <ng-container *ngIf="!showTryAnotherWay"> -->
  <div class="flex-between my-30">
    <div>
      <h4 class="fw-600">Authenticate Your <h4 class="fw-600">Account</h4>
      </h4>
      <h6 class="w-170">{{ 'AUTH.we-sent-an' | translate }} <div class="fw-700 d-inline">
          {{ 'LEAD_FORM.otp' | translate }}</div> to your admin / manager</h6>
    </div>
    <img src="../../../../assets/images/message-otp.svg" alt="otp">
  </div>
  <div class="gap-3 align-center">
    <input type="text" id="digit-1" (keyup)="otpClickEvent($event,'digit-2')" [(ngModel)]="otp[0]" class="otp-block"
      maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-2" (keyup)="otpClickEvent($event,'digit-3','digit-1')" [(ngModel)]="otp[1]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-3" (keyup)="otpClickEvent($event,'digit-4','digit-2')" [(ngModel)]="otp[2]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <div>-</div>
    <input type="text" id="digit-4" (keyup)="otpClickEvent($event,'digit-5','digit-3')" [(ngModel)]="otp[3]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-5" (keyup)="otpClickEvent($event,'digit-6','digit-4')" [(ngModel)]="otp[4]"
      class="otp-block" maxlength="1" placeholder="&#9679;" autocomplete="off" />
    <input type="text" id="digit-6" (keyup)="otpClickEvent($event,'','digit-5')" [(ngModel)]="otp[5]" class="otp-block"
      maxlength="1" placeholder="&#9679;" autocomplete="off" (keyup.enter)="focusableLink.click()" />
  </div>
  <div class="d-flex mt-30 gap-2">
    <div class="fw-semi-bold">{{ 'AUTH.not-receive' | translate }} {{ 'LEAD_FORM.otp' | translate }}?</div>
    <h5 class="fw-semi-bold" [ngClass]="isExpired ? 'text-accent-green cursor-pointer' : 'text-gray-110 pe-none'"
      (click)="resendOtp()"><u>{{ 'AUTH.resend' | translate }} {{ 'LEAD_FORM.otp' | translate }}</u></h5>
    <div *ngIf="!isExpired">{{ getTime() }}</div>


  </div>
  <!-- <div class="m-3 flex-center-col" *ngIf="tryAnotherWayCounterCompleted">
    <h4 class="fw-semi-bold">Having trouble loging in?</h4>
    <h3 class="text-accent-green cursor-pointer fw-semi-bold" (click)="tryAnotherWay()">Try Another Way</h3>
  </div> -->
  <h4 #focusableLink class="btn-accent-green-xl mt-50" (click)="submitOTP()">{{ 'BUTTONS.submit' | translate }}</h4>
<!-- </ng-container> -->


<!-- <ng-container *ngIf="showTryAnotherWay">
  <div class="flex-between my-30">
    <div>
      <h4 class="fw-600 w-150">Trying Different Login Method...</h4>
    </div>
    <img src="../../../../assets/images/laptop-hand-wave.svg" alt="laptop" width="75" height="70">
  </div>
  <div>
    <h5 class="fw-600 text-center">{{ 'AUTH.recovery-options' | translate }}:</h5>
  </div>
  <div class="bg-light-pearl p-16 mt-20 br-6 bg-mail fw-700">
    <div>{{ 'GLOBAL.your' | translate }} {{ 'SHARE.email' | translate }}</div>
    <p class="fw-semi-bold mb-16 text-sm">{{ 'AUTH.otp-to' | translate }} {{ 'GLOBAL.your' | translate }}
      {{ 'SHARE.email' | translate }}</p>
    <div>{{ userData?.email | slice:0:1 }}******{{ userData?.email | slice:userData?.email.indexOf('@') }}</div>
  </div>
  <div class="bg-light-pearl p-16 mt-20 br-6 bg-phone fw-700">
    <div>{{ 'GLOBAL.your' | translate }} {{ 'USER.phone-number' | translate }}</div>
    <p class="fw-semi-bold mb-16 text-sm">{{ 'AUTH.otp-to' | translate }} {{ 'GLOBAL.your' | translate }}
      {{ 'USER.phone-number' | translate }}</p>
    <div>
      {{userData?.phoneNumber | slice:0:4 }}*******{{ userData?.phoneNumber | slice:userData?.phoneNumber.length-2}}
    </div>
  </div>
  <div (click)="submitTryAnotherWay()">
    <h4 class="btn-accent-green-xl mt-40">
      {{ 'AUTH.get' | translate }} {{ 'LEAD_FORM.otp' | translate }}</h4>
  </div>
</ng-container> -->