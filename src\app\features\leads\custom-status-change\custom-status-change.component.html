<div class="scrollbar" id="status-form" #statusForm
  [ngClass]="{'pe-none blinking': (leadStatusIsLoading || multipleLeadsIsLoading), 'h-100-333 ph-h-100-358': !elementHeight && !whatsAppComp, 'h-100-460' : !elementHeight && whatsAppComp}">
  <div *ngIf="!isBulkUpdate">
    <div class="fw-600 text-large text-slate-160 ml-20 fv-sm-caps">{{'GLOBAL.current' | translate}}
      {{'GLOBAL.lead' | translate}} {{'GLOBAL.status' | translate}}</div>
    <div class="bg-secondary mx-20 mb-20 mt-10 px-16 py-12 br-4">
      <div class="align-center w-100">
        <div class="align-center w-50">
          <span class="icon ic-person-walking ic-slate-90 ic-xxs mr-8"></span>
          <h5 class="fv-sm-caps fw-600">{{ leadInfo?.status?.displayName }}<span
              *ngIf="leadInfo?.status?.childType?.displayName"> - {{leadInfo.status.childType.displayName}}</span></h5>
        </div>
        <div class="align-center w-50" *ngIf="leadInfo?.scheduledDate">
          <span class="icon ic-alarm ic-slate-90 ic-xxs mr-8"></span>
          <div>
            <h5 class="fv-sm-caps fw-600">{{leadInfo?.scheduledDate ?
              getTimeZoneDate(leadInfo?.scheduledDate, userBasicDetails?.timeZoneInfo?.baseUTcOffset, 'dateWithTime'):
              '---'}}</h5>
            <div class="text-truncate-1 break-all text-sm"
              *ngIf="userBasicDetails?.timeZoneInfo?.timeZoneName && leadInfo?.scheduledDate &&  userBasicDetails?.shouldShowTimeZone">
              ({{userBasicDetails?.timeZoneInfo?.timeZoneName }})
            </div>
          </div>
        </div>
      </div>
      <div class="d-flex mt-16" *ngIf="leadInfo?.notes">
        <span class="icon ic-message-lines ic-slate-90 ic-xxs mr-8"></span>
        <p class="text-black-20 text-sm word-break max-w-460"
          [ngClass]="leadInfo?.notes?.length > 170 && !isReadMore ? 'text-truncate-2' : ''">{{leadInfo?.notes ?
          leadInfo?.notes:'---' }}
        </p>
      </div>
      <div class="flex-center w-100 cursor-pointer mt-16" *ngIf="leadInfo?.notes?.length > 170"
        (click)="isReadMore = !isReadMore">
        <span class="text-sm fw-600 text-accent-green">read {{isReadMore ? 'less' : 'more'}}</span>
        <span class="icon ic-chevron-down ic-xxxs ic-accent-green ml-8 mt-2"
          [ngClass]="{'rotate-180': isReadMore}"></span>
      </div>
    </div>
  </div>
  <div class="text-red ml-20 mb-10" *ngIf="isUnassignedLead">{{'LEADS.lead-unassigned-status' | translate}}</div>

  <div class="pr-8 ml-20">
    <form [formGroup]="updateForm" autocomplete="off"
      [ngClass]="{ 'pe-none': !canUpdateStatus || isUnassignedLead, 'grid-blur': isUnassignedLead}">
      <div>
        <div class="d-flex flex-wrap" *ngIf="canUpdateStatus && !isCustomStatusListLoading else ratLoader">
          <ng-container *ngIf=!hideStatus>
            <ng-container *ngIf=!canShowStatusPopupInPreview>
              <div *ngIf="currentPath !== '/invoice'" [ngClass]="{'d-none': !isSelectedOnlySomeBooked()}"
                class="text-red my-10">
                Update the leads, except for
                <span *ngIf="bookedAndInvoiceStatus?.length > 0">
                  {{ bookedAndInvoiceStatus.join(', ') }}
                </span>
                as these leads cannot be updated further.
              </div>
              <ng-container *ngFor="let customStatus of customStatusListFiltered; let i = index">
                <ng-container>
                  <input formControlName="leadStatus" type="radio" class="btn-check" [value]="customStatus.id"
                    id="leadOption{{i}}" data-automate-id="leadOption{{i}}" autocomplete="off" required>
                  <label class="status-badge" for="leadOption{{i}}" (click)="showReasons(customStatus)"
                    [class.active]="updateForm?.controls['leadStatus']?.value === customStatus.id">
                    {{ customStatus.actionName | titlecase }}
                  </label>
                </ng-container>
              </ng-container>
              <ng-container
                *ngIf="updateForm?.controls['leadStatus']?.dirty || updateForm?.controls['leadStatus']?.touched">
                <div class="flex-end w-100 text-error-red" *ngIf="updateForm?.controls['leadStatus']?.errors?.required">
                  Status is a required field.
                </div>
              </ng-container>
            </ng-container>
            <div class="w-100 flex-center" *ngIf="canShowStatusPopupInPreview">
              <img src="../../../../assets/gifs/muso-walk.gif" alt="Muso Walking" class="h-100-450" />
            </div>
          </ng-container>
          <div *ngIf=hideStatus>
            <div class="align-center mr-4 mb-4 w-100">
              <div class="m-0 status-badge bg-dark-700 text-white br-5">{{ selectedStatus?.displayName | titlecase }}
              </div><a class="icon ic-close-secondary ic-light-pale ic-xxs ml-10" id="clkCancelSelectedBadge"
                data-automate-id="clkCancelSelectedBadge" (click)="deselectStatuses()"></a>
            </div>
            <div class="p-12 position-relative min-w-137">
              <ng-container *ngFor="let reason of callBackReason; let i = index">
                <input type="radio" class="btn-check" [value]="reason.id" id="option{{i}}"
                  (click)="updateSelectedReason(reason); reasonChanged(reason);" data-automate-id="option{{i}}"
                  autocomplete="off" formControlName="reason">
                <label class="status-badge" [class.active]="updateForm?.controls['reason']?.value === reason.id"
                  for="option{{i}}">
                  {{ reason.displayName }}
                </label>
              </ng-container>
              <ng-container *ngIf="updateForm?.controls['reason']?.dirty || updateForm?.controls['reason']?.touched">
                <div class="error-message" *ngIf="updateForm?.controls['reason']?.errors?.required">
                  Sub-Status is a required field.
                </div>
              </ng-container>
            </div>
          </div>
        </div>
        <ng-container *ngIf="leadSource">
          <ng-container
            *ngIf="['Referral', 'Walk In', 'Direct'].includes(leadSource) && (leadInfo?.leadStatus?.baseStatus == 'New')">
            <label class="checkbox-container mt-10">{{"LEAD_FORM.meeting" | translate}} {{"LEAD_FORM.done" |
              translate}}
              <input type="checkbox" class="mr-10" id="inpMeetingDone" data-automate-id="inpMeetingDone"
                [checked]="isMeetingDone"
                (change)="isMeetingDone?isMeetingDone = false:isMeetingDone = true; patchFormControlValue(updateForm, 'leadStatus' , currentLeadStatus['meeting-done'])">
              <span class="checkmark"></span>
            </label>
            <label class="checkbox-container mt-10">{{"LEAD_FORM.visit" | translate}} {{"LEAD_FORM.done" |
              translate}}
              <input type="checkbox" class="mr-10" id="inpVisitDone" data-automate-id="inpVisitDone"
                [checked]="isVisitDone"
                (change)="isVisitDone?isVisitDone = false:isVisitDone = true; patchFormControlValue(updateForm, 'leadStatus' , currentLeadStatus['visit-done'])">
              <span class="checkmark"></span>
            </label>
          </ng-container>
        </ng-container>

        <ng-template #customForm>
          <ng-container
            *ngFor="let field of selectedReason?.customFields?.length ? selectedReason?.customFields : selectedStatus?.customFields">
            <ng-container [ngSwitch]="field?.field?.name">
              <ng-container *ngSwitchCase="'TotalSoldPrice'">
                <div class="position-relative">
                  <div [ngClass]="field?.validators?.includes('required') ? 'field-label-req' : 'field-label'">
                    {{"LEAD_FORM.sold-price"
                    | translate}}</div>
                  <form-errors-wrapper [control]="updateForm?.controls['soldPrice']"
                    label="{{'LEAD_FORM.sold-price' | translate}}">
                    <input type="text" id="soldPrice" data-automate-id="soldPrice" formControlName="soldPrice"
                      (keydown)="onlyNumbers($event)" placeholder="ex. 4000000" maxlength="10">
                  </form-errors-wrapper>
                  <div *ngIf="updateForm?.controls['soldPrice']?.value"
                    class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm"><span
                      class="icon ic-rupee ic-accent-green ic-x-xs mr-4"></span>{{soldPriceInWords}}</div>
                </div>
              </ng-container>
              <ng-container *ngSwitchCase="'ScheduledDate'">
                <div class="position-relative">
                  <div [ngClass]="field?.validators?.includes('required') ? 'field-label-req' : 'field-label'">
                    {{'LEAD_FORM.schedule-date'
                    | translate}}</div>
                  <form-errors-wrapper [control]="updateForm?.controls['ScheduledDate']"
                    label="{{'LEAD_FORM.schedule-date' | translate}}">
                    <input [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1"
                      [min]="!isPastDateSelectionEnabled ? minDate : null" readonly id="inpAppDateTime"
                      data-automate-id="inpAppDateTime" formControlName="ScheduledDate"
                      placeholder="ex. 19/06/2025, 12:00 pm">
                    <owl-date-time #dt1 [hour12Timer]="'true'" (afterPickerOpen)="onPickerOpened(currentDate)"
                      [startAt]="currentDate"></owl-date-time>
                  </form-errors-wrapper>
                </div>
              </ng-container>
              <ng-container *ngSwitchCase="'Projects'">
                <div class="position-relative">
                  <div [ngClass]="field?.validators?.includes('required') ? 'field-label-req' : 'field-label'">
                    Project(s)</div>
                  <form-errors-wrapper [control]="updateForm?.controls['Projects']" label="Projects">
                    <ng-select [virtualScroll]="true" [items]="projectList" [multiple]="true" [closeOnSelect]="false"
                      ResizableDropdown [addTag]="true" name="Projects" formControlName="Projects" bindLabel="name"
                      bindValue="name" addTagText="Create New Project" class="bg-white"
                      placeholder="Select/Create Project">
                      <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span>{{item.name}}
                        </div>
                      </ng-template>
                    </ng-select>
                  </form-errors-wrapper>
                </div>
              </ng-container>
              <ng-container *ngSwitchCase="'Property'">
                <form-errors-wrapper [control]="updateForm?.controls['Properties']" label="Properties">
                  <div [ngClass]="field?.validators?.includes('required') ? 'field-label-req' : 'field-label'">
                    Property(s)</div>
                  <ng-select *ngIf="!propertyListIsLoading else fieldLoader" [virtualScroll]="true"
                    [items]="propertyList" [multiple]="true" [closeOnSelect]="false" ResizableDropdown [addTag]="true"
                    name="Properties" formControlName="Properties" addTagText="Create New Property" class="bg-white"
                    placeholder="Select/Create Property">
                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                      <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                          data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                          class="checkmark"></span>{{item}}
                      </div>
                    </ng-template>
                  </ng-select>
                </form-errors-wrapper>
              </ng-container>
              <ng-container *ngSwitchCase="'Document'">
                <div class="mt-16 w-100">
                  <div class="flex-between">
                    <div
                      [ngClass]="field?.validators?.includes('required') ? 'field-label-req mt-0' : 'field-label mt-0'">
                      Add
                      Documents</div>
                    <div class="flex-center btn btn-accent-green btn-md w-170 text-sm clear-padding-x" *ngIf="!isUpload"
                      (click)="isUpload = true">
                      <span class="icon ic-cloud-upload ic-xxs mr-8"></span>
                      {{ (uploadedFiles?.length ? 'LEADS.upload-another-document' : 'LEADS.upload-new-document') |
                      translate }}
                    </div>
                  </div>
                  <div *ngIf="isUpload">
                    <form class="mb-16" [formGroup]="documentsForm" autocomplete="off">
                      <div class="form-group">
                        <div class="field-label">{{'LEADS.document-title' | translate }}</div>
                        <input type="text" formControlName="docTitle" id="inpDocTitle" data-automate-id="inpDocTitle"
                          placeholder="ex. Title">
                      </div>
                    </form>
                    <div class="br-6 p-10 w-100 bg-accent-green-light mt-16 custom-flex-row">
                      <browse-drop-upload [allowedFileType]="'imgPdfDoc'" [allowedFileFormat]="fileFormatToBeUploaded"
                        (uploadedFile)="onFileSelection($event)"
                        (uploadedFileName)="selectedFileName = $event"></browse-drop-upload>
                    </div>
                    <div class="flex-end mt-10">
                      <div class="btn-gray mr-10" (click)="isUpload = false">{{ 'BUTTONS.cancel' | translate }}</div>
                      <div class="btn-coal" (click)="addDocument()">{{ 'SIDEBAR.add' | translate }} Document</div>
                    </div>
                  </div>
                  <ng-container *ngIf="uploadedFiles?.length">
                    <div class="d-flex flex-wrap tb-w-100 ip-flex-center my-12">
                      <div *ngFor="let doc of uploadedFiles; let i = index" class="flex-col w-100">
                        <div class="p-4 flex-between bg-white br-6 mb-10 w-100">
                          <div class="align-center">
                            <span class="icon ic-file ic-sm ic-black"></span>
                            <div class="text-truncate-1 break-all fw-600 ml-8">{{uploadedFilesName[i]}}</div>
                          </div>
                          <div class="align-center">
                            <a class="icon ic-delete ic-red ic-sm cursor-pointer ml-10" id="clkDeleteLeadDoc"
                              data-automate-id="clkDeleteLeadDoc" (click)="onClickRemoveDocument(i)"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-template>

        <ng-container
          *ngIf="(isShowBookingFormBtn)
          && !isSelectedAllBookedOrInvoicedLead() && (currentPath === '/invoice' || !selectedStatus?.shouldUseForInvoice) else customForm">
          <div class="flex-between flex-wrap w-100">
            <div class="w-50 ph-w-100">
              <div class="mr-10">
                <div class="field-label-req">Booking under name</div>
                <form-errors-wrapper [control]="updateForm.controls['bookedUnderName']" label="Booking under name">
                  <input type="text" id="bookedUnderName" data-automate-id="bookedUnderName"
                    (blur)="checkBookedUnderName($event)" formControlName="bookedUnderName"
                    placeholder="ex. Mounika Pampana">
                </form-errors-wrapper>
              </div>
            </div>
            <div class="w-50 ph-w-100">
              <div class="field-label-req">Booked Date</div>
              <form-errors-wrapper [control]="updateForm.controls['bookedDate']" label="Booked Date">
                <input [owlDateTime]="dt1" [max]="currentDate" [owlDateTimeTrigger]="dt1" readonly id="inpBookDateTime"
                  data-automate-id="inpBookDateTime" formControlName="bookedDate" placeholder="ex. 5/03/2025, 12:00 pm">
                <owl-date-time #dt1 [hour12Timer]="'true'" (afterPickerOpen)="onPickerOpened(currentDate)"
                  [startAt]="updateForm.controls['bookedDate'].value ? null : currentDate"></owl-date-time>
              </form-errors-wrapper>
            </div>
            <div class="field-rupees-tag w-50 ph-w-100">
              <div class="field-label">Agreement Value</div>
              <div class="position-relative budget-dropdown mr-10">
                <form-errors-wrapper [control]="updateForm.controls['agreementValue']" label="Agreement Value">
                  <input type="number" (wheel)="$event.preventDefault()" formControlName="agreementValue" min="1"
                    id="agreementValue" data-automate-id="agreementValue" placeholder="ex. 4000000" maxlength="10"
                    (input)="onInputAgreementValue($event.target.value)">
                  <div class="no-validation">
                    <ng-container *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                      <ng-select formControlName="currency" class="ml-4 mt-4 position-absolute top-0 manage-dropdown"
                        ResizableDropdown>
                        <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                          <span [title]="curr.currency">
                            {{curr.currency}}
                          </span>
                        </ng-option>
                      </ng-select>
                    </ng-container>
                    <ng-template #showCurrencySymbol>
                      <h5 class="rupees px-12 py-8 fw-600 m-4">{{ leadInfo?.enquiry?.currency || defaultCurrency }}</h5>
                    </ng-template>
                  </div>
                </form-errors-wrapper>
                <div *ngIf="updateForm.controls['agreementValue']?.value"
                  class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                  {{miniagreementValueInWords}}</div>
              </div>
            </div>
            <div class="w-100">
              <div class="field-label">Choose Property/ Project</div>
              <div class="flex-between w-50 ph-w-100">
                <div class="align-center" *ngFor="let data of ['Property', 'Project']; let i = index">
                  <div class="form-check form-check-inline align-center">
                    <input type="radio" id="projectProperty{{ i }}" data-automate-id="projectProperty"
                      name="projectProperty" class="radio-check-input mr-10" formControlName='projectProperty'
                      (change)="switchTabProjectProperty(data)" [value]="data" />
                    <label class="fw-600 text-secondary cursor-pointer text-large m-0" for="projectProperty{{ i }}">{{
                      data }}</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="w-50 ph-w-100" *ngIf="updateForm.get('projectProperty')?.value === 'Property'">
              <div class="field-label-req">Choose Property</div>
              <form-errors-wrapper [control]="updateForm.controls['chosenProperty']" label="Property">
                <ng-select [virtualScroll]="true" *ngIf="!propertyListIsLoading else fieldLoader" ResizableDropdown
                  formControlName="chosenProperty" (change)="onPropertyChange($event?.id)" dropdownPosition="bottom"
                  placeholder="ex. ABC Property" class="bg-white mr-10" [items]="propertyList" bindValue="id"
                  bindLabel="title">
                </ng-select>
              </form-errors-wrapper>
            </div>
            <div class="w-50 ph-w-100" *ngIf="updateForm.get('projectProperty')?.value === 'Project'">
              <div class="mr-10">
                <div [ngClass]="isProjectMandatory ? 'field-label-req' : 'field-label'">Choose Project</div>
                <form-errors-wrapper [control]="updateForm.controls['chosenProject']" label="Project">
                  <ng-select [virtualScroll]="true" *ngIf="!projectListIsLoading else fieldLoader" ResizableDropdown
                    formControlName="chosenProject" dropdownPosition="bottom" placeholder="ex. XYZ Project"
                    class="bg-white" (change)="onProjectChange($event?.id)" [items]="projectList" bindValue="id"
                    bindLabel="name">
                  </ng-select>
                </form-errors-wrapper>
              </div>
            </div>
            <div class="position-relative w-50 ph-w-100"
              *ngIf="isShowUnitInfoField && updateForm.controls['chosenProject']?.value">
              <div [ngClass]="isProjectMandatory ? 'field-label-req' : 'field-label'">Choose Unit</div>
              <div class="form-group w-100">
                <form-errors-wrapper [control]="updateForm.controls['chosenUnit']" label="Unit">
                  <ng-select [virtualScroll]="true" id="chosenUnit" data-automate-id="chosenUnit" ResizableDropdown
                    [items]="unitInfo" bindLabel="name" bindValue="id" formControlName="chosenUnit"
                    dropdownPosition="bottom" placeholder="select" class="bg-white"
                    [readonly]="!updateForm.controls['chosenProject']?.value" (change)="onChoosenUnitChange($event)">
                  </ng-select>
                </form-errors-wrapper>
              </div>
            </div>
          </div>
          <!-- Add Document section to booking form -->
          <ng-container
            *ngFor="let field of selectedReason?.customFields?.length ? selectedReason?.customFields : selectedStatus?.customFields">
            <ng-container [ngSwitch]="field?.field?.name">
              <ng-container *ngSwitchCase="'Document'">
                <div class="mt-16 w-100">
                  <div class="flex-between">
                    <div
                      [ngClass]="field?.validators?.includes('required') ? 'field-label-req mt-0' : 'field-label mt-0'">
                      Add
                      Documents</div>
                    <div class="flex-center btn btn-accent-green btn-md w-170 text-sm clear-padding-x" *ngIf="!isUpload"
                      (click)="isUpload = true">
                      <span class="icon ic-cloud-upload ic-xxs mr-8"></span>
                      {{ (uploadedFiles?.length ? 'LEADS.upload-another-document' : 'LEADS.upload-new-document') |
                      translate }}
                    </div>
                  </div>
                  <div *ngIf="isUpload">
                    <form class="mb-16" [formGroup]="documentsForm" autocomplete="off">
                      <div class="form-group">
                        <div class="field-label">{{'LEADS.document-title' | translate }}</div>
                        <input type="text" formControlName="docTitle" id="inpDocTitle" data-automate-id="inpDocTitle"
                          placeholder="ex. Title">
                      </div>
                    </form>
                    <div class="br-6 p-10 w-100 bg-accent-green-light mt-16 custom-flex-row">
                      <browse-drop-upload [allowedFileType]="'imgPdfDoc'" [allowedFileFormat]="fileFormatToBeUploaded"
                        (uploadedFile)="onFileSelection($event)"
                        (uploadedFileName)="selectedFileName = $event"></browse-drop-upload>
                    </div>
                    <div class="flex-end mt-10">
                      <div class="btn-gray mr-10" (click)="isUpload = false">{{ 'BUTTONS.cancel' | translate }}</div>
                      <div class="btn-coal" (click)="addDocument()">{{ 'SIDEBAR.add' | translate }} Document</div>
                    </div>
                  </div>
                  <ng-container *ngIf="uploadedFiles?.length">
                    <div class="d-flex flex-wrap tb-w-100 ip-flex-center my-12">
                      <div *ngFor="let doc of uploadedFiles; let i = index" class="flex-col w-100">
                        <div class="p-4 flex-between bg-white br-6 mb-10 w-100">
                          <div class="align-center">
                            <span class="icon ic-file ic-sm ic-black"></span>
                            <div class="text-truncate-1 break-all fw-600 ml-8">{{uploadedFilesName[i]}}</div>
                          </div>
                          <div class="align-center">
                            <a class="icon ic-delete ic-red ic-sm cursor-pointer ml-10" id="clkDeleteLeadDoc"
                              data-automate-id="clkDeleteLeadDoc" (click)="onClickRemoveDocument(i)"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="canAssignLead && !canShowStatusPopupInPreview">
          <div class="field-label">{{'BUTTONS.assign' | translate }} {{'GLOBAL.to' | translate }}</div>
          <div class="d-flex flex-wrap ph-flex-col">
            <div class="ph-w-100 dashboard-dropdown" [ngClass]="isDualOwnershipEnabled?'w-50':'w-100'">
              <div class="text-sm text-black-100 mb-4 mt-2" *ngIf="isDualOwnershipEnabled">primary</div>
              <form-errors-wrapper [control]="updateForm?.controls['assignedToUserId']" label="This">
                <ng-select [virtualScroll]="true" placeholder="Select" name="user" formControlName="assignedToUserId"
                  ResizableDropdown class="mr-10 ph-mr-0" [ngClass]="{'blinking': isUserListLoading}">
                  <ng-option *ngIf="leadInfo.assignTo != EMPTY_GUID" [value]="EMPTY_GUID">{{ 'LEADS.unassign-lead' |
                    translate}}<span class="text-light-gray">
                      ({{'LEADS.mark-unassigned' | translate}})</span></ng-option>
                  <ng-option *ngFor="let user of primaryAgentList" [value]="user.id">
                    {{user.firstName}} {{user.lastName}}</ng-option>
                  <!-- <ng-option *ngFor="let user of deactiveUsers" [value]="user.id" [disabled]="true">
                    {{ user.firstName }} {{ user.lastName }} <span class="error-message-custom top-10"
                      *ngIf="!user.isActive">
                      (Disabled)</span>
                  </ng-option> -->
                </ng-select>
              </form-errors-wrapper>
            </div>
            <div class="w-50 ph-w-100 ph-mt-10 dashboard-dropdown" *ngIf="isDualOwnershipEnabled">
              <div class="text-sm text-black-100 mb-4 mt-2">secondary</div>
              <ng-select [virtualScroll]="true" placeholder="Select" name="user" formControlName="secondaryAssignTo"
                ResizableDropdown
                [ngClass]="{'blinking': isUserListLoading,'pe-none':!updateForm?.controls['assignedToUserId']?.value}">
                <ng-option *ngIf="leadInfo.secondaryUserId != EMPTY_GUID" [value]="EMPTY_GUID">{{ 'LEADS.unassign-lead'
                  |
                  translate}}<span class="text-light-gray">
                    ({{'LEADS.mark-unassigned' | translate}})</span></ng-option>
                <ng-option *ngFor="let user of secondaryAgentList" [value]="user.id">
                  {{user.firstName}} {{user.lastName}}</ng-option>
                <!-- <ng-option *ngFor="let user of deactiveUsers" [value]="user.id" [disabled]="true">
                  {{ user.firstName }} {{ user.lastName }} <span class="error-message-custom top-10"
                    *ngIf="!user.isActive">
                    (Disabled)</span>
                </ng-option> -->
              </ng-select>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="canUpdateStatus && !canShowStatusPopupInPreview">
          <div [ngClass]="isNotesMandatory ? 'field-label-req' : 'field-label'">{{'TASK.notes' | translate}}</div>
          <form-errors-wrapper [control]="updateForm?.controls['notes']" label="{{'TASK.notes' | translate}}">
            <textarea rows="2" id="txtUpdateStatusNotes" data-automate-id="txtUpdateStatusNotes" formControlName="notes"
              placeholder="ex. I want to say "></textarea>
          </form-errors-wrapper>
        </ng-container>
      </div>
    </form>
  </div>
</div>
<div class="justify-end mt-20 modal-footer bg-white gap-2" [ngClass]="{'d-none': isCustomStatusListLoading}"
  *ngIf="(!leadStatusIsLoading && !multipleLeadsIsLoading && canUpdateStatus) else ratLoader">
  <h5 class="fw-semi-bold text-black-200 text-decoration-underline cursor-pointer" id="btnCancelUpdateStatus"
    data-automate-id="btnCancelUpdateStatus" (click)="modalService.hide()">{{'BUTTONS.cancel' | translate}}</h5>
  <div class="border-left h-16"></div>
  <button *ngIf="canShowStatusPopupInPreview && canUpdateStatus && !isUnassignedLead"
    class="btn-coal px-10 min-w-fit-content" (click)="openAppointmentPopup()">{{'BUTTONS.update-lead-status' |
    translate}}</button>
  <button class="btn-coal" *ngIf="!canShowStatusPopupInPreview && canUpdateStatus && !isUnassignedLead"
    (click)="updateStatus()">{{(isLeadPreview && !whatsAppComp ?
    'BUTTONS.save-and-close' : 'BUTTONS.save') | translate}}</button>
  <button
    *ngIf="!isLastLead && !canShowStatusPopupInPreview && !isBulkUpdate && canUpdateStatus && !isUnassignedLead && !whatsAppComp"
    class="btn-coal" id="btnSaveUpdateStatus" data-automate-id="btnSaveUpdateStatus"
    (click)="updateStatus(true)">{{'BUTTONS.save-and-next' | translate}}</button>
  <button *ngIf="isShowBookingFormBtn && canUpdateBookedLead && !isBulkUpdate" class="btn-coal w-150"
    id="btnSaveUpdateStatus" data-automate-id="btnSaveUpdateStatus" (click)="fullBookingFormModal(fullBookingForm)">Save
    & fill booking form</button>
</div>

<ng-template #ratLoader>
  <div class="mt-20 modal-footer"
    [ngClass]="{'justify-end': leadStatusIsLoading || multipleLeadsIsLoading,'justify-center': isCustomStatusListLoading}">
    <img src="assets/images/loader-rat.svg" class="rat-loader h-20px w-20px" alt="loader">
  </div>
</ng-template>

<ng-template #fieldLoader>
  <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>

<ng-template #noUnitFound>
  <div class="p-20">
    <h3 class="text-center">There is no unit information provided for this project. Please check with the admin.</h3>
    <div class="mt-20 flex-end">
      <button type="button" class="btn-green" (click)="modalRef.hide()">OK</button>
    </div>
  </div>
</ng-template>

<ng-template #trackerInfoModal>
  <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white">Bulk Update Status</h5>
  <div class="p-20 flex-center-col">
    <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break">Bulk Update Status In progress.
    </h4>
    <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
      <span class="cursor-pointer text-accent-green header-3 fw-600" (click)="openBulkUpdatedStatus()">“Bulk Operation
        Tracker”</span> to view updated status
    </h5>
    <button class="btn-green mt-30" (click)="modalService.hide()">
      {{'BULK_LEAD.got-it' | translate}}</button>
  </div>
</ng-template>