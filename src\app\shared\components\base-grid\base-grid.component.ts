import { RowNode } from 'ag-grid-community';

export class BaseGridComponent {
  public gridApi: any;
  public gridColumnApi: any;
  public hideFloatingFilter = true;

  constructor() {}

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
    // const sortModel = [{colId: 'Priority', sort: 'asc'}];
    // this.gridApi.setSortModel(sortModel);
  }
  onFloatingFilterUpdated(hideFloatingFilter: boolean) {
    this.hideFloatingFilter = hideFloatingFilter;
  }
  //Case-Insensitive Sorting for ag-Grid
  stringComparator(valueA: any, valueB: any, nodeA?: RowNode, nodeB?: RowNode, isInverted?: boolean) {
    if (Array.isArray(valueA) && Array.isArray(valueB)) {
      return valueA[0].toLowerCase() > valueB[0].toLowerCase() ? 1 : -1;
    } 
    else if (typeof (valueA) === 'string' && typeof (valueB) === 'string') {
      return valueA.toLowerCase() > valueB.toLowerCase() ? 1 : -1;
    }
    return isInverted && typeof (valueB) === 'string' ? -1 : 1;
  }
}

