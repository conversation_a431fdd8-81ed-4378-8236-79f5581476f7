import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { NotificationsService } from 'angular2-notifications';
import { AppState } from 'src/app/app.reducer';
import { Store } from '@ngrx/store';
import { OnError } from 'src/app/app.actions';
import { FetchFields, FetchFieldsSuccess, FetchSelectedFields, FetchSelectedFieldsSuccess, FieldsActionTypes, UpdateForm, UpdateFormSuccess } from './fields.action';
import { FieldsService } from 'src/app/services/controllers/fields.service';


@Injectable()
export class FieldsEffects {

    getFields$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FieldsActionTypes.FETCH_FIELDS),
            switchMap((action: FetchFields) => {
                return this.api.getList().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {

                            return new FetchFieldsSuccess(resp.items);
                        } else {
                            return new FetchFieldsSuccess(); // Return a success action even if not succeeded
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getSelectedFields$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FieldsActionTypes.FETCH_SELECTED_FIELDS),
            switchMap((action: FetchSelectedFields) => {
                return this.api.getSelectedFields().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {

                            return new FetchSelectedFieldsSuccess(resp.data);
                        } else {
                            return new FetchSelectedFieldsSuccess(); // Return a success action even if not succeeded
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateForm$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FieldsActionTypes.UPDATE_FORM),
            switchMap((action: UpdateForm) =>
                this.api.updateForm(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(`QR Template updated successfully.`);
                            return [
                                new UpdateFormSuccess()
                            ];
                        } else {
                            // Handle case when update fails
                            return of(new OnError(resp));
                        }
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    constructor(
        private actions$: Actions,
        private api: FieldsService,
        private _notificationService: NotificationsService,
        private _store: Store<AppState>
    ) { }
}
