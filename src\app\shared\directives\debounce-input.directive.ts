import { Directive, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@Directive({
  selector: '[appDebounceInput]',
})
export class DebounceInputDirective {
  @Input() debounceTime = 500;
  @Output() debounceEvent = new EventEmitter<any>();

  private inputSubject: Subject<any> = new Subject();

  constructor() {
    this.inputSubject.pipe(debounceTime(this.debounceTime)).subscribe((value) => {
      this.debounceEvent.emit(value);
    });
  }

  @HostListener('input', ['$event'])
  onInputEvent(event: Event) {
    const inputValue = (event.target as HTMLInputElement).value;
    this.inputSubject.next(inputValue);
  }
}
