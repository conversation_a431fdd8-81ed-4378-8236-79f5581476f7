<div class="bg-white py-20 px-12 border br-4">
    <div class="icon ic-close-secondary ic-large cursor-pointer ntop-20 nright-20 ph-right-10 ntop-30 position-absolute"
        *ngIf="isShowCloseBtn" (click)=" modalRef.hide()"></div>
    <div class="flex-between">
        <div>
            <h4 class="fw-700">{{userData?.firstName}} {{userData?.lastName}}</h4>
            <div class="text-dark-800">
                <a [href]="'tel:'+userData.phoneNumber">{{userData.phoneNumber}}</a>
            </div>
        </div>
        <div class="flex-center bg-white br-20 p-4 border-black">
            <div>
                <img alt="" *ngIf="userData?.orgDetails?.logoImgUrl"
                    [type]="'leadrat'" [appImage]="s3BucketUrl+userData?.orgDetails?.logoImgUrl"
                    class="obj-cover br-50 border-white shadow mr-4 bg-white" width="20" height="20">
            </div>
            <div *ngIf="userData?.orgDetails?.displayName">
                <span class="text-truncate-1 fw-600 text-sm">
                    {{userData?.orgDetails?.displayName ? userData?.orgDetails?.displayName : 'Org Name'}}</span>
            </div>
        </div>
    </div>
    <div class="px-12">
        <form [formGroup]="enquiryForm">
            <div class="w-100 d-flex flex-wrap">
                <div class="w-50 ph-w-100">
                    <div class="mr-10 ph-mr-0">
                        <div class="field-label-req">Name</div>
                        <form-errors-wrapper [control]="enquiryForm.controls['name']" label="Name">
                            <input type="text" formControlName="name" placeholder="ex. Deepak Yadav">
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="w-50 ph-w-100">
                    <div class="mr-20">
                        <div class="field-label-req">Phone Number</div>
                        <form-errors-wrapper [control]="enquiryForm.controls['contactNo']" label="Phone Number">
                            <div *ngIf="!resetNumber">
                                <ngx-mat-intl-tel-input #contactNoInput *ngIf="hasInternationalSupport"
                                    [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                    [enableSearch]="true" formControlName="contactNo"
                                    class="no-validation contactNoInput">
                                </ngx-mat-intl-tel-input>
                                <ngx-mat-intl-tel-input #contactNoInput *ngIf="!hasInternationalSupport"
                                    [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                                    [enablePlaceholder]="true" [enableSearch]="true" formControlName="contactNo"
                                    class="no-validation contactNoInput">
                                </ngx-mat-intl-tel-input>
                            </div>
                        </form-errors-wrapper>
                    </div>
                    <!-- <div *ngIf="checkDuplicacy && numberEdited && enquiryForm?.controls?.['contactNo']?.status === 'VALID'"
                        class="mt-4 text-xs text-red position-absolute right-20 fw-semi-bold">
                        {{ 'CONTACT.already-exist' | translate }}
                    </div> -->
                </div>
            </div>
            <div class="w-100 d-flex flex-wrap">
                <div class="w-50 ph-w-100">
                    <div class="mr-10 ph-mr-0">
                        <div class="field-label">Email</div>
                        <form-errors-wrapper [control]="enquiryForm.controls['email']" label="email">
                            <input type="text" formControlName="email" placeholder="ex. <EMAIL>">
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="w-50 ph-w-100">
                    <div class="field-rupees-tag">
                        <div class="mr-10 ph-mr-0">
                            <div class="field-label">Budget</div>
                            <div class="position-relative budget-dropdown">
                                <form-errors-wrapper [control]="enquiryForm.controls['budget']"
                                    label="{{'LABEL.budget' | translate}}">
                                    <input type="number" formControlName="budget" min="1" id="budget"
                                        data-automate-id="budget" placeholder="ex. 4000000" maxlength="10"
                                        (keydown)="onlyNumbers($event)">
                                    <h5 class="rupees px-12 py-8 fw-600 m-4">{{ projectInfo?.monetaryInfo?.currency
                                        || defaultCurrency }}</h5>
                                </form-errors-wrapper>
                                <div *ngIf="enquiryForm.controls['budget'].value"
                                    class="position-absolute right-5 nbottom-15 text-accent-green fw-semi-bold text-sm">
                                    {{budgetInWords}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-100 d-flex flex-wrap" *ngIf="projectUnit?.unitTypes?.length">
                <div class="w-50 ph-w-100">
                    <div class="field-label">Unit Name</div>
                    <div class="mr-10 mt-4 ng-dropdown-sm">
                        <form-errors-wrapper [control]="enquiryForm.controls['unitId']" label="Unit Name">
                            <ng-select [virtualScroll]="true" [items]="unitList" class="bg-white"
                                formControlName="unitId" placeholder="ex. East luxury" bindLabel="name"
                                bindValue="id"></ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <label class="checkbox-container mt-16">
                <input type="checkbox" formControlName="agreeTerms" id="inpTerms" required data-automate-id="inpTerms"
                    class="mr-10">
                <span class="checkmark"></span>
                <span class="text-dark-gray text-sm">I agree with the<span class="fw-600 text-decoration-underline">
                        Terms & Privacy Policy</span></span>
            </label>
            <span *ngIf="!enquiryForm.controls['agreeTerms'].valid && enquiryForm.controls['agreeTerms'].touched"
                class="text-red mt-4 text-xs">In order to proceed, please check checkbox.</span>
            <h5 *ngIf="!MSProjectleadIsLoading"
                class="mt-12 bg-coal fw-semi-bold br-4 py-10 text-center text-white cursor-pointer"
                (click)="postLead()">Enquire Now
            </h5>
            <div class="flex-center" *ngIf="MSProjectleadIsLoading">
                <img src="assets/images/loader-rat.svg" class="rat-loader h-48 w-48" alt="loader">
            </div>
        </form>
    </div>
</div>