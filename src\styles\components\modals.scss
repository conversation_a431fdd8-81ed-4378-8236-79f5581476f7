$modal-dialog-margin: 0.5rem !default;

//to remove scroll bar of modal
.modal-open {
  overflow: hidden;
}

//to override modals default padding
.modal-header {
  padding: 0 !important;
}

.modal-content {
  border-radius: 10px;
  border: none;
}

.modal-radius {
  .modal-content {
    border: none !important;
    border-radius: unset !important;
  }
}

.modal-border-green {
  .modal-content {
    border-left: 6px solid $green-40;
    border-radius: unset !important;
  }
}

.modal-body {
  &.clear-padding {
    padding: 0;
  }

  &.modal-primary {
    @extend .d-flex;

    .modal-leftbar {
      @extend .brtl-10, .brbl-10;
      color: $white;
      padding: 40px 20px;
      // @extend .bg-theme-color; bg-black-4
      // @extend .bg-black-4;
      background-color: #130f26;
      @extend .d-flex;
      @extend .flex-column;
      @extend .justify-content-between;
      @extend .align-items-center;
    }

    .modal-rightbar {
      background-color: $slate-20;
      @extend .brtr-10, .brbr-10, .flex-grow-1, .p-20;

      .modal-header {
        &.modal-header-primary {
          @extend .border-bottom-0, .flex-between;
        }

        .modal-title {
          font-weight: 500 !important;
        }
      }
    }
  }
}

.modal-body {
  &.modal-primary {
    .modal-leftbar {
      &.align-items-start {
        align-items: start !important;
      }
    }
  }
}

.top-full-modal {
  &.modal-dialog {
    max-width: unset;
    margin: 0px 0px 0px 40px !important;
    top: 0px;
  }
}

.top-modal {
  .modal-content {
    border-radius: 0 0 10px 10px !important;

    &.modal-dialog {
      margin: auto !important;
    }
  }
}

.down-modal {
  position: absolute;
  bottom: 0;
  @extend .m-0;
  @extend .overflow-hidden;

  .modal-footer {
    border-top: none;
    position: sticky;
    bottom: 0;
  }
}

.modal-dialog {
  margin: auto !important;
}

.right-modal {
  height: 100%;
  @extend .m-0;
  float: right;
  @extend .overflow-hidden;
  @extend .d-flex;
  @extend .flex-column;

  .modal-content {
    border-radius: 0px !important;
    min-height: 100%;
    border: none;
    left: 100%;
    transform: translateX(-100%);
    transition: all 0.4s ease-out;
  }

  &.right-modal-400 {
    width: 400px;
  }

  .modal-footer {
    border-top: none;
    position: sticky;
    bottom: 0;
  }
}
.modal.fade .modal-dialog.down-modal {
  transform: translateY(100%);
}

.modal.show .modal-dialog.down-modal {
  transform: none;
}

.modal.fade .modal-dialog.right-modal {
  transform: translateX(100%);
}

.modal.show .modal-dialog.right-modal {
  transform: none;
}

.left-modal {
  height: 100%;
  @extend .m-0;
  float: left;
  @extend .overflow-hidden;
  @extend .d-flex;
  @extend .flex-column;

  .modal-content {
    border-radius: 0px !important;
    min-height: 100%;
    border: none;
    left: 100%;
    transform: translateX(-100%);
    transition: all 0.4s ease- out;
  }

  &.left-modal-400 {
    width: 400px;
  }

  .modal-footer {
    border-top: none;
    position: sticky;
    bottom: 0;
  }
}

.modal.fade .modal-dialog.left-modal {
  transform: translateX(-100%);
}

.modal.show .modal-dialog.left-modal {
  transform: none;
}

.modal-radius-20 {
  .modal-content {
    border-radius: 20px;
  }
}

//need to check another modals
.modal {
  --bs-modal-width: unset !important;
}

//to make modal at exact center of screen
.modal-dialog-centered {
  @extend .flex-center;
}

.modal-100 {
  width: 100px;
  min-width: 100px;
}

.modal-150 {
  width: 150px;
  min-width: 150px;
}

.modal-200 {
  width: 200px;
  min-width: 200px;
}

.modal-250 {
  width: 250px;
  min-width: 250px;
}

.modal-300 {
  width: 300px;
  min-width: 300px;
}

.modal-350 {
  width: 350px;
  min-width: 350px;
}

.modal-400 {
  width: 400px;
  min-width: 400px;
}

.modal-450 {
  width: 450px;
  min-width: 450px;
}

.modal-500 {
  width: 500px;
  min-width: 500px;
}

.modal-550 {
  width: 550px;
  min-width: 550px;
}

.modal-600 {
  width: 600px;
  min-width: 600px;
}

.modal-640 {
  width: 640px;
  min-width: 640px;
}

.modal-650 {
  width: 650px;
  min-width: 650px;
}

.modal-700 {
  width: 700px;
  min-width: 700px;
}

.modal-750 {
  width: 750px;
  min-width: 750px;
}

.modal-800 {
  width: 800px;
  min-width: 800px;
}

.modal-900 {
  width: 900px;
  min-width: 900px;
}

.modal-950 {
  width: 950px;
  min-width: 950px;
}

.modal-1000 {
  width: 1000px;
  min-width: 1000px;
}

.modal-1100 {
  width: 1100px;
  min-width: 1100px;
}

.modal-1200 {
  width: 1200px;
  min-width: 1200px;
}

.modal-1500 {
  width: 1500px;
  min-width: 1500px;
}

.full-modal {
  width: calc(100% - 100px) !important;
  height: calc(100% - 100px) !important;
  min-width: calc(100% - 100px) !important;
}

.no-bg {
  .modal-content {
    background-color: unset;
    margin-top: 30px;
  }

}