import {
  HttpClient,
  HttpErrorResponse,
  HttpHeaders,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root',
})
export abstract class BaseService<T> {
  abstract getResourceUrl(): string;
  protected APIUrl: string;
  constructor(protected httpClient: HttpClient) {
    this.APIUrl =
      environment.baseURL + environment.apiURL + this.getResourceUrl();
  }

  getList(): Observable<T> {
    return this.httpClient
      .get<T>(`${this.APIUrl}`)
      .pipe(catchError(this.handleError));
  }

  get(id: string | number): Observable<T> {
    return this.httpClient
      .get<T>(`${this.APIUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  add(resource: any): Observable<any> {
    return this.httpClient
      .post(`${this.APIUrl}`, resource)
      .pipe(catchError(this.handleError));
  }

  delete(id: string | number): Observable<any> {
    return this.httpClient
      .delete(`${this.APIUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  update(resource: T, id: string) {
    return this.httpClient
      .put(`${this.APIUrl}/${id}`, resource)
      .pipe(catchError(this.handleError));
  }

  updateMultiple(resource: T) {
    return this.httpClient
      .put(`${this.APIUrl}`, resource)
      .pipe(catchError(this.handleError));
  }

  fileUpload(resource: T) {
    let header = new HttpHeaders();
    header = header.append('content-type', 'application/octet-stream');
    return this.httpClient
      .post(`${this.APIUrl}`, resource, {
        headers: header,
        reportProgress: true,
        observe: 'events',
      })
      .pipe(catchError(this.handleError));
  }

  private handleError(error: HttpErrorResponse) {
    // Handle the HTTP error here
    return throwError('Something wrong happened');
  }
}
