.icon {
  height: 24px;
  width: 24px;
  @extend .d-inline-flex, .align-items-center, .justify-content-center;
  color: $white;
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;

  &.ic-xx-xs {
    height: 6px;
    width: 6px;
    font-size: 6px;
  }

  &.ic-x-xs {
    height: 8px;
    width: 8px;
    font-size: 8px;
  }

  &.ic-xxxs {
    height: 8px;
    width: 8px;
    font-size: 10px;
  }

  &.ic-xxs {
    height: 11px;
    width: 11px;
    font-size: 12px;
  }

  &.ic-xs {
    height: 20px;
    width: 20px;
    font-size: 13px;
  }

  &.ic-sm {
    height: 16px;
    width: 16px;
    font-size: 15px;
  }

  &.ic-large {
    height: 20px;
    width: 20px;
    font-size: 20px;
  }

  &.ic-lg {
    height: 28px;
    width: 28px;
    font-size: 20px;
  }

  &.ic-xl {
    height: 32px;
    width: 32px;
    font-size: 24px;
  }

  &.ic-xxl {
    height: 40px;
    width: 40px;
    font-size: 36px;
  }

  &.ic-xxxl {
    height: 48px;
    width: 48px;
    font-size: 44px;
  }

  &.ic-hover-gray {
    &:hover {
      color: $dark-400;
    }
  }

  &.icon-hover-white {
    &:hover {
      color: $white;
    }
  }

  &.icon-hover-green {
    &:hover {
      color: $accent-green;
    }
  }

  &.ic-blue {
    &:hover {
      color: $blue-20;
    }
  }

  &.icon-hover-black {
    &:hover {
      color: $black;
    }
  }

  &.ic-active-black {
    &:active {
      color: $black;
    }
  }
}

.ic-white {
  color: $white !important;
}

.ic-black {
  color: $black;
}

.ic-black-100 {
  color: $black-100;
}

.ic-black-200 {
  color: $black-200;
}

.ic-dark-800 {
  color: $dark-800;
}

.ic-black-300 {
  color: $black-300;
}

.ic-black-350 {
  color: $black-350;
}

.ic-coal {
  color: $primary-black;
}

.ic-dark {
  color: $dark-700;
}

.ic-dark-400 {
  color: $dark-400 !important;
}

.ic-gray {
  color: $slate-10;
}

.ic-slate-90 {
  color: $slate-90;
}

.ic-slate-110 {
  color: $slate-110;
}

.ic-light-gray {
  color: $slate-250;
}

.ic-dark-gray {
  color: $slate-70;
}

.ic-pale {
  color: $slate-600;
}

.ic-light-pale {
  color: $slate-60;
}

.ic-blue {
  color: var(--primary-theme-color) !important;
}

.ic-blue-1150 {
  color: $blue-1150;
}

.ic-dark-blue {
  color: $aqua-400;
}

.ic-aqua-450 {
  color: $aqua-450;
}

.ic-aqua-750 {
  color: $aqua-750;
}

.ic-light-aqua {
  color: $accent-blue-light;
}

.ic-light-blue {
  color: $blue-50;
}

.ic-navy-100 {
  color: $navy-100;
}

.ic-dark-purple {
  color: $dark-purple;
}

.ic-dark-green {
  color: $green-600;
}

.ic-dark-green-100 {
  color: $dark-green-100;
}

.ic-green-190 {
  color: $green-190;
}

.ic-green-900 {
  color: $green-900;
}

.ic-green {
  color: $green-1000;
}

.ic-accent-green {
  color: $accent-green;
}

.ic-red-120 {
  color: $red-120;
}

.ic-red-350 {
  color: $red-350;
}

.ic-red-450 {
  color: $red-450;
}

.ic-red-750 {
  color: $red-750;
}

.ic-red {
  color: $red-50;
}

.ic-dark-red {
  color: $red-600;
}

.ic-yellow {
  color: $yellow-200;
}

.ic-light-orange {
  color: $light-orange;
}

.ic-hover-red {
  &:hover {
    color: $red-400;
  }
}

.icon-badge {
  @extend .dot, .dot-md, .cursor-pointer, .br-4, .ml-6;
}

.ic-close-modal {
  @extend .icon, .ic-hover-gray, .position-absolute, .ntop-10, .nright-30;
}

.ic-close-modal-coal {
  @extend .ic-close-modal, .ic-black, .icon-hover-black;
}