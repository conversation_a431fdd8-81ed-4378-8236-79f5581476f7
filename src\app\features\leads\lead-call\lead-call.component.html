<div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-10">choose which number you want to call?</h3>
    <div class="align-center">
        <div class="form-check form-check-inline" (click)="selectedCallType = data?.contactNo">
            <input type="radio" id="inpPhoneNumber" data-automate-id="inpPhoneNumber" class="radio-check-input"
                name="callType" checked>
            <label class="fw-600 text-secondary cursor-pointer text-large" for="inpPhoneNumber">Primary Number</label>
        </div>
    </div>
    <div class="align-center">
        <div class="form-check form-check-inline" (click)="selectedCallType = data?.alternateContactNo">
            <input type="radio" id="inpAlternateNumber" data-automate-id="inpAlternateNumber" class="radio-check-input"
                name="callType">
            <label class="fw-600 text-secondary cursor-pointer text-large" for="inpAlternateNumber">
                {{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}
            </label>
        </div>
    </div>
    <div class="flex-end mt-30">
        <button class="btn-gray" id="clkCallNumberCancel" data-automate-id="clkCallNumberCancel"
            (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-green ml-20" id="clkCallNumberConfirm" data-automate-id="clkCallNumberConfirm"
            (click)="initCall()">
            {{ 'DASHBOARD.call' | translate }}</button>
    </div>
</div>
<ng-template #mobileNotification>
    <div class="p-20">
        <h3 class="text-black-100 fw-semi-bold mb-10">we have sent call notification to mobile
        </h3>
        <div class="flex-end mt-30">
            <button class="btn-gray" id="clkCallNumberCancel" data-automate-id="clkCallNumberCancel"
                (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-green w-150 ml-20" id="clkCallNumberConfirm" data-automate-id="clkCallNumberConfirm"
                (click)="openDialerPad()">
                Choose Another App</button>
        </div>
    </div>
</ng-template>