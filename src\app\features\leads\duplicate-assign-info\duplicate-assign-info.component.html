<!-- <ng-container *ngIf="assignType == 'reassign' else duplicateAssign">
    <div class="p-16 border br-8">
        <a class="ic-close-secondary ic-close-modal-coal ip-ic-close-modal" (click)="modalRef.hide()"></a>
        <h4 class="text-gray fw-semi-bold my-20 text-center">This lead(s) has already been assigned to this user.</h4>
        <table class="table standard-table no-vertical-border mt-8">
            <thead>
                <th>{{ 'AUTH.user-name' | translate }}</th>
                <th>{{ 'SIDEBAR.lead' | translate }} {{ 'GLOBAL.name' | translate }}</th>
            </thead>
            <tbody>
                <tr>
                    <td>{{response?.data?.user?.name}}</td>
                    <td> <ng-container *ngFor="let lead of response?.data?.leads; let last = last">
                            <span>{{ lead.name }}{{!last ? ', ': ' '}}</span>
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="flex-end mt-20">
            <button class="btn-gray w-130" (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
        </div>
    </div>
</ng-container>
<ng-template #duplicateAssign> -->
<div class="p-16 border br-8">
    <a class="ic-close-secondary ic-close-modal-coal ip-ic-close-modal" (click)="modalRef.hide()"></a>
    <h4 class="text-gray fw-semi-bold my-20 text-center" *ngIf="assignType != '2ndReassign'">
        Out of {{response?.data?.usersCount}}, {{response?.data?.assignedUsersCount}}
        user(s) already have the following lead(s) assigned to them.</h4>
    <h4 class="text-gray fw-semi-bold my-20 text-center" *ngIf="assignType == '2ndReassign'">
        This lead(s) has already been assigned to this user.</h4>
    <table class="table standard-table no-vertical-border mt-8">
        <thead>
            <th>{{ 'AUTH.user-name' | translate }}</th>
            <th>{{ 'SIDEBAR.lead' | translate }} {{ 'GLOBAL.name' | translate }}</th>
        </thead>
        <tbody>
            <ng-container *ngFor="let data of responseData">
                <tr>
                    <td><div class= "text-truncate-1 break-all" >{{data?.user?.name}}</div></td>
                    <td> <ng-container *ngFor="let lead of data?.leads; let last = last">
                            <span class="text-wrap break-all">{{ lead.name }}{{!last ? ', ': ' '}}</span>
                        </ng-container>
                    </td>
                </tr>
            </ng-container>
        </tbody>
    </table>
    <div class="flex-end mt-20">
        <button class="btn-coal mr-20" (click)="modalRef.hide()">Ok, got it</button>
        <button class="btn-gray" (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
    </div>
</div>
<!-- </ng-template> -->