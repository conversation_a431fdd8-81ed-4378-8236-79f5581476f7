<ng-container *ngIf="!isGloabalSettingsLoading && !isProfileLoading; else loader">
  <form [formGroup]="basicDetailsForm" autocomplete="off" class="tb-mb-60">
    <div class="position-absolute right-20 ntop-65 align-center tb-d-none"
      *ngIf="isBasicInfoEditable || isProjectInfoEditable || selectedFile || isBrochureEdited">
      <button class="btn-gray" (click)="cancelChanges()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal ml-20" (click)="onSave()">{{ 'BUTTONS.save' | translate }}</button>
    </div>
    <div class="d-flex tb-flex-col">
      <div class="w-40pr p-20 bg-white br-8 tb-w-100 tb-mb-20">
        <div class="flex-between">
          <h5 class="fw-600">{{ 'USER_MANAGEMENT.basic-info' | translate }}</h5>
          <div title="Edit" *ngIf="canEditProfile" class="icon ic-pen ic-sm ic-accent-green cursor-pointer"
            (click)="isBasicInfoEditable = true"></div>
        </div>
        <div class="align-center mt-20">
          <div class="w-50">
            <div class="field-label-req clear-margin">{{'PROFILE.name'|translate}}</div>
          </div>
          <form-errors-wrapper class="w-50" label="{{'PROFILE.name'|translate}}"
            [control]="basicDetailsForm.controls['name']" [ngClass]="{'no-validation' : !isBasicInfoEditable}">
            <input type="text" id="inpProfName" required data-automate-id="inpProfName"
              [readOnly]="!isBasicInfoEditable" [placeholder]="isBasicInfoEditable ? 'ex. AASHISH  Group': '- - -'"
              formControlName="name" [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'">
          </form-errors-wrapper>
        </div>
        <div class="align-center mt-20">
          <div class="w-50">
            <div class="field-label-req clear-margin">{{'PROFILE.reraNo'|translate}}</div>
          </div>
          <form-errors-wrapper class="w-50" label="{{'PROFILE.reraNo'|translate}}"
            [control]="basicDetailsForm.controls['reraNo']" [ngClass]="{'no-validation' : !isBasicInfoEditable}">
            <input type="text" id="inpProfRera" data-automate-id="inpProfRera" [readOnly]="!isBasicInfoEditable"
              [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'" formControlName="reraNo"
              [placeholder]="isBasicInfoEditable ? 'ex. RAJ/P/2025/2141': '- - -'">
          </form-errors-wrapper>
        </div>
        <div class="align-center mt-20">
          <div class="w-50">
            <div class="field-label-req clear-margin">{{'PROFILE.phone'|translate}}</div>
          </div>
          <form-errors-wrapper class="w-50 no-validation" *ngIf="!isBasicInfoEditable"
            label="{{'PROFILE.phone'|translate}}">
            <input type="tel" id="inpProfPhone" data-automate-id="inpProfPhone" [readOnly]="!isBasicInfoEditable"
              [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'" formControlName="phoneNo"
              [placeholder]="isBasicInfoEditable ? 'ex. 9133XXXXXX': '- - -'">
          </form-errors-wrapper>
          <form-errors-wrapper class="w-50" *ngIf="isBasicInfoEditable" [control]="basicDetailsForm.controls['phoneNo']"
            label="{{'USER.phone-number' | translate}}">
            <ngx-mat-intl-tel-input #contactNoInput *ngIf="hasInternationalSupport"
              [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'" [preferredCountries]="preferredCountries"
              [readOnly]="!isBasicInfoEditable" [enablePlaceholder]="true" [enableSearch]="true"
              formControlName="phoneNo" class="no-validation contactNoInput" placeholder="9133XXXXXX">
            </ngx-mat-intl-tel-input>
            <ngx-mat-intl-tel-input #contactNoInput *ngIf="!hasInternationalSupport"
              [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'" [preferredCountries]="preferredCountries"
              [readOnly]="!isBasicInfoEditable" [onlyCountries]="preferredCountries" [enablePlaceholder]="true"
              [enableSearch]="true" formControlName="phoneNo" class="no-validation contactNoInput"
              placeholder="9133XXXXXX">
            </ngx-mat-intl-tel-input>
          </form-errors-wrapper>
        </div>
        <div class="align-center mt-20">
          <div class="w-50">
            <div class="field-label-req clear-margin">{{'PROFILE.email'|translate}}</div>
          </div>
          <form-errors-wrapper class="w-50" [control]="basicDetailsForm.controls['email']"
            label="{{'PROFILE.email'|translate}}" [ngClass]="{'no-validation' : !isBasicInfoEditable}">
            <input type="text" required id="inpProfEmail" data-automate-id="inpProfEmail"
              [readOnly]="!isBasicInfoEditable" formControlName="email"
              [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'"
              [placeholder]="isBasicInfoEditable ? 'ex. <EMAIL>': '- - -'">
          </form-errors-wrapper>
        </div>
        <div class="align-center mt-20">
          <div class="w-50">
            <div class="field-label clear-margin w-50 pr-20">{{'PROFILE.website'|translate}}</div>
          </div>
          <form-errors-wrapper class="w-50" label="{{'PROFILE.website' | translate}}"
            [ngClass]="{'no-validation' : !isBasicInfoEditable}">
            <input type="text" id="inpProfWebsite" data-automate-id="inpProfWebsite" [readOnly]="!isBasicInfoEditable"
              [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'" formControlName="website"
              [placeholder]="isBasicInfoEditable ? 'ex. aashish.com': '- - -'">
          </form-errors-wrapper>
        </div>
        <div class="align-center mt-20">
          <div class="w-50">
            <div class="field-label-req clear-margin">{{'PROFILE.address' | translate}}</div>
          </div>
          <div class="w-50 org-profile">
            <form-errors-wrapper class="small-dropdown" [ngClass]="{'no-validation' : !isBasicInfoEditable}"
              [control]="basicDetailsForm.controls['addressPlaceId']" label="{{'PROFILE.address' | translate}}"
              ResizableDropdown>
              <ng-select [virtualScroll]="true" (search)="searchPlaceTerm$.next($event.term)"
                formControlName="addressPlaceId" [readonly]="!isBasicInfoEditable"
                [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'"
                [placeholder]="isBasicInfoEditable ? 'ex. Hsr Layout, Bengaluru': '- - -'">
                <ng-option *ngFor="let places of placesList" [value]="places.placeId">
                  {{places.localityDisplayText}},{{places.localitySubtitleText}}</ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
        </div>
        <div class="align-center mt-20">
          <div class="w-50">
            <div class="field-label clear-margin w-50 pr-20">GST Number</div>
          </div>
          <form-errors-wrapper class="w-50" label="GST Number" [ngClass]="{'no-validation' : !isBasicInfoEditable}">
            <input type="text" id="inpGSTNumber" data-automate-id="inpGSTNumber" [readOnly]="!isBasicInfoEditable"
              [ngClass]="isBasicInfoEditable ? 'editable' : 'border-white'" formControlName="gstNumber"
              [placeholder]="isBasicInfoEditable ? 'ex. 29GGGGG1xxxxxx': '- - -'">
          </form-errors-wrapper>
        </div>
      </div>
      <div class="w-40pr tb-w-100 tb-mb-20">
        <div class="ml-16 bg-white p-20 br-8  tb-ml-0">
          <div class="flex-between">
            <h5 class="fw-600">{{ 'PROFILE.project-properties-info' | translate }}</h5>
            <div title="Edit" *ngIf="canEditProfile" class="icon ic-pen ic-sm ic-accent-green cursor-pointer"
              (click)="isProjectInfoEditable = true"></div>
          </div>
          <div class="align-center mt-20">
            <div class="field-label clear-margin w-50 pr-20">{{'PROFILE.units-sold' | translate}}</div>
            <form-errors-wrapper class="w-50" [ngClass]="{'no-validation' : !isProjectInfoEditable}">
              <input type="number" id="inpProfSold" data-automate-id="inpProfSold" [readOnly]="!isProjectInfoEditable"
                [ngClass]="isProjectInfoEditable ? 'editable' : 'border-white'" formControlName="soldUnits"
                [placeholder]="isProjectInfoEditable ? 'ex. 14': '- - -'">
            </form-errors-wrapper>
          </div>
          <div class="align-center mt-20">
            <div class="field-label clear-margin w-50 pr-20">{{'PROFILE.properties-sold' | translate}}</div>
            <form-errors-wrapper class="w-50" [ngClass]="{'no-validation' : !isProjectInfoEditable}">
              <input type="number" id="inpProfProp" data-automate-id="inpProfProp" [readOnly]="!isProjectInfoEditable"
                [ngClass]="isProjectInfoEditable ? 'editable' : 'border-white'" formControlName="soldProperties"
                [placeholder]="isProjectInfoEditable ? 'ex. 26': '- - -'">
            </form-errors-wrapper>
          </div>
          <div class="align-center mt-20">
            <div class="field-label clear-margin w-50 pr-20">{{'PROFILE.projects-developed' | translate}}</div>
            <form-errors-wrapper class="w-50" [ngClass]="{'no-validation' : !isProjectInfoEditable}">
              <input type="number" id="inpProfProj" data-automate-id="inpProfProj" [readOnly]="!isProjectInfoEditable"
                [ngClass]="isProjectInfoEditable ? 'editable' : 'border-white'" formControlName="projectsDeveloped"
                [placeholder]="isProjectInfoEditable ? 'ex. 29': '- - -'">
            </form-errors-wrapper>
          </div>
          <div class="align-center mt-20">
            <div class="field-label clear-margin w-50 pr-20">{{'PROFILE.properties-developed' | translate}}</div>
            <form-errors-wrapper class="w-50" [ngClass]="{'no-validation' : !isProjectInfoEditable}">
              <input type="number" id="inpProfPropdev" data-automate-id="inpProfPropdev"
                [readOnly]="!isProjectInfoEditable" formControlName="propertiesDeveloped"
                [ngClass]="isProjectInfoEditable ? 'editable' : 'border-white'"
                [placeholder]="isProjectInfoEditable ? 'ex. 04': '- - -'">
            </form-errors-wrapper>
          </div>
          <div class="align-center mt-20">
            <div class="field-label clear-margin w-50 pr-20">{{'PROFILE.experience' | translate}}</div>
            <form-errors-wrapper class="w-50" [ngClass]="{'no-validation' : !isProjectInfoEditable}">
              <input type="number" id="inpProfYrs" data-automate-id="inpProfYrs" [readOnly]="!isProjectInfoEditable"
                [ngClass]="isProjectInfoEditable ? 'editable' : 'border-white'" formControlName="experience"
                [placeholder]="isProjectInfoEditable ? 'ex. 19': '- - -'">
            </form-errors-wrapper>
          </div>
        </div>
      </div>
      <div class="w-20 tb-w-100 tb-mb-20">
        <div class="ml-16 bg-white p-10 br-8  tb-ml-0">
          <div class="fw-600 mb-10">{{ 'PROFILE.company-brochure' | translate }}</div>
          <div *ngIf="canEditProfile" class="br-6 border-green-dashed px-20 py-16 w-100 mb-8"
            [ngClass]="isFileTypeSupported?'bg-accent-green-light':'bg-accent-red'">
            <div class="fw-semi-bold mb-12 text-center">
              {{ 'GLOBAL.select' | translate }} {{ 'AUTH.and' | translate }} {{ 'LEADS.upload' | translate }}</div>
            <browse-drop-upload [allowedFileType]="'pdf'" [allowedFileFormat]="fileFormatToBeUploaded"
              (uploadedFile)="onFileSelection($event)" (uploadedFileName)="selectedFileName"></browse-drop-upload>
            <div class="align-center ml-20 mb-20" *ngIf="!isFileTypeSupported">
              <p class="text-danger">{{'BULK_LEAD.upload-correct-format' | translate}}</p>
            </div>
          </div>
          <p *ngIf="canEditProfile">{{ 'PROFILE.supported-formats' | translate}}: <span class="fw-600">pdf</span></p>
          <div *ngIf="selectedFile || profile?.brochurePath" class="flex-between mt-20">
            <a [href]="brochurePath ? s3ImageBucketURL+ brochurePath : s3ImageBucketURL+profile?.brochurePath"
              target="_blank" class="align-center">
              <img src="assets/images/pdf-icon.svg" alt="" class="obj-cover shadow h-30px">
              <span class="ml-8 text-truncate-1 break-all text-xs fw-600 text-nowrap flex-center">
                {{ 'PROFILE.organization-brochure' | translate }}</span></a>
            <div title="Delete" *ngIf="canDelete" class="bg-light-red icon-badge" (click)="deleteBrochure()"><span
                class="icon ic-delete m-auto ic-xxs" id="clkDeleteBrochure" data-automate-id="clkDeleteBrochure"></span>
            </div>
          </div>
          <div class="mt-10" *ngIf="selectedFile">{{ 'PROFILE.uploaded' | translate }} {{
            'PROFILE.organization-brochure' | translate }}</div>
        </div>
      </div>
    </div>
  </form>

  <ng-template #saveDataModal>
    <save-changes (SaveChanges)="saveInModal()" (DiscardChanges)="discardInModal()"
      (Hide)="closeModal()"></save-changes>
  </ng-template>
  <div class="footer align-center tb-flex-center d-none tb-d-block tb-d-flex tb-py-10"
    *ngIf="isBasicInfoEditable || isProjectInfoEditable || selectedFile || isBrochureEdited">
    <button class="btn-gray" (click)="cancelChanges()">{{ 'BUTTONS.cancel' | translate }}</button>
    <button class="btn-coal ml-20" (click)="onSave()">{{ 'BUTTONS.save' | translate }}</button>
  </div>
</ng-container>
<ng-template #loader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>