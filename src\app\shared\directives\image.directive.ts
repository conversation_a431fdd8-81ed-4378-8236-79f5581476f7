import { Directive, ElementRef, Input } from '@angular/core';

const STATIC_IMAGE_MAP: any = {
  defaultAvatar: 'assets/images/user.svg',
  property: 'assets/images/default-property-image.jpg',
  orgProfileLogo: 'assets/images/org-logo.svg',
  squareAvatar: 'assets/images/avatar.svg',
  defaultImg: 'assets/images/preview-gallery.svg',
  defaultTagsImg: 'assets/images/default-tags.svg',
  leadrat: 'assets/images/app-logo-green.svg'
};

@Directive({ selector: 'img[appImage]' })
export class AppImageURLDirective {
  @Input()
  public set appImage(value: string) {
    if (value.includes('data:')) {
      value = value.split('/').splice(3).join('/');
      setTimeout(() => this.setSrc(value));
    }
    setTimeout(() => this.setSrc(value));
  }

  defaultImagePath: string = '';

  @Input()
  public set type(value: string) {
    setTimeout(() => {
      this.defaultImagePath = STATIC_IMAGE_MAP[value];
    });
  }

  constructor(private el: ElementRef) {
    this.el.nativeElement.addEventListener('error', (error: any) => {
      this.el.nativeElement.src = this.defaultImagePath;
    });
  }

  private setSrc(value: string) {
    this.el.nativeElement.src = value;
  }
}
