import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { FetchCustomStatus, FetchCustomStatusSuccess, StatusActionTypes } from 'src/app/reducers/status/status.actions';
import { StatusService } from 'src/app/services/controllers/status.service';

@Injectable()
export class StatusEffects {
    getCustomStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(StatusActionTypes.FETCH_CUSTOM_STATUS),
            switchMap((action: FetchCustomStatus) => {
                return this.api.getCustomStatuses().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchCustomStatusSuccess(resp.items);
                        }
                        return new FetchCustomStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private actions: Actions,
        private store: Store<AppState>,
        private api: StatusService,
        private _notificationService: NotificationsService,
    ) { }
}
