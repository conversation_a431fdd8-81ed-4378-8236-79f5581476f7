import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FormGuard } from 'src/app/core/guards/forms.guard';
import { AboutUsComponent } from './about-us/about-us.component';
import { AwardsComponent } from './awards/awards.component';
import { BasicDetailsComponent } from './basic-details/basic-details.component';
import { ProfileDashboardComponent } from './profile-dashboard/profile-dashboard.component';
import { TestimonialsComponent } from './testimonials/testimonials.component';
import { SubscriptionComponent } from './subscription/subscription.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'profile-dashboard/basic-details',
    pathMatch: 'full',
  },
  {
    path: 'profile-dashboard',
    component: ProfileDashboardComponent,
    children: [
      {
        path: 'basic-details',
        component: BasicDetailsComponent,
        canDeactivate: [FormGuard],
      },
      {
        path: 'about-us',
        component: AboutUsComponent,
        canDeactivate: [FormGuard],
      },
      { path: 'testimonials', component: TestimonialsComponent },
      { path: 'awards', component: AwardsComponent },
      { path: 'subscription', component: SubscriptionComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ProfileRoutingModule { }
