import { AfterViewInit, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { skipWhile, takeUntil } from 'rxjs';
import { PropertyType } from 'src/app/app.enum';

import { Subject } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { FetchAllAmenities } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAmenities, getAmenitiesLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import { AddProjectAmenities, FetchProjectAmenitiesByIds, FetchProjectBasicDetailsById, UpdateProjectAminity } from 'src/app/reducers/project/project.action';
import { getBasicDetailsById, getProjectAmenitiesData } from 'src/app/reducers/project/project.reducer';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'amenities',
  templateUrl: './amenities.component.html'
})
export class AmenitiesComponent implements OnInit, AfterViewInit {
  masterAmenityList: any;
  allSelectedAmenities: any = [];
  toUpdateData: any;
  activeAmenitiesType: any;
  fetchedAmenityList: any;
  projectId: any;
  filterStr: String = "";
  filteredAmenityList: any = [];
  showFilteredData: boolean = false;
  selectedDataId: any;
  // currSearchTab: any;
  onChangeTab: string = "";

  amenitiesLoading: boolean;
  amenitiesCategory: any[];
  allAmenities: any[];
  filteredAmenities: any[];
  allSelected: boolean = false;
  selectedSection: string = 'Amenities';
  propertyTypeInput: any;
  @Output() selectedAmenities = new EventEmitter<any>();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  get noAmenitiesFound(): boolean {
    return this.filteredAmenities?.every(category => category?.amenities?.length === 0);
  }
  searchSubject = new Subject<string>();

  constructor(
    private router: Router,
    private _store: Store<AppState>,
    private sharedDataService: ShareDataService,
    private activatedRoute: ActivatedRoute,
    private modalRef: BsModalRef,
    public trackingService: TrackingService
  ) { }


  ngAfterViewInit(): void {
    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.selectedDataId = params.id;
        this.sharedDataService.setProjectTitleId(params.id);
        this._store.dispatch(new FetchProjectAmenitiesByIds(params.id));
        this._store.dispatch(new FetchProjectBasicDetailsById(params.id));
      }
    });
  }

  ngOnInit(): void {
    this.projectId = this.sharedDataService.getProjectTitleId();
    this._store
      .select(getProjectAmenitiesData)
      .pipe(takeUntil(this.stopper))
      .subscribe(data => {
        this.fetchedAmenityList = data?.[0] ? data?.[0] : [];
        this.allSelectedAmenities = [];
        this.fetchedAmenityList?.map((data: any) => {
          if (!this.allSelectedAmenities.includes(data)) {
            this.allSelectedAmenities.unshift(data);
          }
        })
        this.toUpdateData = this.fetchedAmenityList?.length;
        this.sharedDataService.updateSharedTabData(3);
      });

    this._store
      .select(getBasicDetailsById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        this.propertyTypeInput = data?.[0]?.projectType?.displayName

      })
    this._store.dispatch(new FetchAllAmenities());

    this._store
      .select(getAmenitiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.amenitiesLoading = isLoading;
      })

    this._store
      .select(getAllAmenities)
      .pipe(skipWhile(() => this.amenitiesLoading), takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.amenitiesCategory = [...data].map((category) => category?.categoryName);
        const activeAmenities = data
          .map((category: any) => ({
            ...category,
            amenities: category.amenities.map((amenity: any) => ({
              ...amenity,
              selected: this.allSelectedAmenities.includes(amenity?.id)
            })).filter((amenity: any) =>
              amenity.isActive && amenity.propertyType.includes(PropertyType[this.propertyTypeInput])
            ).sort((a: any, b: any) =>
              a.amenityDisplayName.localeCompare(b.amenityDisplayName)
            )
          }))
          .sort((a: any, b: any) => a.categoryName.localeCompare(b.categoryName))
          .filter((category: any) => category.amenities.length > 0);
        this.allAmenities = activeAmenities;

        this.filteredAmenities = activeAmenities;
        this.emitSelection();
      });
  }

  hideModal() {
    this.modalRef.hide();
  }

  onSelectAllChange(category: any, event: any) {
    const isChecked = event.target.checked;

    category.amenities = category.amenities.map((amenity: any) => ({
      ...amenity,
      selected: isChecked
    }));

    this.filteredAmenities = this.filteredAmenities.map((cat: any) =>
      cat.categoryName === category.categoryName ? category : cat
    );
    this.emitSelection();
  }

  isAllSelected(category: any): boolean {
    return category.amenities.every((amenity: any) => amenity.selected);
  }

  emitSelection() {
    const selectedAmenityIds = this.filteredAmenities
      .flatMap((category: any) => category.amenities)
      .filter((amenity: any) => amenity.selected)
      .map((amenity: any) => amenity.id);
    this.allSelectedAmenities = selectedAmenityIds
  }

  getFirstCharacter(name: string) {
    return name?.charAt(0).toUpperCase();
  }

  saveAndNext() {
    let payload = {
      "projectId": this.selectedDataId,
      "amenities": this.allSelectedAmenities
    };
    if (this.toUpdateData === 0) {
      this._store.dispatch(new AddProjectAmenities(payload));
    }
    else {
      this._store.dispatch(new UpdateProjectAminity(payload));
    }
    this.sharedDataService.setProjectTitleId(this.projectId);
    this.allSelectedAmenities = [];
    if (this.selectedDataId) {
      this.router.navigate(['/projects/edit-project/gallery/' + this.selectedDataId]);
    }
  }

  manageProject() {
    this.router.navigate(['/projects/manage-projects']);
  }

  search($event: any) {
    const searchTerm = $event.target.value.toLowerCase().replace(/\s+/g, '');
    const searchWords = searchTerm.split(' ').filter((word: any) => word);

    this.filteredAmenities = this.allAmenities.map((category: any) => {
      const filteredData = category.amenities?.filter((amenity: any) => {
        const normalizedAttributeName = amenity.amenityName.toLowerCase().replace(/\s+/g, '');

        return searchWords.every((word: any) =>
          normalizedAttributeName.includes(word)
        );
      });
      return { ...category, amenities: [...filteredData] };
    });
    this.trackingService.trackFeature(`Web.ProjectAmenities.${this.filteredAmenityList}.Search.DataEntry`);
  }


  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
