.top-navbar {
  @extend .d-flex, .flex-grow-1, .bg-white, .px-30, .py-12, .text-large, .text-mud, .fw-semi-bold;

  ul {
    @extend .align-center, .flex-wrap;

    a {
      @extend .mr-30;

      &.active {
        @extend .text-accent-green, .fw-700;
        
        .down-border {
          @extend .position-absolute, .border-green-2, .nbottom-10, .brtl-3, .brtr-3, .w-40
        }
      }
    }
  }
}

.top-navbar-black {
  @extend .top-navbar;
  flex-grow: unset !important;
  padding: 0 !important;

  ul {
    @extend .align-center, .flex-nowrap, .text-nowrap;

    a {
      margin-right: 0px !important;

      &.active {
        font-weight: 700 !important;
        color: $black !important;
      }

      // &.active-bar {
      //   @extend .position-absolute, .top-24, .left-0, .w-100, .br-2, .tb-d-none;
      //   content: '';
      //   border: 2px solid $black;
      // }

      // &.active-pointer {
      //   @extend .position-absolute, .top-24, .left-50;
      //   border-left: 6px solid transparent;
      //   border-right: 6px solid transparent;
      //   border-top: 9px solid $black;
      // }
    }
  }
}

.top-nav-bar {
  a {
    filter: grayscale(100%);

    &.active {
      filter: unset;
    }
  }

  span {
    &.active {
      @extend .fw-bold;
    }
  }
}

.section-navbar {
  @extend .d-flex, .flex-grow-1, .bg-white, .px-20, .py-12, .text-mud;

  ul {
    @extend .align-center, .flex-wrap;

    li:not(:first-child) {
      list-style-type: disc;
    }

    a {
      @extend .mr-30, .text-large;

      &.active {
        @extend .text-accent-green, .fw-600;
      }
    }
  }
}