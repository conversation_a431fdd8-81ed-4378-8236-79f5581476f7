<div>
    <div class="flex-between w-100 bg-coal px-24 py-12 text-white">
        <h3 class="fw-600">
            {{ 'TASK.view-task' | translate }}</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="px-12 py-24 bg-white">
        <h4 class="fw-semi-bold">{{data.title}}</h4>
        <div class="text-dark-gray text-sm my-12">assignee</div>
        <div class="align-center">
            <div class="flex-center-col">
                <h4 class="dot dot-x-xxl bg-green-50 text-accent-green fw-600 text-uppercase mb-4">
                    {{data.assignedFrom?.firstName[0] + data.assignedFrom?.lastName[0]}}</h4>
                <div>{{data.assignedFrom?.firstName + ' ' + data.assignedFrom?.lastName}}</div>
            </div>
            <span class="icon ic-down-arrow-secondary ic-xxxl ic-gray rotate-270 mx-12 mb-16"></span>
            <div class="flex-center-col">
                <h4 class="dot dot-x-xxl bg-green-50 text-accent-green fw-600 text-uppercase mb-4">
                    {{data.assignedUsers?.[0]?.firstName[0] + data.assignedUsers?.[0]?.lastName[0]}}</h4>
                <div>{{data.assignedUsers?.[0]?.firstName + ' ' + data.assignedUsers?.[0]?.lastName}}</div>
            </div>
        </div>
        <div class="align-center mt-12">
            <div class="text-dark-gray text-sm mr-12">status:</div>
            <div class="fw-semi-bold">
                <ng-container *ngIf="data.status == 1">
                    <p class="text-purple">{{TodoFilterType[data.status]}}</p>
                </ng-container>
                <ng-container *ngIf="data.status == 2">
                    <p class="text-green-900">{{TodoFilterType[data.status]}}</p>
                </ng-container>
                <ng-container *ngIf="data.status == 3">
                    <p class="text-orange">{{TodoFilterType[data.status]}}</p>
                </ng-container>
                <ng-container *ngIf="data.status == 4">
                    <p class="text-red">{{TodoFilterType[data.status]}}</p>
                </ng-container>
            </div>
        </div>
        <div class="align-center mt-12">
            <div class="text-dark-gray text-sm mr-12">priority:</div>
            <div class="btn btn-xxs br-20 flex-center px-12 fw-semi-bold pe-none"
                [ngClass]="'btn-' + (data.priority | lowercase)">{{data.priority}}</div>
        </div>
        <div class="align-center mt-12">
            <div class="text-dark-gray text-sm mr-8">date:</div>
            <div class="fw-600">
                {{ getTimeZoneDate(data.scheduledDateTime,userData?.timeZoneInfo?.baseUTcOffset,'dayMonthYear') }}</div>
            <div class="mx-24 text-dark-gray">&</div>
            <div class="text-dark-gray text-sm mr-8">time:</div>
            <div class="fw-600">
                {{ getTimeZoneDate(data.scheduledDateTime,userData?.timeZoneInfo?.baseUTcOffset, 'timeWithMeridiem') }}
            </div>
        </div>
        <div class="align-center mt-12">
            <div class="w-50">
                <div class="text-dark-gray text-sm mr-8">created by:</div>
                <div class="fw-600">{{getAssignedToDetails(data?.createdBy, users, true) || '--'}}</div>
            </div>
            <div class="w-50">
                <div class="text-dark-gray text-sm mr-8">created date:</div>
                <div class="fw-600">
                    {{ getTimeZoneDate(data?.createdOn,userData?.timeZoneInfo?.baseUTcOffset,'dayMonthYear')
                    }} at
                    {{ getTimeZoneDate(data?.createdOn,userData?.timeZoneInfo?.baseUTcOffset,'timeWithMeridiem')
                    }}</div>
            </div>
        </div>
        <div class="align-center mt-12">
            <div class="w-50">
                <div class="text-dark-gray text-sm mr-8">modified by:</div>
                <div class="fw-600">{{getAssignedToDetails(data?.lastModifiedBy, users, true) || '--'}}</div>
            </div>
            <div class="w-50">
                <div class="text-dark-gray text-sm mr-8">modified date:</div>
                <div class="fw-600">
                    {{ getTimeZoneDate(data?.lastModifiedOn,userData?.timeZoneInfo?.baseUTcOffset,'dayMonthYear')}} at
                    {{ getTimeZoneDate(data?.lastModifiedOn,userData?.timeZoneInfo?.baseUTcOffset,'timeWithMeridiem')}}
                </div>
            </div>
        </div>
        <div class="border-bottom-slate m-12"></div>
        <div class="text-dark-gray text-sm mr-8">description:</div>
        <div class="fw-600">{{data?.notes}}</div>
        <h5 [ngClass]="data?.isMarkedDone ? 'bg-linear-gradient-green' : 'bg-accent-green cursor-pointer border-green-30'"
            class="text-white fw-semi-bold py-12 br-4 text-center mt-16" (click)="markAsDone(data)">
            {{data?.isMarkedDone ? 'completed' : 'Mark as complete'}}</h5>
    </div>
</div>