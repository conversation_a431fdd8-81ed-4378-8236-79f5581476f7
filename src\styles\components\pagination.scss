.pagination {
  @extend .text-large;
  justify-content: flex-end;
  border-radius: 10px !important;

  .prev {
    background-color: $dark-400 !important;

    a {
      color: $slate-600 !important;
    }

    &.disabled {
      cursor: no-drop;
      background-color: $dark-400 !important;

      a {
        color: #77777733 !important;
        cursor: no-drop;
      }
    }

    img {
      width: 15px;
    }
  }

  .single-prev {
    background-color: $slate-80 !important;

    &.disabled {
      cursor: no-drop;

      a {
        color: #77777733 !important;
        cursor: no-drop;
      }
    }
  }

  .single-next {
    background-color: $slate-80 !important;

    a {
      color: $slate-600 !important;
    }

    &.disabled {
      cursor: no-drop;

      a {
        color: #77777733 !important;
        cursor: no-drop;
      }
    }
  }

  .next {
    background-color: $dark-400 !important;

    a {
      color: $slate-600 !important;
    }

    &.disabled {
      background-color: $dark-400 !important;
      cursor: no-drop;

      a {
        color: #77777733 !important;
        cursor: no-drop;
      }
    }

    img {
      width: 15px;
      transform: rotate(180deg);
    }
  }

  ul {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;

    li {
      margin: 2px 0px;
      width: 30px;
      height: 30px;
      line-height: 1.5;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid $dark-400;
      border-left-width: 0;
      background-color: $white;
      cursor: pointer;

      &.active {
        background: $accent-green;
        text-align: center;
        border: none;

        a {
          color: $white !important;
        }
      }

      &:first-child {
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
      }

      &:last-child {
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
      }

      a {
        color: $slate-600 !important;
        font-size: 12px;

        .fd-peg {
          display: block;
        }

        .nrm-peg {
          display: block;
        }

        &:hover {
          text-decoration: none;
        }
      }
    }
  }
}

.pagination-v2 {
  @extend .text-large, .bg-white, .text-black-10;

  &.disabled {
    cursor: no-drop;
    color: #8f8e8e33 !important;
  }

  ul {
    @extend .d-flex;

    li {
      @extend .flex-center, .w-32, .h-32, .cursor-pointer;

      &.active {
        @extend .bg-black-10, .text-white, .br-20;
      }
    }
  }
}