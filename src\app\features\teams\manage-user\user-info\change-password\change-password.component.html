<div class="align-center fw-600 ip-flex-col">
    <div class="text-large ip-w-100 ip-text-center ip-mb-20">
        <div>{{ 'AUTH.enter-new-password' | translate }}</div>
        <div class="text-light-slate mt-10">{{ 'AUTH.type-twice' | translate }}.</div>
    </div>
</div>
<form [formGroup]="changePasswordForm" class="flex-between w-100 mt-20 ip-flex-col">
    <div class="w-33 mr-50 ip-w-100 ip-mr-0">
        <div class="field-label-req">{{ 'GLOBAL.old' | translate }} {{ 'AUTH.password' | translate }}</div>
        <form-errors-wrapper label="{{ 'GLOBAL.old' | translate }} {{ 'AUTH.password' | translate }}"
            [control]="changePasswordForm?.controls['password']">
            <input [type]="showOldPassword ? 'text' : 'password'" placeholder="Enter the old password"
                formControlName="password">
            <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
                [ngClass]="showOldPassword ? 'ic-eye-slash' : 'ic-eye-solid'"
                (click)="showOldPassword = !showOldPassword"></a>
        </form-errors-wrapper>
    </div>
    <div class="w-33 mr-50 ip-w-100 ip-mr-0">
        <div class="field-label-req">{{ 'GLOBAL.new' | translate }} {{ 'AUTH.password' | translate }}</div>
        <form-errors-wrapper label="{{ 'GLOBAL.new' | translate }} {{ 'AUTH.password' | translate }}"
            [control]="changePasswordForm?.controls['newPassword']">
            <input [type]="showNewPassword ? 'text' : 'password'" placeholder="Enter the new password"
                formControlName="newPassword" minlength="5">
            <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
                [ngClass]="showNewPassword ? 'ic-eye-slash' : 'ic-eye-solid'"
                (click)="showNewPassword = !showNewPassword"></a>
        </form-errors-wrapper>
    </div>
    <div class="w-33 ip-w-100 ip-mr-0">
        <div class="field-label-req">{{ 'AUTH.confirm-password' | translate }}</div>
        <form-errors-wrapper label="{{ 'AUTH.confirm-password' | translate }}"
            [control]="changePasswordForm?.controls['confirmPassword']">
            <input [type]="showConfirmPassword ? 'text' : 'password'" placeholder="Re-enter the new password"
                formControlName="confirmPassword">
            <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
                [ngClass]="showConfirmPassword ? 'ic-eye-slash' : 'ic-eye-solid'"
                (click)="showConfirmPassword = !showConfirmPassword"></a>
            <div class="error-message" *ngIf="changePasswordForm?.controls?.confirmPassword?.status==='VALID'">
                {{changePasswordForm?.errors?.match_error}}</div>
            <div class="error-message text-accent-green" *ngIf="changePasswordForm?.valid">
                {{ 'AUTH.password' | translate }} {{ 'AUTH.matches' | translate }}</div>
        </form-errors-wrapper>
    </div>
</form>
<div class="flex-end my-20">
    <button class="btn-gray" (click)="cancelChanges()">{{ 'BUTTONS.cancel' | translate }}</button>
    <button class="btn-coal ml-20" (click)="changePassword()">{{ 'BUTTONS.save' | translate }}</button>
</div>