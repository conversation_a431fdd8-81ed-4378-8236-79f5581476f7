import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';

import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { OnError } from 'src/app/app.actions';
import {
  AddTracks,
  AddTracksSuccess,
  AnalyticsActionTypes,
  FetchActionsList,
  FetchActionsListSuccess,
  FetchFeaturesList,
  FetchFeaturesListSuccess,
} from './analytics.actions';
import { AnalyticsService } from 'src/app/services/controllers/analytics.service';

@Injectable()
export class AnalyticsEffects {
  getActionsList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AnalyticsActionTypes.FETCH_ACTIONS_LIST),
      map((action: FetchActionsList) => action),
      switchMap((data: any) => {
        return this.api.getActionsList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchActionsListSuccess(resp.data);
            }
            return new FetchActionsListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addtracks$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AnalyticsActionTypes.ADD_TRACKS),
      switchMap((action: AddTracks) => {
        return this.api.addTracks(action?.payload).pipe(
          map((resp: any) => {
            return new AddTracksSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getFeaturesList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AnalyticsActionTypes.FETCH_FEATURES_LIST),
      map((action: FetchFeaturesList) => action),
      switchMap((data: any) => {
        return this.api.getFeaturesList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchFeaturesListSuccess(resp.data);
            }
            return new FetchFeaturesListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(private actions$: Actions, private api: AnalyticsService) { }
}
