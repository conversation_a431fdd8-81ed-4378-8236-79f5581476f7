import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { PAGE_SIZE } from 'src/app/app.constants';
import { FileUploadStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getPages, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { FetchDeletedUserList } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUserDeletedList, getUserDeletedListIsLoading } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';

@Component({
  selector: 'deleted-users-tracker',
  templateUrl: './deleted-users-tracker.component.html',
})
export class DeletedUsersTrackerComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  rowData: any = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  defaultColDef: any;
  totalReqCount: number;
  getPages = getPages;
  filtersPayload = {
    pageNumber: 1,
    pageSize: this.pageSize,
    path: 'user',
  };
  isDeletedUserListLoading: boolean = true;


  userData: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    public modalService: BsModalService,
    private _store: Store<AppState>
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();

    this._store
      .select(getUserDeletedList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data?.items;
        this.totalReqCount = data?.totalCount;
      });
  }

  ngOnInit(): void {
    this._store
      .select(getUserDeletedListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isDeletedUserListLoading = isLoading;
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 85,
        valueGetter: (params: any) => [
          params.data?.status ? FileUploadStatus[params.data?.status] : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Deleted By',
        field: 'Deleted By',
        minWidth: 140,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.deletedBy?.name ? params.data?.deletedBy?.name : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Deleted User',
        field: 'Deleted User',
        minWidth: 140,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.userName ? params.data?.userName : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Leads Count',
        field: 'Leads Count',
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.leadsCount ? params.data?.leadsCount : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Secondary Leads Count',
        field: 'Secondary Leads Count',
        minWidth: 110,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.secondaryLeadsCount ? params.data?.secondaryLeadsCount : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Data Count',
        field: 'Data Count',
        minWidth: 90,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.prospectsCount ? params.data?.prospectsCount : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Requested on',
        field: 'Requested on',
        minWidth: 150,
        valueGetter: (params: any) => [
          getTimeZoneDate(params.data?.createdOn, this.userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime'),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap">${params.value}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName && this.userData?.shouldShowTimeZone && params.value[0]
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Error Message',
        field: 'Error Message',
        minWidth: 180,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.message ? params.data?.message : '--',
        ],
        cellRenderer: (params: any) => {
          const message = params.value[0];
          const isError = message !== '--';
          const className = isError ? 'text-danger' : '';
          return `<p class="${className} text-truncate-2 text-sm">${message}</p>`;
        },
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.updateTrackerList();
  }

  updateTrackerList() {
    this._store.dispatch(
      new FetchDeletedUserList(
        this.filtersPayload?.pageNumber,
        this.filtersPayload.pageSize
      )
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
