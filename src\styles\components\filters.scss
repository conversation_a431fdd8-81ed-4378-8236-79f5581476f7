.grid-filters {
  @extend .p-10, .no-validation;

  .date-picker {
    background-color: $white !important;
    border: none;
    color: $slate-500 !important;
    @extend .text-normal;
    padding: 4px 0 4px 10px;
    position: relative;

    input {
      border: none !important;
      outline: none !important;
      background-color: transparent !important;
      font-weight: 400 !important;
    }

    &.green-900{
      input {
        border: none !important;
        outline: none !important;
        background-color: transparent !important;
        font-weight: 400 !important;
        color: $green-900 !important;
        font-size: 9px;
      }
    }

    .icon {
      position: absolute;
    }

    &.no-bg {
      background-color: transparent !important;
    }
  }

  .filter-badge {
    background-color: $slate-400 !important;
    color: $slate-500 !important;
    border-radius: 7px;
    @extend .text-normal;
    padding: 4px 10px;
  }

  .filter-dropdown-bg {
    @extend .px-8, .w-150, .br-5, .mr-4, .bg-white, .pb-4;
  }

  ng-select {
    @extend .border-0;
    background-color: unset;
    padding: 0px !important;
  }

  .ng-select .ng-arrow-wrapper .ng-arrow,
  .ng-select.ng-select-focused .ng-select-container .ng-arrow-wrapper .ng-arrow,
  .ng-select .ng-select-container .ng-value-container .ng-placeholder,
  .ng-select .ng-select-container {
    @extend .text-light-gray;
  }

  .lead-date {
    .ng-dropdown-panel {
      width: 120px;
    }

    &.ng-select.ng-select-single .ng-select-container .ng-value-container,
    &.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
      word-break: unset !important;
      white-space: nowrap !important;
    }
  }
}

.filters-grid {
  @extend .grid-filters;

  .dropdown-date-picker {
    border: 1px solid $dark-400 !important;
  }

  .ng-select {
    @extend .bg-white;
    padding: 0px 6px !important;

    .ng-select-container {
      min-height: 30px !important;
    }

    .ng-value-container {
      @extend .mr-10;
    }
  }

  .ng-select .ng-arrow-wrapper .ng-arrow,
  .ng-select.ng-select-focused .ng-select-container .ng-arrow-wrapper .ng-arrow,
  .ng-select .ng-select-container {
    @extend .text-black;
  }

  .date-picker {
    border-left: 1px solid $dark-400;
  
    ::-webkit-input-placeholder {
      font-size: 9px;
    }
  
    &.green-900 {
      ::-webkit-input-placeholder {
        color: $green-900 !important;
        font-size: 9px;
      }
    }
  }
}

.hide-arrow {
  .ng-arrow-wrapper {
    display: none;
  }
}