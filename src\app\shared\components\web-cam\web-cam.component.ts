import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { WebcamImage, WebcamInitError } from 'ngx-webcam';
import { Observable, Subject } from 'rxjs';

@Component({
  selector: 'web-cam',
  templateUrl: './web-cam.component.html',
})
export class WebcamComponent implements OnDestroy {
  @Output() photoCaptured = new EventEmitter<WebcamImage>();
  @Input() isClockInMode: boolean;
  @Input() isMandatorySelfieClockin: boolean;
  @Input() isMandatorySelfieClockout: boolean;

  public showWebcam = true;
  public multipleWebcamsAvailable = false;
  public deviceId: string;
  public errors: WebcamInitError[] = [];
  private stream: MediaStream;
  private trigger: Subject<void> = new Subject<void>();

  constructor(public bsModalRef: BsModalRef) { }

  public triggerSnapshot(): void {
    this.trigger.next();
  }

  public handleImage(webcamImage: WebcamImage): void {
    this.photoCaptured.emit(webcamImage);
    this.stopStream();
    this.bsModalRef.hide();
  }

  setStream(stream: MediaStream) {
    this.stream = stream;
  }

  stopStream() {
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
    }
  }
  public cameraWasSwitched(deviceId: string): void {
    this.deviceId = deviceId;
  }

  public get triggerObservable(): Observable<void> {
    return this.trigger.asObservable();
  }

  ngOnDestroy() {
    this.stopStream();
  }
}
