import { Component, EventEmitter, TemplateRef, ViewChild } from '@angular/core';
import { MapInfoWindow } from '@angular/google-maps';
import { Store } from '@ngrx/store';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'clock-in-out',
  templateUrl: './clock-in-out.component.html',
})
export class ClockInOutComponent implements ICellRendererAngularComp {
  @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  params: any;
  clockData: any;
  moment = moment;
  currentImage: any;

  markers: any[] = [];
  center = {
    lat: 12.9106262,
    lng: 77.6405173,
  };
  latitude: number = 12.8898194;
  longitude: number = 77.64237;
  label = 1;
  selectedAddress: string;
  s3BucketUrl: string = env.s3ImageBucketURL;
  userData: any;
  getTimeZoneDate = getTimeZoneDate;

  constructor(
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private _store: Store<AppState>
  ) { }

  ngOnInit(): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  agInit(params: any): void {
    this.params = params;
    if (this.params.value[0]?.data) {
      this.clockData = [...this.params.value[0]?.data]?.reverse();
    }
  }

  refresh(): boolean {
    return false;
  }

  openLocationModal(locationModal: any) {
    this.modalRef = this.modalService.show(locationModal, {
      class: 'right-modal modal-650 ip-modal-unset',
    });
  }

  openInfoWindow(marker: any) {
    this.infoWindow.open(marker);
  }

  showLocation(lat: any, long: any, loc: any, label: any) {
    if (loc)
      this.markers[label - 1] = {
        latitude: Number(lat),
        longitude: Number(long),
        label: String(label),
        address: loc,
      };
    this.selectedAddress = this.markers[label - 1]?.address;
    this.center = {
      lat: Number(lat),
      lng: Number(long),
    };
  }

  openImage(image: any, modal: TemplateRef<any>) {
    this.currentImage = image;
    this.modalRef = this.modalService.show(modal, {
      class: 'modal-350 modal-dialog-centered ph-modal-unset',
      keyboard: false,
    });
  }
}
