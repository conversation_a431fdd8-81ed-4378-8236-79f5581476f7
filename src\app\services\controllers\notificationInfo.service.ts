import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class GetNotificationInfoService extends BaseService<any> {
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}/`;
  }

  getResourceUrl(): string {
    return 'notificationinfo';
  }

  notificationCall(notificationInfo: any) {
    return this.http.post(
      `${this.serviceBaseUrl}notification/user?UserId=${notificationInfo?.payload?.userId}&ContactNo=${encodeURIComponent(notificationInfo?.payload?.contactNo)}&Name=${notificationInfo?.payload?.name}`,
      ''
    );
  }
}
