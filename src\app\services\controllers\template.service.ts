import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root'
})
export class TemplateService extends BaseService<any> {
  serviceBaseUrl: string;

  getResourceUrl(): string {
    return 'template';
  }

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getModuleTemplate(payload: any, pageNumber?:number,pageSize?:number,searchTerm:string = '') {
    if ((pageNumber !== undefined && pageNumber !== null) && (pageSize !== undefined && pageSize !== null)) {
      return this.http.get(`${this.serviceBaseUrl}/?ModuleNames=${payload}&PageNumber=${pageNumber}&PageSize=${pageSize}&Search=${searchTerm}`);
    }    
  return this.http.get(`${this.serviceBaseUrl}/?ModuleNames=${payload}`);
  }

  updateTemplate(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}`, payload);
  }
}
