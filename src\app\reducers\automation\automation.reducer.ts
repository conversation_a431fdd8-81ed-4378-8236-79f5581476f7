import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  AutomationActionTypes,
  FetchIntegrationAssignmentSuccess,
  FetchPriorityListSuccess,
  FetchUserAssignmentByEntitySuccess,
  FetchUserAssignmentByIdSuccess,
  FetchUserAssignmentSuccess,
} from './automation.actions';

export type AutomationState = {
  priorityList?: Array<any>;
  integrationAssignment?: Object;
  userAssignment?: Array<any>;
  userAssignmentById?: Object;
  userAssignmentByEntity: any;
};

const initialState: AutomationState = {
  priorityList: [],
  integrationAssignment: {},
  userAssignmentById: {},
  userAssignmentByEntity: {}
};

export function automationReducer(
  state: AutomationState = initialState,
  action: Action
): AutomationState {
  switch (action.type) {
    case AutomationActionTypes.FETCH_PRIORITY_LIST_SUCCESS:
      return {
        ...state,
        priorityList: (action as FetchPriorityListSuccess).response.items,
      };
    case AutomationActionTypes.FETCH_USER_ASSIGNMENT_SUCCESS:
      return {
        ...state,
        userAssignment: (action as FetchUserAssignmentSuccess).response.items,
      };
    case AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ID_SUCCESS:
      return {
        ...state,
        userAssignmentById: (action as FetchUserAssignmentByIdSuccess).response
          .data,
      };
    case AutomationActionTypes.FETCH_USER_ASSIGNMENT_BY_ENTITY_SUCCESS:
      return {
        ...state,
        userAssignmentByEntity: (action as FetchUserAssignmentByEntitySuccess)
          .response.data,
      };
    case AutomationActionTypes.FETCH_INTEGRATION_ASSIGNMENT_SUCCESS:
      return {
        ...state,
        integrationAssignment: (action as FetchIntegrationAssignmentSuccess)
          .response.data,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.automation;

export const getPriorityList = createSelector(
  selectFeature,
  (state: AutomationState) => state.priorityList
);

export const getIntegrationAssignment = createSelector(
  selectFeature,
  (state: AutomationState) => state.integrationAssignment
);

export const getUserAssignmentByEntity = createSelector(
  selectFeature,
  (state: AutomationState) => state.userAssignmentByEntity
);