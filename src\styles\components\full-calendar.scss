.fc-theme-standard .fc-scrollgrid {
  border-top: 1px solid $navy-100 !important;
}

.leave-fullcalendar {
  full-calendar {
    .fc-header-toolbar.fc-toolbar {
      display: none;
    }

    .fc-view-harness {
      min-width: 730px;

      .fc-scroller {
        .fc-daygrid-day {
          height: 70px !important;
          background-color: $white;
        }
      }
    }

    thead {
      tr {
        .fc-col-header-cell:first-child {
          .fc-scrollgrid-sync-inner {
            border-left: 0.7px solid $navy-100;
          }
        }

        .fc-col-header-cell:last-child {
          .fc-scrollgrid-sync-inner {
            border-right: 0.7px solid $navy-100;
          }
        }

        .fc-col-header-cell {
          background-color: $accent-blue-light;
          color: $navy-100;
          @extend .fw-semi-bold;
          border: 0.7px solid $navy-100;
        }

        td {
          .fc-daygrid-day-frame {
            .fc-daygrid-day-top {
              justify-content: left;
            }

            .fc-daygrid-day-top>.fc-daygrid-day-number,
            .fc-daygrid-day-events>.fc-daygrid-day-bottom,
            .fc-daygrid-day-bg {
              padding: 4px 8px !important;
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

.dot-falling {
  @extend .d-inline-block, .br-50, .bg-black-100;
  width: 4px;
  height: 4px;

  &.dot-white {
    background-color: $white !important;
  }
}

.container .dot-falling:nth-last-child(1) {
  animation: fallingAnimation 1.6s 0.1s ease-in infinite;
}

.container .dot-falling:nth-last-child(2) {
  animation: fallingAnimation 1.6s 0.2s ease-in infinite;
}

.container .dot-falling:nth-last-child(3) {
  animation: fallingAnimation 1.6s 0.3s ease-in infinite;
}

@keyframes fallingAnimation {
  0% {
    transform: translate(0, -15px);
    opacity: 0;
  }

  25%,
  50%,
  75% {
    transform: translate(0, 0);
    opacity: 1;
  }

  100% {
    transform: translate(0, 15px);
    opacity: 0;
  }
}

.dot-elastic {
  @extend .position-relative, .bg-gray-darker, .br-5;
  width: 5px;
  height: 5px;
  animation: dot-elastic 1s infinite linear;
}

.dot-elastic::before,
.dot-elastic::after {
  content: "";
  @extend .d-inline-block, .position-absolute, .top-0;
}

.dot-elastic::before {
  @extend .bg-gray-darker, .br-5;
  left: -8px;
  width: 5px;
  height: 5px;
  animation: dot-elastic-before 1s infinite linear;
}

.dot-elastic::after {
  @extend .bg-gray-darker, .br-5;
  left: 8px;
  width: 5px;
  height: 5px;
  animation: dot-elastic-after 1s infinite linear;
}

@keyframes dot-elastic-before {
  0% {
    transform: scale(1, 1);
  }

  25% {
    transform: scale(1, 1.5);
  }

  50% {
    transform: scale(1, 0.67);
  }

  75% {
    transform: scale(1, 1);
  }

  100% {
    transform: scale(1, 1);
  }
}

@keyframes dot-elastic {
  0% {
    transform: scale(1, 1);
  }

  25% {
    transform: scale(1, 1);
  }

  50% {
    transform: scale(1, 1.5);
  }

  75% {
    transform: scale(1, 1);
  }

  100% {
    transform: scale(1, 1);
  }
}

@keyframes dot-elastic-after {
  0% {
    transform: scale(1, 1);
  }

  25% {
    transform: scale(1, 1);
  }

  50% {
    transform: scale(1, 0.67);
  }

  75% {
    transform: scale(1, 1.5);
  }

  100% {
    transform: scale(1, 1);
  }
}

@keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }

  100% {
    background-position: 468px 0;
  }
}

.shimmer-animation {
  background-color: #f6f7f8;
  background-image: linear-gradient(to right,
      #f6f7f8 0%,
      #edeef1 20%,
      #f6f7f8 40%,
      #f6f7f8 100%);
  background-repeat: no-repeat;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: placeholderShimmer;
  animation-timing-function: linear;

  &.gray {
    background-color: #c9cfd5;
    background-image: linear-gradient(to right,
        #c9cfd5 0%,
        #edeef1 20%,
        #c9cfd5 40%,
        #c9cfd5 100%);
  }
}

.loader-shimmer-banner {
  height: 10rem;
  border-radius: 5px;
}

@keyframes loader_box {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.square {
  background: #ddd;
  width: 10px;
  height: 10px;
  position: absolute;
  left: 50%;
  margin-top: -5px;
  margin-left: -5px;
}

#sq1 {
  margin-top: -25px;
  margin-left: -25px;
  animation: loader_box 675ms ease-in-out 0s infinite alternate;
}

#sq2 {
  margin-top: -25px;
  animation: loader_box 675ms ease-in-out 75ms infinite alternate;
}

#sq3 {
  margin-top: -25px;
  margin-left: 15px;
  animation: loader_box 675ms ease-in-out 150ms infinite;
}

#sq4 {
  margin-left: -25px;
  animation: loader_box 675ms ease-in-out 225ms infinite;
}

#sq5 {
  animation: loader_box 675ms ease-in-out 300ms infinite;
}

#sq6 {
  margin-left: 15px;
  animation: loader_box 675ms ease-in-out 375ms infinite;
}

#sq7 {
  margin-top: 15px;
  margin-left: -25px;
  animation: loader_box 675ms ease-in-out 450ms infinite;
}

#sq8 {
  margin-top: 15px;
  animation: loader_box 675ms ease-in-out 525ms infinite;
}

#sq9 {
  margin-top: 15px;
  margin-left: 15px;
  animation: loader_box 675ms ease-in-out 600ms infinite;
}

.loader-blink span {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 100%;
  opacity: 0;
  margin-right: 2px;
}

.loader-blink span:nth-child(1) {
  @extend .bg-green-40;
  animation: opacityChange 1s ease-in-out infinite;
}

.loader-blink span:nth-child(2) {
  @extend .bg-orange-800;
  animation: opacityChange 1s ease-in-out 0.33s infinite;
}

.loader-blink span:nth-child(3) {
  @extend .bg-blue-50;
  animation: opacityChange 1s ease-in-out 0.66s infinite;
}

@keyframes opacityChange {

  0%,
  100% {
    opacity: 0;
  }

  60% {
    opacity: 1;
  }
}

.spin-loader {
  height: 0;
  width: 0;
  padding: 15px;
  border: 6px solid #ccc;
  border-right-color: #888;
  border-radius: 22px;
  -webkit-animation: spinRotate 1s infinite linear;
  /* left, top and position just for the demo! */
  position: relative;
  left: 50%;
  top: 50%;
}

@keyframes spinRotate {

  /* 100% keyframe for  clockwise. 
     use 0% instead for anticlockwise */
  100% {
    -webkit-transform: rotate(360deg);
  }
}

.uploading-text {
  animation: moveLeft 0.5s linear forwards;
}

@keyframes moveLeft {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-21%);
  }
}

.custom-upload-button.isUploadingImage .custom-button-wrapper {
  animation: moveLeft 0.5s linear forwards;
}

.custom-upload-button.isUploadingImage .custom-icon-container {
  display: none;
}

.custom-upload-button.isUploadingImage {
  position: relative;
  overflow: hidden;
  border: 2px dashed $accent-green-100;
}

.custom-upload-button.isUploadingImage::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: $accent-green-100;
  opacity: 0.2;
  animation: fillAnimation 0.5s linear forwards;
}

@keyframes fillAnimation {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}