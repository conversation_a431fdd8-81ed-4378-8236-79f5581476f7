import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import {
  catchError,
  map,
  switchMap,
  withLatestFrom
} from 'rxjs/operators';
import { Store, select } from '@ngrx/store';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AssignTotalCount,
  CreateEmailSMTP,
  CreateEmailSMTPSuccess,
  DeleteEmailSMTP,
  EditEmailSMTP,
  EditEmailSMTPSuccess,
  EmailSettingsActions,
  FetchEmailSMTPListByUserId,
  FetchEmailSMTPListByUserIdSuccess,
  SendTestEmail,
  SendTestEmailBulk,
  getEmailSMTPList,
  getEmailSMTPListSuccess,
} from './email-settings.action';
import { getFiltersPayload } from './email-settings.reducer';
import { EmailSMTPService } from 'src/app/services/controllers/emailSMTP.service';

@Injectable()
export class EmailSettingsEffects {
  getEmailSMTP$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EmailSettingsActions.GET_EMAIL_SMTP),
      withLatestFrom(this.store.pipe(select(getFiltersPayload))),
      switchMap(([action, payload]) =>
        this.api.getEmailSMTP(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.store.dispatch(new AssignTotalCount(resp.totalCount));
              return new getEmailSMTPListSuccess(resp.items);
            } else {
              return new getEmailSMTPListSuccess();
            }
          }),
          catchError((error) => of(new OnError(error)))
        )
      )
    )
  );

  createEmailSMTP$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EmailSettingsActions.CREATE_EMAIL),
      switchMap((action: CreateEmailSMTP) =>
        this.api.createEmailSMTP(action.payload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Email Created Successfully');
              return [
                new getEmailSMTPList(),
                new CreateEmailSMTPSuccess(resp)
              ];
            } else {
              return of (new OnError(resp.error));
            }
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  sendTestEmail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EmailSettingsActions.SEND_TEST_EMAIL),
      switchMap((action: SendTestEmail) =>
        this.api.sendTestEmail(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Test Email Sent Successfully');
            } else {
              return null;
            }
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  sendTestEmailBulk$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(EmailSettingsActions.SEND_TEST_EMAIL_BULK),
        switchMap((action: SendTestEmailBulk) =>
          this.api.sendTestEmailBulk(action.payload, action.Files).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success('Email Sent Successfully');
              } else {
                return null;
              }
            }),
            catchError((err) => of(new OnError(err)))
          )
        )
      ),
    { dispatch: false }
  );

  editEmailSMTP$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EmailSettingsActions.EDIT_EMAIL_SMTP),
      switchMap((action: EditEmailSMTP) =>
        this.api.editEmailSMTP(action.payload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Email Updated Successfully');
              return [
                new getEmailSMTPList(),
                new EditEmailSMTPSuccess()
              ];
            } else {
              return of (new OnError(resp.error));
            }
          })
        )
      )
    )
  );

  deleteEmailSMTP$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EmailSettingsActions.Delete_EMAIL_SMTP),
      switchMap((action: DeleteEmailSMTP) =>
        this.api.deleteEmailSMTP(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Email deleted Successfully');
              return new getEmailSMTPList();
            } else {
              return new OnError(resp.error);
            }
          })
        )
      )
    )
  );

  getEmailListByUserId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(EmailSettingsActions.GET_EMAIL_SMTP_BY_USERID),
      switchMap((action: FetchEmailSMTPListByUserId) =>
        this.api.getEmailSMTPByUserId(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchEmailSMTPListByUserIdSuccess(resp.data);
            }
            return new FetchEmailSMTPListByUserIdSuccess();
          })
        )
      )
    )
  );

  constructor(
    private actions$: Actions,
    private _notificationService: NotificationsService,
    private api: EmailSMTPService,
    private store: Store<AppState>
  ) {}
}
