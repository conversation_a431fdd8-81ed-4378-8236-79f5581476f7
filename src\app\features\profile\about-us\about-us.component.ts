import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Observable, of, takeUntil } from 'rxjs';
import { SOCIAL_MEDIA } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { CanComponentDeactivate } from 'src/app/core/guards/forms.guard';
import { AboutUs, Profile } from 'src/app/core/interfaces/profile.interface';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  UpdateAboutUs,
  UpdateSocialMedia,
} from 'src/app/reducers/profile/profile.actions';
import { getProfile } from 'src/app/reducers/profile/profile.reducers';

@Component({
  selector: 'about-us',
  templateUrl: './about-us.component.html',
})
export class AboutUsComponent implements OnInit, CanComponentDeactivate, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('saveDataModal') saveDataModal: TemplateRef<any>;
  canRedirect: boolean = false;
  socialMediaId: FormControl = new FormControl(null, Validators.required);
  isAboutUsEditable: boolean = false;
  isVisionEditable: boolean = false;
  isWhatWeDoEditable: boolean = false;
  aboutUsForm: FormGroup;
  profile: Profile;
  notLinked: boolean = true;
  socialMedias: Array<any> = SOCIAL_MEDIA;
  currentSocialMediaName: string;
  canEditProfile: boolean = false;
  isProfileLoading:boolean;

  constructor(
    private fb: FormBuilder,
    private _store: Store<AppState>,
    private modalService: BsModalService,
    private modalRef: BsModalRef
  ) {
    this._store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit.includes('OrgProfile')) this.canEditProfile = true;
      });

    this.aboutUsForm = this.fb.group({
      aboutUs: [''],
      mission: [''],
      whatWeDo: [''],
    });
  }

  canDeactivate(): Observable<boolean> {
    if (
      this.aboutUsForm.dirty &&
      (this.isAboutUsEditable ||
        this.isVisionEditable ||
        this.isWhatWeDoEditable)
    ) {
      this.modalRef = this.modalService.show(
        this.saveDataModal,
        Object.assign(
          {},
          {
            class: 'modal-350 modal-dialog-centered ip-modal-unset',
          }
        )
      );
      return this.modalService.onHide;
    }
    return of(true);
  }

  ngOnInit(): void {
    this._store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.profile = data;
        this.isProfileLoading = data.isProfileLoading;
        this.patchValues(this.profile);
      });
  }

  patchValues(profile: Profile) {
    this.aboutUsForm.patchValue({
      aboutUs: profile.aboutUs,
      mission: profile.missionAndVision,
      whatWeDo: profile.bio,
    });
  }

  onSave() {
    let aboutUs: AboutUs;
    let payload = {
      ...aboutUs,
      aboutUs: this.aboutUsForm.value.aboutUs,
      missionAndVision: this.aboutUsForm.value.mission,
      bio: this.aboutUsForm.value.whatWeDo,
    };
    this.isAboutUsEditable = false;
    this.isVisionEditable = false;
    this.isWhatWeDoEditable = false;
    this._store.dispatch(new UpdateAboutUs(payload));
  }

  openAddAccountModal(accountAddModal: any, accountName: any) {
    this.currentSocialMediaName = accountName;
    this.socialMediaId.setValue(
      this.profile?.socialMedias?.find(
        (i) => i.socialMediaPlatform == accountName
      )?.socialMediaId
    );
    this.modalRef = this.modalService.show(accountAddModal, {
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
  }

  saveSocialMediaDetails() {
    if (this.socialMediaId.value) {
      let existingSocialMedias: any[] = [...this.profile.socialMedias];
      let newSocialMediaAcc = {
        socialMediaPlatform: this.currentSocialMediaName,
        socialMediaId: this.socialMediaId.value,
      };
      var index = existingSocialMedias?.findIndex(
        (i) => i.socialMediaPlatform == this.currentSocialMediaName
      );
      index >= 0
        ? (existingSocialMedias[index] = newSocialMediaAcc)
        : existingSocialMedias.push(newSocialMediaAcc);
      var payload = {
        socialMedias: existingSocialMedias,
      };
      this._store.dispatch(new UpdateSocialMedia(payload));
      this.closeModal();
    }
  }

  isNotLinked(accountName: any) {
    let data = this.profile?.socialMedias?.find(
      (i) => i.socialMediaPlatform == accountName
    )?.socialMediaId;
    return data == null || data == '';
  }

  getIdbyScName(accountName: any) {
    return this.profile?.socialMedias?.find(
      (i) => i.socialMediaPlatform == accountName
    )?.socialMediaId;
  }

  saveInModal() {
    this.onSave();
    this.canRedirect = true;
    this.modalRef.hide();
  }

  discardInModal() {
    this.canRedirect = true;
    this.modalRef.hide();
  }

  closeModal() {
    this.modalRef.hide();
    this.socialMediaId.reset();
  }

  cancelChanges() {
    this.patchValues(this.profile);
    this.isAboutUsEditable = false;
    this.isVisionEditable = false;
    this.isWhatWeDoEditable = false;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
