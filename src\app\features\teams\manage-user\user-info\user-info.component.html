<div routerLink='/teams/manage-user' *ngIf="canNavigateToUsers" [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
  class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div class="w-100 d-flex tb-flex-col">
  <div class="w-30 tb-w-100">
    <div class="m-20 border-gray bg-white br-6">
      <div class="align-center p-16">
        <div class="w-80px h-80px">
          <img [appImage]="userData?.imageUrl ? s3BucketUrl+userData?.imageUrl : ''" [type]="'squareAvatar'"
            class="obj-cover br-4 border" width="80px" height="80px" alt="">
        </div>
        <div class="ml-10">
          <h3 class="fw-700 text-truncate-1 break-all mb-4">{{userData?.firstName}} {{userData?.lastName}}</h3>
          <span class="br-12 text-white px-8 py-2"
            [ngClass]="userData?.isActive ? 'border-light-green-500 bg-light-green-400' : 'bg-red-350 border-red-30'">
            {{((userData?.isActive ? 'LABEL.active' : 'LABEL.inactive') | translate)?.toLowerCase()}}</span>
          <div class="align-center mt-10">
            <div title="Edit User" class="bg-light-pearl br-10 cursor-pointer w-30px h-30px flex-center"
              (click)="editUser()">
              <span class="icon ic-pen ic-coal ic-xxs"></span>
            </div>
            <div [title]="userData?.isActive ? 'Active' : 'Deactive'" *ngIf="!isLoggedInUser"
              class="bg-light-pearl br-10 cursor-pointer w-30px h-30px flex-center ml-10"
              (click)="initToggleUserStatus(userData)">
              <img width="16" height="16" alt=""
                [type]="'leadrat'" [appImage]="userData?.isActive ? s3BucketUrl + 'logos/user-activate.svg' : s3BucketUrl + 'logos/user-deactivate.svg'">
            </div>
            <div title="Roles & permissions" class="bg-light-pearl br-10 cursor-pointer w-30px h-30px flex-center ml-10"
              (click)="viewRoles(rolesView)">
              <span class="icon ic-user-setting ic-coal ic-sm"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="border-bottom"></div>
      <div class="tb-d-flex ph-flex-col max-h-100-215 scrollbar tb-max-h-unset">
        <div class="tb-w-50 ph-w-100">
          <div class="mt-16 fw-semi-bold">
            <div class="cursor-pointer align-center gap-3 ml-20" (click)="showBasicDetails = !showBasicDetails">
              <span class="ic-triangle-down icon ic-coal ic-xxxs"
                [ngClass]="{ 'rotate-270' : !showBasicDetails}"></span>
              <h5 class="fw-700">{{ 'USER.basic-details' | translate }}</h5>
            </div>
            <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
            <div class="ml-24 mr-10" *ngIf="showBasicDetails">
              <div class="mt-10 align-center">
                <span class="ic-circle-user ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray mr-4">{{ 'AUTH.user-name' | translate }}:</span>
                <span class="fw-600">{{userData?.userName}}</span>
              </div>
              <div class="mt-12 align-center">
                <span class="ic-suitcase-solid ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray mr-4">{{ 'USER_MANAGEMENT.designation' | translate }}:</span>
                <span class="fw-600 text-truncate-1 break-all">{{userData?.designation?.name ?
                  userData?.designation?.name : '--'}}</span>
              </div>
              <div class="mt-12 align-center">
                <span class="ic-suitcase-solid ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray mr-4">{{ 'USER_MANAGEMENT.department' | translate }}:</span>
                <span class="fw-600 text-truncate-1 break-all">{{userData?.department?.name ? userData?.department?.name
                  : '--'}}</span>
              </div>
              <div class="mt-8 align-center flex-wrap">
                <span class="ic-phone-ring-solid ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray">{{'PROPERTY.OWNER_INFO.phone' | translate }}:</span>
                <a [href]="'tel:'+userData?.phoneNumber"
                  class="text-xs m-4 px-6 py-2 border bg-light-pearl br-12 text-nowrap"
                  *ngIf="userData?.phoneNumber">{{userData?.phoneNumber}}</a>
                <a [href]="'tel:'+userData?.altPhoneNumber"
                  class="text-xs m-4 px-6 py-2 border bg-light-pearl br-12 text-nowrap"
                  *ngIf="userData?.altPhoneNumber">{{userData?.altPhoneNumber}}</a>
              </div>
              <div class="mt-8 align-center flex-wrap">
                <div class="ic-envelope-solid ic-light-gray ic-xxs mr-12"></div>
                <div class="text-sm text-dark-gray">{{ 'SHARE.email' | translate }}:</div>
                <a [href]="'mailto:'+userData?.email"
                  class="text-xs m-4 px-6 py-2 border bg-light-pearl br-12 text-truncate-1 break-all text-nowrap"
                  *ngIf="userData?.email">
                  {{userData?.email}} </a>
                <a [href]="'mailto:'+userData?.altEmail"
                  class="text-xs m-4 px-6 py-2 border bg-light-pearl br-12 text-truncate-1 break-all text-nowrap"
                  *ngIf="userData?.altEmail">
                  {{userData?.altEmail}}</a>
              </div>
              <div class="mt-8 align-center">
                <span class="ic-user-tie ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray mr-4">{{ 'USER_MANAGEMENT.reporting-to' | translate }}:</span>
                <span class="fw-600 break-all text-truncate-1">{{userData?.reportsTo?.name ? userData?.reportsTo?.name :
                  '--'}}</span>
              </div>
              <div class="mt-12 align-center">
                <span class="ic-dna-solid ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray mr-4">{{ 'GLOBAL.blood-group' | translate }}:</span>
                <span class="fw-600">{{ userData?.bloodGroup ? getBloodGroupName(userData?.bloodGroup) : '--' }}</span>
              </div>
              <div class="mt-12">
                <span class="icon ic-venus-mars ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray mr-4">{{ 'GLOBAL.gender' | translate }}:</span>
                <span class="fw-600 text-normal text-coal">{{ userData?.gender ? getGender(userData?.gender) :
                  '--'}}</span>
              </div>
              <div class="mt-12 align-center">
                <span class="ic-clock-list ic-light-gray ic-xxs mr-12"></span>
                <span class="text-sm text-dark-gray mr-4 text-nowrap">Time Zone:</span>
                <span class="fw-600 text-truncate-1 break-all">{{userData?.timeZoneInfo?.timeZoneDisplay ?
                  userData.timeZoneInfo.timeZoneDisplay :
                  '--'}}</span>
              </div>
              <div class="mt-12 d-flex ip-flex-col ph-d-flex w-100 fw-semi-bold">
                <div class="w-50">
                  <div class="text-sm text-dark-gray">{{'LEADS.created-by' | translate}}</div>
                  <div class="fw-600 text-normal text-coal mt-4">{{ getAssignedToDetails(userData?.createdBy,
                    allUserList, true) || '--'}}</div>
                </div>
                <div class="w-50 ip-mt-10 ph-mt-0">
                  <div class="text-sm text-dark-gray">{{'DASHBOARD.created-on' | translate}}</div>
                  <div class="fw-600 text-normal text-coal mt-4">{{ userData?.createdOn ?
                    getTimeZoneDate(userData?.createdOn , userBasicDetails?.timeZoneInfo?.baseUTcOffset,
                    'dateWithTime') : '--'}}</div>
                </div>
              </div>
              <div class="mt-10 d-flex ip-flex-col ph-d-flex w-100 fw-semi-bold tb-mb-10">
                <div class="w-50">
                  <div class="text-sm text-dark-gray">{{'LEADS.modified-by' | translate}}</div>
                  <div class="fw-600 text-normal text-coal mt-4">{{ getAssignedToDetails(userData?.lastModifiedBy,
                    allUserList, true) || '--'}}</div>
                </div>
                <div class="w-50 ip-mt-10 ph-mt-0">
                  <div class="text-sm text-dark-gray">{{'USER.modified-on' | translate}}</div>
                  <div class="fw-600 text-normal text-coal mt-4">{{userData?.lastModifiedOn ?
                    getTimeZoneDate(userData?.lastModifiedOn, userBasicDetails?.timeZoneInfo?.baseUTcOffset,
                    'dateWithTime') : '--'}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tb-w-50 ph-w-100">
          <div class="mt-24 tb-mt-16 cursor-pointer align-center gap-3 ml-20" (click)="showLocation = !showLocation">
            <span class="ic-triangle-down icon ic-coal ic-xxxs" [ngClass]="{ 'rotate-270' : !showLocation}"></span>
            <h5 class="fw-700">{{'USER.location-info' | translate}}</h5>
          </div>
          <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
          <div class="ml-24 mr-10 mb-28" *ngIf="showLocation">
            <div class="mt-10">
              <span class="icon ic-location-circle ic-light-gray ic-xxs mr-12"></span>
              <span class="fw-semi-bold text-sm text-dark-gray">{{'GLOBAL.current' | translate }} {{'PROJECTS.address' |
                translate}}:</span>
              <div class="fw-600 text-normal mt-6 ml-20 text-truncate-3">{{userData?.address ? userData?.address :
                '--'}}</div>
            </div>
            <div class="mt-16">
              <span class="icon ic-location-map ic-light-gray ic-xxs mr-12"></span>
              <span class="fw-semi-bold text-sm text-dark-gray">{{'PROJECTS.permanent' | translate}}
                {{'PROJECTS.address' | translate}}:</span>
              <div class="fw-600 text-normal mt-6 ml-20 text-truncate-3">{{userData?.permanentAddress ?
                userData?.permanentAddress : '--'}}</div>
            </div>
            <div class="mt-16">
              <span class="icon ic-mail-location ic-light-gray ic-xxs mr-12"></span>
              <span class="fw-semi-bold text-sm text-dark-gray">{{'USER_MANAGEMENT.office' | translate}}
                {{'PROJECTS.address' | translate}}:</span>
              <div class="fw-600 text-normal mt-6 ml-20 text-truncate-3">{{userData?.officeAddress ?
                userData?.officeAddress : '--'}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="w-70 tb-w-100">
    <div class="mt-20 mr-20 tb-ml-20 tb-mt-0">
      <div class="d-flex">
        <div class="border br-20 bg-white align-center user">
          <div class="activation" [ngClass]="{'active' : selectedSection == 'Attendance'}"
            (click)="selectedSection = 'Attendance';trackingService.trackFeature('Web.UserDetails.Button.Attendance.Click')"
            *ngIf="canViewAttendance || isLoggedInUser">
            <span class="icon ic-clock-list ic-sm mr-8 ip-mr-4"
              [ngClass]="{'active' : selectedSection !== 'Attendance'}"></span>
            <span class="ph-d-none">{{'SIDEBAR.attendance'| translate }}</span>
          </div>
          <div class="activation" *ngIf="canViewForFilter" [ngClass]="{'active' : selectedSection == 'Assigned'}"
            (click)="selectedSection = 'Assigned';trackingService.trackFeature('Web.UserDetails.Button.AssignedData.Click')">
            <span class="icon ic-circle-nodes ic-sm mr-8 ip-mr-4"
              [ngClass]="{'active' : selectedSection !== 'Assigned'}"></span>
            <span class="ph-d-none">{{'LEADS.assigned' | translate }} {{'CONTACT.data'| translate}}</span>
          </div>
          <div class="activation" [ngClass]="{'active' : selectedSection == 'Document'}"
            (click)="selectedSection = 'Document';trackingService.trackFeature('Web.UserDetails.Button.Documents.Click')">
            <span class="icon ic-folder-solid ic-sm mr-8 ip-mr-4"
              [ngClass]="{'active' : selectedSection !== 'Document'}"></span>
            <span class="ph-d-none"> {{'LEAD_FORM.documents' | translate }}</span>
          </div>
          <div class="activation" [ngClass]="{'active' : selectedSection == 'changePassword'}"
            (click)="selectedSection = 'changePassword';trackingService.trackFeature('Web.UserDetails.Button.ChangePassword.Click')"
            *ngIf="isLoggedInUser">
            <span class="icon ic-key ic-xxs mr-8 ip-mr-4"
              [ngClass]="{'active' : selectedSection !== 'changePassword'}"></span>
            <span class="ph-d-none">{{'USER_MANAGEMENT.change-password'| translate }}</span>
          </div>
        </div>
      </div>
      <div class="mt-30" *ngIf="selectedSection == 'Attendance'">
        <user-attendance [loggedInUser]="isLoggedInUser"></user-attendance>
      </div>
      <div class="mt-30" *ngIf="selectedSection == 'Assigned'">
        <assigned-details></assigned-details>
      </div>
      <div *ngIf="selectedSection == 'Document'">
        <user-uploaded-documents [userData]="userData" [loggedInUser]="isLoggedInUser"></user-uploaded-documents>
      </div>
      <div class="mt-30" *ngIf="selectedSection == 'changePassword'">
        <change-password [loggedInUser]="isLoggedInUser"></change-password>
      </div>
    </div>
  </div>
</div>
<ng-template #rolesView>
  <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
    <h3 class="fv-sm-caps fw-700">{{'USER_MANAGEMENT.roles-permission' | translate}}</h3>
    <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
  </div>
  <div class="px-30 pt-20 scrollbar">
    <ng-container *ngFor="let role of userData?.rolePermission">
      <h5 class="fw-600 mb-10">{{role.name}}</h5>
      <p class="text-gray text-xs word-break mb-20 ml-10" *ngIf="role.permissionDescription"
        [innerHTML]="getSanitizedHtml(role.permissionDescription)"></p>
    </ng-container>
  </div>
</ng-template>