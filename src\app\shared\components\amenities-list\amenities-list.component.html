<div class="d-flex ip-pt-30">
  <div class="align-center mb-10 bg-white br-8">
    <ng-container *ngFor="let amenityType of masterAmenityList | keyvalue">
      <div>
        <h4 (click)="setActiveAmenityType(amenityType.key)" class="px-30 py-10 br-8 cursor-pointer fw-semi-bold"
          [ngClass]="{
            'fw-700 bg-accent-green text-white': activeAmenitiesType == amenityType.key
          }">
          {{ amenityType.key }}
        </h4>
      </div>
    </ng-container>
  </div>
</div>
<ng-container *ngIf="activeAmenitiesType">
  <div class="flex-between mt-20">
    <!-- Don't Remove Empty Div -->
    <div>
      <div *ngIf="filteredAmenityList.length">
        <label class="checkbox-container text-secondary fw-semi-bold">
          {{ 'PROPERTY.select-all' | translate }}
          <input type="checkbox" class="mr-10" [id]="'inpPropertyAmenities' + activeAmenitiesType"
            [checked]="allAmenitySelectionCheck(activeAmenitiesType) ? 'checked' : null"
            (change)="changeAllAmenitySelectionForType(activeAmenitiesType, $event.target.checked)" />
          <span class="checkmark" [class.select-all]="allAmenitySelectionCheck(activeAmenitiesType)"></span>
        </label>
      </div>
    </div>
    <div>
      <div class="w-330 tb-w-200">
        <div class="align-center bg-white h-35px px-10 py-12 border br-20">
          <span class="icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
          <input placeholder="search" class="border-0 outline-0 w-100" (input)="onSearch($event)" />
        </div>
      </div>
    </div>
  </div>
  <div class="d-flex flex-wrap w-100 mt-12">
    <ng-container *ngFor="let amenity of filteredAmenityList">
      <label class="checkbox-container w-25 tb-w-33 ip-w-50 ph-w-100 mt-20 text-secondary align-start">
        {{ amenity.amenityDisplayName }}
        <input type="checkbox" class="mr-10" [value]="amenity.amenityName"
          [checked]="amenity.selected ? 'checked' : null"
          (change)="amenity.selected = $event.target.checked; emitSelection()" />
        <span class="checkmark"></span>
      </label>
    </ng-container>
  </div>
</ng-container>