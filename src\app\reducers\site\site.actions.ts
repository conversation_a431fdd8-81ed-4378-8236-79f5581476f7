import { Action } from '@ngrx/store';

export enum SiteActionTypes {
  FETCH_LOCATION_LIST = "[SITE] Fetch Location List",
  FETCH_LOCATION_LIST_SUCCESS = "[SITE] Fetch Location List Success",
  UPDATE_LOCATION_FILTERS_PAYLOAD = '[SITE] Update Location Filters Payload',
  ADD_LOCATION = '[SITE] Add Location',
  UPDATE_LOCATION = '[SITE] Update Location',
  DELETE_LOCATION = '[SITE] Delete Location',
  BULK_UPDATE_CITY = '[SITE] Bulk Update City',
  BULK_UPDATE_ZONE = '[SITE] Bulk Update Zone',
  FETCH_ALL_CITY_LIST = "[SITE] Fetch all City List",
  FETCH_ALL_CITY_LIST_SUCCESS = "[SITE] Fetch all City List Success",
  FETCH_CITY_LIST = "[SITE] Fetch City List",
  FETCH_CITY_LIST_SUCCESS = "[SITE] Fetch City List Success",
  UPDATE_CITY_FILTERS_PAYLOAD = '[SITE] Update City Filters Payload',
  ADD_CITY = '[SITE] Add City',
  UPDATE_CITY = '[SITE] Update City',
  DELETE_CITY = '[SITE] Delete City',
  DELETE_CITIES = '[SITE] Delete Cities',
  FETCH_ALL_ZONE_LIST = "[SITE] Fetch all Zone List",
  FETCH_ALL_ZONE_LIST_SUCCESS = "[SITE] Fetch all Zone List Success",
  FETCH_ZONE_LIST = "[SITE] Fetch Zone List",
  FETCH_ZONE_LIST_SUCCESS = "[SITE] Fetch Zone List Success",
  UPDATE_ZONE_FILTERS_PAYLOAD = '[SITE] Update Zone Filters Payload',
  ADD_ZONE = '[SITE] Add Zone',
  UPDATE_ZONE = '[SITE] Update Zone',
  DELETE_ZONE = '[SITE] Delete Zone',
  FETCH_ALL_LOCATIONS = "[SITE] Fetch All Locations",
  FETCH_ALL_LOCATIONS_SUCCESS = "[SITE] Fetch All Locations Success",
  FETCH_LOCATIONS_WITH_GOOGLE_API = "[SITE] Fetch All Locations With Google API",
  FETCH_LOCATIONS_WITH_GOOGLE_API_SUCCESS = "[SITE] Fetch All Locations With Google API Success",
  LOCATION_EXCEL_UPLOAD = '[SITE] Upload Locations Excel File',
  LOCATION_EXCEL_UPLOAD_SUCCESS = '[SITE] Upload Locations Excel File Success',
  REMOVE_LOCATIONS_WITH_GOOGLE_API = "[SITE] Remove Locations With Google API",

  FETCH_STATE = '[SITE] Fetch State',
  FETCH_STATE_SUCCESS = '[SITE] Fetch State Success',
  ADD_STATE = '[SITE] Add State',
  ADD_STATE_SUCCESS = '[SITE] Add State Success',
  UPDATE_STATE = '[SITE] Update State',
  UPDATE_STATE_SUCCESS = '[SITE] Update State Success',
  DELETE_STATE = '[SITE] Delete State',
  DELETE_STATE_SUCCESS = '[SITE] Delete State Success',
  UPDATE_STATE_FILTERS_PAYLOAD = '[SITE] Update State Filters Payload',
  // ----------- COUNTRY ------------------------
  FETCH_COUNTRY = '[SITE] Fetch Country',
  FETCH_COUNTRY_SUCCESS = '[SITE] Fetch Country Success',
  ADD_COUNTRY = '[SITE] Add Country',
  ADD_COUNTRY_SUCCESS = '[SITE] Add Country Success',
  UPDATE_COUNTRY = '[SITE] Update Country',
  UPDATE_COUNTRY_SUCCESS = '[SITE] Update Country Success',
  DELETE_COUNTRY = '[SITE] Delete Country',
  DELETE_COUNTRY_SUCCESS = '[SITE] Delete Country Success',
  UPDATE_COUNTRY_FILTERS_PAYLOAD = '[SITE] Update Country Filters Payload',

  FETCH_INTERNATIONAL_LOCATIONS = "[SITE] Fetch International Locations",
  FETCH_INTERNATIONAL_LOCATIONS_SUCCESS = "[SITE] Fetch International Locations Success",
}

export class FetchLocationList implements Action {
  readonly type: string = SiteActionTypes.FETCH_LOCATION_LIST;
  constructor() { }
}
export class FetchLocationListSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_LOCATION_LIST_SUCCESS;
  constructor(public response: any = {}) { }
}
export class UpdateLocationFiltersPayload implements Action {
  readonly type: string =
    SiteActionTypes.UPDATE_LOCATION_FILTERS_PAYLOAD;
  constructor(public payload: any) { }
}
export class AddLocation implements Action {
  readonly type: string = SiteActionTypes.ADD_LOCATION;
  constructor(public payload: any) { }
}
export class UpdateLocation implements Action {
  readonly type: string = SiteActionTypes.UPDATE_LOCATION;
  constructor(public id: string, public payload: any) { }
}
export class DeleteLocation implements Action {
  readonly type: string = SiteActionTypes.DELETE_LOCATION;
  constructor(public payload: any) { }
}
export class BulkUpdateCity implements Action {
  readonly type: string = SiteActionTypes.BULK_UPDATE_CITY;
  constructor(public payload: any) { }
}
export class BulkUpdateZone implements Action {
  readonly type: string = SiteActionTypes.BULK_UPDATE_ZONE;
  constructor(public payload: any) { }
}
export class FetchAllCityList implements Action {
  readonly type: string = SiteActionTypes.FETCH_ALL_CITY_LIST;
  constructor() { }
}
export class FetchAllCityListSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_ALL_CITY_LIST_SUCCESS;
  constructor(public response: any = {}) { }
}
export class FetchCityList implements Action {
  readonly type: string = SiteActionTypes.FETCH_CITY_LIST;
  constructor() { }
}
export class FetchCityListSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_CITY_LIST_SUCCESS;
  constructor(public response: any = {}) { }
}
export class UpdateCityFiltersPayload implements Action {
  readonly type: string =
    SiteActionTypes.UPDATE_CITY_FILTERS_PAYLOAD;
  constructor(public payload: any) { }
}
export class AddCity implements Action {
  readonly type: string = SiteActionTypes.ADD_CITY;
  constructor(public payload: any) { }
}
export class UpdateCity implements Action {
  readonly type: string = SiteActionTypes.UPDATE_CITY;
  constructor(public id: string, public payload: any) { }
}
export class DeleteCity implements Action {
  readonly type: string = SiteActionTypes.DELETE_CITY;
  constructor(public id: string) { }
}
export class DeleteCities implements Action {
  readonly type: string = SiteActionTypes.DELETE_CITIES;
  constructor(public payload: any) { }
}
export class FetchAllZoneList implements Action {
  readonly type: string = SiteActionTypes.FETCH_ALL_ZONE_LIST;
  constructor() { }
}
export class FetchAllZoneListSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_ALL_ZONE_LIST_SUCCESS;
  constructor(public response: any = {}) { }
}
export class FetchZoneList implements Action {
  readonly type: string = SiteActionTypes.FETCH_ZONE_LIST;
  constructor() { }
}
export class FetchZoneListSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_ZONE_LIST_SUCCESS;
  constructor(public response: any = {}) { }
}
export class UpdateZoneFiltersPayload implements Action {
  readonly type: string =
    SiteActionTypes.UPDATE_ZONE_FILTERS_PAYLOAD;
  constructor(public payload: any) { }
}
export class AddZone implements Action {
  readonly type: string = SiteActionTypes.ADD_ZONE;
  constructor(public payload: any) { }
}
export class UpdateZone implements Action {
  readonly type: string = SiteActionTypes.UPDATE_ZONE;
  constructor(public id: string, public payload: any) { }
}
export class DeleteZone implements Action {
  readonly type: string = SiteActionTypes.DELETE_ZONE;
  constructor(public payload: any) { }
}
export class FetchAllLocations implements Action {
  readonly type: string = SiteActionTypes.FETCH_ALL_LOCATIONS;
  constructor(public response: any = {}) { }
}
export class FetchAllLocationsSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_ALL_LOCATIONS_SUCCESS;
  constructor(public response: any = {}) { }
}
export class FetchLocationsWithGoogle implements Action {
  readonly type: string = SiteActionTypes.FETCH_LOCATIONS_WITH_GOOGLE_API;
  constructor(public payload?: string, public page: number = 1, public pagesize: number = 500) { }
}

export class FetchLocationsWithGoogleSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_LOCATIONS_WITH_GOOGLE_API_SUCCESS;
  constructor(public response: any[]) { }
}
export class LocationExcelUpload implements Action {
  readonly type: string = SiteActionTypes.LOCATION_EXCEL_UPLOAD;
  constructor(public file: File) { }
}
export class LocationExcelUploadSuccess implements Action {
  readonly type: string = SiteActionTypes.LOCATION_EXCEL_UPLOAD_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class removeLocationsWithGoogleApi implements Action {
  readonly type: string = SiteActionTypes.REMOVE_LOCATIONS_WITH_GOOGLE_API;
  constructor() { }
}

// ...............state...........................
export class FetchState implements Action {
  readonly type: string = SiteActionTypes.FETCH_STATE;
  constructor(public payload?: any) { }
}
export class FetchStateSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_STATE_SUCCESS;
  constructor(public response: any = {}) { }
}
export class AddState implements Action {
  readonly type: string = SiteActionTypes.ADD_STATE;
  constructor(public payload: any) { }
}
export class UpdateState implements Action {
  readonly type: string = SiteActionTypes.UPDATE_STATE;
  constructor(public id: string, public payload: any) { }
}
export class DeleteState implements Action {
  readonly type: string = SiteActionTypes.DELETE_STATE;
  constructor(public id: any) { }
}
export class UpdateStateFiltersPayload implements Action {
  readonly type: string =
    SiteActionTypes.UPDATE_STATE_FILTERS_PAYLOAD;
  constructor(public payload: any) { }
}

// ............... country ............................
export class FetchCountry implements Action {
  readonly type: string = SiteActionTypes.FETCH_COUNTRY;
  constructor(public payload?: any) { }
}
export class FetchCountrySuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_COUNTRY_SUCCESS;
  constructor(public response: any = {}) { }
}
export class AddCountry implements Action {
  readonly type: string = SiteActionTypes.ADD_COUNTRY;
  constructor(public payload: any) { }
}
export class UpdateCountry implements Action {
  readonly type: string = SiteActionTypes.UPDATE_COUNTRY;
  constructor(public id: string, public payload: any) { }
}
export class DeleteCountry implements Action {
  readonly type: string = SiteActionTypes.DELETE_COUNTRY;
  constructor(public id: any) { }
}
export class UpdateCountryFiltersPayload implements Action {
  readonly type: string =
    SiteActionTypes.UPDATE_COUNTRY_FILTERS_PAYLOAD;
  constructor(public payload: any) { }
}

export class FetchInternationalLocations implements Action {
  readonly type: string = SiteActionTypes.FETCH_INTERNATIONAL_LOCATIONS;
  constructor(public payload?: string, public page: number = 1, public pagesize: number = 500) { }
}

export class FetchInternationalLocationsSuccess implements Action {
  readonly type: string = SiteActionTypes.FETCH_INTERNATIONAL_LOCATIONS_SUCCESS;
  constructor(public response: any[]) { }
}