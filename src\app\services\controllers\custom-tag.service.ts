import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';


@Injectable({
  providedIn: 'root'
})
export class CustomTagService {
  serviceBaseUrl: string;
  iconServiceBaseUrl: string;
  getResourceUrl(): string {
    return 'flags';
  }

  getIconsResourceUrl(): string {
    return 'icons';
  }

  constructor(private http: HttpClient) {
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    this.iconServiceBaseUrl = `${env.baseURL}${env.apiURL}${this.getIconsResourceUrl()}`;
  }

  getAllCustomTag() {
    return this.http.get(`${this.serviceBaseUrl}`);
  }

  getAllCustomTagWithCount(){
    return this.http.get(`${this.serviceBaseUrl}/flags/leadcount`);
  }

  createTag(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}`,payload);
  }

  updateTags(payload:any) {
    return this.http.put(`${this.serviceBaseUrl}`,payload);
  }

  deleteTag(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/${id}`); 
  }

  getAllFlagIcons() {
    return this.http.get(`${this.iconServiceBaseUrl}`);
  }

  checkDuplicate(flagName:string) {
    return this.http.get(`${this.serviceBaseUrl}/custom/flag-exist?name=${flagName}`);
  }
}
