import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getPages, getTimeZoneDate } from 'src/app/core/utils/common.util';
import {
  FetchAgencyExportTracker,
  FetchCampaignExportTracker,
  FetchChannelPartnerExportTracker
} from 'src/app/reducers/manage-marketing/marketing.action';
import {
  getAgencyExportStatus,
  getAgencyExportStatusLoading,
  getCPExportStatus,
  getCPExportStatusLoading,
  getCampaignExportStatus,
  getCampaignExportStatusLoading
} from 'src/app/reducers/manage-marketing/marketing.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'export-marketing-tracker',
  templateUrl: './export-marketing-tracker.component.html',
})
export class ExportMarketingTrackerComponent implements OnInit {

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  fieldType: any;
  pageSize: number = PAGE_SIZE;
  currOffset: number = 0;
  rowData: any = [];
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  defaultColDef: any;
  totalReqCount: number;
  s3BucketUrl: string = env.s3ImageBucketURL;
  getPages = getPages;
  filtersPayload = {
    pageNumber: 1,
    pageSize: this.pageSize,
  };
  isDataManagement: boolean;
  isMarketingExportLoading: boolean;


  userData: any;

  constructor(
    private gridOptionsService: GridOptionsService,
    public modalService: BsModalService,
    private _store: Store<AppState>
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
    this.initializeGridSettings();
  }

  ngOnInit(): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    if (this.fieldType === 'Agency Name') {
      this._store
        .select(getAgencyExportStatus)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalReqCount = data?.totalCount;
        });

      this._store
        .select(getAgencyExportStatusLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {
          this.isMarketingExportLoading = isLoading;
        });
    }
    if (this.fieldType === 'Channel partner') {
      this._store
        .select(getCPExportStatus)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalReqCount = data?.totalCount;
        });

      this._store
        .select(getCPExportStatusLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {
          this.isMarketingExportLoading = isLoading;
        });
    }
    
    if (this.fieldType === 'Campaign name') {
      this._store
        .select(getCampaignExportStatus)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.rowData = data?.items;
          this.totalReqCount = data?.totalCount;
        });

      this._store
        .select(getCampaignExportStatusLoading)
        .pipe(takeUntil(this.stopper))
        .subscribe((isLoading: boolean) => {          
          this.isMarketingExportLoading = isLoading;
        });
    }
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Export By',
        field: 'Export By',
        minWidth: 180,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.exportedUser?.firstName,
          params.data?.exportedUser?.lastName,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value[0]} ${params.value[1]}</p>`;
        },
      },
      {
        headerName: 'Requested on',
        field: 'Requested on',
        valueGetter: (params: any) => [
          getTimeZoneDate(params.data?.createdOn, this.userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime')
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap">${params.value}</p>
             <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName && this.userData?.shouldShowTimeZone && params.value
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Last Modified on',
        field: 'Last Modified on',
        minWidth: 150,
        valueGetter: (params: any) => [
          getTimeZoneDate(params.data?.lastModifiedOn, this.userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime')
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-nowrap">${params.value}</p>
             <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName && this.userData?.shouldShowTimeZone && params.value
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Total Count',
        field: 'Total Count',
        maxWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.count],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'File Name',
        field: 'File Name',
        minWidth: 100,
        filter: false,
        valueGetter: (params: any) => [params.data?.fileName],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Excel File',
        field: 'Status',
        maxWidth: 180,
        minWidth: 180,
        filter: false,
        valueGetter: (params: any) => [
          params.data?.s3BucketKey ? params.data?.s3BucketKey : ''
        ],
        cellRenderer: (params: any) => {
          return params.value[0]
            ? `<a href="${params.value}" class="btn btn-xxs btn-linear-green text-nowrap flex-center w-150">
            <span class="icon ic-xxs ic-download"></span>
            <span class="text-white ml-8">Ready to Download</span></a>`
            : '';
        },
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this.updateTrackerList();
  }

  updateTrackerList() {    
    if (this.fieldType === 'Agency Name') {
      this._store.dispatch(new FetchAgencyExportTracker(this.filtersPayload?.pageNumber, this.filtersPayload?.pageSize))
    } else if (this.fieldType === 'Campaign name') {
      this._store.dispatch(new FetchCampaignExportTracker(this.filtersPayload?.pageNumber, this.filtersPayload?.pageSize))
    } else {
      this._store.dispatch(new FetchChannelPartnerExportTracker(this.filtersPayload?.pageNumber, this.filtersPayload?.pageSize))
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}