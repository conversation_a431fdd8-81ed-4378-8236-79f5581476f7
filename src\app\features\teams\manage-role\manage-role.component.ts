import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getPages, isEmptyObject } from 'src/app/core/utils/common.util';
import { RolesActionComponent } from 'src/app/features/teams/manage-role/roles-action/roles-action.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  DeleteRoles,
  FetchRolesList,
} from 'src/app/reducers/teams/teams.actions';
import { bulkDeleteRoles, getIsRolesLoading, getRolesList } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { AddRoleComponent } from './add-role/add-role.component';

@Component({
  selector: 'manage-role',
  templateUrl: './manage-role.component.html',
})
export class UserRolesComponent implements OnInit, OnDestroy {
  public pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  offset: number = 0;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  gridOptions: any;
  gridApi: any;
  searchTerm: string;
  gridColumnApi: any;
  rowData: any = [];
  defaultColDef: any;
  selectedPageSize: number;
  totalRolesCount: number;
  getPages = getPages;
  canView: boolean = false;
  canAdd: boolean = false
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: this.pageSize,
    path: 'roles/withpermissions',
  };
  isRolesLoading: boolean;
  canBulkDelete: boolean = false;

  selectedRoles: any;
  @ViewChild('roleMessageModal') roleMessageModal: any;
  bulkDeleteRoles: any;
  constructor(
    public router: Router,
    private gridOptionsService: GridOptionsService,
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    public metaTitle: Title,
    public modalService: BsModalService,
    private bulkDeleteModalRef: BsModalRef,
    private modalRef: BsModalRef,
    public trackingService: TrackingService
  ) {
    this.metaTitle.setTitle('CRM | Roles');
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
  }

  ngOnInit(): void {
    this.selectedPageSize = 10;
    this.store.dispatch(new FetchRolesList(this.filtersPayload));
    this.store
      .select(getRolesList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data.items;
        this.totalRolesCount = data.totalCount;
        if (this.rowData && this.rowData?.length === 0 && this.offset > 0) {
          this.filtersPayload = {
            ...this.filtersPayload,
            pageNumber: this.offset
          };
          this.offset = this.offset - 1;
          this.store.dispatch(new FetchRolesList(this.filtersPayload));
        }
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canView = permissionsSet.has('Permissions.Roles.View');
        this.canAdd = permissionsSet.has('Permissions.Roles.Create');
        this.canBulkDelete = permissionsSet.has('Permissions.Roles.BulkDelete');
        this.initializeGridSettings();
      });

    this.store
      .select(getIsRolesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isRolesLoading = loading;
      });

    this.headerTitle.setLangTitle('Roles and Permissions');
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Role Name',
        field: 'Roles',
        valueGetter: (params: any) => [params.data.name],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Description',
        field: 'Description',
        valueGetter: (params: any) => [params.data.description],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        minWidth: 150,
        maxWidth: 150,
        filter: false,
        valueGetter: (params: any) => [this.offset + 1, this.pageSize, this.searchTerm],
        cellRenderer: RolesActionComponent,
      },
    ];

    if (this.canBulkDelete) {
      this.gridOptions.columnDefs.unshift({
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        resizable: false,
        lockPosition: true,
        maxWidth: 35,
      });
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
  }

  onPageChange(e: any) {
    this.offset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: e + 1,
    };
    this.store.dispatch(new FetchRolesList(this.filtersPayload));
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    this.trackingService.trackFeature(`Web.RolePermission.Button.BulkDelete.Click`)
    this.selectedRoles = this.gridApi?.getSelectedNodes().map((role: any) => {
      return role.data;
    });
    let initialState: any = {
      data: this.selectedRoles,
      class: 'right-modal modal-300',
    };
    this.bulkDeleteModalRef = this.modalService.show(
      BulkDeleteModal,
      initialState
    );
  }

  openConfirmDeleteModal(roleName: string, roleId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: roleName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeSelection(roleId);
        }
      });
    }
  }

  removeSelection(id: string): void {
    const node = this.gridApi?.getSelectedNodes()?.filter(
      (role: any) => role?.data?.id === id
    );
    this.gridApi?.deselectNode(node?.[0]);

    this.selectedRoles = this.selectedRoles?.filter(
      (role: any) => role?.id !== id
    );
    if (this.selectedRoles?.length <= 0) {
      this.bulkDeleteModalRef.hide();
    }
  }

  updateBulkDelete() {
    this.filtersPayload = {
      ...this.filtersPayload,
      Name: this.searchTerm,
    };
    const ids = this.selectedRoles?.map((role: any) => role.id);
    this.store.dispatch(new DeleteRoles(ids, this.filtersPayload));
    this.bulkDeleteModalRef.hide();
    let isModalOpen = false;
    this.store
      .select(bulkDeleteRoles)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          if (data?.length > 0 && !isModalOpen && this.bulkDeleteRoles !== data) {
            this.bulkDeleteRoles = data;
            this.modalRef = this.modalService.show(
              this.roleMessageModal,
              Object.assign({}, {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              })
            );
            isModalOpen = true;
          }
        }
      });
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;

    this.filtersPayload = {
      ...this.filtersPayload,
      pageSize: this.pageSize,
      pageNumber: 1,
    };
    this.trackingService.trackFeature(`Web.RolePermission.Option.${this.pageSize}.Click`)
    this.store.dispatch(new FetchRolesList(this.filtersPayload));
    this.gridOptions.paginationPageSize = this.pageSize;
    this.gridOptions.api?.paginationSetPageSize(this.selectedPageSize);
    this.gridApi.setRowData([]);
    this.gridApi.applyTransaction({ add: this.rowData });
    this.offset = 0;
  }

  addRoleModal(offset: number, pageSize: number) {
    this.trackingService.trackFeature(`Web.RolePermission.Button.AddNewRole.Click`)
    this.modalService.show(AddRoleComponent, {
      class: 'right-modal modal-400 ip-modal-unset',
      initialState: {
        pageNumber: offset + 1,
        pageSize: pageSize,
      },
    });
  }

  search(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.gridApi.setQuickFilter(null);
      }
      this.filtersPayload = {
        ...this.filtersPayload,
        pageNumber: 1,
        Name: this.searchTerm
      };
      this.store.dispatch(new FetchRolesList(this.filtersPayload));
      this.offset = 0;
      this.trackingService.trackFeature(`Web.RolePermission.DataEntry.Search.DataEntry`)
    }
  }

  clearSearch() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.gridApi.setQuickFilter(null);
      this.filtersPayload = {
        ...this.filtersPayload,
        Name: this.searchTerm
      };
      this.store.dispatch(new FetchRolesList(this.filtersPayload));
    }
  }

  getRoles(data: any) {
    return data?.filter((item: any) => item !== null && item !== "")
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
