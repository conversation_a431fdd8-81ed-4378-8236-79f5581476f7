import { EventEmitter, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { DeviceDetectorService } from 'ngx-device-detector';
import { skipWhile, takeUntil } from 'rxjs';
import { APP_VERSION } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getTenantName, isEmptyObject } from 'src/app/core/utils/common.util';
import { AddTracks } from 'src/app/reducers/analytics/analytics.actions';
import { getFeaturesList } from 'src/app/reducers/analytics/analytics.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { environment } from 'src/environments/environment';
import { DeviceInfoService } from './device-info.service';

@Injectable({
    providedIn: 'root',
})
export class TrackingService {
    private stopper: EventEmitter<void> = new EventEmitter<void>();
    versionNo: string = APP_VERSION;
    deviceInfo: any;
    userId: any;
    featuresData: any[] = [];
    userData: any;

    constructor(
        private store: Store<AppState>,
        private deviceDataService: DeviceInfoService,
        private deviceService: DeviceDetectorService
    ) {
        this.store
            .select(getUserBasicDetails)
            .pipe(takeUntil(this.stopper))
            .subscribe((data: any) => {
                this.userData = data;
            });
        this.store
            .select(getFeaturesList)
            .pipe(
                takeUntil(this.stopper),
                skipWhile((data: any) => !data?.length)
            )
            .subscribe((data: any) => {
                if (!isEmptyObject(data)) {
                    this.featuresData = data;
                }
            });
        this.fetchDeviceInfo();
    }

    private async fetchDeviceInfo() {
        this.deviceInfo = await this.deviceDataService.getDeviceInfo();
    }

    trackFeature(featureName: string, leadId?: string) {
        const feature = this.featuresData?.find(
            (item: any) => item.name === featureName
        );
        if (!feature) return;


        const payload: any = {
            userId: this.userData?.id,
            tenantId: getTenantName(),
            env: environment.envt,
            source: 'Web',
            deviceId: this.deviceInfo?.deviceId,
            deviceType: this.deviceService.getDeviceInfo().deviceType,
            osVer: null,
            appVer: APP_VERSION,
            IP: this.deviceInfo?.ip,
            refId: leadId || null,
            featureName: featureName,
            featureId: feature._id,
            actionId: feature.actions?.length ? feature.actions[0]._id : null,
            sequenceNo: 0,
        };

        if (environment.production) {
            this.store.dispatch(new AddTracks(payload));
        }
    }
}
