import { Action } from '@ngrx/store';

export enum QRFormActionTypes {
    FETCH_QR_FORM = '[QR_FORM] Fetch QR Form',
    FETCH_QR_FORM_SUCCESS = '[QR_FORM] Fetch QR Form Success',
    FETCH_QR_FORM_LIST = '[QR_FORM] Fetch QR Form List',
    FETCH_QR_FORM_LIST_SUCCESS = '[QR_FORM] Fetch QR Form List Success',
    FETCH_QR_FORM_BY_ID = '[QR_FORM] Fetch QR Form by ID',
    FETCH_QR_FORM_BY_ID_SUCCESS = '[QR_FORM] Fetch QR Form by ID Success',
    ADD_QR_FORM = '[QR_FORM] Add QR Form',
    ADD_QR_FORM_SUCCESS = '[QR_FORM] ADD QR FORM SUCCESS',
    UPDATE_QR_FORM = '[QR_FORM] Update QR Form',
    UPDATE_QR_FORM_SUCCESS = '[QR_FORM] Update QR Form Success',
    DELETE_QR_FORM = '[QR_FORM] Delete QR Form',
    UPDATE_QR_FORM_STATUS = '[QR_FORM] Update QR Form Status',
    FETCH_QR_FORM_FIELDS = '[QR_FORM] Fetch QR Form Fields',
    FETCH_QR_FORM_FIELDS_SUCCESS = '[QR_FORM] Fetch QR Form Fields Success',
    TEMPLATE_NAME_EXISTS = '[QR_FORM] Template name exists',
    TEMPLATE_NAME_EXISTS_SUCCESS = '[QR_FORM] Template name exists success',
    FETCH_QR_BY_SEARCH_SUCCESS = '[QR_FORM] Fetch QR Form by search success',
    ASSIGN_PAGE_SIZE = '[QR_FORM] Assign PAGE SIZE',
    RESTORE_TEMPLATE = '[QR_FORM] Restore template',
    BULK_DELETE_QR_FORM = '[QR_FORM] Bulk Delete QR Form',
    BULK_DELETE_QR_FORM_SUCCESS = '[QR_FORM] Bulk Delete QR Form Success',
    BULK_RESTORE_QR_FORM = '[QR_FORM] Bulk Restore QR Form',
    BULK_RESTORE_QR_FORM_SUCCESS = '[QR_FORM] Bulk Restore QR Form Success',

}
export class FetchAllQrForms implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM;
    constructor(public pageNumber?: any, public pageSize?: any, public keyword?: string, public shouldGetDeletedTemplatesOnly?: boolean) { }
}

export class FetchQRBySearchSuccess implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_BY_SEARCH_SUCCESS;
    constructor(public response: any) { }
}
export class FetchAllQrFormsSuccess implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM_SUCCESS;
    constructor(public response: any = {}) { }
}
export class FetchQrFormsList implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM_LIST;
    constructor() { }
}
export class FetchQrFormsListSuccess implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM_LIST_SUCCESS;
    constructor(public response: any = {}) { }
}
export class FetchQrFormById implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM_BY_ID;
    constructor(public id: string) { }
}
export class FetchQrFormByIdSuccess implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM_BY_ID_SUCCESS;
    constructor(public response: any = {}) { }
}
export class AddQrForm implements Action {
    readonly type: string = QRFormActionTypes.ADD_QR_FORM;
    constructor(public payload: any) { }
}
export class AddQrFormSuccess implements Action {
    readonly type: string = QRFormActionTypes.ADD_QR_FORM_SUCCESS;
    constructor() { }
}
export class UpdateQrForm implements Action {
    readonly type: string = QRFormActionTypes.UPDATE_QR_FORM;
    constructor(public payload: any, public id: string) { }
}

export class UpdateQrFormSuccess implements Action {
    readonly type: string = QRFormActionTypes.UPDATE_QR_FORM_SUCCESS;
    constructor() { }
}

export class DeleteQrForm implements Action {
    readonly type: string = QRFormActionTypes.DELETE_QR_FORM;
    constructor(public id: string, public pageNumber?: number, public pageSize?: number, public shouldDeletePermanently?: boolean) { }
}
export class UpdateQrFormStatus implements Action {
    readonly type: string = QRFormActionTypes.UPDATE_QR_FORM_STATUS;
    constructor(public id: string, public pageNumber?: number, public pageSize?: number) { }
}
export class FetchQrFormsFields implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM_FIELDS;
    constructor() { }
}
export class FetchQrFormsFieldsSuccess implements Action {
    readonly type: string = QRFormActionTypes.FETCH_QR_FORM_FIELDS_SUCCESS;
    constructor(public response: any = {}) { }
}

export class TemplateNameExists implements Action {
    readonly type: string = QRFormActionTypes.TEMPLATE_NAME_EXISTS;
    constructor(public name: string) {

    }
}
export class TemplateNameExistsSuccess implements Action {
    readonly type: string = QRFormActionTypes.TEMPLATE_NAME_EXISTS_SUCCESS;
    constructor(public isTemplateNameExists: boolean) {

    }
}
export class AssignPageSize implements Action {
    readonly type: string = QRFormActionTypes.ASSIGN_PAGE_SIZE;
    constructor(public pageSize: number, public pageNumber: number) {

    }
}

export class RestoreTemplate implements Action {
    readonly type: string = QRFormActionTypes.RESTORE_TEMPLATE;
    constructor(public id: string, public pageNumber?: number, public pageSize?: number) { }
}


export class BulkDelete implements Action {
    readonly type: string = QRFormActionTypes.BULK_DELETE_QR_FORM;
    constructor(public ids: string[], public shouldDeletePermanently: boolean) { }
  }
  
  export class BulkDeleteSuccess implements Action {
    readonly type: string = QRFormActionTypes.BULK_DELETE_QR_FORM_SUCCESS;
    constructor() { }
  }

  export class BulkRestore implements Action {
    readonly type: string = QRFormActionTypes.BULK_RESTORE_QR_FORM;
    constructor(public ids: string[]) { }
  }
  
  export class BulkRestoreSuccess implements Action {
    readonly type: string = QRFormActionTypes.BULK_RESTORE_QR_FORM_SUCCESS;
    constructor() { }
  }