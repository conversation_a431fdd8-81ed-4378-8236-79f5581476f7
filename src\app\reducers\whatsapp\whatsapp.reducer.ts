import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { LeadsFilter } from 'src/app/core/interfaces/leads.interface';
import {
  Fetch24HrValidationSuccess,
  FetchConversationSuccess,
  FetchWhatsappChildListSuccess,
  FetchWhatsappListSuccess,
  FetchWhatsappTemplateSuccess,
  UpdateFilterPayload,
  WhatsappActionTypes,
} from 'src/app/reducers/whatsapp/whatsapp.actions';

export type WhatsappState = {
  templates?: any;
  validation24Hr?: boolean;
  conversation?: any;
  conversationIsLoading: boolean;
  filtersPayload: any;
  whatsAppList: any;
  whatsAppListIsLoading: boolean;
  whatsAppChildList: any;
  whatsAppChildListIsLoading: boolean;
};

export const initialState: WhatsappState = {
  templates: [],
  validation24Hr: false,
  conversation: [],
  conversationIsLoading: false,
  filtersPayload: {
    path: 'wa/leads-with-message',
    PageNumber: 1,
    PageSize: 10,
    DateType: 0,
    Visibility: 0,
    IsWithTeam: null,
    Sources: null,
    SubSources: null,
    Projects: null,
    Properties: null,
    UserIds: null,
    TextByIds: null,
    StatusesIds: null,
    SubStatusesIds: null,
  },
  whatsAppList: [],
  whatsAppListIsLoading: false,
  whatsAppChildList: [],
  whatsAppChildListIsLoading: false,
};
export function whatsappReducer(
  state: WhatsappState = initialState,
  action: Action
): WhatsappState {
  switch (action.type) {
    case WhatsappActionTypes.FETCH_WHATSAPP_TEMPLATE_SUCCESS:
      return {
        ...state,
        templates: (action as FetchWhatsappTemplateSuccess).resp,
      };
    case WhatsappActionTypes.FETCH_24HR_VALIDATION_SUCCESS:
      return {
        ...state,
        validation24Hr: (action as Fetch24HrValidationSuccess).resp,
      };
    case WhatsappActionTypes.FETCH_CONVERSATION_SUCCESS:
      return {
        ...state,
        conversation: (action as FetchConversationSuccess).resp,
        conversationIsLoading: false,
      };
    case WhatsappActionTypes.FETCH_CONVERSATION:
      return {
        ...state,
        conversationIsLoading: true,
      };
    case WhatsappActionTypes.UPDATE_FILTER_PAYLOAD:
      return {
        ...state,
        filtersPayload: (action as UpdateFilterPayload).filter,
      };
    case WhatsappActionTypes.FETCH_WHATSAPP_LIST:
      return {
        ...state,
        whatsAppListIsLoading: true,
      };
    case WhatsappActionTypes.FETCH_WHATSAPP_LIST_SUCCESS:
      return {
        ...state,
        whatsAppList: (action as FetchWhatsappListSuccess).response,
        whatsAppListIsLoading: false,
      };
    case WhatsappActionTypes.FETCH_WHATSAPP_CHILD_LIST_SUCCESS:
      return {
        ...state,
        whatsAppChildList: (action as FetchWhatsappChildListSuccess).response,
        whatsAppChildListIsLoading: false,
      };
    case WhatsappActionTypes.FETCH_WHATSAPP_CHILD_LIST:
      return {
        ...state,
        whatsAppChildListIsLoading: true,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.whatsapp;

export const getWhatsAppTemplates = createSelector(
  selectFeature,
  (state: WhatsappState) => state.templates
);

export const get24HrValidation = createSelector(
  selectFeature,
  (state: WhatsappState) => state.validation24Hr
);

export const getConversation = createSelector(
  selectFeature,
  (state: WhatsappState) => state.conversation
);

export const getConversationIsLoading = createSelector(
  selectFeature,
  (state: WhatsappState) => state.conversationIsLoading
);

export const getWhatsAppList = createSelector(
  selectFeature,
  (state: WhatsappState) => state.whatsAppList
);

export const getFiltersPayload = createSelector(
  selectFeature,
  (state: WhatsappState) => state.filtersPayload
);

export const getWhatsAppListIsLoading = createSelector(
  selectFeature,
  (state: WhatsappState) => state.whatsAppListIsLoading
);

export const getWhatsAppChildList = createSelector(
  selectFeature,
  (state: WhatsappState) => state.whatsAppChildList
);

export const getWhatsAppChildListIsLoading = createSelector(
  selectFeature,
  (state: WhatsappState) => state.whatsAppChildListIsLoading
);