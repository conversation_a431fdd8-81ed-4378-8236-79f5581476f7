import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TourService {
  private tourSteps: any[] = []; 
  private currentStepIndex: number = 0; 
  private tourStepSubject = new BehaviorSubject<any>(null); 
  constructor() {}

  setTourSteps(steps: any[]): void {
    this.tourSteps = steps || [];
    this.currentStepIndex = 0;
    this.emitCurrentStep();
  }

  startTour(steps: any[]): void {
    this.setTourSteps(steps);
  }

  getCurrentStep(): any {
    return this.tourSteps[this.currentStepIndex] || null;
  }

  getCurrentStepIndex(): number {
    return this.currentStepIndex;
  }

  nextStep(): void {
    if (this.currentStepIndex < this.tourSteps.length - 1) {
      this.currentStepIndex++;
      this.emitCurrentStep();
    }
  }

  previousStep(): void {
    if (this.currentStepIndex > 0) {
      this.currentStepIndex--;
      this.emitCurrentStep();
    }
  }

  //no need
  stopTour(): void {
    this.tourSteps = [];
    this.currentStepIndex = 0;
    this.emitCurrentStep();
  }

  private emitCurrentStep(): void {
    const currentStep = this.getCurrentStep();
    this.tourStepSubject.next(currentStep);
  }

  getTourStepObservable(): Observable<any> {
    return this.tourStepSubject.asObservable();
  }
}
