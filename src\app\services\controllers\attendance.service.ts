import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { getSystemTimeZoneId } from 'src/app/core/utils/common.util';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root'
})
export class AttendanceService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string;
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'attendance';
  }

  getAttendanceListById(id: string, timeZone: any) {
    const timeZoneId = timeZone?.timeZoneId ? timeZone?.timeZoneId:   getSystemTimeZoneId();
    let params = new HttpParams()
      .set('timeZoneId', timeZoneId);
    return this.http.get(`${this.serviceBaseUrl}/${id}?${params.toString()}`);
  }

  clockIn(payload: any, component?: string) {
    return this.http.post(`${this.serviceBaseUrl}/clockin`, payload);
  }

  clockOut(payload: any, component?: string) {
    return this.http.post(`${this.serviceBaseUrl}/clockout`, payload);
  }

  exportAttendance(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/export`, payload);
  }

  getExportAttendanceStatus(payload: any, pageNumber: number, pageSize: number) {
    return this.http.get(`${this.serviceBaseUrl}/trackers?TrackerPermission=${payload?.trackerPermission}&PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  getAttendanceSetting() {
    return this.http.get(`${this.serviceBaseUrl}/settings`);
  }

  updateSetting(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/settings`, payload);
  }

  getNotification() {
    return this.http.get(`${this.serviceBaseUrl}/notification`);
  }
}
