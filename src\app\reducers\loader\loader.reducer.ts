import { Action, createSelector } from '@ngrx/store';
import { HIDE_LOADER, LEAD_PREVIEW_CHANGED, LEAD_PREVIEW_SAVED, SHOW_LOADER } from "./loader.actions";
import { AppState } from "src/app/app.reducer";
export type LoaderState = {
  loading?: number;
  leadPreviewChanged: boolean;
};

export function loaderReducer(
  state: LoaderState = {
    loading: 0,
    leadPreviewChanged: false,
  },
  action: Action
): LoaderState {
  switch (action.type) {
    case SHOW_LOADER:
      return Object.assign({}, state, { loading: ((state || {}).loading || 0) + 1 });
    case HIDE_LOADER:
      return Object.assign({}, state, {
        loading: Math.max((((state || {}).loading || 0) - 1), 0),
      });
    case LEAD_PREVIEW_CHANGED:
      return {
        ...state,
        leadPreviewChanged: true
      };
    case LEAD_PREVIEW_SAVED:
      return {
        ...state,
        leadPreviewChanged: false
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.loader;

export const getLeadPreviewChanged = createSelector(selectFeature, (state: LoaderState) => {
  return state.leadPreviewChanged;
});