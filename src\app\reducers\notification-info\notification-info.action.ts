import { Action } from '@ngrx/store';

export enum NotificationInfoActionTypes {
  NOTIFICATION = '[NOTIFICATION] Notification Message',
  NOTIFICATION_SUCCESS = '[NOTIFICATION] Notification Success',
}

export class NotificationCall implements Action {
  readonly type: string = NotificationInfoActionTypes.NOTIFICATION;
  constructor(public payload :any) { }
}
export class NotificationCallSuccess implements Action {
  readonly type: string = NotificationInfoActionTypes.NOTIFICATION_SUCCESS;
  constructor(public resp: string = '') { }
}
