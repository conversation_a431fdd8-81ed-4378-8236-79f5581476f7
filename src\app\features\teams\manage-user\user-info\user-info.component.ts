import { Component, <PERSON>E<PERSON>ter, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { skipWhile, takeUntil } from 'rxjs/operators';

import { BLOOD_GROUP, EMPTY_GUID, GENDER } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getSystemTimeOffset, getSystemTimeZoneId, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { getPermissions, getViewPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  DeleteUser,
  FetchUserAssignmentsById,
  FetchUserById,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getSelectedUser,
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment as env } from 'src/environments/environment';
import { EditUserComponent } from './edit-user/edit-user.component';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'user-info',
  templateUrl: './user-info.component.html',
})
export class UserInfoComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedSection: string = 'Attendance';
  showBasicDetails: boolean = true;
  showLocation: boolean = true;
  EMPTY_GUID = EMPTY_GUID;
  getAssignedToDetails = getAssignedToDetails;
  selectedUserId: any;
  userData: any;
  s3BucketUrl: string = env.s3ImageBucketURL;
  allUserList: any;
  canViewAttendance: boolean;
  isLoggedInUser: boolean;
  userId = JSON.parse(localStorage.getItem('userDetails')).sub;
  canNavigateToUsers: boolean = false;
  showLeftNav: boolean = true;
  getTimeZoneDate = getTimeZoneDate
  userBasicDetails: any;
  canViewForFilter: boolean;
  constructor(
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    private activatedRoute: ActivatedRoute,
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private sanitizer: DomSanitizer,
    private router: Router,
    private shareDataService: ShareDataService,
    public trackingService: TrackingService

  ) {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
      });

    this.activatedRoute.params.subscribe(async (params: any) => {
      if (params && params.id) {
        const permission = await firstValueFrom(
          this.store.select(getPermissions).pipe(
            takeUntil(this.stopper),
            skipWhile((data) => !data?.length)
          )
        );
        this.selectedUserId = params.id;
        if (this.selectedUserId) {
          this.store.dispatch(new FetchUserById(this.selectedUserId));
          if(permission?.includes('Permissions.Users.ViewForFilter')){
            this.canViewForFilter = true;
            this.store.dispatch(
              new FetchUserAssignmentsById(this.selectedUserId)
            );
          }
          if (this.selectedUserId === this.userId) {
            this.isLoggedInUser = true;
          } else {
            this.isLoggedInUser = false;
          }
        }
      }
    });
    this.store
      .select(getSelectedUser)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.store
      .select(getViewPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canView: any) => {
        this.canNavigateToUsers = canView.includes('Users');
      });
  }

  refresh(): boolean {
    return false;
  }

  ngOnInit() {
    this.headerTitle.setTitle('User Details');
    this.store.dispatch(new FetchUsersListForReassignment());
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Attendance.ViewAllUsers') || permissions?.includes('Permissions.Attendance.ViewReportees') || this.isLoggedInUser) {
          this.canViewAttendance = true;
          this.selectedSection = this.selectedSection || 'Attendance';
        } else {
          this.selectedSection = this.selectedSection || 'Assigned';
        }
      });

    this.shareDataService.showLeftNav$.subscribe(show => {
      this.showLeftNav = show;
    });
  }

  getBloodGroupName(value: number): string {
    const bloodGroup = BLOOD_GROUP.find(
      (group: { id: number }) => group.id === value
    );
    return bloodGroup ? bloodGroup.name : '';
  }

  getGender(value: number): string {
    const gender = GENDER.find((gender: { id: number }) => gender.id === value);
    return gender ? gender.type : '';
  }

  editUser() {
    this.trackingService.trackFeature(`Web.UserDetails.Button.Edit.Click`)
    if (this.isLoggedInUser) {
      let initialState: any = {
        userId: this.userData?.userId,
      };
      this.modalRef = this.modalService.show(
        EditUserComponent,
        Object.assign(
          {},
          {
            class: 'left-modal modal-400 ph-modal-unset',
            initialState,
          }
        )
      );
    } else {
      this.router.navigate(['teams/edit-user/' + this.userData?.userId]);
    }
  }

  initToggleUserStatus(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: data.isActive ? 'deactivate' : 'activate',
      title: data.firstName + ' ' + data.lastName,
      fieldType: 'user',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.toggleUserStatus(data.userId, data.isActive, this.userData?.timeZoneInfo);
        }
      });
    }
  }

  toggleUserStatus(id: string, isActive: boolean, timeZoneInfo: any) {
    const payload = {
      userId: id,
      activateUser: !isActive,
      timeZoneId: timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      currentUserId: this.userId,
    }
    this.store.dispatch(new DeleteUser(payload));
  }

  viewRoles(rolesView: TemplateRef<any>) {
    this.trackingService.trackFeature(`Web.UserDetails.Button.Setting.Click`)
    this.modalService.show(rolesView, {
      class: 'right-modal modal-400 ip-modal-unset',
    });
  }

  getSanitizedHtml(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
