<div [formGroup]="filtersForm">
    <div class="px-24 bg-white  py-4 tb-left-200 mq-top-navbar border-bottom"
        [ngClass]="showLeftNav ? 'left-230' : 'left-140'">
        <div class="flex-between flex-grow-1">
            <div class="align-center p-8 scroll-hide scrollbar ph-w-100-210 tb-w-100-320">
                <div>
                    <button class="header-5 text-nowrap fw-300 min-w-50 mr-10 px-12 py-8 border br-20"
                        [ngClass]="{'btn-residential-active' : getFormValue('ListingSourceId') === null}"
                        (click)="filtersForm.patchValue({ListingSourceId:null});filterFunction()">
                        All(<span *ngIf="!loaders.isCountLoading;else dotLoader">{{counts?.allCount ?? 0}}</span>)
                    </button>
                </div>
                <div *ngFor="let type of listingSources">
                    <button class="header-5 text-nowrap fw-300 min-w-120 mr-10 px-12 py-8 border br-20"
                        [ngClass]="{'btn-residential-active' : getFormValue('ListingSourceId') === type.id}"
                        (click)="filtersForm.patchValue({ ListingSourceId: type.id });filterFunction()">
                        {{ type?.displayName }} (<span *ngIf="!loaders.isCountLoading;else dotLoader">{{
                            counts?.[type?.displayName?.toLowerCase() + 'Count'] ?? 0
                            }}</span>)
                    </button>
                </div>

            </div>
            <div class="align-center">
                <div class="btn-full-dropdown btn-w-100"
                    *ngIf="permissions?.has('Permissions.Properties.BulkUpload')|| permissions?.has('Permissions.Properties.Export')">
                    <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
                        <span class="ic-tracker icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Tracker</span>
                    </div>
                    <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                        [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedTrackerOption"
                        (click)="openReferenceTracker()">
                        <ng-option (click)="selectedTrackerOption = null" value="bulkUpload"
                            *ngIf="permissions?.has('Permissions.Properties.BulkUpload')">
                            <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                            {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }} Tracker</ng-option>
                    </ng-select>
                </div>
                <ng-container
                    *ngIf="permissions?.has('Permissions.Properties.BulkUpload') || permissions?.has('Permissions.Properties.Create')">
                    <div class="btn-left-dropdown ml-10"
                        (click)="permissions?.has('Permissions.Properties.Create') ? openAddReference() : ''">
                        <span class="ic-add icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Add Reference ID</span>
                    </div>
                    <div class="btn-right-dropdown btn-w-30 black-100">
                        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                            [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedOption"
                            (click)="openReferenceBulk()">
                            <ng-option (click)="selectedOption = null" value="bulkUpload"
                                *ngIf="permissions?.has('Permissions.Properties.BulkUpload')">
                                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
                        </ng-select>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
    <div class="py-20 px-24">
        <div class="align-center bg-white w-100 border-gray">
            <div class="align-center flex-grow-1 no-validation border-end">
                <div *ngIf="permissions?.has('Permissions.Properties.Search')" class="position-relative flex-grow-1">
                    <div class="align-center w-100 px-10 py-12">
                        <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                        <input placeholder="type to search" autocomplete="off" name="search"
                            class="border-0 outline-0 w-100" [value]="getFormValue('SearchFilter')"
                            (input)="isEmptyInput($event)" (keydown.enter)="onEnterKey($event)" />
                    </div>
                    <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
                        ({{ 'LEADS.lead-search-prompt' | translate }})</small>
                </div>
            </div>
            <div class="align-center ip-flex-col ip-align-center-unset">
                <div class="d-flex w-100">
                    <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1 ph-w-40px ph-flex-grow-unset"
                        (click)="openAdvFiltersModal()">
                        <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
                        <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
                    </div>
                </div>
            </div>
            <div class="show-dropdown-white align-center  position-relative">
                <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">{{ 'GLOBAL.show' |
                        translate
                        }}</span> {{ 'GLOBAL.entries' |
                    translate }}</span>
                <ng-select [virtualScroll]="true" class="w-150 tb-w-120px" ResizableDropdown [items]="showEntriesSize"
                    formControlName="PageSize" (change)="filtersForm.get('PageNumber').patchValue(1);filterFunction()">
                </ng-select>
            </div>
        </div>
        <div class="bg-white px-4 py-12 tb-w-100-40" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
            <ng-container *ngIf="showFilters">
                <div class="bg-secondary flex-between">
                    <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                        <ng-container *ngFor="let filter of filteredFilters | keyvalue">
                            <div class="d-flex" *ngIf="!isArray(filter.value) && filter.value">
                                <div
                                    class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap">
                                    {{referenceLabel[filter.key] ??
                                    filter.key}}: {{getFilterValue(filter.key,filter.value)}}<span
                                        class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                        (click)="onRemoveFilter(filter.key,value,filter.value)"></span></div>
                            </div>
                            <ng-container *ngIf="isArray(filter.value)&& filter.value?.length">
                                <ng-container *ngFor="let value of filter.value">
                                    <div class="d-flex">
                                        <div
                                            class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap">
                                            {{referenceLabel[filter.key] ??
                                            filter.key}}: {{getFilterValue(filter.key,value)}}<span
                                                class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                                (click)="onRemoveFilter(filter.key,value,filter.value)"></span>
                                        </div>
                                    </div>
                                </ng-container>

                            </ng-container>

                        </ng-container>
                    </drag-scroll>
                    <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                        (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
                    </div>
                </div>
            </ng-container>
        </div>
        <ng-container *ngIf="!loaders?.referenceIdListIsLoading; else gridLoader">
            <ng-container *ngIf="rowData?.length else noData">
                <div class="manage-reference pinned-grid">
                    <ag-grid-angular class="ag-theme-alpine" #agGrid [gridOptions]="gridOptions"
                        (gridReady)="onGridReady($event)" [rowData]="rowData" [suppressPaginationPanel]="true"
                        [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
                        (filterChanged)="onFilterChanged($event)">
                    </ag-grid-angular>
                </div>
                <div class="my-20 flex-end">
                    <div class="mr-10">{{ 'GLOBAL.showing' | translate }}
                        {{(filtersPayload?.PageNumber-1)*filtersPayload?.PageSize + 1}}
                        {{ 'GLOBAL.to-small' | translate }} {{(filtersPayload?.PageNumber-1)*filtersPayload?.PageSize +
                        gridApi?.getDisplayedRowCount()}}
                        {{ 'GLOBAL.of-small' | translate }} {{totalCount ?? 0}} {{ 'GLOBAL.entries-small' |
                        translate
                        }}</div>
                    <pagination [offset]="filtersPayload?.PageNumber-1" [limit]="1" [range]="1"
                        [size]='getPages(totalCount,filtersPayload?.PageSize)'
                        (pageChange)="this.filtersForm.patchValue({PageNumber: $event+1});filterFunction()">
                    </pagination>
                </div>
            </ng-container>
            <ng-container>
                <div class="justify-center">
                    <div class="position-absolute bg-white gap-2 bottom-12 br-12 flex-between box-shadow-10 p-16 tb-flex-col z-index-2"
                        [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
                        <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                            {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' |
                            translate}}
                        </div>
                        <button class="btn-bulk mr-0" (click)="bulkReassign(assign)">
                            Bulk Reassign</button>
                        <button class="btn-bulk-red" (click)="openBulkDeleteModal(BulkDeleteModal)">Bulk
                            Delete</button>
                    </div>
                </div>
            </ng-container>
        </ng-container>
    </div>
</div>
<ng-template #BulkDeleteModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h5> {{ 'LEADS.bulk' | translate }}
                {{'BUTTONS.delete' | translate}}</h5>
            <div class="icon ic-close  ic-sm cursor-pointer" (click)="bulkDeleteModalRef.hide()"></div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Selected reference id(s)</div>
            <div class="flex-column scrollbar max-h-100-150">
                <ng-container *ngFor="let reference of selectedNodes">
                    <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
                        <span class="text-truncate-1 break-all ml-6"> {{ reference.referenceId }}</span>
                        <div (click)="openConfirmDeleteModal(reference?.referenceId, reference?.id)"
                            class="bg-light-red icon-badge" id="clkBulkDelete" data-automate-id="clkBulkDelete">
                            <span class="icon ic-trash m-auto ic-xxxs"></span>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="flex-center">
                <button (click)="bulkDelete()" class="btn-coal mt-20 mr-8 px-10 min-w-fit-content" id="bulkdelete"
                    data-automate-id="bulkdelete">
                    <span *ngIf="!isBulkDeleteLoading; else buttonDots">{{ 'BUTTONS.delete' |
                        translate}}</span>
                </button>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>
<ng-template #noData>
    <div class="flex-col flex-center h-100-260 min-h-250">
        <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">{{'PROFILE.no-data-found' | translate}}</div>
    </div>
</ng-template>

<ng-template #gridLoader>
    <div class="flex-center h-370">
        <application-loader></application-loader>
    </div>
</ng-template>

<ng-template #dotLoader>
    <div class="container px-4 d-inline">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling"></div>
        </ng-container>
    </div>
</ng-template>

<ng-template #assign>
    <form class="h-100vh text-coal">
        <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
            <h5 class="fw-semi-bold">Reference Assignment</h5>
            <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalRef.hide()"></div>
        </div>
        <div class="p-16 scrollbar min-w-350 max-w-350 h-100-108">
            <integration-assignment [isReferenceId]="true" [isBulkAssignModel]="false"
                [updatedIntegrationList]="updatedIntegrationList" [moduleId]="moduleId"
                [canAssignToAny]="permissions.has('Permissions.Users.AssignToAny')" [allActiveUsers]="allActiveUsers"
                [activeUsers]="activeUsers" [selectedAccountId]="params?.data?.id"
                [canEnableAllowDuplicates]="canEnableAllowDuplicates"
                [canEnableAllowSecondaryUsers]="canEnableAllowSecondaryUsers" [allUserList]="allUserList"
                [userList]="reporteesAdminuserList" (isShowAssignModalChanged)="modalRef.hide()"
                [selectedAccountName]="params?.data?.referenceId" [moduleId]="moduleId"
                [selectedReferences]="selectedReferences"></integration-assignment>
        </div>
    </form>
</ng-template>