<div routerLink='/global-config' [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div class="bg-white w-100 px-24 py-8 flex-between h-48">
    <ul class="align-center top-nav-bar text-nowrap ip-scrollbar ip-w-100-78 scroll-hide">
        <li (click)="filter = 'all'; moveToFirstPage(); fetchData()" [ngClass]="{'gray-scale': filter !== 'all'}"
            class="cursor-pointer align-center mr-10">
                <div [ngClass]="filter !== 'all' ? 'bg-grey' : 'bg-linear-green-10'" class="dot dot-lg">
                    <span [ngClass]="filter !== 'all' ? 'ic-black-350' : ''" class="icon ic-notes-solid ic-xxs"></span>
                </div>
                <span class="text-large mx-8" [ngClass]="filter !== 'all' ? 'fw-semi-bold' : 'fw-700'">
                    {{'GLOBAL.all' | translate}}</span>
        </li>
        <li (click)="filter = 'deleted'; moveToFirstPage(); fetchData()" class="cursor-pointer align-center">
                <div [ngClass]="filter !== 'deleted' ? 'bg-grey' : 'bg-red-750'" class=" dot dot-lg">
                    <span [ngClass]="filter !== 'deleted' ? 'ic-black-350' : ''" class="icon ic-trash ic-xxs"></span>
                </div>
                <span class="text-large mx-8" [ngClass]="filter !== 'deleted' ? 'fw-semi-bold' : 'fw-700'">
                    {{'DASHBOARD.deleted' | translate}}</span>
        </li>
    </ul>
    <button class="btn-coal w-150 ip-w-30px" (click)="navigateToFormCustom()" *ngIf="qrFormList?.length">
        <span class="ic-add icon ic-sm mr-10 ip-mr-0"></span>
        <span class="ip-d-none">Add QR Template</span>
    </button>
</div>
<div class="p-20">
    <div class="bg-white w-100 border-gray flex-between">
        <form autocomplete="off" class="align-center border-end  w-100 py-10 px-12 no-validation">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
            <input placeholder="type template name" name="searchQrForm" class="border-0 outline-0 w-100"
                (keydown.enter)="searchTermSubject.next($event.target.value)" (input)="isEmptyInput($event)"
                [(ngModel)]="searchTerm" autocomplete="off" id="inpSearchQrForm">
            <small class="text-muted text-nowrap ph-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate
                }})</small>
        </form>
        <div class="show-dropdown-white align-center position-relative ip-br-0">
            <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                    {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
            <ng-select [items]="showEntriesSize" [formControl]="pageEntry" (change)="assignPageSize()" ResizableDropdown
                [virtualScroll]="true" [placeholder]="pageSize" class="w-150 tb-w-120px" [searchable]="false">
            </ng-select>
        </div>
    </div>
    <div class="bg-white pt-20 ip-d-none"></div>
    <ng-container *ngIf="!allQRFormsIsLoading else loader">
        <div class="scrollbar scroll-hide tb-w-100-40 table-scrollbar">
            <table class="table standard-table no-vertical-border">
                <thead [ngClass]="qrFormList?.length ?'':'ph-d-none'">
                    <tr class="w-100 text-nowrap">
                        <th class="w-40" *ngIf="filter !== 'all' ? canBulkRestore || canBulkDelete : canBulkDelete">
                            <div class="mb-16">
                                <label class="checkbox-container justify-center mr-6"><input type="checkbox"
                                        [checked]="isAllSelected()" (change)="selectAllRows($event)">
                                    <span class="checkmark justify-center"></span>
                                </label>
                            </div>
                        </th>
                        <th class="w-70px">Status</th>
                        <th class="w-150">Template Name</th>
                        <th class="w-150">Created By & Date</th>
                        <th class="w-150">Modified By & Date</th>
                        <th class="w-110">Actions</th>
                    </tr>
                </thead>
                <ng-container *ngIf="qrFormList?.length; else noDataFound">
                    <tbody class="text-secondary fw-semi-bold max-h-100-277">

                        <tr *ngFor="let form of qrFormList; let i = index">
                            <td class="w-40" *ngIf="filter !== 'all' ? canBulkRestore || canBulkDelete: canBulkDelete">
                                <div class="mb-16">
                                    <label class="checkbox-container">
                                        <input type="checkbox" (change)="onCheckboxChange($event)"
                                            [(ngModel)]="form.selected" /><span class="checkmark"></span>
                                    </label>
                                </div>
                            </td>
                            <td class="w-70px">
                                <ng-container *ngIf="form?.isArchived; else toggleSwitchTemplate">
                                    <div class="bg-accent-red-40 text-dark-red br-50px text-center p-4 text-xs">
                                        {{ 'LABEL.inactive' | translate }}
                                    </div>
                                </ng-container>
                                <ng-template #toggleSwitchTemplate>
                                    <div [title]="form.status == 0 ? 'Active' : 'Inactive'">
                                        <input type="checkbox" class="toggle-switch toggle-active-sold"
                                            (click)="openConfirmModal(form, 'status')" [checked]="form.status === 0"
                                            [id]="'chkToggle' + i">
                                        <label [for]="'chkToggle' + i" class="switch-label"></label>
                                    </div>
                                </ng-template>
                            </td>
                            <td class="w-150">
                                <div class="text-truncate-1 break-all">{{form.name}}</div>
                            </td>
                            <td class="w-150">
                                <h6 class="fw-600 mb-4">{{getAssignedToDetails(form?.createdBy, allUserList, true)
                                    ||
                                    ''}}</h6>
                                <div *ngIf="form.createdOn">At {{
                                    getTimeZoneDate(form?.createdOn,userData?.timeZoneInfo?.baseUTcOffset,
                                    'fullDateTime')}}
                                </div>
                                <div class="text-truncate-1 break-all text-xs text-dark-gray fst-italic"
                                    *ngIf="userData?.timeZoneInfo?.timeZoneName && userData?.shouldShowTimeZone">
                                    ( {{userData?.timeZoneInfo?.timeZoneName &&
                                    userData?.shouldShowTimeZone && form?.createdOn
                                    ? userData?.timeZoneInfo?.timeZoneName : ''}} )</div>
                            </td>
                            <td class="w-150">
                                <h6 class="fw-600 mb-4">{{getAssignedToDetails(form?.lastModifiedBy, allUserList,
                                    true) || ''}}</h6>
                                <div *ngIf="form.lastModifiedOn">At
                                    {{getTimeZoneDate(form?.lastModifiedOn,userData?.timeZoneInfo?.baseUTcOffset,
                                    'fullDateTime')}}</div>
                                <div class="text-truncate-1 break-all text-xs text-dark-gray fst-italic"
                                    *ngIf="userData?.timeZoneInfo?.timeZoneName && userData?.shouldShowTimeZone">
                                    ({{userData?.timeZoneInfo?.timeZoneName &&
                                    userData?.shouldShowTimeZone && form?.lastModifiedOn
                                    ? userData?.timeZoneInfo?.timeZoneName : ''}})</div>
                            </td>
                            <td class="w-110">
                                <div *ngIf="!form.isArchived" class="align-center">
                                    <div title="Edit" class="bg-accent-green icon-badge" (click)="editTemplate(form)">
                                        <span class="icon m-auto ic-xxs ic-pen"></span>
                                    </div>
                                    <div title="Config" class="bg-dark-red-30 icon-badge"
                                        (click)="OpenConfig(form,configModal)"><span
                                            class="icon m-auto ic-xxs ic-user-setting"></span></div>
                                    <div title="Clone" class="bg-dark-red-40 icon-badge" (click)="cloneTemplate(form)">
                                        <span class="icon ic-split-arrows m-auto ic-xxxs"></span>
                                    </div>
                                    <div title="QR Preview" class="bg-green-100 icon-badge" (click)="openQrCode(form)">
                                        <span class="icon ic-qr-code m-auto ic-xxs"></span>
                                    </div>
                                    <a [href]="generateQrUrl(form.id)" target="_blank" title="Preview"
                                        class=" bg-accent-blue icon-badge"><span
                                            class="icon m-auto ic-xxs ic-eye"></span></a>
                                    <div title="Delete" class="bg-light-red icon-badge"
                                        (click)="openConfirmModal(form, 'delete')">
                                        <span class="icon ic-delete m-auto ic-xxs"></span>
                                    </div>
                                </div>
                                <div *ngIf="form.isArchived" class="align-center">
                                    <div title="Restore" class="bg-blue-450 icon-badge"
                                        (click)="restoreTemplate($event, form)"><span
                                            class="icon ic-update m-auto ic-xxs"></span></div>
                                    <div title="Permanent Delete" class="bg-light-red icon-badge"
                                        (click)="permanentDelete($event,form)">
                                        <span class="icon ic-delete m-auto ic-xxs"></span>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </ng-container>
            </table>

        </div>
    </ng-container>
    <div class="mt-16  flex-end" *ngIf="qrFormList?.length>0 && !allQRFormsIsLoading">
        <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*pageSize + 1}}
            {{ 'GLOBAL.to-small' | translate }} {{currOffset*pageSize + qrFormList?.length}}
            {{ 'GLOBAL.of-small' | translate }} {{totalPages}} {{ 'GLOBAL.entries-small' | translate }}</div>
        <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalPages,pageSize)'
            (pageChange)="onPageChange($event)">
        </pagination>
    </div>
</div>
<ng-template #noDataFound>
    <tr class="bg-white  h-100-186">
        <div class="flex-center-col bg-white br-4 ip-bg-unset my-20 pt-3">
            <img src="assets/images/layered-cards.svg" alt="No lead found">
            <div *ngIf="filter !== 'deleted'" class="header-4 text-dark-gray">no templates added yet...</div>
            <div *ngIf="filter === 'deleted'" class="header-4 text-dark-gray">no templates deleted yet...</div>
            <button *ngIf="filter !== 'deleted'" class="btn btn-linear-green my-10 flex-center w-150 text-nowrap"
                (click)="navigateToFormCustom()">
                <span class="ic-add icon ic-sm mr-10"></span>
                <span class="text-white mb-0">Add New Template</span>
            </button>
        </div>
    </tr>
</ng-template>
<div class="justify-center">
    <div *ngFor="let form of qrFormList"
        class="position-absolute bg-white bottom-12 br-12 shadow-sm p-10 z-index-2 scrollbar ph-w-100-80 scroll-hide align-center"
        [ngClass]="{'d-none': selectedCount === 0}">
        <div class="align-center">
            <div class="fw-600 text-coal mr-20  text-l">{{ selectedCount }}
                {{ selectedCount > 1 ? 'Items' : 'Item' }} {{ 'LEADS.selected' | translate}}
            </div>
        </div>
        <div class="flex-center">
            <button *ngIf="filter !== 'all' && canBulkRestore" [disabled]="selectedCount === 1" class="btn-bulk-blue"
                id="btnBulkRestore" data-automate-id="btnBulkRestore" (click)="openBulkRestoreModal(BulkRestoreModal)">
                Bulk Restore</button>
            <button class="btn-bulk-red" id="btnBulkDelete" *ngIf="canBulkDelete" [disabled]="selectedCount === 1"
                data-automate-id="btnBulkDelete" (click)="openBulkDeleteModal(BulkDeleteModal)">
                Bulk Delete</button>
        </div>
    </div>
</div>
<ng-template #BulkRestoreModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.restore' | translate }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkDeleteModalRef.hide()"></div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Selected Template(s)</div>
            <div class="flex-column scrollbar max-h-100-176">
                <ng-container *ngFor="let template of selectedTemplates">
                    <div class="p-12 fw-600 text-sm border-bottom text-secondary bg-white flex-between">
                        <div class="text-truncate-1 break-all"> {{ template?.name }}</div>
                        <div>
                            <a (click)="openConfirmDeleteModal(template?.name, template?.id)"
                                class="bg-light-red icon-badge" id="clkDeleteBulk" data-automate-id="clkDeleteBulk">
                                <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="flex-center">
                <button class="btn-coal mt-20" (click)="updateBulkRestore()">{{ 'BUTTONS.restore' | translate
                    }}</button>

            </div>
        </div>
    </div>
</ng-template>
<ng-template #BulkDeleteModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.delete' | translate }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkDeleteModalRef.hide()"></div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Selected Template(s)</div>
            <div class="flex-column scrollbar max-h-100-176">
                <ng-container *ngFor="let template of selectedTemplates">
                    <div class="p-12 fw-600 text-sm border-bottom text-secondary bg-white flex-between">
                        <div class="text-truncate-1 break-all">{{ template?.name }} </div>
                        <div>
                            <a (click)="openConfirmDeleteModal(template?.name, template?.id)"
                                class="bg-light-red icon-badge" id="clkDeleteBulk" data-automate-id="clkDeleteBulk">
                                <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="flex-center">
                <button class="btn-coal mt-20" (click)="updateBulkDelete(selectedTemplates)">{{ 'BUTTONS.delete' |
                    translate
                    }}</button>

            </div>
        </div>
    </div>
</ng-template>
<ng-template #loader>
    <div class="flex-center h-100 mt-80">
        <application-loader></application-loader>
    </div>
</ng-template>