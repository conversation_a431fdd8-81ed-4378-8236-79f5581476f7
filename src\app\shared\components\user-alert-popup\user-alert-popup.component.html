<div class="br-4">
    <h2 [ngClass]="data?.fieldType === 'Delete' ? ' delete-bg-pattern' : 'waring-bg-pattern'"
        class="h-100px align-center text-white fw-800 pl-20 brtr-4 brtl-4">{{data?.heading}}</h2>
    <div class="bg-white p-30 flex-col br-4">
        <ng-container [ngSwitch]="type">
            <div *ngSwitchCase="'userDelete'">
                <div *ngIf="!data?.isDeleted">
                    <p>User "{{data?.userName}}" has assigned with:</p>
                    <div class="d-flex flex-wrap">
                        <ng-container *ngFor="let field of fields">
                            <div class="w-50 mt-4" *ngIf="data[field.key]">
                                <ng-container
                                    *ngIf="field.key === 'leads' || field.key === 'prospects'; else regularField">
                                    <ng-container *ngIf="isDualOwnership ; else normal">
                                        <div *ngIf="field.key === 'leads'" class="d-flex">
                                            {{ field.label }} - P(
                                            <div class="ml-2 text-decoration-underline cursor-pointer text-accent-green fw-semi-bold text-truncate-1 break-all text-nowrap"
                                                (click)="onFieldClick(field.key,'primary'); $event.stopPropagation();">
                                                {{ data['leads'] }}
                                            </div>)
                                            <span class="ml-8">S(</span>
                                            <div class="ml-2 text-decoration-underline cursor-pointer text-accent-green fw-semi-bold text-truncate-1 break-all text-nowrap"
                                                (click)="onFieldClick(field.key,'sec'); $event.stopPropagation();">
                                                {{data['secondaryLeads']}}
                                            </div>)
                                        </div>
                                        <div *ngIf="field.key === 'prospects'" class="d-flex">
                                            {{ field.label }} -
                                            <div class="ml-2 text-decoration-underline cursor-pointer text-accent-green fw-semi-bold text-truncate-1 break-all text-nowrap"
                                                (click)="onFieldClick(field.key,''); $event.stopPropagation();">
                                                {{ data[field?.key] }}
                                            </div>
                                        </div>
                                    </ng-container>
                                    <ng-template #normal>
                                        <div class="d-flex">
                                            {{ field.label }} -
                                            <div class="ml-2 text-decoration-underline cursor-pointer text-accent-green fw-400 text-truncate-1 break-all text-nowrap"
                                                (click)="onFieldClick(field.key); $event.stopPropagation();">
                                                <b>{{ data[field.key] }}</b>
                                            </div>
                                        </div>
                                    </ng-template>
                                </ng-container>
                                <ng-template #regularField>
                                    {{ field.label }} - <b>{{ data[field.key] }}</b>
                                </ng-template>
                            </div>
                        </ng-container>
                    </div>
                    <div class="mt-12">
                        <p class="text-red text-decoration-underline">Important Notes</p>
                    </div>
                    <div class="mt-10">
                        <div *ngFor="let message of deletionMessages" class="d-flex mt-2">
                            <span class="dot dot-xxs bg-dark-700 mt-6 mx-4 mr-12"></span>
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngSwitchCase="'permanentDelete'">
                <h4 class="align-center gap-1">
                    ⚠️<span class="mt-4">Attention:</span>
                </h4>
                <div class="d-flex mt-4 ml-6">
                    <span class="dot dot-xxs bg-dark-700 mt-8 mx-4 mr-12"></span>
                    <h4 [innerHTML]="data?.message"></h4>
                </div>
                <h4 class="mt-16">{{data?.description}}</h4>
            </div>
            <div *ngSwitchCase="'manageMarketingDelete'">
                <div class="d-flex mt-4 ml-6 text-center">
                    <h4 *ngIf="data?.bulkMessage" [innerHTML]="data?.bulkMessage"></h4>
                    <h4 *ngIf="data?.agencyData?.leadsCount > 0 && data?.agencyData?.prospectCount > 0">
                        There are {{ data?.agencyData?.leadsCount }} Leads and {{ data?.agencyData?.prospectCount }}
                        Data associated with
                        <span class="fw-600">"{{data?.buttonContent === 'Delete Channel Partner' ?
                            data?.agencyData?.firmName : data?.agencyData?.name}}"</span>.
                        Are you sure you want to delete?
                    </h4>
                    <h4 *ngIf="data?.agencyData?.leadsCount > 0 && data?.agencyData?.prospectCount === 0">
                        {{ data?.agencyData?.leadsCount === 1 ? 'There is' : 'There are' }} {{
                        data?.agencyData?.leadsCount }} Lead{{ data?.agencyData?.leadsCount > 1 ? 's' : '' }}
                        associated
                        with
                        <span class="fw-600">"{{ data?.buttonContent === 'Delete Channel Partner' ?
                            data?.agencyData?.firmName : data?.agencyData?.name }}"</span>.
                        Are you sure you want to delete?
                    </h4>

                    <h4 *ngIf="data?.agencyData?.prospectCount > 0 && data?.agencyData?.leadsCount === 0">
                        {{ data?.agencyData?.prospectCount === 1 ? 'There is' : 'There are' }} {{
                        data?.agencyData?.prospectCount }} Data{{ data?.agencyData?.prospectCount > 1 ? 's' : '' }}
                        associated with
                        <span class="fw-600">"{{ data?.buttonContent === 'Delete Channel Partner' ?
                            data?.agencyData?.firmName : data?.agencyData?.name }}"</span>.
                        Are you sure you want to delete?
                    </h4>

                    <h4 *ngIf="data?.agencyData?.prospectCount > 0 && data?.agencyData?.leadsCount === 0">
                        {{ data?.agencyData?.prospectCount === 1 ? 'There is' : 'There are' }} {{
                        data?.agencyData?.prospectCount }} Data{{ data?.agencyData?.prospectCount > 1 ? 's' : '' }}
                        associated with
                        <span class="fw-600">"{{ data?.buttonContent === 'Delete Channel Partner' ?
                            data?.agencyData?.firmName : data?.agencyData?.name }}"</span>.
                        Are you sure you want to delete?
                    </h4>

                    <h4 *ngIf="data?.agencyData?.leadsCount === 0 && data?.agencyData?.prospectCount === 0">
                        Are you sure you want to delete <span class="fw-600">
                            "{{ data?.buttonContent === 'Delete Channel Partner' ? data?.agencyData?.firmName :
                            data?.agencyData?.name }}"</span>?
                    </h4>

                </div>

                <div class="flex-center mt-30">
                    <h4 class="w-120 h-40 br-4 border flex-center fw-600 border-slate-60 cursor-pointer mr-16"
                        (click)="modalRef.hide()">Cancel</h4>

                    <h4 class="text-white flex-center bg-red-750 w-120 h-40 br-4 fw-600 cursor-pointer"
                        (click)="deleteMarketing()">
                        <span *ngIf="!isDeleting else buttonDots">{{ 'BUTTONS.delete' |translate}}</span>
                    </h4>
                </div>
            </div>
            <div *ngSwitchCase="'amenityAttr'">
                <h5 [innerHTML]="data?.message"></h5>
            </div>
            <div *ngSwitchCase="'ListingManagement'">
                <div class="d-flex mt-4 ml-6">
                    <h5 [innerHTML]="data?.message"></h5>
                </div>
            </div>
            <div *ngSwitchCase="'leadDuplicate'">
                <div class="d-flex mt-4 ml-6">
                    <h5>{{data?.message}}<span *ngIf="data?.module === 'lead'"
                            class="ml-2 text-decoration-underline cursor-pointer text-accent-green fw-semi-bold"
                            (click)="navigateToParentLead.emit()">View
                            Parent Lead</span></h5>
                </div>
            </div>
            <div *ngSwitchCase="'reportAutomation'">
                <div class="text-center">
                    <h5 [innerHTML]="data?.message"></h5>
                </div>
            </div>
        </ng-container>
        <div class="flex-center mt-30" *ngIf="type !=='manageMarketingDelete'">
            <h4 class="w-120 h-40 br-4 border flex-center fw-600 border-slate-60 cursor-pointer mr-16"
                (click)="modalRef.hide()">Cancel</h4>
            <h4 *ngIf="type !=='ListingManagement' && type !=='leadDuplicate'"
                class="text-white flex-center bg-red-750 w-120 h-40 br-4 fw-600 cursor-pointer"
                (click)="deleteConfirm()">Delete</h4>
            <h4 *ngIf="type ==='ListingManagement' && !data?.allPropertiesWithoutPermits"
                class="text-white flex-center bg-black-10 w-120 h-40 br-4 fw-600 cursor-pointer"
                (click)="deleteConfirm()">Publish</h4>
            <h4 *ngIf="type ==='leadDuplicate'"
                class="text-white flex-center bg-black-10 w-160 h-40 br-4 fw-400 cursor-pointer"
                (click)="deleteConfirm()">Proceed With Adding</h4>
        </div>
    </div>
</div>
<ng-template #buttonDots>
    <div class="container ml-40">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>