import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { FetchWhatsappTemplate } from 'src/app/reducers/whatsapp/whatsapp.actions';
import { getWhatsAppTemplates } from 'src/app/reducers/whatsapp/whatsapp.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'whatsapp-chat-bulk',
  templateUrl: './whatsapp-chat-bulk.component.html',
})
export class WhatsappChatBulkComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  data: any;
  whatsAppForm: FormGroup;
  whatsAppTemplateList: Array<any> = [];
  selectedLeads: any;

  constructor(private fb: FormBuilder, public modalService: BsModalService,

    public modalRef: BsModalRef, private store: Store<AppState>) {
    this.whatsAppForm = this.fb.group({
      template: [null, Validators.required],
      content: [''],
    });

    this.store.dispatch(new FetchWhatsappTemplate());
  }

  ngOnInit() {
    this.selectedLeads = this.data;
    this.store
      .select(getWhatsAppTemplates)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.whatsAppTemplateList = data?.waTemplates;
      });

    this.whatsAppForm
      .get('template')
      .valueChanges.subscribe((template) => {
        let content = '';

        if (template.header) {
          content += template.header;
        }

        if (template.message) {
          if (content) {
            content += '\n\n';
          }
          content += template.message;
        }

        if (template.footer) {
          if (content) {
            content += '\n\n';
          }
          content += template.footer;
        }

        this.whatsAppForm.patchValue({
          content: content,
        });
      });
  }

  sendMessage() { }

  openConfirmDeleteModal(leadName: string, leadId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: leadName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeLead(leadId);
        }
      });
    }
  }

  removeLead(id: string): void {
    this.selectedLeads = this.data.filter(
      (lead: any) => lead.id !== id
    );
    if (this.selectedLeads.length <= 0) this.modalRef.hide();
  }
}
