import { Action, createSelector } from "@ngrx/store";
import { FetchBulkShiftTimingSuccess, ShiftTimingActionTypes } from "./shift-timing.actions";
import { AppState } from "src/app/app.reducer";

export type ShiftTimingState = {
  allBulkShiftTiming: any[];
  shiftTimingIsLoading: boolean;
  isBulkShiftTimingLoading: boolean;
};

const initialState: ShiftTimingState = {
  allBulkShiftTiming: [],
  shiftTimingIsLoading: false,
  isBulkShiftTimingLoading: true,
};

export function shiftTimingReducer(
  state: ShiftTimingState = initialState,
  action: Action
): ShiftTimingState {
  switch (action.type) {
    case ShiftTimingActionTypes.FETCH_BULK_SHIFT_TIMING:
      return {
        ...state,
        isBulkShiftTimingLoading: true,
      };
     case ShiftTimingActionTypes.FETCH_BULK_SHIFT_TIMING_SUCCESS:
      return {
        ...state,
        allBulkShiftTiming: (action as FetchBulkShiftTimingSuccess).response?.items,
        isBulkShiftTimingLoading: false,
      };
    case ShiftTimingActionTypes.ADD_SHIFT_TIMING:
      return {
        ...state,
        shiftTimingIsLoading: true,
      };
    case ShiftTimingActionTypes.ADD_SHIFT_TIMING_SUCCESS:
      return {
        ...state,
        shiftTimingIsLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.shiftTiming;

export const getBulkShiftTiming = createSelector(
  selectFeature,
  (state: ShiftTimingState) => state.allBulkShiftTiming
);

export const getBulkShiftTimingIsLoading = createSelector(
  selectFeature,
  (state: ShiftTimingState) => state.isBulkShiftTimingLoading,
);

export const getUpdateSTIsLoading = createSelector(
  selectFeature,
  (state: ShiftTimingState) => state.shiftTimingIsLoading
);
