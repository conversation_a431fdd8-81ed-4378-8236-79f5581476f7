import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { APP_VERSION } from './app/app.constants';
import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

import { Chart, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend } from 'chart.js';
Chart.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend);

if (environment.production) {
  enableProdMode();
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => {
    console.error(err);
    // if (environment.production) sendErrorEmail(err);
  });

// Override console.error to capture all errors
// (function () {
//   if (!environment.production) return;
//   const oldConsoleError = console.error;
//   console.error = function (...args: any[]) {
//     oldConsoleError.apply(console, args);
//     sendErrorEmail(args.join(' '));
//   };
// })();

// // Function to send an error email
// function sendErrorEmail(error: any) {
//   const errorMessage = error.message || error.toString();
//   const errorStack = error.stack || '';
//   const userDetails = JSON.parse(localStorage.getItem('userDetails') || '{}');

//   const content = `Error Log Notification:

//   Application Version: v${APP_VERSION},
//   Tenant Name: ${getTenantName()},
//   Environment: ${environment.envt},
//   Module: '${window.location.pathname}',

//   User Details: ${JSON.stringify(userDetails, null, 10)},

//   Error Message: ${JSON.stringify(errorMessage || '', null, 10)},
//   Stack Trace: ${JSON.stringify(errorStack || '', null, 10)}`
//   const formData = new FormData();
//   formData.append('Sender', '<EMAIL>');
//   formData.append('Subject', `WEB(${environment.envt}): Console Error Alert v${APP_VERSION}`);
//   formData.append('ContentBody', content);

//   // Add multiple recipients properly
//   ['<EMAIL>', '<EMAIL>', '<EMAIL>'].forEach(recipient => {
//     formData.append('ToRecipients', recipient);
//   });
//   // Here, call your backend API or third-party email service
//   fetch(`${environment.baseURL}${environment.apiURL}/utility/sendemail/form`, {
//     method: 'POST',
//     body: formData
//   })
//     .then(data => console.log('Error report sent successfully:', data))
//     .catch(err => console.error('Failed to send error email:', err));
// }
