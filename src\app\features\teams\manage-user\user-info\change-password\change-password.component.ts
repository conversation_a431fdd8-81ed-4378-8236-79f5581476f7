import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import {
  matchValidator,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ChangePassword } from 'src/app/reducers/forgot-password/forgot-password.actions';

@Component({
  selector: 'change-password',
  templateUrl: './change-password.component.html',
})
export class ChangePasswordComponent {
  changePasswordForm: FormGroup;
  showOldPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;
  constructor(
    private fb: FormBuilder,
    public router: Router,
    private store: Store<AppState>
  ) {
    this.changePasswordForm = this.fb.group({
      password: ['', Validators.required],
      newPassword: ['', Validators.required],
      confirmPassword: ['', Validators.required],
    });
    this.changePasswordForm.addValidators(
      matchValidator(
        this.changePasswordForm.get('newPassword'),
        this.changePasswordForm.get('confirmPassword')
      )
    );
  }


  changePassword() {
    if (!this.changePasswordForm.valid) {
      validateAllFormFields(this.changePasswordForm);
      return;
    }

    let payload = {
      password: this.changePasswordForm.value.password,
      newPassword: this.changePasswordForm.value.newPassword,
      ConfirmNewPassword: this.changePasswordForm.value.confirmPassword,
    };
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    this.store.dispatch(new ChangePassword(payload, userId));
  }

  cancelChanges() {
    this.changePasswordForm.reset();
  }
}
