<ng-container *ngIf="!isTestimonalLoading; else loader">
    <ng-container *ngIf="!testimonials.length; else testimonialsData">
        <div class="flex-center ip-flex-col">
            <img src="assets/images/no-testimonials.svg" alt="No testimonial found" class="mr-16 ip-mr-0">
            <div class="flex-center-col ph-ml-0">
                <div class="pb-36 text-gray text-center">
                    <h4 class="mb-4">{{'PROFILE.no-data-found' | translate}}</h4>
                    <h4>{{'PROFILE.empty-message' | translate}}</h4>
                </div>
                <button (click)="addTestimonial()" class="btn btn-accent-green flex-center" id="btnAddTestmonial"
                    data-automate-id="btnAddTestmonial">
                    <span class="ic-add icon icon-xxs mr-8"></span>{{ 'BUTTONS.add-testimonial' | translate }}
                </button>
            </div>
        </div>
    </ng-container>
    <ng-template #testimonialsData>
        <div class="text-sm d-flex flex-wrap ip-mt-40">
            <div *ngFor="let testimonial of testimonials" class="w-33 tb-w-50 ip-w-100">
                <div class="bg-white p-10 br-6 m-10 ip-ml-0">
                    <div class="flex-between">
                        <div class="align-center">
                            <img [appImage]="testimonial?.imageURL ? s3BucketUrl+testimonial?.imageURL : ''"
                                [type]="'defaultAvatar'" alt="profile" class="w-60 h-60 obj-cover br-50 mr-10">
                            <div>
                                <div>{{testimonial?.givenBy}}</div>
                                <div>{{testimonial?.companyName}}</div>
                            </div>
                        </div>
                        <div>
                            <div title="Delete" *ngIf="canDelete" (click)="deleteTestimonial(testimonial?.id)"
                                class="icon ic-delete ic-sm ic-red cursor-pointer"></div>
                            <div title="Edit" *ngIf="canEditProfile" (click)="editTestimonial(testimonial)"
                                class="icon ic-pen ic-sm ic-accent-green cursor-pointer ml-12"></div>
                        </div>
                    </div>
                    <div class="mt-12 word-break">{{testimonial?.description}}</div>
                </div>
            </div>
        </div>
    </ng-template>

</ng-container>

<ng-template #loader>
    <div class="flex-center h-100">
        <application-loader></application-loader>
    </div>
</ng-template>