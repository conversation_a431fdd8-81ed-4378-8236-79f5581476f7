import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { take, takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';

import { BillingType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  Profile,
  Subscription,
  Transaction,
} from 'src/app/core/interfaces/profile.interface';
import {
  getEnvDetails,
  getPages,
  getTenantName,
  getTimeZoneDate,
  onlyNumbers,
} from 'src/app/core/utils/common.util';
import {
  AssignPageSize,
  FetchProfile,
  FetchSubscription,
  FetchTransaction,
} from 'src/app/reducers/profile/profile.actions';

import {
  getProfile,
  getProfileIsLoading,
  getSubscription,
  getTransaction,
} from 'src/app/reducers/profile/profile.reducers';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { ProfileService } from 'src/app/services/controllers/profile.service';

@Component({
  selector: 'subscription',
  templateUrl: './subscription.component.html',
})
export class SubscriptionComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedSection: string = 'QRCode';
  pageSize: number = PAGE_SIZE;
  pageEntry: FormControl = new FormControl(this.pageSize);
  showEntriesSize: number[] = SHOW_ENTRIES;
  transaction: Transaction;
  moment = moment;
  subscription: Subscription;
  BillingType = BillingType;
  showTransactionData: boolean = false;
  selectedIndex: number = -1;
  subSelectedIndex: number = -1;
  numberOfLicenses: number;
  netPayableAmount: any = 0;
  gstAmount: any = 0;
  totalPayableAmount: any = 0;
  transactionInfo: any;
  userData: any;
  getTimeZoneDate = getTimeZoneDate;
  tenantId: string = getTenantName();
  totalPages: number;
  onlyNumbers = onlyNumbers;
  getPages = getPages;
  currOffset: number = 0;
  currPageNumber: number = 1;
  isSubscriptionLoading: boolean;

  constructor(
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    private _notificationsService: NotificationsService,
    private _translateService: TranslateService,
    private api: ProfileService
  ) {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.store.dispatch(new FetchTransaction());
    this.store.dispatch(new FetchSubscription(this.userData?.timeZoneInfo));
    this.store.dispatch(new FetchProfile());
  }

  ngOnInit() {
    this.store
      .select(getSubscription)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subscription = data;
      });

    this.store
      .select(getProfileIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isSubscriptionLoading = data;
      });

    this.fetchData();
  }
  fetchData() {
    this.store
      .select(getTransaction)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.transaction = data.items;
        this.totalPages = data.totalCount;
      });
  }

  openPaymentModal(payment: TemplateRef<any>) {
    let initialState: any = {
      class: 'modal-500 modal-dialog-centered ip-modal-unset',
    };
    this.modalRef = this.modalService.show(payment, initialState);
  }

  openLicenseModal(license: TemplateRef<any>) {
    let initialState: any = {
      class: 'modal-500 top-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(license, initialState);
  }

  openDetailsModal(Details: TemplateRef<any>, index: number) {
    this.selectedIndex = index;
    let initialState: any = {
      class: 'modal-1000 top-modal tb-modal-unset',
    };

    this.modalRef = this.modalService.show(Details, initialState);
  }

  openSubDetailsModal(
    SubDetails: TemplateRef<any>,
    index: number,
    srno: number
  ) {
    this.selectedIndex = index;
    this.subSelectedIndex = srno;
    let initialState: any = {
      class: 'modal-1000 top-modal tb-modal-unset',
    };
    this.modalRef = this.modalService.show(SubDetails, initialState);
  }

  copyToClipboard(text: string): void {
    navigator.clipboard?.writeText(text),
      this._notificationsService.success(
        this._translateService.instant('Copied ' + text + ' to Clipboard')
      );
  }

  toggleTransactionData(index: number): void {
    if (this.selectedIndex === index && this.showTransactionData) {
      this.showTransactionData = false;
    } else {
      this.selectedIndex = index;
      this.showTransactionData = true;
    }
  }

  calculateAmounts(): void {
    const pricePerUserPerMonthWithoutGST =
      this.subscription?.pricePerUserPerMonthWithoutGST;
    const daysLeft = this.subscription?.daysLeft;
    const monthsLeft = (daysLeft / 30).toFixed(2);
    this.netPayableAmount = (
      pricePerUserPerMonthWithoutGST *
      +monthsLeft *
      this?.numberOfLicenses
    ).toFixed(2);
    this.gstAmount = (this.netPayableAmount * 0.18).toFixed(2);
    this.totalPayableAmount = (
      +this.netPayableAmount + +this.gstAmount
    ).toFixed(2);
  }

  cancel() {
    this.numberOfLicenses = null;
    this.calculateAmounts();
  }

  payNow(NoUrl: any) {
    let profile: Profile;
    this.store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((resp: any) => {
        profile = resp;
      });

    let payload: any = {
      companyName: profile?.displayName,
      phoneNumber: profile?.phoneNumber,
      companyEmail: profile?.email,
      noOfLicences: this.numberOfLicenses,
      gstNumber: profile?.gstNumber,
      netAmount: this.netPayableAmount,
      gstAmount: this.gstAmount,
      totalAmount: this.totalPayableAmount,
      redirectUrl: `https://www.leadrat.com/paymentcrm/Callback?tenant=${this.tenantId}`,
    };
    this.api
      .paymentGateway(payload)
      .pipe(take(1))
      .subscribe((resp: any) => {
        this.modalRef.hide();
        if (resp?.data?.length) {
          window.open(
            `https://integration${getEnvDetails()}/payment?paymentSessionId=${resp?.data
            }`,
            '_blank'
          );
        } else {
          let initialState: any = {
            class: 'modal-350 top-modal',
          };
          this.modalRef = this.modalService.show(NoUrl, initialState);
        }
        this.cancel();
      });
  }

  onPageChange(offset: number) {
    this.currOffset = offset;
    this.currPageNumber = offset + 1;
    this.store.dispatch(new AssignPageSize(this.pageSize, this.currPageNumber));
    this.fetchData();
    this.filterTransactionList();
  }

  moveToFirstPage() {
    this.store.dispatch(new AssignPageSize(this.pageSize, 1));
  }

  filterTransactionList() {
    this.store.dispatch(
      new FetchTransaction(this.currPageNumber, this.pageSize)
    );
  }

  assignPageSize() {
    this.pageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    this.store.dispatch(new AssignPageSize(this.pageSize, this.currPageNumber));
    this.fetchData();
    this.filterTransactionList();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    this.store.dispatch(new AssignPageSize(10, 1));
  }
}
