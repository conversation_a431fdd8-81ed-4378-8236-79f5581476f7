import { Action } from '@ngrx/store';

export enum AnalyticsActionTypes {
    FETCH_ACTIONS_LIST = '[APP ANALYTICS] Fetch Actions List',
    FETCH_ACTIONS_LIST_SUCCESS = '[APP ANALYTICS] Fetch Actions List Success',
    ADD_TRACKS = '[APP ANALYTICS] Add Tracks',
    ADD_TRACKS_SUCCESS = '[APP ANALYTICS] Add Tracks Success',
    FETCH_FEATURES_LIST = '[APP ANALYTICS] Fetch Features List',
    FETCH_FEATURES_LIST_SUCCESS = '[APP ANALYTICS] Fetch Features List Success',
}

export class FetchActionsList implements Action {
    readonly type: string = AnalyticsActionTypes.FETCH_ACTIONS_LIST;
    constructor() { }
}
export class FetchActionsListSuccess implements Action {
    readonly type: string = AnalyticsActionTypes.FETCH_ACTIONS_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}
export class AddTracks implements Action {
    readonly type: string = AnalyticsActionTypes.ADD_TRACKS;
    constructor(public payload: any = {}) { }
}

export class AddTracksSuccess implements Action {
    readonly type: string = AnalyticsActionTypes.ADD_TRACKS_SUCCESS;
    constructor() { }
}

export class FetchFeaturesList implements Action {
    readonly type: string = AnalyticsActionTypes.FETCH_FEATURES_LIST;
    constructor() { }
}
export class FetchFeaturesListSuccess implements Action {
    readonly type: string = AnalyticsActionTypes.FETCH_FEATURES_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}
