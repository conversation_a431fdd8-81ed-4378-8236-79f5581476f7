import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'information',
  templateUrl: './information.component.html',
})
export class InformationComponent implements OnInit {
  searchTerm = '';
  data: any;
  isListOpen: boolean = false;

  highlightedData: { key: SafeHtml; value: SafeHtml; isOpen?: boolean }[] = [];

  constructor(
    private sanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef,
    public modalRef: BsModalRef
  ) { }

  ngOnInit(): void {
    this.highlightedData = this.data.map((info: any) => ({
      key: this.sanitizer.bypassSecurityTrustHtml(info.key),
      value: this.sanitizer.bypassSecurityTrustHtml(info.value),
    }));
  }

  highlightText() {
    if (!this.searchTerm) {
      this.highlightedData = this.data.map((info: any) => ({
        key: this.sanitizer.bypassSecurityTrustHtml(info.key),
        value: this.sanitizer.bypassSecurityTrustHtml(info.value),
      }));
      return;
    }

    const regex = new RegExp(`(${this.searchTerm})`, 'gi');
    this.highlightedData = this.data.map((info: any) => ({
      key: this.sanitizer.bypassSecurityTrustHtml(
        info.key.replace(regex, `<span class="bg-yellow">$1</span>`)
      ),
      value: this.sanitizer.bypassSecurityTrustHtml(
        info.value.replace(regex, `<span class="bg-yellow">$1</span>`)
      ),
    }));

    this.cdr.detectChanges();
  }
}
