<div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-16 py-12 text-white">
        <h4>Bulk WhatsApp</h4>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <form [formGroup]="shareDetailForm" class="px-12">
        <!-- <div class="field-label mb-10">{{'LEADS.selected-lead' | translate}}</div>
        <div class="flex-column">
            max-h-100-176 
            <ng-container *ngFor="let lead of data">
                <div class="p-12 fw-600 text-sm border-bottom text-secondary bg-white flex-center">
                    <div class="w-50 text-truncate">{{ lead.name }}</div>
                    <div class="w-50 text-truncate ml-6">{{ lead.contactNo }}</div>
                </div>
            </ng-container>
        </div> -->
        <h5 class="field-label-req">{{ 'GLOBAL.select' | translate }} Campaign</h5>
        <form-errors-wrapper [control]="shareDetailForm.controls['campaignName']" label="campaign">
            <ng-select [virtualScroll]="true" [items]="campaigns" [closeOnSelect]="true" ResizableDropdown
                [ngClass]="{'blinking pe-none': isCampaignListLoading}" [addTag]="true" addTagText="Create New Campaign"
                formControlName="campaignName" placeholder="{{ 'GLOBAL.select' | translate }}/Create Campaign">
            </ng-select>
        </form-errors-wrapper>
        <h5 class="field-label-req">{{ 'GLOBAL.select' | translate }} {{ 'BULK_LEAD.template' | translate }}</h5>
        <form-errors-wrapper [control]="shareDetailForm.controls['template']" label="Template">
            <ng-select [virtualScroll]="true" [items]="templates" bindLabel="name" formControlName="template"
                (change)="trackingService.trackFeature('Web.Leads.Options.Selecttemplate.Click')" ResizableDropdown
                placeholder="{{ 'GLOBAL.select' | translate }} {{'BULK_LEAD.template' | translate}}"></ng-select>
        </form-errors-wrapper>
        <ng-container *ngIf="shareDetailForm.controls['template']?.value?.message?.length">
            <h5 class="field-label">Message</h5>
            <div class="form-group no-validation">
                <textarea class="form-control scrollbar border-0" [rows]="4" [disabled]="true"
                    [value]="getMessage()"></textarea>
            </div>
        </ng-container>

        <h5 class="field-label-req">Test Contact No</h5>
        <form-errors-wrapper [control]="shareDetailForm.controls['testContactNo']" label="Contact No">
            <ngx-mat-intl-tel-input #contactNoInput [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                [enableSearch]="true" formControlName="testContactNo" placeholder="ex. 9133XXXXXX"
                class="no-validation contactNoInput">
            </ngx-mat-intl-tel-input>
        </form-errors-wrapper>
        <div class="position-relative"
            *ngIf="shareDetailForm.controls['template']?.value?.whatsAppHeaderTypes == 1 && shareDetailForm.controls['template']?.value?.headerValuesCount > 0">
            <div
                [ngClass]="(shareDetailForm.controls['template']?.value?.whatsAppHeaderTypes == 1 && shareDetailForm.controls['template']?.value?.headerValuesCount > 0) ? 'field-label-req' : 'field-label'">
                Head Variable</div>
            <form-errors-wrapper [control]="shareDetailForm.controls['headerVariable']" label="Header Variable">
                <input type="text" placeholder="ex. Text" formControlName="headerVariable"
                    [required]="(shareDetailForm.controls['template']?.value?.whatsAppHeaderTypes == 1 && shareDetailForm.controls['template']?.value?.headerValuesCount > 0)">
            </form-errors-wrapper>
        </div>
        <div class="position-relative" *ngIf="shareDetailForm.controls['template']?.value && bodyValuesCount">
            <div class="field-label-req">Body Variable(s) -
                {{shareDetailForm.controls['template']?.value?.bodyValuesCount}}
                <span class="ml-4 text-sm">(with comma separated)</span>
            </div>
            <form-errors-wrapper [control]="shareDetailForm.controls['bodyVariables']" label="Body Variable(s)">
                <input type="text" placeholder="ex. name, time, company name" formControlName="bodyVariables">
            </form-errors-wrapper>
            <div class="error-message" *ngIf="!isBodyVarCountValid && shareDetailForm.controls['bodyVariables']?.value">
                Body Variable(s) should be {{shareDetailForm.controls['template']?.value?.bodyValuesCount}}</div>
        </div>
        <h5 [ngClass]="fileNameReq ? 'field-label-req' : 'field-label'">File Name</h5>
        <form-errors-wrapper [control]="shareDetailForm.controls['fileName']" label="File Name">
            <input type="text" placeholder="File Name" formControlName="fileName">
            <!-- (focus)="trackingService.trackFeature('Web.Leads.Dataentry.FileName.Click')" -->
        </form-errors-wrapper>
        <div class="flex-center mt-20">
            <button class="btn-gray mr-20"
                (click)="modalService.hide(); trackingService.trackFeature('Web.Leads.Button.cancel.Click')">Cancel</button>
            <button class="btn-coal" (click)="updateBulkShare(ConfirmationModal)" *ngIf="!testTemplateSent">Send
                Test</button>
            <button class="btn-coal" (click)="updateBulkShare(ConfirmationModal)" *ngIf="testTemplateSent">Send
                Bulk</button>
        </div>
    </form>
</div>

<ng-template #ConfirmationModal>
    <div class="px-40 py-30 flex-center-col">
        <a class="ic-close-secondary ic-close-modal ip-ic-close-modal" (click)="modalRef.hide()"></a>
        <h5 class="fw-600 text-center">Did you Receive Message?</h5>
        <div class="flex-center mt-24">
            <button class="btn-gray mr-20" (click)="modalRef.hide(); ">{{ 'GLOBAL.no' | translate}}</button>
            <button class="btn-coal" (click)="testTemplateSent = true;modalRef.hide();">{{ 'GLOBAL.yes' |
                translate}}</button>
        </div>
    </div>
</ng-template>