import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { OwlDateTimeModule } from '@danielmoncada/angular-datetime-picker';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { AgGridModule } from 'ag-grid-angular';
import { ModalModule } from 'ngx-bootstrap/modal';
import { DragScrollModule } from 'ngx-drag-scroll';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';

import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { FormErrorModule } from 'src/app/shared/components/form-error-wrapper/form-error.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { GlobalConfigRoutingModule } from './global-config-routing.module';

export const GLOBAL_CONFIG_IMPORTS = [
  FormsModule,
  ReactiveFormsModule,
  FormErrorModule,
  HttpClientModule,
  SharedModule,
  AgGridModule,
  DragScrollModule,
  FormsModule,
  OwlDateTimeModule,
  NgxMatIntlTelInputComponent,
  ModalModule.forRoot(),
  TranslateModule.forChild({
    loader: {
      provide: TranslateLoader,
      useFactory: HttpLoaderFactory,
      deps: [HttpClient],
    },
  }),
  GlobalConfigRoutingModule,
  CommonModule,
  LottieModule.forRoot({ player: playerFactory }),
];
