import {
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  filter,
  Observable,
  of,
  takeUntil,
} from 'rxjs';

import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { CanComponentDeactivate } from 'src/app/core/guards/forms.guard';
import { Address, Profile } from 'src/app/core/interfaces/profile.interface';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import {
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  getDeletePermissions,
  getEditPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import { FetchPlacesList } from 'src/app/reducers/places/places.actions';
import { UpdateProfile } from 'src/app/reducers/profile/profile.actions';
import { getProfile, getProfileIsLoading } from 'src/app/reducers/profile/profile.reducers';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'basic-details',
  templateUrl: './basic-details.component.html',
})
export class BasicDetailsComponent
  implements OnInit, CanComponentDeactivate, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('saveDataModal') saveDataModal: TemplateRef<any>;
  canRedirect: boolean = false;
  isFileTypeSupported: boolean = true;
  isBasicInfoEditable: boolean = false;
  isProjectInfoEditable: boolean = false;
  isBrochureEdited: boolean = false;
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  placesList: any[] = [];
  basicDetailsForm: FormGroup;
  profile: Profile;
  selectedFile: any;
  brochurePath: any;
  fileFormatToBeUploaded: string = 'application/pdf';
  selectedFileName: string = '';
  s3ImageBucketURL: string = environment.s3ImageBucketURL;
  canEditProfile: boolean = false;
  canDelete: boolean = false;
  preferredCountries = ['in'];
  hasInternationalSupport: boolean = false;
  @ViewChild('contactNoInput') contactNoInput: any;
  isGloabalSettingsLoading: boolean;
  isProfileLoading: boolean;

  constructor(
    private fb: FormBuilder,
    private _store: Store<AppState>,
    private s3UploadService: BlobStorageService,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) {
    this._store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit.includes('OrgProfile')) this.canEditProfile = true;
      });

    this._store
      .select(getDeletePermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canDelete: any) => {
        if (canDelete.includes('OrgProfile')) this.canDelete = true;
      });

    this.basicDetailsForm = this.fb.group({
      name: ['', Validators.required],
      reraNo: ['', Validators.required],
      phoneNo: ['', [this.contactNumberValidator(true)]],
      email: ['', [Validators.required, Validators.email]],
      website: [''],
      gstNumber: [''],
      addressPlaceId: ['', Validators.required],
      soldUnits: [null],
      soldProperties: [null],
      projectsDeveloped: [null],
      propertiesDeveloped: [null],
      experience: [null],
      brochure: [''],
    });
    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchPlacesList(searchStr));
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = data?.hasInternationalSupport
          ? data.countries.length
            ? [data.countries[0].code.toLowerCase()]
            : ['in']
          : ['in'];
      });
    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.isGloabalSettingsLoading = data));
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  contactNumberValidator(isPrimaryNo: boolean = false): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      if (isPrimaryNo) {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          this.contactNoInput?.value || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  canDeactivate(): Observable<boolean> {
    if (
      this.basicDetailsForm.dirty &&
      (this.isBasicInfoEditable || this.isProjectInfoEditable)
    ) {
      this.modalRef = this.modalService.show(
        this.saveDataModal,
        Object.assign(
          {},
          {
            class: 'modal-350 modal-dialog-centered',
          }
        )
      );
      return this.modalService.onHide;
    }
    return of(true);
  }

  ngOnInit() {
    this._store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.profile = data;
        this.patchValues(this.profile);
      });

    this._store
      .select(getProfileIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProfileLoading = isLoading;
      });

    this._store
      .select((state) => state.places)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        return (this.placesList = data?.placesList || []);
      });
  }

  patchValues(profile: Profile) {
    this.basicDetailsForm.patchValue({
      name: profile.displayName,
      reraNo: profile.reraNumber,
      phoneNo: profile.phoneNumber,
      email: profile.email,
      addressPlaceId: profile.address?.subLocality
        ? `${profile.address.subLocality}, ${this.profile.address?.city}`
        : profile.address?.city || null,
      website: profile.website,
      gstNumber: profile.gstNumber,
      experience: profile.yearsOfExperience,
      soldUnits: profile.totalUnitsSold,
      soldProperties: profile.totalPropertiesSold,
      projectsDeveloped: profile.projectsDeveloped,
      propertiesDeveloped: profile.propertiesDeveloped,
    });
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.uploadToS3Bucket();
  }

  deleteBrochure() {
    this.selectedFile = '';
    this.brochurePath = '';
    this.profile = {
      ...this.profile,
      brochurePath: '',
    };
    this.isBrochureEdited = true;
  }

  checkFileFormat(fileName: string) {
    const allowedFiles = ['pdf'];
    const regex = /(?:\.([^.]+))?$/;
    const extension = regex.exec(fileName);
    this.isFileTypeSupported = allowedFiles.includes(extension[1]);
    return this.isFileTypeSupported;
  }

  uploadToS3Bucket() {
    const filesToBeUploadToS3Bucket = this.selectedFile.filter(
      (filePath: string) => filePath.includes('data:')
    );
    if (filesToBeUploadToS3Bucket.length) {
      //upload to s3
      this.s3UploadService
        .uploadDocBase64(filesToBeUploadToS3Bucket, FolderNamesS3.OrgBrochure)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          if (response.data.length) {
            this.brochurePath = response.data?.[0];
          }
        });
    }
  }

  onSave() {
    if (!this.basicDetailsForm.valid) {
      validateAllFormFields(this.basicDetailsForm);
      return;
    }
    let address: Address;
    let basicDetailsPayload: Profile = {
      ...this.profile,
      displayName: this.basicDetailsForm.value.name,
      reraNumber: this.basicDetailsForm.value.reraNo,
      phoneNumber: this.basicDetailsForm.value.phoneNo,
      email: this.basicDetailsForm.value.email,
      website: this.basicDetailsForm.value.website,
      gstNumber: this.basicDetailsForm.value.gstNumber,
      address: {
        ...address,
        id: this.profile.address?.id,
        placeId: this.basicDetailsForm.value.addressPlaceId,
      },
      brochurePath: this.profile?.brochurePath || '',
      yearsOfExperience: this.basicDetailsForm.value.experience,
      totalUnitsSold: this.basicDetailsForm.value.soldUnits,
      projectsDeveloped: this.basicDetailsForm.value.projectsDeveloped,
      totalPropertiesSold: this.basicDetailsForm.value.soldProperties,
      propertiesDeveloped: this.basicDetailsForm.value.propertiesDeveloped,
    };

    if (
      `${this.profile?.address?.city}` ===
      this.basicDetailsForm.value.addressPlaceId ||
      `${this.profile.address?.subLocality}, ${this.profile.address?.city}` ===
      this.basicDetailsForm.value.addressPlaceId
    ) {
      basicDetailsPayload.address.placeId = this.profile.address?.placeId;
    }
    if (this.selectedFile?.length) {
      if (this.brochurePath?.length) {
        basicDetailsPayload.brochurePath = this.brochurePath;
        if (
          `${this.profile.address?.subLocality}, ${this.profile.address?.locality}, ${this.profile.address?.state}` ===
          basicDetailsPayload.address.placeId
        ) {
          basicDetailsPayload.address.placeId = this.profile.address?.placeId;
        }
        this._store.dispatch(new UpdateProfile(basicDetailsPayload));
        this.isBrochureEdited = false;
        this.selectedFile = '';
      }
    } else {
      if (
        `${this.profile.address?.subLocality}, ${this.profile.address?.locality}, ${this.profile.address?.state}` ===
        basicDetailsPayload.address.placeId
      ) {
        basicDetailsPayload.address.placeId = this.profile.address?.placeId;
      }
      this.isBasicInfoEditable = false;
      this.isProjectInfoEditable = false;
      this.isBrochureEdited = false;
      this.selectedFile = '';
      this._store.dispatch(new UpdateProfile(basicDetailsPayload));
    }
  }

  saveInModal() {
    this.onSave();
    this.discardInModal();
  }

  discardInModal() {
    this.canRedirect = true;
    this.modalRef.hide();
  }

  closeModal() {
    this.canRedirect = false;
    this.modalRef.hide();
  }

  cancelChanges() {
    this.patchValues(this.profile);
    this.isBasicInfoEditable = false;
    this.isProjectInfoEditable = false;
    this.selectedFile = '';
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
