<!-- <ng-container *ngIf="!(!userData?.userId && userId) else underConstruction"> -->
<simple-notifications [options]="options"></simple-notifications>
<!-- <ngw-loader></ngw-loader> -->
<div class="main-container overflow-hidden w-100" *ngIf="showAppLayout; else noAppLayout">
  <h5 *ngIf="!isOnline" class="position-absolute left-0 top-45 bg-warning text-nowrap w-100 flex-center z-index-1021">
    <ng-lottie [options]="warn" width="30px" height="30px" class="mr-4"></ng-lottie>
    No Internet Connection. Please check your network.
  </h5>

  <left-nav></left-nav>
  <div class="app-container">
    <header></header>
    <div class="flex-grow-1 h-100-46 overflow-auto scroll-hide w-100">
      <div (click)="openSupport()" cdkDrag [cdkDragFreeDrag]="false" [cdkDragLockAxis]="'y'"
        [cdkDragBoundary]="boundaryElement" class="draggable-right">
        <span class="ic-support icon"></span>
      </div>
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
<ng-template #noAppLayout>
  <div class="h-100vh w-100">
    <router-outlet></router-outlet>
  </div>
</ng-template>
<!-- </ng-container> -->
<!-- <ng-template #underConstruction>
  <under-construction></under-construction>
</ng-template> -->