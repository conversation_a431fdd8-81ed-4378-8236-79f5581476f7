import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
    AnalyticsActionTypes,
    FetchActionsListSuccess,
    FetchFeaturesListSuccess,
} from './analytics.actions';

export type AnalyticsState = {
    featuresList: any;
    actionsList: any;
};

const initialState: AnalyticsState = {
    actionsList: {},
    featuresList: {},
};

export function AnalyticsReducer(
    state: AnalyticsState = initialState,
    action: Action
): AnalyticsState {
    switch (action.type) {
        case AnalyticsActionTypes.FETCH_ACTIONS_LIST_SUCCESS:
            return {
                ...state,
                actionsList: (action as FetchActionsListSuccess).response,
            };

        case AnalyticsActionTypes.FETCH_FEATURES_LIST_SUCCESS:
            return {
                ...state,
                featuresList: (action as FetchFeaturesListSuccess).response,
            };

        default:
            return state;
    }
}
export const selectFeature = (state: AppState) => state.analytics;

export const getActionsList = createSelector(
    selectFeature,
    (state: AnalyticsState) => state.actionsList
);

export const getFeaturesList = createSelector(
    selectFeature,
    (state: AnalyticsState) => state.featuresList
);
