<div class="align-center p-16">
    <div class="on-hover position-relative mr-10">
        <img class="br-4 obj-cover border" [appImage]="userData?.imageUrl ? s3BucketUrl+userData.imageUrl : ''"
            [type]="'squareAvatar'" alt="profile pic" width="85" height="80">
        <div
            class="bg-white w-120 position-absolute nbottom-80 nleft-10 fw-semi-bold text-large text-nowrap br-4 z-index-2 text-black-200 box-shadow-10 cursor-pointer on-hover-show">
            <span class="icon ic-triangle-up  position-absolute left-40 ntop-10"></span>
            <label class="px-16 py-12 cursor-pointer"><input type="file" (change)="onFileSelected($event)"
                    [accept]="'image/x-png,image/gif,image/jpeg,image/tiff'">{{'USER.replace-image' | translate}}
            </label>
            <div class="px-16 py-12 cursor-pointer" (click)="removeProfile()">{{'USER.remove-image' | translate}}</div>
        </div>
    </div>
    <div>
        <h3 class="fw-700 text-coal mb-4 text-truncate-1 break-all">{{userData.firstName}} {{userData.lastName}}</h3>
        <span class="br-12 text-white px-8 py-2"
            [ngClass]="userData?.isActive ? 'border-light-green-500 bg-light-green-400' : 'bg-red-350 border-red-30'">
            {{((userData?.isActive ? 'LABEL.active' : 'LABEL.inactive') | translate)?.toLowerCase()}}</span>
        <div class="align-center mt-10">
            <button class="btn-gray mr-6" (click)="modalRef.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" (click)="onSave()">{{ 'BUTTONS.save' | translate }}</button>
        </div>
    </div>
</div>
<div class="border-bottom"></div>
<form [formGroup]="basicInfoForm" autocomplete="off">
    <div class="scrollbar h-100-114">
        <div class="mt-16 fw-semi-bold">
            <div class="cursor-pointer align-center gap-3 ml-20" (click)="showBasicDetails = !showBasicDetails">
                <span class="ic-triangle-down icon ic-coal ic-xxxs"
                    [ngClass]="{ 'rotate-270' : !showBasicDetails}"></span>
                <h5 class="fw-700">{{ 'USER.basic-details' | translate }}</h5>
            </div>
            <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
            <div class="ml-24 mr-10" *ngIf="showBasicDetails">
                <div class="mt-10 align-center">
                    <span class="ic-circle-user ic-light-gray ic-xxs mr-12"></span>
                    <span class="text-sm text-dark-gray mr-4">{{ 'AUTH.user-name' | translate }}:</span>
                    <span class="fw-600">{{userData?.userName}}</span>
                </div>
                <div class="mt-12 align-center">
                    <span class="ic-apartment ic-light-gray ic-xxs mr-12"></span>
                    <span class="text-sm text-dark-gray mr-4 text-nowrap">{{ 'AUTH.company-name' | translate }}:</span>
                    <span class="fw-600 text-truncate-1 break-all">{{userData?.officeName
                        ? userData?.officeName : '--'}}</span>
                </div>
                <div class="mt-12 align-center">
                    <span class="ic-suitcase-solid ic-light-gray ic-xxs mr-12"></span>
                    <span class="text-sm text-dark-gray mr-4">{{ 'USER_MANAGEMENT.designation' | translate }}:</span>
                    <span class="fw-600">{{userData?.designation?.name ? userData?.designation?.name : '--'}}</span>
                </div>
                <div class="mt-12 align-center">
                    <span class="ic-suitcase-solid ic-light-gray ic-xxs mr-12"></span>
                    <span class="text-sm text-dark-gray mr-4">{{ 'USER_MANAGEMENT.department' | translate }}:</span>
                    <span class="fw-600">{{userData?.department?.name ? userData?.department?.name : '--'}}</span>
                </div>
                <div class="mt-12 align-center">
                    <span class="ic-phone-ring-solid ic-light-gray ic-xxs mr-12"></span>
                    <span class="text-sm text-dark-gray">{{'GLOBAL.work' | translate}} {{'PROPERTY.OWNER_INFO.phone' |
                        translate
                        }}:</span>
                    <a [href]="'tel:'+userData?.phoneNumber" class="text-xs mx-4 px-6 py-2 border bg-light-pearl br-12"
                        *ngIf="userData?.phoneNumber">{{userData?.phoneNumber}}</a>
                </div>
                <div class="mt-12 align-center flex-wrap">
                    <div class="ic-envelope-solid ic-light-gray ic-xxs mr-12"></div>
                    <div class="text-sm text-dark-gray">{{'GLOBAL.work' | translate}} {{ 'SHARE.email' | translate }}:
                    </div>
                    <a [href]="'mailto:'+userData?.email" class="text-xs mx-4 px-6 py-2 border bg-light-pearl br-12"
                        *ngIf="userData?.email">
                        {{userData?.email}} </a>
                </div>
                <div class="mt-12 align-center">
                    <span class="ic-user-tie ic-light-gray ic-xxs mr-12"></span>
                    <span class="text-sm text-dark-gray mr-4">{{ 'USER_MANAGEMENT.reporting-to' | translate }}:</span>
                    <span class="fw-600">{{userData?.reportsTo?.name ? userData?.reportsTo?.name : '--'}}</span>
                </div>
                <div class="mt-12 w-100 align-center input-sm">
                    <div class="w-30 d-flex">
                        <span class="ic-phone-ring-solid ic-light-gray ic-xxs mr-12"></span>
                        <span class="text-sm text-dark-gray text-nowrap">{{'GLOBAL.personal' | translate}}
                            {{'PROPERTY.OWNER_INFO.phone' | translate }}:</span>
                    </div>
                    <div class="w-70">
                        <form-errors-wrapper [control]="basicInfoForm.controls['altPhoneNumber']">
                            <input formControlName="altPhoneNumber" type="number" id="inpPersPhone"
                                data-automate-id="inpPersPhone" placeholder='ex. 9133XXXXXX' class="ph-mx-4">
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="w-100 mt-12 align-center flex-wrap input-sm">
                    <div class="d-flex w-30">
                        <div class="ic-envelope-solid ic-light-gray ic-xxs mr-12"></div>
                        <div class="text-sm text-dark-gray text-nowrap">{{'GLOBAL.personal' | translate}}
                            {{'SHARE.email' | translate }}:
                        </div>
                    </div>
                    <div class="w-70 mt-8">
                        <form-errors-wrapper [control]="basicInfoForm.controls['altEmail']">
                            <input formControlName="altEmail" type="email" id="inpPerEmail"
                                data-automate-id="inpPerEmail" placeholder="ex. <EMAIL>"
                                class="ph-mx-4">
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="org-profile mt-12 w-100 align-center">
                    <div class="w-30 align-center">
                        <span class="ic-dna-solid ic-light-gray ic-xxs mr-12"></span>
                        <span class="text-sm text-dark-gray mr-4 text-nowrap">
                            {{ 'GLOBAL.blood-group' | translate }}:</span>
                    </div>
                    <div class="w-70 ng-select-sm mt-8">
                        <ng-select [virtualScroll]="true" formControlName="bloodGroup" placeholder="ex. o+"
                            class="ph-mx-4">
                            <ng-option *ngFor="let group of bloodGroup" [value]="group.id">{{group.name}}
                            </ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="w-100 flex-end mt-12 pr-4"
                    [ngClass]="basicInfoForm.controls['timeZone'].value ? '' : 'pe-none disabled'">
                    <label class="checkbox-container mb-4">
                        <div class="d-flex align-end">
                            <input type="checkbox" formControlName="shouldShowTimeZone">
                            <span class="checkmark"></span>Show Time Zone
                        </div>
                    </label>
                </div>
                <div class="org-profile w-100 align-center">
                    <div class="w-30 align-center">
                        <span class="ic-clock-list ic-light-gray ic-xxs mr-12"></span>
                        <span class="text-sm text-dark-gray mr-4 text-nowrap">Time Zone:</span>
                    </div>
                    <div class="w-70 ng-select-sm">
                        <ng-select [virtualScroll]="true" placeholder="ex. India" class="ph-mx-4"
                            formControlName="timeZone">
                            <ng-option *ngFor="let time of timeZoneList" [value]="time?.displayName">
                                <div class="flex-between">
                                    <div class="text-truncate-1 break-all">{{time?.displayName}}</div>
                                    <i class="text-disabled gray">({{time?.ianaZoneId}})</i>
                                </div>
                            </ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="round-radio w-100 align-center mt-12">
                    <div class="w-30 d-flex">
                        <span class="icon ic-venus-mars ic-light-gray ic-xxs mr-12"></span>
                        <span class="text-sm text-dark-gray mr-4">{{ 'GLOBAL.gender' | translate }}:</span>
                    </div>
                    <div class="align-center">
                        <ng-container *ngFor="let gender of genderList">
                            <div>
                                <input type="radio" class="btn-check" name="gender" [id]="gender.type"
                                    formControlName="gender" [value]="gender.id">
                                <label class="btn btn-outline-secondary mr-30 position-relative" [for]="gender.type"
                                    [ngClass]="gender.bgColor">
                                    <img [type]="'leadrat'" [appImage]="gender.image" alt="Gender Type">
                                    <div class="text-label">{{gender.type}}</div>
                                </label>
                            </div>
                        </ng-container>
                    </div>
                </div>
                <!-- app Tour -->
                <!-- <div class="w-100 align-center mt-30">
                    <div class="w-30 d-flex">
                        <span class="icon ic-globe ic-light-gray ic-xxs mr-12"></span>
                        <span class="text-sm text-dark-gray mr-4">App Tour :</span>
                    </div>
                    <div class="align-center position-relative">
                        <div class="text-xs mr-8">{{ isAppTourEnabled ? 'On' : 'Off' }}</div>
                        <input type="checkbox" id="chkToggle" name="darkTheme" class="toggle-switch toggle-active-sold"
                            (change)="ToggleAppTour($event)" [checked]="isAppTourEnabled" />
                        <label for="chkToggle" class="switch-label"></label>
                    </div>
                </div> -->
            </div>
        </div>
        <div class="mt-24 cursor-pointer align-center gap-3 ml-20" (click)="showLocation = !showLocation">
            <span class="ic-triangle-down icon ic-coal ic-xxxs" [ngClass]="{ 'rotate-270' : !showLocation}"></span>
            <h5 class="fw-700">{{'USER.location-info' | translate}}</h5>
        </div>
        <div class="border-bottom-slate-20 mt-10 ml-40 mr-20"></div>
        <div class="ml-24 mr-10 mb-28" *ngIf="showLocation">
            <div class="mt-10">
                <span class="icon ic-location-circle ic-light-gray ic-xxs mr-12"></span>
                <span class="fw-semi-bold text-sm text-dark-gray">{{'GLOBAL.current' | translate }} {{'PROJECTS.address'
                    |
                    translate}}:</span>
                <div class="form-group ml-20 mt-6">
                    <textarea name="currentAddress" id="txtCurrentAddress" data-automate-id="txtCurrentAddress"
                        formControlName="address" class="scrollbar fw-semi-bold text-sm text-coal"
                        placeholder="ex. 1585/1 level, 2, 20th Main Rd, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102"
                        rows="3"></textarea>
                </div>
            </div>
            <div class="mt-16">
                <span class="icon ic-location-map ic-light-gray ic-xxs mr-12"></span>
                <span class="fw-semi-bold text-sm text-dark-gray">{{'PROJECTS.permanent' | translate}}
                    {{'PROJECTS.address' | translate}}:</span>
                <div class="form-group ml-20 mt-6">
                    <textarea rows="3" name="permanentAddress" id="txtPermanentAddress"
                        data-automate-id="txtPermanentAddress" formControlName="permanentAddress"
                        class="scrollbar fw-semi-bold text-sm text-coal"
                        placeholder="ex. 1585/1 level, 2, 20th Main Rd, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102"></textarea>
                </div>
            </div>
            <div class="mt-16">
                <span class="icon ic-mail-location ic-light-gray ic-xxs mr-12"></span>
                <span class="fw-semi-bold text-sm text-dark-gray">{{'USER_MANAGEMENT.office' | translate}}
                    {{'PROJECTS.address' | translate}}:</span>
                <div class="fw-600 text-normal mt-6 ml-20 text-truncate-3">{{userData?.officeAddress ?
                    userData?.officeAddress : '--'}}</div>
            </div>
        </div>
    </div>
</form>