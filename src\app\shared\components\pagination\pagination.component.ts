import {
  Component,
  OnInit,
  OnChanges,
  Input,
  Output,
  EventEmitter,
  AfterViewInit,
  ElementRef,
  ViewChild,
  Renderer2,
  TemplateRef,
  HostListener,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Observable, range } from 'rxjs';
import { toArray, map, filter } from 'rxjs/operators';

@Component({
  selector: 'pagination',
  templateUrl: './pagination.component.html',
})
export class PaginationComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() offset: number;
  @Input() limit = 1;
  @Input() size: number;
  @Input() range = 3;
  @Input() isV2Pagination: boolean = false;
  @Output() pageChange: EventEmitter<number> = new EventEmitter<number>();
  @ViewChild('searchInput') inputField: ElementRef;
  @ViewChild('pageNotFoundTemplate') pageNotFoundTemplate: TemplateRef<any>;
  pages: Observable<number[]>;
  currentPage: number;
  totalPages: number;
  inputPageNumber: string;
  isShowSearchIcons: boolean = true;
  pageNotFoundMessage: string;
  modalRef: BsModalRef;
  showsearchIcon: boolean = true;

  constructor(
    private renderer: Renderer2,
    private modalService: BsModalService
  ) { }

  ngOnInit() {
    this.getPages(this.offset, this.limit, this.size);
  }

  ngOnChanges() {
    this.getPages(this.offset, this.limit, this.size);
  }

  getPages(offset: number, limit: number, size: number) {
    this.currentPage = this.getCurrentPage(offset, limit);
    this.totalPages = this.getTotalPages(limit, size);
    this.pages = range(-this.range, this.range * 2 + 1).pipe(
      map((offsetData) => this.currentPage + offsetData),
      filter((page) => this.isValidPageNumber(page, this.totalPages)),
      toArray()
    );
  }

  isValidPageNumber(page: number, totalPages: number): boolean {
    return page > 0 && page <= totalPages;
  }

  getCurrentPage(offset: number, limit: number): number {
    return Math.floor(offset / limit) + 1;
  }

  getTotalPages(limit: number, size: number): number {
    return Math.ceil(Math.max(size, 1) / Math.max(limit, 1));
  }

  selectPage(page: number, event: any) {
    this.cancelEvent(event);
    if (this.isValidPageNumber(page, this.totalPages)) {
      this.pageChange.emit((page - 1) * this.limit);
    }
  }

  cancelEvent(event: any) {
    event.preventDefault();
  }

  goToPage(event: Event) {
    if (this.getTotalPages(this.limit, this.size) < +this.inputPageNumber) {
      this.pageNotFoundPopup(
        `Page number exceeds total pages (${this.totalPages}).`
      );
    }
    this.isShowSearchIcons = true;
    this.cancelEvent(event);
    if (this.isValidPageNumber(+this.inputPageNumber, this.totalPages)) {
      this.pageChange.emit((+this.inputPageNumber - 1) * this.limit);
    }
    this.inputPageNumber = '';
  }

  pageNotFoundPopup(message: string) {
    this.pageNotFoundMessage = message;
    this.modalRef = this.modalService.show(this.pageNotFoundTemplate, {
      class: 'modal-350 top-modal',
    });
  }

  toggleSearch() {
    this.isShowSearchIcons = false;
    setTimeout(() => {
      if (this.inputField && this.inputField.nativeElement) {
        this.renderer.selectRootElement(this.inputField.nativeElement).focus();
      }
    });
  }

  @HostListener('document:click', ['$event'])
  handleClick(event: Event) {
    const target = event.target as HTMLElement;
    if (target.closest('.ic-search') || target.closest('.page-search')) {
      return;
    }

    if (!this.isShowSearchIcons) {
      this.isShowSearchIcons = true;
    }
  }

  ngAfterViewInit() {
    if (!this.isShowSearchIcons) {
      this.renderer.selectRootElement(this.inputField.nativeElement).focus();
    }
  }
}
