import {
  Component,
  EventEmitter,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import {
  MONTHS,
  PAGE_SIZE,
  POSSESSION_DATE_FILTER_LIST,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import { PossessionType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getISODateFormat,
  getPages,
  onlyNumbers,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import {
  AddProjectBlocks,
  FetchBlockById,
  UpdateProjectBlock,
} from 'src/app/reducers/project/project.action';
import { getBlockData } from 'src/app/reducers/project/project.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'add-block-info',
  templateUrl: './add-block-info.component.html',
})
export class AddBlockInfoComponent implements OnInit {
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;
  gridApi: GridApi;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  basicInfoForm: FormGroup;
  areaSizeUnits: Array<any>;
  blockData: any;
  totalCount: number;
  currOffset: number = 0;
  pageSize: number = PAGE_SIZE;
  getPages = getPages;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: 10,
  };
  selectedNodes: any;
  onlyNumbers = onlyNumbers;
  selectedBlockData: any;
  selectedDataId: any;
  isOpenPossessionModal: boolean = false;
  noBlocks: boolean = false;
  rowData: any = [];
  blockInfo = {
    name: '',
    area: '',
    startdate: '',
    enddate: '',
    possessiondate: '',
  };
  isClosePossessionModal: boolean = true;
  dateFilterList = POSSESSION_DATE_FILTER_LIST;
  selectedMonthAndYear: any;
  currentMonth: Date = new Date();
  selectedMonth: any;
  selectedYear: any;
  isPresentMonth: boolean;
  isValidPossessonDate: boolean = false;
  selectedPossession: any;
  possessionType: any = PossessionType;
  userBasicDetails: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened
  globalSettingsDetails: any;

  constructor(
    private router: Router,
    private store: Store<AppState>,
    private sharedDataService: ShareDataService,
    private modalService: BsModalService,
    private fb: FormBuilder
  ) {
    this.basicInfoForm = this.fb.group({
      title: [null, Validators.required],
      area: [null],
      areaUnit: [null],
      startDate: [null],
      endDate: [null],
      possessionDate: [null],
      noOfFloors: 0,
      globalRange: [null],
      globalDate: [null],
      monthAndYear: [null],
      possesionType: [null],
    });
  }

  ngOnInit(): void {
    this.basicInfoForm.controls['area'].valueChanges.subscribe((value) => {
      if (value) {
        toggleValidation(VALIDATION_SET, this.basicInfoForm, 'areaUnit', [
          Validators.required,
        ]);
      } else {
        this.basicInfoForm.controls['areaUnit'].setValue(null);
        toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'areaUnit');
      }
    });

    this.basicInfoForm.controls['globalRange'].valueChanges.subscribe(
      (value) => {
        if (value !== 'Custom Date') {
          this.selectedPossession = null;
          this.isValidPossessonDate = false;
        } else {
          this.convertDateToMonth(this.selectedBlockData?.possessionDate);
        }
        this.basicInfoForm.controls['possesionType'].setValue(
          PossessionType[value]
        );
      }
    );
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data) this.globalSettingsDetails = data
        const currentValues = this.basicInfoForm.value;
        this.basicInfoForm.patchValue({
          areaUnit: currentValues.areaUnit || data?.defaultValues?.masterAreaUnit,
        });
      });

    if (this.selectedBlockData) {
      this.basicInfoForm.patchValue({
        title: this.selectedBlockData.name,
        area: this.selectedBlockData.area,
        areaUnit: this.selectedBlockData.areaUnitId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        startDate: patchTimeZoneDate(this.selectedBlockData?.startDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
        endDate: patchTimeZoneDate(this.selectedBlockData?.endDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
        possessionDate: this.convertDateToMonth(
          this.selectedBlockData?.possessionDate
        ),
        noOfFloors: this.selectedBlockData.numberOfFloors,
        globalRange:
          this.selectedBlockData?.possesionType === 0 &&
            this.selectedBlockData?.possessionDate
            ? PossessionType[5]
            : this.selectedBlockData?.possesionType === 0
              ? ''
              : PossessionType[this.selectedBlockData?.possesionType],
      });
    }

    this.store.select(getBlockData).subscribe((data) => {
      this.rowData = data[0]?.items ? data[0].items : [];
      this.totalCount = data[0]?.totalCount;
      this.pageSize = this.filtersPayload?.pageSize;
      this.currOffset = this.filtersPayload?.pageNumber - 1;
      if (
        data.length &&
        data[0]?.items.length === 0 &&
        this.filtersPayload.pageNumber > 1
      ) {
        this.filtersPayload = {
          ...this.filtersPayload,
          pageNumber: this.filtersPayload.pageNumber - 1,
        };
        this.store.dispatch(new FetchBlockById(this.filtersPayload));
      }
    });

    this.store.dispatch(new FetchAreaUnitList());
    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });
    this.sharedDataService.updateSharedTabData(2);
  }

  stop(event: Event) {
    event.stopPropagation();
  }

  changeDateFormat(value: any) {
    const prevDate = String(value)?.includes('00.000Z')
      ? value
      : getISODateFormat(value);

    const date = new Date(prevDate);
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    const formattedDate = `${day}-${month}-${year}`;
    return formattedDate;
  }

  increaseNum() {
    let propName = this.basicInfoForm.get('noOfFloors').value;
    this.basicInfoForm.get('noOfFloors')?.setValue(++propName);
  }

  decreaseNum() {
    if (this.basicInfoForm.get('noOfFloors').value > 0) {
      let propName = this.basicInfoForm.get('noOfFloors').value;
      this.basicInfoForm.get('noOfFloors')?.setValue(--propName);
    }
  }

  // get currentDate(): Date {
  //   const currentDate = new Date();
  //   currentDate.setHours(0, 0, 0, 0);
  //   return currentDate;
  // }

  addBlock() {
    let blockInfoData = this.basicInfoForm.value;

    if (this.basicInfoForm.invalid) {
      validateAllFormFields(this.basicInfoForm);
      return;
    }
    this.modalService.hide();
    let payload: any = {
      name: blockInfoData.title,
      area: blockInfoData.area,
      areaUnitId: blockInfoData.areaUnit,
      startDate: setTimeZoneDate(blockInfoData.startDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      endDate: setTimeZoneDate(blockInfoData.endDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      possessionDate: setTimeZoneDate(blockInfoData?.possessionDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      possesionType: blockInfoData.possesionType
        ? blockInfoData.possesionType
        : 0,
      numberOfFloors: blockInfoData.noOfFloors ? blockInfoData.noOfFloors : '0',
      id: this.selectedBlockData?.id,
      projectId: this.selectedDataId,
    };
    if (this.selectedBlockData?.id) {
      payload = {
        projectId: this.selectedDataId,
        blockDtos: [
          {
            ...payload,
          },
        ],
      };
      this.store.dispatch(new UpdateProjectBlock(payload));
    } else {
      this.store.dispatch(new AddProjectBlocks(payload));
    }
    this.basicInfoForm.reset();
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: 1,
      pageSize: 10,
    };
  }

  hideModalAndResetForm() {
    this.basicInfoForm.reset();
    this.modalService.hide();
  }

  manageProject() {
    this.router.navigate(['/projects/manage-projects']);
  }

  monthChanged(event: any) {
    this.isClosePossessionModal = true;
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;
    this.basicInfoForm.controls['possessionDate'].setValue(
      this.selectedMonthAndYear
    );
    this.isValidPossessonDate = false;
    this.currentMonth = this.selectedMonthAndYear;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.dt5.close();
  }

  checkCurrentMonth() {
    this.selectedMonthAndYear = new Date(
      `${this.selectedYear}-${this.currentMonth.getMonth() + 1}-01`
    );
  }

  onDateSelection() {
    this.currentMonth = this.selectedMonthAndYear;
    this.selectedMonth = this.selectedMonthAndYear.toString().slice(4, 7);
    this.selectedYear = this.selectedMonthAndYear.toString().slice(11, 15);
    this.checkCurrentMonth();
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  convertDateToMonth(data: any) {
    this.selectedMonthAndYear = data;
    this.selectedMonth = MONTHS[parseInt(data?.slice(5, 7), 10) - 1];
    this.selectedYear = parseInt(data?.slice(0, 4), 10);
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  selectedMonthNull(data: any) {
    if (data?.value === 'Custom Date') {
      this.selectedPossession = null;
      return;
    }
    this.selectedPossession = data?.value;
  }

  closePossessionModal() {
    if (
      this.basicInfoForm.controls['globalRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.isValidPossessonDate = true;
      return;
    }
    this.isOpenPossessionModal = false;
  }
}
