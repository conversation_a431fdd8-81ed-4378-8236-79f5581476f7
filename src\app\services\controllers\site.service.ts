import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NO_BLOCK_PARAM } from 'src/app/app.constants';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class SiteService extends BaseService<any> {
  serviceBaseUrl: string;
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}api/${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'site';
  }

  addLocation(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/location`, payload);
  }

  updateLocation(payload: any, id: string) {
    return this.http.put(`${this.serviceBaseUrl}/location/${id}`, payload);
  }

  deleteLocation(payload: any) {
    let headers: any = {
      body: {
        ...payload
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/location`, headers);
  }

  bulkUpdateZone(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/location/zone`, payload);
  }

  bulkUpdateCity(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/location/city`, payload);
  }

  getAllCityList() {
    return this.http.get(`${this.serviceBaseUrl}/city`);
  }

  addCity(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/city`, payload);
  }

  updateCity(payload: any, id: string) {
    return this.http.put(`${this.serviceBaseUrl}/city/${id}`, payload);
  }

  deleteCity(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/city/${id}`);
  }

  deleteCities(payload: any) {
    let headers: any = {
      body: {
        ...payload
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/city`, headers);
  }

  getAllZoneList() {
    return this.http.get(`${this.serviceBaseUrl}/zone`);
  }

  addZone(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/zone`, payload);
  }

  updateZone(payload: any, id: string) {
    return this.http.put(`${this.serviceBaseUrl}/zone/${id}`, payload);
  }

  deleteZone(payload: any) {
    let headers: any = {
      body: {
        ...payload
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/zone`, headers);
  }

  getAllLocations() {
    return this.http.get(`${this.serviceBaseUrl}/location/lead`);
  }

  getAllLocationsWithGoogleMap(text: string, page: number, pagesize: number) {
    let params = new HttpParams()
      .set('PageNumber', page.toString())
      .set('PageSize', pagesize.toString());
    if (text) {
      params = params.set('SearchText', text.toString());
    }
    return this.http.get(`${this.serviceBaseUrl}/location/lead/googlemap?${params?.toString()}`,
      {
        params: NO_BLOCK_PARAM['params'],
      }
    );
  }

  uploadExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/location/bulk/excel`, formData);
  }

  // ........... state ............

  addState(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/state`, payload);
  }
  updateState(payload: any, id: string) {
    return this.http.put(`${this.serviceBaseUrl}/state/${id}`, payload);
  }

  deleteState(id: any) {
    const headers = {
      body: {
        ids: Array.isArray(id) ? id : [id] 
      }
    };
    return this.http.delete(`${this.serviceBaseUrl}/state/`, headers);
  }  
  
  // -------- country----------------
  addCountry(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/country`, payload);
  }
  updateCountry(payload: any, id: string) {
    return this.http.put(`${this.serviceBaseUrl}/country/${id}`, payload);
  }
  deleteCountry(id: any) {
    const headers = {
      body: {
        ids: Array.isArray(id) ? id : [id]
      }
    };
    return this.http.delete(`${this.serviceBaseUrl}/country/`, headers);
  }
  

  getAllCustomLocations(text: string, page: number, pagesize: number) {
    let params = new HttpParams()
      .set('PageNumber', page.toString())
      .set('PageSize', pagesize.toString());
    if (text) {
      params = params.set('SearchText', text.toString());
    }
    return this.http.get(`${this.serviceBaseUrl}/custom/all/addresses?${params?.toString()}`,
      {
        params: NO_BLOCK_PARAM['params'],
      }
    );
  }
}
