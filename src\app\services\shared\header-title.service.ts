import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class HeaderTitleService {

  pageTitle = new BehaviorSubject('');
  openAddRole = new Subject<boolean>();
  openApplyLeave = new Subject<boolean>();
  openAddGroup = new Subject<boolean>();
  
  constructor(private translate: TranslateService) { }

  setTitle(pageTitle: string) {
    this.pageTitle.next(pageTitle);
  }

  setLangTitle(title: string) {
    this.translate.get(title).subscribe((res: string) => {
      this.setTitle(res);
    });
  }

}
