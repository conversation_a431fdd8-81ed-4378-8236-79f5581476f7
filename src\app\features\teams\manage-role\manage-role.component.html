<div *ngIf="canView" class="px-24 py-16 flex-grow-1">
  <div class="flex-end mb-20">
    <!-- <div class="align-center">
      <span class="icon ic-chevron-left ic-coal ic-xs mr-12 cursor-pointer" (click)="router.navigate(['teams/manage-team/'])"></span>
      <h5 class="fw-600"><span class="fw-semi-bold">{{ 'SIDEBAR.team' | translate }}</span> /
        {{'USER_MANAGEMENT.roles-permission' | translate}}</h5>
    </div> -->
    <ng-container *ngIf="canAdd">
      <div class="btn-coal" (click)="addRoleModal(offset,pageSize)">
        <span class="ic-add icon ic-xxs"></span>
        <span class="ml-8 ph-d-none">{{ 'USER.add-new-role' | translate }}</span>
      </div>
    </ng-container>
  </div>
  <div class="align-center bg-white w-100" *ngIf="!isRolesLoading">
    <div class="align-center bg-white w-100 border-gray">
      <div class="align-center px-10 border-end flex-grow-1 no-validation" [ngClass]="canSearch ? 'py-12' : 'py-12'">
        <ng-container>
          <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
          <input type="text" [(ngModel)]="searchTerm" (keyup.enter)="search($event)" (input)="clearSearch()"
            placeholder="{{'GLOBAL.search'|translate}}" class="border-0 outline-0 w-100">
          <small class="text-muted text-nowrap">({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </ng-container>
      </div>
      <div class="flex-center">
        <div class="show-dropdown-white align-center position-relative">
          <span class="fw-600 position-absolute ml-8 left-5 z-index-2">
            <span class="tb-d-none">{{ 'GLOBAL.show' | translate }}</span>
            <span> {{ 'GLOBAL.entries' | translate }}</span> </span>
          <div class="text-secondary-color">
            <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" [searchable]="false"
              ResizableDropdown class="w-150 tb-w-120px" (change)="assignCount()" [(ngModel)]="selectedPageSize">
              <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                {{pageSize}}</ng-option>
            </ng-select>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="!isRolesLoading" class="bg-white px-4 py-12 border-left border-right"></div>
  <div class="manage-user checkbox-align  pinned-grid" *ngIf="!isRolesLoading else loader">
    <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize"
      [gridOptions]="gridOptions" [rowData]="rowData" [alwaysShowHorizontalScroll]="true"
      [alwaysShowVerticalScroll]="true" [suppressPaginationPanel]="true"
      (gridReady)="onGridReady($event)"></ag-grid-angular>
  </div>
  <div class="flex-end mt-16" *ngIf="totalRolesCount>0 && !isRolesLoading">
    <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{(offset * pageSize) + 1}} {{ 'GLOBAL.to-small' | translate
      }}
      {{(offset * pageSize) + pageSize > totalRolesCount ? totalRolesCount : (offset * pageSize) + pageSize}}
      {{'GLOBAL.of-small' | translate }}
      {{totalRolesCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
    <pagination [offset]="offset" [limit]="1" [range]="1" [size]="getPages(totalRolesCount,pageSize)"
      (pageChange)="onPageChange($event)">
    </pagination>
  </div>
</div>
<div class="justify-center">
  <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2"
    [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
    <div class="align-center">
      <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
        {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}</div>
    </div>
    <div class="flex-center flex-wrap">
      <button class="btn-bulk" id="btnBulkDelete" data-automate-id="btnBulkDelete" *ngIf="canBulkDelete"
        (click)="openBulkDeleteModal(BulkDeleteModal)">Bulk Delete
      </button>
    </div>
  </div>
</div>
<ng-template #loader>
  <div class="flex-center h-100-170">
    <application-loader></application-loader>
  </div>
</ng-template>

<ng-template #BulkDeleteModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>Bulk Delete</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkDeleteModalRef.hide()"></div>
    </div>
    <div class="px-12">
      <div class="field-label mb-10">Selected Role(s) -
        {{selectedRoles?.length}}
      </div>
      <div class="flex-column scrollbar max-h-100-176">
        <ng-container *ngFor="let role of selectedRoles">
          <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
            <span class="text-truncate-1 break-all ml-8">{{ role.name }}</span>
            <div (click)="openConfirmDeleteModal(role?.name, role?.id)" class="bg-light-red icon-badge"
              id="clkBulkDelete" data-automate-id="clkBulkDelete">
              <span class="icon ic-cancel m-auto ic-xx-xs"></span>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="flex-center">
        <button class="btn-coal mt-20" (click)="updateBulkDelete()">Delete</button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #roleMessageModal>
  <h4 class="px-20 py-16 fw-semi-bold bg-coal text-white">Alert</h4>
  <div class="p-20">
    <div class="scrollbar max-h-100-400">
      <ng-container *ngFor="let role of getRoles(bulkDeleteRoles)">
        <h5 class="text-black-100 mb-10 word-break line-break">{{role}}</h5>
      </ng-container>
    </div>
    <div class="flex-center w-100">
      <button class="btn-green mt-30" (click)="modalRef.hide()">
        Ok, got it </button>
    </div>
  </div>
</ng-template>