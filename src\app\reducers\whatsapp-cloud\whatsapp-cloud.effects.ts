import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import {
  AddWhatsappCloud,
  AddWhatsappCloudTest,
  FetchWhatsappCloud,
  FetchWhatsappCloudSuccess,
  SendEmailForm,
  WhatsappCloudActionTypes,
} from 'src/app/reducers/whatsapp-cloud/whatsapp-cloud.actions';
import { BsModalService } from 'ngx-bootstrap/modal';
import { CommunicationBulkCount, CommunicationBulkMessage } from '../lead/lead.actions';
import { AppState } from 'src/app/app.reducer';
import { Store } from '@ngrx/store';
import { CommunicationBulkDataCount, CommunicationBulkDataMessage } from '../data/data-management.actions';
import { WhatsappCloudService } from 'src/app/services/controllers/whatsapp-cloud.service';

@Injectable()
export class WhatsappCloudEffects {
  getWhatsappCloud$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappCloudActionTypes.FETCH_WHATSAPP_CLOUD),
      switchMap((action: FetchWhatsappCloud) => {
        return this.api.getWhatsappCloud().pipe(
          map((resp: any) => {
            return new FetchWhatsappCloudSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  AddWhatsappCloud$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappCloudActionTypes.ADD_WHATSAPP_CLOUD),
      switchMap((action: AddWhatsappCloud) => {
        return this.api.addWhatsappCloud(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Bulk Templates Queued successfully.`);
              this.modalService.hide();
              const payloadCount: any = {
                ids: action?.payload?.leadContactInfoDtos.map((item: any) => item?.id),
                contactType: 0
              };
              const payload: any = {
                leadIds: action?.payload?.leadContactInfoDtos.map((item: any) => item?.id),
                contactType: 0
              };
              const DataPayload: any = {
                prospectIds: action?.payload?.leadContactInfoDtos.map((item: any) => item?.id),
                contactType: 0
              };
              if (action?.payload?.isData) {
                this._store.dispatch(new CommunicationBulkDataCount(payloadCount));
                this._store.dispatch(new CommunicationBulkDataMessage(DataPayload));
              } else {
                this._store.dispatch(new CommunicationBulkCount(payloadCount));
                this._store.dispatch(new CommunicationBulkMessage(payload));
              }
            }
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    ), { dispatch: false }
  );

  AddWhatsappCloudTest$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappCloudActionTypes.ADD_WHATSAPP_CLOUD_TEST),
      switchMap((action: AddWhatsappCloudTest) => {
        return this.api.addWhatsappCloudTest(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Test Message Sent Successfully.`);
            }
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    ), { dispatch: false }
  );

  utilitySendEmail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(WhatsappCloudActionTypes.SEND_EMAIL_FORM),
      switchMap((action: SendEmailForm) => {
        return this.api.utilitySendEmail(action.payload).pipe(
          map((resp: any) => {
            // if (resp.succeeded) {
            //   this._notificationService.success(`Test Message Sent Successfully.`);
            // }
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    ), { dispatch: false }
  );
  constructor(
    private _store: Store<AppState>,
    private actions$: Actions,
    private api: WhatsappCloudService,
    public modalService: BsModalService,
    private _notificationService: NotificationsService
  ) { }
}
