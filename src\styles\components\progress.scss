.rounded-progress[role="progressbar"] {
    --progress-percentage: var(--value);
    animation: growProgressBar 1.5s 1 forwards;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: grid;
    place-items: center;
    background:
        radial-gradient(closest-side, white 80%, transparent 0 99.9%, white 0),
        conic-gradient($orange-400 calc(var(--progress-percentage) * 1%), $dark-300 0);
    font-size: $base-font-size - 2px;
    color: $orange-400;
    @extend .position-relative;
}

.rounded-progress[role="progressbar"]::before {
    counter-reset: percentage var(--value);
    content: counter(percentage) '%';
    color: $dark-300 ;
}

.circular-progress-bar {
    .rounded-progress {
        width: 60px;
        height: 60px;
    }
}

.circular-progress[role="progressbar"]::before {
    counter-reset: percentage var(--value);
    content: counter(percentage) '%';
    color: $slate-600 !important;
}

@property --progress-percentage {
    syntax: '<number>';
    inherits: false;
    initial-value: 0;
}

@keyframes growProgressBar {

    0%,
    33% {
        --progress-percentage: 0;
    }

    100% {
        --progress-percentage: var(--value);
    }
}

.header-progress {
    &::before {
        @extend .position-absolute;
    }

    .ic-user-secondary {
        &::before {
            opacity: 0.5;
        }
    }
}

.percentage {
    &::before {
        @extend .position-absolute;
        top: 69px;
        right: 40px;
    }
}