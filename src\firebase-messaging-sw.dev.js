importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.16.0/firebase-messaging-compat.js");

firebase.initializeApp({
    apiKey: 'AIzaSyCvkYfeOMpCptyHD0IbDCvAVC3dnpEV62A',
    authDomain: 'leadrat-black-web-dev.firebaseapp.com',
    projectId: 'leadrat-black-web-dev',
    storageBucket: 'leadrat-black-web-dev.appspot.com',
    messagingSenderId: '835486458422',
    appId: '1:835486458422:web:052d0b383f9deb26f2ae97',
    measurementId: 'G-MVX9PDM05J',
    vapidKey: 'BB061xrwxKbl2zl87CxyMnXeU62lL6eVdV0yWO8rFUkFbA9TrSeZuLJ_4uyNSdbqgYFcQ2r4dTke1jwL6uIQJD8'
});

const messaging = firebase.messaging();
self.addEventListener('push', function (event) {
    const payload = event.data ? event.data.json() : {};
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png'
        // data: { url: payload.data.click_action},
        // actions: [
        //     {
        //         action: 'reply', // Identifier for the action
        //         title: 'Reply', // Button text
        //         icon: payload.notification.icon // Optional icon for the button,
        //     },
        //     {
        //         action: 'dismiss', // Identifier for the action
        //         title: 'Dismiss', // Button text
        //         icon: payload.notification.icon // Optional icon for the button
        //     }
        // ],
    };

    event.waitUntil(
        // Retrieve all existing notifications and close them
        self.registration.getNotifications().then(function (notifications) {
            return Promise.all(notifications.map(function (notification) {
                return notification.close();
            }));
        })
            .then(function () {
                return self.registration.showNotification(notificationTitle, notificationOptions);
            })
    );
});

// self.addEventListener('notificationclick', function(event) {
//     event.notification.close();
//     console.log(event);

//     // Determine action based on notification data
//     const actionUrl =  event.notification.data.url;

//     event.waitUntil(
//         clients.openWindow(actionUrl)
//     );
// });


messaging.onBackgroundMessage(function (payload) {
    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: 'https://leadrat-black.s3.ap-south-1.amazonaws.com/webNotificationLogo.png'
    };

    self.registration.showNotification(notificationTitle, notificationOptions);
});
