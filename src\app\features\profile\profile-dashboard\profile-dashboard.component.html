<div *ngIf="canView" class="px-30 py-20">
  <profile-overview></profile-overview>
  <div class="section-navbar br-6 mb-20 justify-content-between position-relative ip-flex-col ip-mb-20 ip-mr-10">
    <ul>
      <li [routerLink]="navigation.link" *ngFor="let navigation of navigations" class="py-8"><a
          routerLinkActive="active">{{navigation.title}}</a></li>
    </ul>
    <button *ngIf="router.url.includes('testimonial') && canAdd" class="btn-coal w-150 ip-bottom" (click)="addTestimonial()"><span
        class="ic-add icon ic-xxs mx-8"></span>{{ 'BUTTONS.add-testimonial' | translate }}</button>
  </div>
  <div class="profile">
    <router-outlet></router-outlet>
  </div>
</div>
