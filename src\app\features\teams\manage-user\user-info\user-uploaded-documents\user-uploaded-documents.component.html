<div class="mt-28 scrollbar h-100-176">
  <div class="flex-between ip-flex-between-unset ip-flex-col">
    <div class="align-center w-260 px-12 no-validation py-8 br-4 border">
      <span class="search icon ic-search ic-xxs ic-slate-90 mr-6"> </span>
      <input placeholder="Search for document name.." (keydown.enter)="searchDocuments($event.target.value)"
        name="search" [(ngModel)]="searchTerm" class="border-0 outline-0 w-100 bg-light-pearl">
    </div>
    <div *ngIf="loggedInUser || canEdit"
      class="bg-accent-green-300 br-20 h-30px align-center p-2 w-140 cursor-pointer mr-10 ip-mt-10"
      (click)="isShowDocUpload = !isShowDocUpload;trackingService.trackFeature('Web.UserDetails.Button.DocumentsUploadFiles.Click')">
      <div class="bg-accent-green dot dot-lg"><span class="ic-upload ic-sm p-4"></span></div>
      <span class="ml-8  text-white">upload new file</span>
    </div>
  </div>
  <ng-container *ngIf="isShowDocUpload">
    <div class="d-flex tb-flex-col mt-30">
      <div class="w-30 tb-w-100 tb-mb-20">
        <div class="vertical-navbar mr-30">
          <div class="nav-item mb-30" [ngClass]="{'active' : selectedSection == 'Identity'}"
            (click)="onSectionSelect('Identity')">Identity Documents</div>
          <div class="nav-item mb-30" [ngClass]="{'active' : selectedSection == 'Experience'}"
            (click)="onSectionSelect('Experience')">Previous Experience</div>
          <div class="nav-item" [ngClass]="{'active' : selectedSection == 'Signature'}"
            (click)="onSectionSelect('Signature')">Signature</div>
        </div>
      </div>
      <div class="w-100">
        <div *ngIf="selectedSection == 'Identity'">
          <div class="d-flex ip-flex-col">
            <div class="w-50 tb-w-100 tb-mb-20">
              <h3 class="fw-600">{{'PROPERTY.STEPS.add-document' | translate}}</h3>
              <div class="mt-30 mr-20 tb-mt-0">
                <div class="field-label-req">{{'PROPERTY.STEPS.name-of-document' | translate}}</div>
                <ng-select [virtualScroll]="true" [items]="documents" bindLabel="name" bindValue="name"
                  placeholder="ex. Aadhaar Card" [(ngModel)]="selectedDocName" class="w-100"></ng-select>
              </div>
              <div *ngIf="!selectedDocName && (filesToBeUploadToS3Bucket)" class="mt-4 text-xs text-red">
                {{'PROJECTS.title'
                | translate}} {{'GLOBAL.is-required-field' | translate}}</div>
            </div>
            <div class="w-50 tb-w-100">
              <div class="p-20 mr-20 br-8 position-relative"
                [ngClass]="isFileTypeSupported ? 'bg-accent-green-light' : 'bg-accent-red'">
                <div class="flex-center-col">
                  <h5 class="mb-12 fw-600 text-center">You can select and upload your file</h5>
                  <p class="text-slate mb-10">Supported formats pdf</p>
                </div>
                <browse-drop-upload [allowedFileType]="'pdf'" [allowedFileFormat]="fileFormatToBeUploaded"
                  (uploadedFile)="onFileSelection($event)"></browse-drop-upload>
                <div class="position-absolute bottom-4 right-20"
                  *ngIf="(!filesToBeUploadToS3Bucket)&&validationPerformed">
                  <p class="text-danger text-nowrap">{{'BULK_LEAD.select-file' | translate}}</p>
                </div>
                <div class="align-center mx-20 mb-20 " *ngIf="!isFileTypeSupported">
                  <p class="text-danger">{{'BULK_LEAD.upload-correct-format' | translate}}</p>
                </div>
              </div>
              <div *ngIf="filesToBeUploadToS3Bucket" class="flex-between mt-20 mr-20">
                <span>{{ 'GLOBAL.selected-file' | translate }}</span>
                <div title="Delete" class="bg-light-red icon-badge ml-12"><span class="icon ic-delete m-auto ic-xxs"
                    id="clkDeleteBrochure" data-automate-id="clkDeleteBrochure"
                    (click)="filesToBeUploadToS3Bucket=null"></span></div>
              </div>
              <div class="btn btn-sm btn-accent-green flex-center mr-20 mt-20" (click)="UploadDoc()">Upload</div>
            </div>
          </div>
        </div>
        <div *ngIf="selectedSection == 'Experience'">
          <div class="d-flex ip-flex-col">
            <div class="w-50 ip-w-100">
              <h3 class="fw-600">{{'PROPERTY.STEPS.add-previous-experience' | translate}}</h3>
              <div class="mt-30 tb-mt-0 mr-30">
                <div class="field-label-req">{{'PROPERTY.STEPS.name-of-document' | translate}}</div>
                <div class="form-group ip-mb-20">
                  <input type="text" name="documentsName" placeholder="ex. Offer Letter" [(ngModel)]="selectedDocName">
                </div>
                <div *ngIf="!selectedDocName && (filesToBeUploadToS3Bucket)" class="mt-4 text-xs text-red">
                  {{'PROJECTS.title' | translate}} {{'GLOBAL.is-required-field' | translate}}</div>
              </div>
            </div>
            <div class="w-50 ip-w-100">
              <div class="p-20 mr-20 br-8 position-relative"
                [ngClass]="isFileTypeSupported ? 'bg-accent-green-light' : 'bg-accent-red'">
                <div class="flex-center-col">
                  <h5 class="mb-12 fw-600 text-center">You can select and upload your file</h5>
                  <p class="text-slate mb-10">Supported formats pdf</p>
                </div>
                <browse-drop-upload [allowedFileType]="'pdf'" [allowedFileFormat]="fileFormatToBeUploaded"
                  (uploadedFile)="onFileSelection($event)"></browse-drop-upload>
                <div class="position-absolute bottom-4 right-20"
                  *ngIf="(!filesToBeUploadToS3Bucket)&&validationPerformed">
                  <p class="text-danger text-nowrap">{{'BULK_LEAD.select-file' | translate}}</p>
                </div>
                <div class="align-center mx-20 mb-20" *ngIf="!isFileTypeSupported">
                  <p class="text-danger">{{'BULK_LEAD.upload-correct-format' | translate}}</p>
                </div>
              </div>
              <div *ngIf="filesToBeUploadToS3Bucket" class="flex-between mt-20 mr-20">
                <span>{{ 'GLOBAL.selected-file' | translate }}</span>
                <a title="Delete" class="bg-light-red icon-badge ml-12"><span class="icon ic-delete m-auto ic-xxs"
                    id="clkDeleteBrochure" data-automate-id="clkDeleteBrochure"
                    (click)="filesToBeUploadToS3Bucket=null"></span></a>
              </div>
              <div class="btn btn-sm btn-accent-green flex-center mt-20 mr-20" (click)="UploadDoc()">Upload</div>
            </div>
          </div>
        </div>
        <div *ngIf="selectedSection == 'Signature'">
          <div class="d-flex ip-flex-col">
            <div class="w-20 ip-w-100 ip-mb-20">
              <h4 class="fw-600 mt-50 mr-20 ip-mt-0">{{'PROPERTY.STEPS.add-signature' | translate}}</h4>
            </div>
            <div class="w-80pr ip-w-100">
              <div class="p-20 br-8 mr-10 position-relative"
                [ngClass]="isFileTypeSupported ? 'bg-accent-green-light' : 'bg-accent-red'">
                <div class="flex-center-col">
                  <h5 class="mb-12 fw-600 text-center">You can select and upload your file</h5>
                  <p class="text-slate mb-10">Supported formats pdf</p>
                </div>
                <browse-drop-upload [allowedFileType]="'pdf'" [allowedFileFormat]="fileFormatToBeUploaded"
                  (uploadedFile)="onFileSelection($event)"></browse-drop-upload>
                <div class="position-absolute bottom-4 right-20"
                  *ngIf="(!filesToBeUploadToS3Bucket)&&validationPerformed">
                  <p class="text-danger text-nowrap">{{'BULK_LEAD.select-file' | translate}}</p>
                </div>
                <div class="align-center mx-20 mb-20" *ngIf="!isFileTypeSupported">
                  <p class="text-danger">{{'BULK_LEAD.upload-correct-format' | translate}}</p>
                </div>
              </div>
              <div *ngIf="filesToBeUploadToS3Bucket" class="flex-between mt-20 mr-20">
                <span>{{ 'GLOBAL.selected-file' | translate }}</span>
                <a title="Delete" class="bg-light-red icon-badge ml-12"><span class="icon ic-delete m-auto ic-xxs"
                    id="clkDeleteBrochure" data-automate-id="clkDeleteBrochure"
                    (click)="filesToBeUploadToS3Bucket=null"></span></a>
              </div>
              <div class="btn btn-sm btn-accent-green flex-center mr-20 mt-20" (click)="UploadDoc()">Upload</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>


  <ng-container *ngIf="allDoc?.length else noDocument">
    <div class="w-100 d-flex flex-wrap">
      <div *ngFor="let doc of allDoc; let i = index">
        <div *ngIf="!searchTerm || doc.docName.toLowerCase().includes(searchTerm.toLowerCase())">
          <div class="flex-center-col card-hover" (mouseenter)="hoveredIndex = i" (mouseleave)="hoveredIndex = -1">
            <div
              class="box-shadow-10 w-30px h-30px position-absolute ntop-10 nright-10 rounded-circle bg-white flex-center cursor-pointer"
              *ngIf="hoveredIndex === i && (loggedInUser || canEdit)" (click)="openDeleteConfirmModal(doc)">
              <div title="Delete" class="icon ic-delete ic-red-350 ic-xxs"></div>
            </div>
            <a [href]="s3BucketPath+doc?.filePath" target="_blank" id="clkViewDoc" data-automate-id="clkViewDoc">
              <img *ngIf="fileType(doc.filePath) == 'file'" src="../../../../assets/images/img-file.svg" alt="file">
              <img *ngIf="fileType(doc.filePath) == 'pdf'" src="../../../../assets/images/img-pdf.svg" alt="Pdf">
              <img *ngIf="fileType(doc.filePath) == 'document'" src="../../../../assets/images/img-doc.svg" alt="Doc">
              <img *ngIf="fileType(doc.filePath) == 'xls'" src="../../../../assets/images/img-xls.svg" alt="xls">
              <img *ngIf="fileType(doc.filePath) == 'img'" src="../../../../assets/images/img.svg" alt="img">
              <div [title]="doc?.docName" class="text-large text-black-200 mt-10 text-truncate w-90">{{ doc?.docName}}
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-template #noDocument>
    <div class="flex-col flex-center h-100-250 min-h-250">
      <img src="assets/images/layered-cards.svg" alt="No leads found" width="160" height="140">
      <div class="fw-semi-bold text-xl text-mud">{{'USER.no-document-found' | translate}}</div>
    </div>
  </ng-template>
</div>