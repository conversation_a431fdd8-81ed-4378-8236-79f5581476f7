import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { catchError, map, of, switchMap, take, throwError } from 'rxjs';

import { CloseModal, OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { ManageMarketingService } from 'src/app/services/controllers/marketing.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import {
    AddAgencyName,
    AddAgencyNameSuccess,
    AddCampaignName,
    AddCampaignNameSuccess,
    AddChannelPartnerName,
    AddChannelPartnerNameSuccess,
    CampaignExcelUpload,
    CampaignExcelUploadSuccess,
    ChannelPartnerExcelUpload,
    ChannelPartnerExcelUploadSuccess,
    DeleteAgency,
    DeleteAgencySuccess,
    DeleteCampaign,
    DeleteCampaignSuccess,
    DeleteChannelPartner,
    DeleteChannelPartnerSuccess,
    ExistAgency,
    ExistAgencySuccess,
    ExistCampaign,
    ExistCampaignSuccess,
    ExistChannelPartner,
    ExistChannelPartnerSuccess,
    ExportAgency,
    ExportAgencySuccess,
    ExportCampaign,
    ExportCampaignSuccess,
    ExportChannelPartner,
    ExportChannelPartnerSuccess,
    FetchAgencyExcelUploadedList,
    FetchAgencyExcelUploadedSuccess,
    FetchAgencyExportTracker,
    FetchAgencyExportTrackerSuccess,
    FetchAgencyLocations,
    FetchAgencyLocationsSuccess,
    FetchAgencyName,
    FetchAgencyNameSuccess,
    FetchCampaignExcelUploadedList,
    FetchCampaignExcelUploadedSuccess,
    FetchCampaignExportTracker,
    FetchCampaignExportTrackerSuccess,
    FetchCampaignName,
    FetchCampaignNameSuccess,
    FetchChannelPartner,
    FetchChannelPartnerExcelUploadedList,
    FetchChannelPartnerExcelUploadedSuccess,
    FetchChannelPartnerExportTracker,
    FetchChannelPartnerExportTrackerSuccess,
    FetchChannelPartnerLocations,
    FetchChannelPartnerLocationsSuccess,
    FetchChannelPartnerSuccess,
    MarketingAction,
    MarketingExcelUpload,
    MarketingExcelUploadSuccess,
    UpdateAgencyName,
    UpdateAgencyNameSuccess,
    UpdateCampaignName,
    UpdateCampaignNameSuccess,
    UpdateChannelPartner,
    UpdateChannelPartnerSuccess,
    UploadAgencyMappedColumns,
    UploadCampaignMappedColumns,
    UploadChannelPartnerMappedColumns,
} from './marketing.action';
import {
    getAgencyFiltersPayload,
    getCampaignFiltersPayload,
    getChannelPartnerNameFiltersPayload,
} from './marketing.reducer';

@Injectable()
export class MarketingEffects {
    getAgencyName$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_AGENCY_NAME),
            switchMap((action: FetchAgencyName) => {
                let payload = {
                    ...action.filtersPayload,
                    path: 'v1/marketing/agencies',
                };
                return this.commonService.getModuleListByAdvFilter(payload, true).pipe(
                    map((resp: any) => {
                        if (resp) {
                            return new FetchAgencyNameSuccess(resp);
                        }
                        return new FetchAgencyNameSuccess(resp);
                    }),
                    catchError((err) => {
                        this._notificationService.error(`Error in fetching agency`);
                        return of(new OnError(err));
                    })
                );
            })
        )
    );

    fetchAgencyLocation$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_AGENCY_LOCATION),
            switchMap((action: FetchAgencyLocations) => {
                return this.manageMarketingService.getAgencyLocation().pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return [new FetchAgencyLocationsSuccess(resp.data)];
                        }
                        return [new FetchAgencyLocationsSuccess(resp.data)];
                    }),
                    catchError((err) => {
                        this._notificationService.error(`Error in adding agency.`);
                        return of(new OnError(err));
                    })
                );
            })
        )
    );

    addAgency$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.ADD_AGENCY_NAME),
            switchMap((action: AddAgencyName) => {
                return this.manageMarketingService.addAgencyName(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            this._notificationService.success(`Agency added successfully.`);
                            return [
                                new AddAgencyNameSuccess(resp.data),
                                new FetchAgencyName({
                                    path: 'v1/marketing',
                                    PageNumber: 1,
                                    PageSize: 10,
                                }),
                            ];
                        }
                        return [new AddAgencyNameSuccess(resp.data)];
                    }),
                    catchError((err) => {
                        return of(new OnError(err));
                    })
                );
            })
        )
    );

    deleteAgency$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.DELETE_AGENCY),
            switchMap((action: DeleteAgency) =>
                this.manageMarketingService.deleteAgency(action.id).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return this.store.select(getAgencyFiltersPayload).pipe(
                                take(1),
                                switchMap((payload: any) => {
                                    this._notificationService.success(`deleted successfully.`);
                                    return [
                                        new DeleteAgencySuccess(resp),
                                        new FetchAgencyName(payload),
                                    ];
                                })
                            );
                        }
                        return [new DeleteAgencySuccess(resp)];
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    updateAgencyName$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.UPDATE_AGENCY_NAME),
            switchMap((action: UpdateAgencyName) =>
                this.manageMarketingService.updateAgencyName(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return this.store.select(getAgencyFiltersPayload).pipe(
                                take(1),
                                switchMap((payload: any) => {
                                    this._notificationService.success(
                                        `Agency updated successfully.`
                                    );
                                    return [
                                        new UpdateAgencyNameSuccess(resp),
                                        new FetchAgencyName(payload),
                                    ];
                                })
                            );
                        }
                        return [new UpdateAgencyNameSuccess(resp)];
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    // ---------------------- Bulk Upload ----------------------
    addBulkMarketingExcel$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.MARKETING_EXCEL_UPLOAD),
            switchMap((action: MarketingExcelUpload) => {
                return this.manageMarketingService
                    .uploadMarketingExcel(action.file)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                this._notificationService.success(
                                    'Excel uploaded Successfully'
                                );
                                return new MarketingExcelUploadSuccess(resp.data);
                            } else {
                                this._store.dispatch(new CloseModal());
                                this._notificationService.warn(`${resp.message}`);
                                return new FetchAgencyName(resp);
                            }
                        }),
                        catchError((err: any) => {
                            throwError(err);
                            Array.isArray(err?.error?.errors.AgencyNames)
                                ? this._notificationService.error(
                                    err?.error?.errors.AgencyNames[0]
                                )
                                : this._notificationService.error(err?.error?.errors?.messages);
                            return throwError(() => err);
                        })
                    );
            })
        )
    );

    getAgencyExcelUploadedList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_AGENCY_EXCEL_UPLOADED_LIST),
            map((action: FetchAgencyExcelUploadedList) => action),
            switchMap((data: any) => {
                return this.manageMarketingService
                    .getAgencyExcelUploadedList(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchAgencyExcelUploadedSuccess(resp);
                            }
                            return new FetchAgencyExcelUploadedSuccess();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    uploadAgencyMappedColumns$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.UPLOAD_AGENCY_MAPPED_COLUMNS),
            switchMap((action: UploadAgencyMappedColumns) => {
                return this.manageMarketingService
                    .uploadAgencyMappedColumns(action.payload)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                if (resp.data) {
                                    if (resp.data?.excelUrl) {
                                        const dataCount = resp.message?.DataCount || '';
                                        this._notificationService.success(
                                            `${dataCount} Invalid Data Not Uploaded`
                                        );
                                        return new MarketingExcelUploadSuccess(resp.data);
                                    } else {
                                        this._notificationService.success(
                                            'Excel Uploaded Successfully'
                                        );
                                        return new MarketingExcelUploadSuccess(resp.data);
                                    }
                                } else {
                                    this._notificationService.error(resp.message);
                                }
                                return new FetchAgencyName(resp);
                            }
                            return new FetchAgencyNameSuccess();
                        }),
                        catchError((err: any) => {
                            Array.isArray(err?.error?.messages)
                                ? this._notificationService.error(err.error.messages[0])
                                : this._notificationService.error(err?.error?.messages);
                            return throwError(() => err);
                        })
                    );
            })
        )
    );

    exportAgencyUsers$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXPORT_AGENCY),
            switchMap((action: ExportAgency) => {
                return this.manageMarketingService.exportAgency(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                'Agency(s) is/are being exported in excel format'
                            );
                            return new ExportAgencySuccess();
                        }
                        return new ExportAgencySuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getAgencyExportStatusList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXPORT_AGENCY_TRACKER),
            map((action: FetchAgencyExportTracker) => action),
            switchMap((data: any) => {
                return this.manageMarketingService
                    .getAgencyExportStatus(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchAgencyExportTrackerSuccess(resp);
                            }
                            return new FetchAgencyExportTrackerSuccess();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    existAgency$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXIST_AGENCY),
            map((action: ExistAgency) => action.agencyName),
            switchMap((data: any) => {
                return this.manageMarketingService.existAgencyName(data).pipe(
                    map((resp: any) => {
                        return new ExistAgencySuccess(resp.data);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    //   --------------------- CAMPAIGN PART ------------------------

    getCampaignName$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_CAMPAIGN_NAME),
            switchMap((action: FetchCampaignName) => {
                let payload = {
                    ...action.filtersPayload,
                    path: 'v1/marketing/campaigns',
                };
                return this.commonService.getModuleListByAdvFilter(payload, true).pipe(
                    map((resp: any) => {
                        if (resp) {
                            return new FetchCampaignNameSuccess(resp);
                        }
                        return new FetchCampaignNameSuccess(resp);
                    }),
                    catchError((err) => {
                        this._notificationService.error(`Error in fetching campaign`);
                        return of(new OnError(err));
                    })
                );
            })
        )
    );

    addCampaignName$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.ADD_CAMPAIGN_NAME),
            map((action: AddCampaignName) => action),
            switchMap((data: any) => {
                return this.manageMarketingService.addCampaignName(data.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            this._notificationService.success(`Campaign added successfully.`);
                            return [
                                new AddCampaignNameSuccess(resp.data),
                                new FetchCampaignName({
                                    path: 'v1/marketing/campaigns',
                                    PageNumber: 1,
                                    PageSize: 10,
                                }),
                            ];
                        }
                        return [new AddCampaignNameSuccess(resp.data)];
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateCampaignName$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.UPDATE_CAMPAIGN_NAME),
            switchMap((action: UpdateCampaignName) => {
                return this.manageMarketingService
                    .updateCampaignName(action.payload)
                    .pipe(
                        switchMap((resp: any) => {
                            if (resp) {
                                return this.store.select(getCampaignFiltersPayload).pipe(
                                    take(1),
                                    switchMap((payload: any) => {
                                        this._notificationService.success(
                                            `Campaign updated successfully.`
                                        );
                                        return [
                                            new UpdateCampaignNameSuccess(resp),
                                            new FetchCampaignName(payload),
                                        ];
                                    })
                                );
                            }
                            return [new UpdateCampaignNameSuccess(resp)];
                        }),
                        catchError((err) => {
                            this._notificationService.error(`Error in updating campaign`);
                            return of(new OnError(err));
                        })
                    );
            })
        )
    );

    deleteCampaign$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.DELETE_CAMPAIGN),
            switchMap((action: DeleteCampaign) =>
                this.manageMarketingService.deleteCampaign(action.id).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return this.store.select(getCampaignFiltersPayload).pipe(
                                take(1),
                                switchMap((payload: any) => {
                                    this._notificationService.success(`Deleted successfully.`);
                                    return [
                                        new DeleteCampaignSuccess(resp),
                                        new FetchCampaignName(payload),
                                    ];
                                })
                            );
                        }
                        return [new DeleteCampaignSuccess(resp)];
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    exportCampaign$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXPORT_CAMPAIGN),
            switchMap((action: ExportCampaign) => {
                return this.manageMarketingService.exportCampaign(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                'Campaign(s) is/are being exported in excel format'
                            );
                            return new ExportCampaignSuccess();
                        }
                        return new ExportCampaignSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getCampaignExportStatusList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXPORT_CAMPAIGN_TRACKER),
            map((action: FetchCampaignExportTracker) => action),
            switchMap((data: any) => {
                return this.manageMarketingService
                    .getCampaignExportStatus(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchCampaignExportTrackerSuccess(resp);
                            }
                            return new FetchCampaignExportTrackerSuccess(resp);
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    // ---------------------- Bulk Upload ----------------------
    addBulkCampaignExcel$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.CAMPAIGN_EXCEL_UPLOAD),
            switchMap((action: CampaignExcelUpload) => {
                return this.manageMarketingService
                    .uploadCampaignExcel(action.file)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                this._notificationService.success(
                                    'Campaign Excel uploaded Successfully'
                                );
                                return new CampaignExcelUploadSuccess(resp.data);
                            } else {
                                this._store.dispatch(new CloseModal());
                                this._notificationService.warn(`${resp.message}`);
                                return new FetchCampaignName(resp);
                            }
                        }),
                        catchError((err: any) => {
                            throwError(err);
                            Array.isArray(err?.error?.errors.AgencyNames)
                                ? this._notificationService.error(
                                    err?.error?.errors.AgencyNames[0]
                                )
                                : this._notificationService.error(err?.error?.errors?.messages);
                            return throwError(() => err);
                        })
                    );
            })
        )
    );

    getCampaignExcelUploadedList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_CAMPAIGN_EXCEL_UPLOADED_LIST),
            map((action: FetchCampaignExcelUploadedList) => action),
            switchMap((data: any) => {
                return this.manageMarketingService
                    .getCampaignExcelUploadedList(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchCampaignExcelUploadedSuccess(resp);
                            }
                            return new FetchCampaignExcelUploadedSuccess();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    uploadCampaignMappedColumns$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.UPLOAD_CAMPAIGN_MAPPED_COLUMNS),
            switchMap((action: UploadCampaignMappedColumns) => {
                return this.manageMarketingService
                    .uploadCampaignMappedColumns(action.payload)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                if (resp.data) {
                                    if (resp.data?.excelUrl) {
                                        const dataCount = resp.message?.DataCount || '';
                                        this._notificationService.success(
                                            `${dataCount} Invalid Data Not Uploaded`
                                        );
                                        return new CampaignExcelUploadSuccess(resp.data);
                                    } else {
                                        this._notificationService.success(
                                            'Excel Uploaded Successfully'
                                        );
                                        return new CampaignExcelUploadSuccess(resp.data);
                                    }
                                } else {
                                    this._notificationService.error(resp.message);
                                }
                                return new FetchCampaignName(resp);
                            }
                            return new FetchCampaignNameSuccess(resp);
                        }),
                        catchError((err: any) => {
                            Array.isArray(err?.error?.messages)
                                ? this._notificationService.error(err.error.messages[0])
                                : this._notificationService.error(err?.error?.messages);
                            return throwError(() => err);
                        })
                    );
            })
        )
    );

    existCampaign$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXIST_CAMPAIGN),
            map((action: ExistCampaign) => action.agencyName),
            switchMap((data: any) => {
                return this.manageMarketingService.existCampaignName(data).pipe(
                    map((resp: any) => {
                        return new ExistCampaignSuccess(resp.data);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );
    //   -------------------- CHANNEL PARTNER PART ----------------------



    fetchChannelPartner$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_CHANNEL_PARTNER),
            switchMap((action: FetchChannelPartner) => {
                return this.commonService
                    .getModuleListByAdvFilter(action.payload, true)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchChannelPartnerSuccess(resp);
                            }
                            return new FetchChannelPartnerSuccess(resp);
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    fetchChannelPartnerLocation$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_CHANNEL_PARTNER_LOCATION),
            switchMap((action: FetchChannelPartnerLocations) => {
                return this.manageMarketingService.getChannelPartnerLocation().pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return [new FetchChannelPartnerLocationsSuccess(resp.data)];
                        }
                        return [new FetchChannelPartnerLocationsSuccess(resp.data)];
                    }),
                    catchError((err) => {
                        this._notificationService.error(`Error in adding channel partner.`);
                        return of(new OnError(err));
                    })
                );
            })
        )
    );

    addChannelPartner$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.ADD_CHANNEL_PARTNER),
            switchMap((action: AddChannelPartnerName) => {
                return this.manageMarketingService
                    .addChannelPartner(action.payload)
                    .pipe(
                        switchMap((resp: any) => {
                            if (resp) {
                                this._notificationService.success(
                                    'Channel Partner added successfully.'
                                );
                                return [
                                    new AddChannelPartnerNameSuccess(resp.data),
                                    new FetchChannelPartner({
                                        path: 'v1/marketing/channelpartners',
                                        PageNumber: 1,
                                        PageSize: 10,
                                    }),
                                ];
                            }
                            return [new AddChannelPartnerNameSuccess(resp.data)];
                        }),
                        catchError((err) => {
                            return of(new OnError(err));
                        })
                    );
            })
        )
    );

    deleteChannelPartner$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.DELETE_CHANNEL_PARTNER),
            switchMap((action: DeleteChannelPartner) =>
                this.manageMarketingService.deleteChannelPartner(action.id).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return this.store
                                .select(getChannelPartnerNameFiltersPayload)
                                .pipe(
                                    take(1),
                                    switchMap((payload: any) => {
                                        this._notificationService.success(`deleted successfully.`);
                                        return [
                                            new DeleteChannelPartnerSuccess(resp),
                                            new FetchChannelPartner(payload),
                                        ];
                                    })
                                );
                        }
                        return [new DeleteChannelPartnerSuccess(resp)];
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    updateChannelPartner$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.UPDATE_CHANNEL_PARTNER),
            switchMap((action: UpdateChannelPartner) =>
                this.manageMarketingService.updateChannelPartner(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return this.store
                                .select(getChannelPartnerNameFiltersPayload)
                                .pipe(
                                    take(1),
                                    switchMap((payload: any) => {
                                        this._notificationService.success(
                                            `Channel Partner updated successfully.`
                                        );
                                        return [
                                            new UpdateChannelPartnerSuccess(resp),
                                            new FetchChannelPartner(payload),
                                        ];
                                    })
                                );
                        }
                        return [new UpdateChannelPartnerSuccess(resp)];
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    // ---------------------- Bulk Upload ----------------------
    addBulkChannelPartnerExcel$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.CHANNEL_PARTNER_EXCEL_UPLOAD),
            switchMap((action: ChannelPartnerExcelUpload) => {
                return this.manageMarketingService
                    .uploadChannelPartnerExcel(action.file)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                this._notificationService.success(
                                    'Channel Partner Excel uploaded Successfully'
                                );
                                return new ChannelPartnerExcelUploadSuccess(resp.data);
                            } else {
                                this._store.dispatch(new CloseModal());
                                this._notificationService.warn(`${resp.message}`);
                                return new FetchChannelPartner({
                                    path: 'v1/marketing/channelpartners',
                                    PageNumber: 1,
                                    PageSize: 10,
                                });
                            }
                        }),
                        catchError((err: any) => {
                            throwError(err);
                            Array.isArray(err?.error?.errors.AgencyNames)
                                ? this._notificationService.error(
                                    err?.error?.errors.AgencyNames[0]
                                )
                                : this._notificationService.error(err?.error?.errors?.messages);
                            return throwError(() => err);
                        })
                    );
            })
        )
    );

    getChannelPartnerExcelUploadedList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.FETCH_CP_EXCEL_UPLOADED_LIST),
            map((action: FetchChannelPartnerExcelUploadedList) => action),
            switchMap((data: any) => {
                return this.manageMarketingService
                    .getChannelPartnerExcelUploadedList(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchChannelPartnerExcelUploadedSuccess(resp);
                            }
                            return new FetchChannelPartnerExcelUploadedSuccess();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    uploadChannelPartnerMappedColumns$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.UPLOAD_CHANNEL_PARTNER_MAPPED_COLUMNS),
            switchMap((action: UploadChannelPartnerMappedColumns) => {
                return this.manageMarketingService
                    .uploadChannelPartnerMappedColumns(action.payload)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                if (resp.data) {
                                    if (resp.data?.excelUrl) {
                                        const dataCount = resp.message?.DataCount || '';
                                        this._notificationService.success(
                                            `${dataCount} Invalid Data Not Uploaded`
                                        );
                                        return new ChannelPartnerExcelUploadSuccess(resp.data);
                                    } else {
                                        this._notificationService.success(
                                            'Excel Uploaded Successfully'
                                        );
                                        return new ChannelPartnerExcelUploadSuccess(resp.data);
                                    }
                                } else {
                                    this._notificationService.error(resp.message);
                                }
                                return new FetchChannelPartner({
                                    path: 'v1/marketing/channelpartners',
                                    PageNumber: 1,
                                    PageSize: 10,
                                });
                            }
                            return new FetchChannelPartnerSuccess(resp);
                        }),
                        catchError((err: any) => {
                            Array.isArray(err?.error?.messages)
                                ? this._notificationService.error(err.error.messages[0])
                                : this._notificationService.error(err?.error?.messages);
                            return throwError(() => err);
                        })
                    );
            })
        )
    );

    exportCPUsers$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXPORT_CHANNEL_PARTNER),
            switchMap((action: ExportChannelPartner) => {
                return this.manageMarketingService.exportCP(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                'Channel Partner(s) is/are being exported in excel format'
                            );
                            return new ExportChannelPartnerSuccess();
                        }
                        return new ExportChannelPartnerSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getCPExportStatusList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXPORT_CHANNEL_PARTNER_TRACKER),
            map((action: FetchChannelPartnerExportTracker) => action),
            switchMap((data: any) => {
                return this.manageMarketingService
                    .getCPExportStatus(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchChannelPartnerExportTrackerSuccess(resp);
                            }
                            return new FetchChannelPartnerExportTrackerSuccess();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    existChannelPArtner$ = createEffect(() =>
        this.actions$.pipe(
            ofType(MarketingAction.EXIST_CHANNEL_PARTNER),
            map((action: ExistChannelPartner) => action.CPName),
            switchMap((data: any) => {
                return this.manageMarketingService.existChannelPartnerName(data).pipe(
                    map((resp: any) => {
                        return new ExistChannelPartnerSuccess(resp.data);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private _store: Store<AppState>,
        private actions$: Actions,
        private _notificationService: NotificationsService,
        private store: Store<AppState>,
        private sharedDataService: ShareDataService,
        private router: Router,
        private manageMarketingService: ManageMarketingService,
        private commonService: CommonService,
        public modalRef: BsModalRef,
        private modalService: BsModalService,
    ) { }
}
