import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { FetchCustomStatusSuccess, StatusActionTypes } from './status.actions';
export type CustomStatusState = {
    customStatus: CustomStatus[],
    isCustomStatusLoading: boolean,
};
export interface CustomField {
    isRequired: boolean
    value: any
    field: Field
    validators?: string[]
}
export interface Field {
    lastModifiedOn: string
    createdOn: string
    createdBy: string
    lastModifiedBy: string
    id: string
    name: string
    orderRank: number
    module: string
    notes: any
}
export type CustomStatus = {
    customFields: CustomField[];
    isLrbStatus: boolean;
    id: string;
    baseId?: any;
    level: number;
    status: string;
    orderRank: number;
    displayName: string;
    actionName: string;
    masterLeadStatusId?: any;
    isDefault: boolean;
    isDefaultChild: boolean;
    childTypes: any[];
    shouldUseForBooking: boolean;
    shouldUseForBookingCancel: boolean;
    shouldUseForInvoice: boolean;
};
const initialState: CustomStatusState = {
    customStatus: [],
    isCustomStatusLoading: true,
};


export function customStatusReducer(
    state: CustomStatusState = initialState,
    action: Action
): CustomStatusState {
    switch (action.type) {
        case StatusActionTypes.FETCH_CUSTOM_STATUS:
            return {
                ...state,
                isCustomStatusLoading: true,
            };
        case StatusActionTypes.FETCH_CUSTOM_STATUS_SUCCESS:
            return {
                ...state,
                customStatus: (action as FetchCustomStatusSuccess).response,
                isCustomStatusLoading: false,
            };
        default:
            return state;
    }
}
export const selectFeature = (state: AppState) => state.customStatus;

export const getCustomStatusList = createSelector(
    selectFeature,
    (state: CustomStatusState) => state.customStatus
);

export const getCustomStatusListIsLoading = createSelector(
    selectFeature,
    (state: CustomStatusState) => state.isCustomStatusLoading
);
