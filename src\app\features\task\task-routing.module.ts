import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { TaskComponent } from './task.component';
import { ManageTaskComponent } from 'src/app/features/task/manage-task/manage-task.component';

const routes: Routes = [
  {
    path: '',
    component: TaskComponent,
    children: [
      { path: '', redirectTo: 'manage-task', pathMatch: 'full' },
      { path: 'manage-task', component: ManageTaskComponent }
    ],
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TaskRoutingModule {}
