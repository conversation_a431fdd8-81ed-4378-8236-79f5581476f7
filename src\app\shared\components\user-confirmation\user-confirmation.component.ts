import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'user-confirmation',
  templateUrl: './user-confirmation.component.html',
})
export class UserConfirmationComponent implements OnInit {
  message: string;
  confirmType: string;
  title:any;
  fieldType: string;

  currentItemId: string;
  currentLang: string = localStorage.getItem('locale') || 'en';
  constructor(
    public translate: TranslateService,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) {
    this.translate.use(this.currentLang);
  }

  ngOnInit(): void {
    this.translate.get(this.message).subscribe((res: string) => {
      this.message = res;
    });
  }

  deleteConfirm() {
    this.modalService.setDismissReason('confirmed');
    this.modalRef.hide();
  }

  closeModal() {
    this.modalRef.hide();
  }
}
