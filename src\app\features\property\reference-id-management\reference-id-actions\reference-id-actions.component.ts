import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, firstValueFrom, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { assignToSort } from 'src/app/core/utils/common.util';
import { FetchPriorityList } from 'src/app/reducers/automation/automation.actions';
import { getPriorityList } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { DeleteReferenceId } from 'src/app/reducers/reference-id-management/reference-id-management.action';
import { getAdminsAndReportees, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { AddReferenceIdComponent } from '../add-reference-id/add-reference-id.component';

@Component({
  selector: 'reference-id-actions',
  templateUrl: './reference-id-actions.component.html',
})
export class ReferenceIdActionsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  globalSettings: any;
  permissions: any;
  allUserList: any;
  allActiveUsers: any;
  activeUsers: any
  canEnableAllowDuplicates: any;
  canEnableAllowSecondaryUsers: any;
  userList: any;
  moduleId: string;

  constructor(
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    private _store: Store<AppState>,
  ) { }

  agInit(params: any): void {
    this.params = params;
  }

  edit() {
    this.modalService.show(AddReferenceIdComponent, {
      class: 'right-modal modal-400 ip-modal-unset',
      initialState: {
        referenceId: this.params.data,
      }
    }
    );
  }

  async assignUsers(assignModal: any) {
    this._store.dispatch(new FetchPriorityList());
    this._store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'ReferenceId');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
    const globalSettings: any = await firstValueFrom(this._store.select(getGlobalSettingsAnonymous));
    this.canEnableAllowDuplicates =
      globalSettings?.duplicateFeatureInfo?.isFeatureAdded;
    this.canEnableAllowSecondaryUsers = globalSettings?.isDualOwnershipEnabled;

    this.permissions = new Set(await firstValueFrom(this._store.select(getPermissions)));
    const data: any = await firstValueFrom(this._store.select(getUsersListForReassignment).pipe(filter((data: any) => data?.length)));

    this.allUserList = data;
    this.allActiveUsers = data?.filter((user: any) => user.isActive).map((user: any) => ({
      ...user,
      fullName: user.firstName + ' ' + user.lastName,
    }));

    this.allActiveUsers = assignToSort(this.allActiveUsers, '');
    if (!this.permissions.has('Permissions.Users.AssignToAny')) {
      this.userList = await firstValueFrom(this._store.select(getAdminsAndReportees).pipe(filter((data: any) => data?.length)));

      this.activeUsers = this.userList
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));

      this.activeUsers = assignToSort(this.activeUsers, '');
    }
    this.modalRef = this.modalService.show(assignModal, {
      class: 'ip-modal-unset modal-dialog right-modal',
    })

  }

  delete() {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: this.params?.data?.referenceId,
      fieldType: 'reference Id',
    };

    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this._store.dispatch(new DeleteReferenceId(this.params?.data?.id));
        }
      });
    }
  }

  ngOnInit(): void {

  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }

}
