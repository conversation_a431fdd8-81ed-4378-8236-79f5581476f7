import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import {
    AddInvoice,
    AddInvoiceSuccess,
    FetchInvoiceById,
    FetchInvoiceByIdSuccess,
    InvoiceActionTypes,
} from 'src/app/reducers/invoice/invoice.actions';
import { InvoiceService } from 'src/app/services/controllers/invoice.service';

@Injectable()
export class InvoiceEffects {
    constructor(
        private actions$: Actions,
        private invoiceService: InvoiceService,
        private _notificationService: NotificationsService,
        private router: Router
    ) { }

    addInvoice$ = createEffect(() =>
        this.actions$.pipe(
            ofType(InvoiceActionTypes.ADD_INVOICE),
            map((action: AddInvoice) => action),
            switchMap((data: any) => {
                return this.invoiceService.addInvoiceData(data.payload).pipe(
                    map((resp: any) => {
                        if (resp) {
                            this._notificationService.success(`Invoice added successfully.`);
                            if (data.isGoToInvoice) {
                                this.router.navigate(['/invoice']);
                            }
                            return new AddInvoiceSuccess(resp.data);
                        }
                        return new AddInvoiceSuccess({});
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    fetchInvoices = createEffect(() =>
        this.actions$.pipe(
            ofType(InvoiceActionTypes.FETCH_INVOICES_BY_ID),
            map((action: FetchInvoiceById) => action.id),
            switchMap((leadId: any) => {
                return this.invoiceService.getInvoiceData(leadId).pipe(
                    map((resp: any) => {
                        if (resp) {
                            return new FetchInvoiceByIdSuccess(resp.data);
                        }
                        return new FetchInvoiceByIdSuccess('');
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );
}
