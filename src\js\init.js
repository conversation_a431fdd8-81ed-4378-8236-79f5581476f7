(function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) return;
    js = d.createElement(s); js.id = id;
    js.src = "https://connect.facebook.net/en_US/sdk.js";
    fjs.parentNode.insertBefore(js, fjs);
  }(document, 'script', 'facebook-jssdk'));

  // initialize the facebook sdk

  window.fbAsyncInit = function() {
    FB.init({
      appId      : '1491491247883558',
      cookie     : false,  // enable cookies to allow the server to access
                          // the session
      xfbml      : true,  // parse social plugins on this page
      version    : 'v15.0' // The Graph API version to use for the call
    });
}


// 892934951397001
// 1491491247883558
// 399398615137370 --- real one
