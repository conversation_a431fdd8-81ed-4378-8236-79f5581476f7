import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AttendanceActionTypes,
  ClockIn,
  ClockOut,
  ExportAttendance,
  ExportAttendanceSuccess,
  FetchAttendanceExportStatus,
  FetchAttendanceExportStatusSuccess,
  FetchAttendanceList,
  FetchAttendanceListById,
  FetchAttendanceListByIdSuccess,
  FetchAttendanceListNoAuth,
  FetchAttendanceListNoAuthSuccess,
  FetchAttendanceListSuccess,
  FetchAttendanceSetting,
  FetchAttendanceSettingSuccess,
  FetchNotification,
  FetchNotificationSuccess,
  UpdateAttendanceSetting,
  UpdateAttendanceSettingSuccess,
} from 'src/app/reducers/attendance/attendance.actions';
import { getFiltersPayload, getFiltersPayloadNoAuth } from 'src/app/reducers/attendance/attendance.reducer';
import { AttendanceService } from 'src/app/services/controllers/attendance.service';
import { CommonService } from 'src/app/services/shared/common.service';

@Injectable()
export class AttendanceEffects {
  getAttendanceList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.FETCH_ATTENDANCE_LIST),
      map((action: FetchAttendanceList) => action),
      switchMap((data: any) => {
        let filterPayload: any = {};
        this._store.select(getFiltersPayload).subscribe((data: any) => {
          filterPayload = data;
        });
        const currentPath = window.location.pathname;
        if (!currentPath.includes('/attendance')) {
          if (!filterPayload?.Year) {
            filterPayload.Year = (new Date()).getFullYear();
          }
          if (!filterPayload?.Month) {
            filterPayload.Month = (new Date())?.getMonth() + 1;
          }
        }
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAttendanceListSuccess(resp);
            }
            return new FetchAttendanceListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAttendanceListNoAuth$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.FETCH_ATTENDANCE_LIST_NO_AUTH),
      map((action: FetchAttendanceListNoAuth) => action),
      switchMap((data: any) => {
        let filterPayload;
        this._store.select(getFiltersPayloadNoAuth).subscribe((data: any) => {
          filterPayload = data;
        });
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAttendanceListNoAuthSuccess(resp);
            }
            return new FetchAttendanceListNoAuthSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAttendanceListById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.FETCH_ATTENDANCE_LIST_BY_ID),
      map((action: FetchAttendanceListById) => action),
      switchMap((action: FetchAttendanceListById) => {
        return this.api.getAttendanceListById(action.id, action.timeZone).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAttendanceListByIdSuccess(resp.data);
            }
            return new FetchAttendanceListByIdSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  clockIn$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.CLOCK_IN),
      map((action: ClockIn) => action),
      switchMap((data: any) => {
        return this.api.clockIn(data.payload, data.component).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Clocked in successfully');
              this._store.dispatch(new FetchAttendanceListById(this.userId, data.payload?.timeZone));
              if (data.moduleName != 'header') {
                this._store.dispatch(new FetchAttendanceList());
              }
            }
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    ), { dispatch: false }
  );

  clockOut$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.CLOCK_OUT),
      map((action: ClockOut) => action),
      switchMap((data: any) => {
        return this.api.clockOut(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Clocked out successfully');
              this._store.dispatch(new FetchAttendanceListById(this.userId, data.payload?.timeZone));
              if (data.moduleName != 'header') {
                this._store.dispatch(new FetchAttendanceList());
              }
            }
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          }));
      })
    ), { dispatch: false }
  );

  exportAttendance$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.EXPORT_ATTENDANCE),
      switchMap((action: ExportAttendance) => {
        return this.api.exportAttendance(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Attendance are being exported in excel format`
              );
              return new ExportAttendanceSuccess(resp);
            }
            return new ExportAttendanceSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getExportAttendanceStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.FETCH_ATTENDANCE_EXPORT_STATUS),
      map((action: FetchAttendanceExportStatus) => action),
      switchMap((data: any) => {
        return this.api.getExportAttendanceStatus(data.payload, data?.pageNumber, data?.pageSize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAttendanceExportStatusSuccess(resp);
            }
            return new FetchAttendanceExportStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAttendanceSetting$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.FETCH_ATTENDANCE_SETTING),
      map((action: FetchAttendanceSetting) => action),
      switchMap((data: any) => {
        return this.api.getAttendanceSetting().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAttendanceSettingSuccess(resp);
            }
            return new FetchAttendanceSettingSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  UpdateAttendanceSetting$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.UPDATE_ATTENDANCE_SETTING),
      switchMap((action: UpdateAttendanceSetting) => {
        return this.api.updateSetting(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Updated successfully.`);
              this._store.dispatch(new FetchAttendanceSetting());
              return new UpdateAttendanceSettingSuccess(resp);
            }
            return new UpdateAttendanceSettingSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getNotification$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AttendanceActionTypes.FETCH_NOTIFICATION),
      map((action: FetchNotification) => action),
      switchMap((data: any) => {
        return this.api.getNotification().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchNotificationSuccess(resp);
            }
            return new FetchNotificationSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;

  constructor(
    private actions$: Actions,
    private api: AttendanceService,
    private _notificationService: NotificationsService,
    private _store: Store<AppState>,
    private commonService: CommonService
  ) { }
}
