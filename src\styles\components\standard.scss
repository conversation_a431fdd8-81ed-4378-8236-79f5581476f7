.flex-col {
  @extend .d-flex, .flex-column;
}

.flex-center {
  @extend .align-center, .justify-content-center;
}

.flex-center-col {
  @extend .flex-center, .flex-column;
}

.flex-around {
  @extend .align-center, .justify-content-around;
}

.flex-between {
  @extend .align-center, .justify-content-between;
}

.flex-between-col {
  @extend .flex-between, .flex-column;
}

.flex-start {
  @extend .align-center, .justify-content-start;
}

.flex-end {
  @extend .align-center, .justify-content-end;
}

.align-start {
  @extend .d-flex, .align-items-start;
}

.align-center {
  @extend .d-flex, .align-items-center;
}

.align-center-col {
  @extend .align-center, .flex-column;
}

.align-end {
  @extend .d-flex, .align-items-end;
}

.align-end-col {
  @extend .align-end, .flex-column;
}

.justify-center {
  @extend .d-flex, .justify-content-center;
}

.justify-center-col {
  @extend .justify-center, .flex-column;
}

.justify-around {
  @extend .d-flex, .justify-content-around;
}

.justify-between {
  @extend .d-flex, .justify-content-between;
}

.justify-between-col {
  @extend .justify-between, .flex-column;
}

.justify-end {
  @extend .d-flex, .justify-content-end;
}

.field-label {
  @extend .text-large, .mt-20, .mb-4;
  font-weight: 600;
}

.field-label-underline {
  @extend .field-label, .position-relative, .flex-center;

  &:after {
    content: "";
    @extend .position-absolute, .nbottom-4, .w-70;
    border: 1px solid $primary-black;
  }
}

.field-label-underline-green {
  @extend .field-label-underline;

  &:after {
    border: 1px solid $accent-green-100;
  }
}

.label-underline {
  @extend .field-label-underline, .label;
}

.field-label-req {
  @extend .field-label;
  position: relative;
  display: inline-block;

  &::after {
    font-family: "leadRat" !important;
    content: "\e923";
    color: $red-10;
    font-size: 7px;
    position: absolute;
    top: -2px;
    right: -10px;
  }
}

.field-label-clear-m {
  @extend .field-label, .text-nowrap;
  margin-top: 0 !important;
}

.field-label-clear-m-req {
  @extend .field-label-req, .text-nowrap;
  margin-top: 0 !important;
}

.label-req {
  @extend .fw-semi-bold, .text-large, .text-black-100, .mt-16, .mb-4;
  position: relative;
  display: inline-block;

  &::after {
    font-family: "leadRat" !important;
    content: "\e923";
    color: $red-10;
    font-size: 7px;
    position: absolute;
    top: -2px;
    right: -10px;
  }
}

.label {
  @extend .fw-semi-bold, .text-large, .text-black-100, .mt-16, .mb-4;
}

.form-group {
  @extend .bg-white, .position-relative;
  border-radius: 5px;
  --bs-bg-opacity: unset;

  input,
  textarea {
    @extend .w-100, .text-normal, .text-coal, .p-12;
    border-radius: 5px;

    &:focus {
      outline: none !important;
      box-shadow: none !important;
    }

    &::placeholder {
      @extend .fw-semi-bold, .text-sm;
      color: $slate-40;
    }
  }
}

.remove-form-group {
  .form-group {
    border: unset !important;
    padding: unset !important;
  }
}

textarea {
  &.textarea-noresize {
    resize: none;
  }
}

.field-rupees-tag {
  input {
    padding-left: 70px !important;
  }

  .rupees {
    @extend .border-gray,
    .position-absolute,
    .top-0,
    .left-0,
    .br-5,
    .bg-light-slate;
    margin: 3px;
    padding: 17px 24px 16px;
  }

  .rupees-sm {
    @extend .rupees;
    padding: 12px 24px 13px;
  }
}

.box-radio {
  .btn {
    @extend .text-sm;
    line-height: 18px;
    padding: 7px 14px;
    background-color: $white;
    border-radius: 5px;
    border: 1px solid $dark-400 !important;
  }

  .btn-outline {
    @extend .btn, .btn-sm, .btn-outline-secondary;
    margin: 0px 4px 4px 0px;
  }

  .btn-check:checked+.btn {
    background: $accent-green;
    border: 1px solid $accent-green !important;
    box-shadow: 0px 4px 4px #50bea733 !important;
  }

  &.black {
    .btn-check:checked+.btn {
      background: $black-100;
      border: 1px solid $black-100 !important;
      box-shadow: 0px 4px 4px #00000033 !important;
    }
  }
}

.oval-radio {
  .btn {
    @extend .text-sm, .text-dark-gray;
    line-height: 12px;
    padding: 8px 14px;
    background-color: $white;
    border-radius: 18px;
    border: 1px solid $dark-400 !important;
  }

  .btn-outline {
    @extend .btn, .btn-sm, .btn-outline-secondary, .flex-center;
    margin: 0px 6px 6px 0px;
  }

  .btn-check:checked+.btn {
    background: $black-100;
    color: $white !important;
    border: 1px solid $black-100 !important;
  }
}

.box-radio-lg {
  @extend .box-radio;

  .btn {
    @extend .br-10;
  }

  .btn-outline {
    color: $slate-40;
    height: 60px;
    width: 48%;
    margin: 0px 8px 0px 0px;
  }
}

.img-radio {
  .btn {
    @extend .clear-padding;
    border-radius: 8px;
    border: 1px solid $dark-400 !important;
    filter: grayscale(100%);
    background-color: $dark-400;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;
    height: unset;

    img {
      mix-blend-mode: hard-light;
      border-radius: 8px;
    }
  }

  .text-label {
    @extend .position-absolute, .text-xs;
    top: 5px;
    left: 5px;
  }

  .btn-check+.btn:hover {
    background-color: $dark-400;
  }

  .btn-check:checked+.btn {
    @extend .text-coal, .fw-700;
    filter: unset;
    background: unset;
    border: unset !important;
    box-shadow: 0px 12px 15px -10px #afb2c6 !important;

    img {
      mix-blend-mode: unset;
    }

    &.residential {
      box-shadow: 0px 12px 15px -10px #c1d5db !important;
    }

    &.commercial {
      box-shadow: 0px 12px 15px -10px #91c9e1 !important;
    }

    &.agricultural {
      box-shadow: 0px 12px 15px -10px #e0d751 !important;
    }
  }
}

.round-radio {
  .btn {
    @extend .clear-padding, .flex-center-col, .rounded-circle;
    filter: grayscale(100%);
    background-color: $dark-400;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
    width: 40px;
  }

  .text-label {
    @extend .position-absolute, .top-50px, .text-nowrap, .text-xs;
  }

  .btn-check+.btn:hover {
    background-color: $dark-400;
  }

  .btn-check:checked+.btn {
    @extend .text-coal;
    filter: unset;
    background: unset;
    border: unset !important;

    &.orange {
      background-color: #fbd6b5;
    }

    &.pink {
      background-color: #ffced2;
    }

    &.yellow {
      background-color: #fff3ce;
    }
  }
}

.add-image {
  @extend .cursor-pointer, .border-green-dashed, .flex-center-col, .bg-white;
  border-radius: 8px;
  background-image: url(../../assets/images/square-pattern.svg);
  background-size: cover;
}

.gallery-cover {
  @extend .position-absolute,
  .fw-600,
  .text-xxxs,
  .text-white,
  .align-center,
  .bg-black-9;
  border-radius: 2px;
  padding: 1px 3px;
  top: 6px;
  left: 3px;

  &:hover {
    background-color: $yellow-400;
    cursor: pointer;
  }
}

.gallery-cover-selected {
  @extend .gallery-cover;
  background-color: $yellow-400;
}

.ngx-file-drop__content {
  height: unset !important;
  color: $accent-green !important;
}

.prop-phone {
  @extend .position-relative, .px-12, .pt-16;
  border: 5px solid $primary-black;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  border-bottom: 0;
}

.neg-block {
  @extend .flex-between,
  .bg-accent-green,
  .br-50px,
  .w-70px,
  .text-white,
  .fw-600,
  .text-xxxs;
  padding: 2px 6px;
  margin-top: 2px;
}

.checkbox-container {
  @extend .d-block, .position-relative, .pl-24, .cursor-pointer, .text-large;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  input {
    @extend .position-absolute, .d-none;

    &:checked~.checkmark {
      background-color: $accent-green !important;
      border: 1px solid $green-30;

      &:after {
        @extend .d-block;
      }
    }

    &:disabled~.checkmark {
      opacity: 0.5;
      background-color: transparent !important;
    }
  }

  &:hover input~.checkmark,
  &:disabled {
    background-color: $border-light-grey;
  }

  .checkmark {
    @extend .position-absolute, .left-0, .br-4, .text-white;
    height: 16px;
    width: 16px;
    background-color: transparent;
    border: 1px solid $slate-60;

    &:after {
      @extend .d-none, .text-sm;
      font-family: "leadRat" !important;
      content: "\e906";
      padding: 1px 0px 0px 2px;
    }

    &.select-all:after {
      font-family: "leadRat" !important;
      content: "\e97d";
    }
  }
}

.spin-btn-container {
  @extend .bg-white, .br-4, .w-90, .flex-between, .p-4;

  .spin-btn {
    @extend .dot, .dot-lg, .bg-light-pearl, .br-4, .cursor-pointer;

    .spin-ic {
      @extend .icon, .ic-xxxs, .ic-coal;
    }
  }

  input {
    @extend .fw-600, .border-0, .text-center;
    padding: 0 !important;
  }
}

.spin-btn-container-gray {
  @extend .spin-btn-container;
  background-color: $slate-20 !important;

  .spin-btn {
    background-color: $white !important;
  }

  input {
    @extend .bg-light-pearl;
  }
}

.lead-preview {
  .bg-lead-info {
    @extend .bg-white;
  }
}

.lead-status {
  .bg-lead-info {
    @extend .bg-light-pearl;
  }
}

.shadow-hover:hover {
  @extend .shadow;
}

.shadow-hover-sm:hover {
  @extend .shadow-sm;
}

.gray-scale {
  filter: grayscale(100%);
}

.bg-mail {
  background-image: url(../../assets/images/email.svg);
  background-position: right bottom;
  background-repeat: no-repeat;
}

.bg-phone {
  background-image: url(../../assets/images/phone.svg);
  background-position: right bottom;
  background-repeat: no-repeat;
}

.otp-block {
  @extend .w-35px,
  .h-45px,
  .br-4,
  .bg-light-pearl,
  .text-center,
  .border-0,
  .outline-0;
}

.non-editable {

  .form-group,
  input {
    background-color: $dark-500 !important;
  }
}

.adv-filter {
  .field-label {
    margin-top: 12px !important;
  }
}

.lead-adv-filter {
  .lead {
    .form-group input {
      padding: 9px 8px 8px 65px !important;
    }
  }
}

.property-adv-filter {
  .form-group input {
    padding: 8px !important;
  }

  .field-rupees-tag {
    input {
      padding-left: 66px !important;
    }
  }
}

.qr-code {
  .field-rupees-tag {
    input {
      padding-left: 65px !important;
    }
  }
}

.dashboard-filter.form-group input {
  padding: 6px !important;
}

.dashboard-filter.h-padding.form-group input {
  padding: 7px 6px 6px !important;
}

.input-sm {
  input {
    padding: 8px !important;
  }
}