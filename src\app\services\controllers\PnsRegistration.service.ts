import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PnsService {
  baseURL: string = `${env.baseURL}api/v1/pnsregistration`;
  private httpClient: HttpClient;

  constructor(private http: HttpClient) {
  }

  sendToken(payload: any) {
    return this.http.put(`${this.baseURL}/device-registration`, payload)
  }

  deviceInfo(payload: any) {
    return this.http.post(`${this.baseURL}/device-info`, payload);
  }

  deleteDevice(deviceId: any) {
    return this.http.delete(`${this.baseURL}/${deviceId}`);
  }

}
