import { Component, EventEmitter, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, setTimeZoneDate } from 'src/app/core/utils/common.util';
import { UpdateDataActivityFilterPayload } from 'src/app/reducers/data-reports/data-reports.action';
import { UpdateActivityFilterPayload, UpdateAllActivityFilterPayload } from 'src/app/reducers/reports/reports.actions';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'activity-dashboard-report',
  templateUrl: './activity-dashboard-report.component.html',
})
export class ActivityDashboardReportComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedSection: string = 'All';
  userData: any;
  currentDate: Date = new Date();

  constructor(private _store: Store<AppState>,) { }

  ngOnInit(): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset);
      });
  }

  handleSelection(section: string) {
    this.selectedSection = section;
  }
  ngOnDestroy(): void {
    this._store.dispatch(new UpdateActivityFilterPayload({
      pageNumber: 1,
      pageSize: 50,
      path: 'report/activity/level9',
      fromDate: setTimeZoneDate(new Date(this.currentDate), this.userData?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(new Date(this.currentDate), this.userData?.timeZoneInfo?.baseUTcOffset)
    }))
    this._store.dispatch(new UpdateDataActivityFilterPayload({
      pageNumber: 1,
      pageSize: 50,
      path: 'datareport/activity/communication',
      fromDate: setTimeZoneDate(new Date(this.currentDate), this.userData?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(new Date(this.currentDate), this.userData?.timeZoneInfo?.baseUTcOffset)
    }))
    this._store.dispatch(new UpdateAllActivityFilterPayload({
      pageNumber: 1,
      pageSize: 50,
      path: 'report/activity/level9',
      fromDate: setTimeZoneDate(new Date(this.currentDate), this.userData?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(new Date(this.currentDate), this.userData?.timeZoneInfo?.baseUTcOffset)
    }))
  }
}
