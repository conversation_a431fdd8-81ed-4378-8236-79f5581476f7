import { Component, EventE<PERSON>ter, On<PERSON><PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from '@microsoft/signalr';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { skipWhile, take, takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getPages, getSystemTimeOffset, getSystemTimeZoneId } from 'src/app/core/utils/common.util';
import { MemberActionsComponent } from 'src/app/features/teams/manage-team/team-members/member-actions/member-actions.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  DeleteMember,
  FetchTeamMember,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAllTeamMembers,
  getAllTeamMembersIsLoading,
  getBulkDeleteMemberIsLoading,
  getUserBasicDetails,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'team-members',
  templateUrl: './team-members.component.html',
})
export class TeamMembersComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  public pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  currOffset: number = 0;
  gridOptions: any;
  userData: any;
  gridApi: any;
  searchTerm: string;
  gridColumnApi: any;
  rowData: any = [];
  defaultColDef: any;
  selectedPageSize: number;
  defaultColumns: any[];
  columns: any[];
  selectedMember: any;
  teamName: any;
  managerName: any;
  canView: boolean = false;
  canDelete: boolean = false;
  canExport: boolean = false;
  bulkDeleteIsLoading: boolean = false;
  teamMemberIsLoading: boolean = false;
  allData: any;
  teamId: string;
  getPages = getPages;

  dataTotalCount: number;
  filtersPayload: any = {
    path: 'team/id',
  };
  constructor(
    private gridOptionsService: GridOptionsService,
    private headerTitle: HeaderTitleService,
    private store: Store<AppState>,
    private route: ActivatedRoute,
    public modalService: BsModalService,
    private modalRef: BsModalRef,
    public trackingService: TrackingService
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridOptions.rowData = this.rowData;
  }
  ngOnInit(): void {
    this.headerTitle.setLangTitle('Manage Members');
    this.teamId = this.route.snapshot.paramMap.get('id');
    this.selectedPageSize = 10;
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canExport = permissionsSet.has('Permissions.Teams.Export');
        this.canView = permissionsSet.has('Permissions.Teams.View');
        this.canDelete = permissionsSet.has('Permissions.Teams.Delete');
        this.initializeGridSettings();
      });

    this.filtersPayload = {
      ...this.filtersPayload,
      Id: this.teamId,
    };
    this.store.dispatch(new FetchTeamMember(this.filtersPayload));

    this.store
      .select(getAllTeamMembers)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const items = data?.items || [];

        if (items.length > 0) {
          const firstItem = items[0];
          this.teamName = firstItem.name;
          this.managerName = firstItem.manager?.name;
          this.allData = firstItem.users.map((user: any) => ({
            ...user,
            teamId: firstItem.id,
            teamName: firstItem.name,
          }));

          const currentPage = Math.max(this.currOffset, 0);
          const pageSize = this.selectedPageSize;
          const startRow = currentPage * pageSize;
          const endRow = startRow + pageSize;

          this.rowData = this.allData.slice(startRow, endRow);

          // Adjust pagination if needed
          if (this.rowData.length === 0 && this.currOffset > 0) {
            this.currOffset = Math.max(this.currOffset - 1, 0);
            const newStartRow = this.currOffset * pageSize;
            this.rowData = this.allData.slice(newStartRow, newStartRow + pageSize);
          }

          this.dataTotalCount = this.allData.length;
        } else {
          this.rowData = [];
          this.dataTotalCount = 0;
        }
      });

    this.store
      .select(getAllTeamMembersIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: any) => {
        this.teamMemberIsLoading = loading;
        // this.currOffset = 0;
      });
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.columnDefs = [
      {
        headerName: 'Member Name',
        field: 'Member Name',
        resizable: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.name || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="text-truncate-1 break-all">${value}</p>`;
        },
        sortable: true,
      },
      {
        headerName: 'Designation',
        field: 'Designation',
        resizable: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.designation?.name || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="text-truncate-1 break-all">${value}</p>`;
        },
        sortable: true,
      },
      {
        headerName: 'Department',
        field: 'Department',
        resizable: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.department?.name || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="text-truncate-1 break-all">${value}</p>`;
        },
        sortable: true,
      },
      {
        headerName: 'Number of leads',
        field: 'Number of leads',
        resizable: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.leadsCount || 0,
        cellRenderer: (params: any) => {
          const value = params.value || 0;
          return `<p>${value}</p>`;
        },
        sortable: true,
      },
      {
        headerName: 'Number of Data',
        field: 'Number of Data',
        resizable: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.prospectsCount || 0,
        cellRenderer: (params: any) => {
          const value = params.value || 0;
          return `<p>${value}</p>`;
        },
        sortable: true,
      },
      {
        headerName: 'Work Phone Number',
        field: 'Work Phone Number',
        resizable: true,
        hide: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.contactNo || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="text-truncate-1 break-all">${value}</p>`;
        },
        sortable: true,
      },
      {
        headerName: 'Work Email',
        field: 'Work Email',
        resizable: true,
        hide: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.email || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="text-truncate-1 break-all">${value}</p>`;
        },
        sortable: true,
      },
      {
        headerName: 'Work Location',
        field: 'Work Location',
        resizable: true,
        hide: true,
        autoHeight: true,
        valueGetter: (params: any) => params.data?.officeAddress || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="text-truncate-1 break-all">${value}</p>`;
        },
        sortable: true,
      },
    ];

    if (this.canDelete) {
      this.gridOptions.columnDefs.unshift({
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        resizable: false,
        maxWidth: 50,
      });
      this.gridOptions.columnDefs.push({
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 150,
        maxWidth: 150,
        menuTabs: [],
        filter: false,
        suppressMovable: true,
        lockPosition: 'right',
        valueGetter: (params: any) => [this.pageSize, this.searchTerm],
        cellRenderer: MemberActionsComponent,
        cellRendererParams: {
          teamId: (params: any) => {
            return params.data?.teamId;
          },
        },
      });
    }
    this.gridOptions.context = {
      componentParent: this,
    };

  }


  onGridReady(params: any) {
    this.gridApi = params.api;
    params.api.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
    this.gridApi.setRowData(this.rowData);
    this.columns = this.gridColumnApi.getColumns();
    this.columns = this.columns.map((column: any) => {
      return {
        label: column.getColDef().headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(3, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myColumnStateMember'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('manage-member-columns')?.split(',');
    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
    this.trackingService.trackFeature(`Web.ManageMembers.Button.Default.Click`)
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-member-columns', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem('myColumnStateMember', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.currOffset = 0;
    const currentPage = this.currOffset;
    const pageSize = this.selectedPageSize;
    const startRow = currentPage * pageSize;
    const endRow = startRow + pageSize;
    this.rowData = this.allData.slice(startRow, endRow);
    this.gridApi.setRowData(this.rowData);
    this.trackingService.trackFeature(`Web.ManageMembers.Option.${pageSize}.Click`)
  }

  onPageChange(e: any) {
    this.currOffset = e;
    const currentPage = this.currOffset;
    const pageSize = this.selectedPageSize;
    const startRow = currentPage * pageSize;
    const endRow = startRow + pageSize;
    this.rowData = this.allData.slice(startRow, endRow);
    this.gridApi.setRowData(this.rowData);
  }

  onSearch(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      if (!this.searchTerm || this.searchTerm.trim() === '') {
        this.gridApi.setRowData(this.allData);
        this.dataTotalCount = this.allData?.length;
      } else {
        const searchTermLower = this.searchTerm.trim().toLowerCase();
        const filteredData = this.allData?.filter(
          (item: any) =>
            item.name && item.name.toLowerCase().includes(searchTermLower)
        );
        this.gridApi.setRowData(filteredData);
        this.dataTotalCount = filteredData?.length;
      }
      this.trackingService.trackFeature(`Web.ManageMembers.DataEntry.Search.DataEntry`)
    }
  }

  isEmptyInput() {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.gridApi.setRowData(this.rowData);
      this.dataTotalCount = this.allData?.length;
    }
  }

  exportTeams() {
    this.trackingService.trackFeature(`Web.ManageMembers.Button.Export.Click`)
    let initialState: any = {
      payload: {
        path: 'team/id',
        teamId: this.teamId,
        timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    this.selectedMember = this.gridApi
      ?.getSelectedNodes()
      .map((member: any) => {
        return member.data;
      });
    let initialState: any = {
      data: this.selectedMember,
      class: 'right-modal modal-300',
    };
    this.modalRef = this.modalService.show(BulkDeleteModal, initialState);
  }

  removeMember(id: string): void {
    const node = this.gridApi
      ?.getSelectedNodes()
      ?.filter((team: any) => team?.data?.id === id);
    this.gridApi?.deselectNode(node?.[0]);

    this.selectedMember = this.selectedMember?.filter(
      (team: any) => team?.id !== id
    );
    if (this.selectedMember?.length <= 0) {
      this.modalRef.hide();
    }
  }

  bulkDelete(): void {
    if (this.modalRef) this.modalRef.hide();
    let ids: any = [];
    this.selectedMember?.map((member: any) => ids.push(member.id));
    const payload = {
      teamId: this.selectedMember[0]?.teamId,
      userIds: this.selectedMember?.map((member: any) => member?.id),
    };
    this.store.dispatch(new DeleteMember(payload));
    this.store
      .select(getBulkDeleteMemberIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.bulkDeleteIsLoading = isLoading;
        this.modalRef.hide();
      });
  }

  openConfirmDeleteModal(teamName: string, teamId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: teamName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeMember(teamId);
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
