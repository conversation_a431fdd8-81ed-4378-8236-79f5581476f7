import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
  FetchAllCityListSuccess,
  FetchAllLocationsSuccess,
  FetchAllZoneListSuccess,
  FetchCityListSuccess,
  FetchCountrySuccess,
  FetchInternationalLocationsSuccess,
  FetchLocationListSuccess,
  FetchLocationsWithGoogleSuccess,
  FetchZoneListSuccess,
  LocationExcelUploadSuccess,
  SiteActionTypes,
  UpdateCityFiltersPayload,
  UpdateLocationFiltersPayload,
  UpdateStateFiltersPayload,
  UpdateZoneFiltersPayload,
} from './site.actions';

export type SiteState = {
  locationList: Array<any>;
  locationFiltersPayload: any;
  allCityList: Array<any>;
  cityList: Array<any>;
  allStateList: Array<any>;
  allCountryList: Array<any>;
  cityFiltersPayload: any;
  stateFiltersPayload: any;
  countryFiltersPayload: any;
  allZoneList: Array<any>;
  zoneList: Array<any>;
  zoneFiltersPayload: any;
  allLocations: Array<any>;
  placesListWithGoogle: Array<any>;
  isPlacesListWithGoogleLoading: boolean;
  isAllCountryListLoading: boolean;
  isAllLocationsLoading: boolean;
  locationExcelUpload: Array<any>;
  internationalLocations: Array<any>;
  isInternationalLocationLoading: boolean;
};

const initialState: SiteState = {
  locationList: [],
  allCityList: [],
  cityList: [],
  allStateList: [],
  allCountryList: [],
  cityFiltersPayload: {
    path: 'site/city',
    SearchText: null,
  },
  stateFiltersPayload: {
    path: 'site/state',
    SearchText: null,
  },
  countryFiltersPayload: {
    path: 'site/country',
    SearchText: null,
  },
  allZoneList: [],
  zoneFiltersPayload: {
    path: 'site/zone',
    SearchText: null,
  },
  zoneList: [],
  locationFiltersPayload: {
    path: 'site/location',
    SearchText: null,
  },
  allLocations: [],
  placesListWithGoogle: [],
  isPlacesListWithGoogleLoading: false,
  isAllLocationsLoading: false,
  isAllCountryListLoading: false,
  locationExcelUpload: [],
  internationalLocations: [],
  isInternationalLocationLoading: false,
};

export function siteReducer(
  state: SiteState = initialState,
  action: Action
): SiteState {
  switch (action.type) {
    case SiteActionTypes.FETCH_LOCATION_LIST_SUCCESS:
      return {
        ...state,
        locationList: (action as FetchLocationListSuccess).response,
      };
    case SiteActionTypes.UPDATE_LOCATION_FILTERS_PAYLOAD:
      return {
        ...state,
        locationFiltersPayload: (action as UpdateLocationFiltersPayload).payload,
      };
    case SiteActionTypes.FETCH_ALL_CITY_LIST_SUCCESS:
      return {
        ...state,
        allCityList: (action as FetchAllCityListSuccess).response || [],
      };
    case SiteActionTypes.FETCH_CITY_LIST_SUCCESS:
      return {
        ...state,
        cityList: (action as FetchCityListSuccess).response,
      };
    case SiteActionTypes.UPDATE_CITY_FILTERS_PAYLOAD:
      return {
        ...state,
        cityFiltersPayload: (action as UpdateCityFiltersPayload).payload,
      };
    case SiteActionTypes.FETCH_ALL_ZONE_LIST_SUCCESS:
      return {
        ...state,
        allZoneList: (action as FetchAllZoneListSuccess).response || [],
      };
    case SiteActionTypes.FETCH_ZONE_LIST_SUCCESS:
      return {
        ...state,
        zoneList: (action as FetchZoneListSuccess).response,
      };
    case SiteActionTypes.UPDATE_ZONE_FILTERS_PAYLOAD:
      return {
        ...state,
        zoneFiltersPayload: (action as UpdateZoneFiltersPayload).payload,
      };
    case SiteActionTypes.FETCH_ALL_LOCATIONS:
      return {
        ...state,
        isAllLocationsLoading: true,
      };
    case SiteActionTypes.FETCH_ALL_LOCATIONS_SUCCESS:
      return {
        ...state,
        allLocations: (action as FetchAllLocationsSuccess).response,
        isAllLocationsLoading: false,
      };
    case SiteActionTypes.FETCH_LOCATIONS_WITH_GOOGLE_API:
      return {
        ...state,
        isPlacesListWithGoogleLoading: true,
      };
    case SiteActionTypes.FETCH_LOCATIONS_WITH_GOOGLE_API_SUCCESS:
      return {
        ...state,
        placesListWithGoogle: (action as FetchLocationsWithGoogleSuccess).response || [],
        isPlacesListWithGoogleLoading: false,
      };
    case SiteActionTypes.LOCATION_EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        locationExcelUpload: (action as LocationExcelUploadSuccess).resp,
      };
    case SiteActionTypes.REMOVE_LOCATIONS_WITH_GOOGLE_API:
      return {
        ...state,
        placesListWithGoogle: [],
      };
    case SiteActionTypes.FETCH_STATE_SUCCESS:
      return {
        ...state,
        allStateList: (action as FetchAllCityListSuccess).response || [],
      };
    case SiteActionTypes.FETCH_COUNTRY:
      return {
        ...state,
        isAllCountryListLoading: true,
      };
    case SiteActionTypes.FETCH_COUNTRY_SUCCESS:
      return {
        ...state,
        allCountryList: (action as FetchCountrySuccess).response || [],
        isAllCountryListLoading: false
      };
    case SiteActionTypes.UPDATE_STATE_FILTERS_PAYLOAD:
      return {
        ...state,
        stateFiltersPayload: (action as UpdateStateFiltersPayload).payload,
      };
    case SiteActionTypes.UPDATE_COUNTRY_FILTERS_PAYLOAD:
      return {
        ...state,
        countryFiltersPayload: (action as UpdateStateFiltersPayload).payload,
      };
    case SiteActionTypes.FETCH_INTERNATIONAL_LOCATIONS:
      return {
        ...state,
        isInternationalLocationLoading: true,
      };
    case SiteActionTypes.FETCH_INTERNATIONAL_LOCATIONS_SUCCESS:
      return {
        ...state,
        internationalLocations: (action as FetchInternationalLocationsSuccess).response || [],
        isInternationalLocationLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.site;

export const getLocationList = createSelector(
  selectFeature,
  (state: SiteState) => state.locationList
);

export const getLocationFiltersPayload = createSelector(
  selectFeature,
  (state: SiteState) => {
    return state.locationFiltersPayload;
  }
);

export const getAllCityList = createSelector(
  selectFeature,
  (state: SiteState) => state.allCityList
);

export const getCityList = createSelector(
  selectFeature,
  (state: SiteState) => state.cityList
);

export const getCityFiltersPayload = createSelector(
  selectFeature,
  (state: SiteState) => {
    return state.cityFiltersPayload;
  }
);

export const getAllZoneList = createSelector(
  selectFeature,
  (state: SiteState) => state.allZoneList
);

export const getZoneList = createSelector(
  selectFeature,
  (state: SiteState) => state.zoneList
);

export const getZoneFiltersPayload = createSelector(
  selectFeature,
  (state: SiteState) => {
    return state.zoneFiltersPayload;
  }
);

export const getAllLocations = createSelector(
  selectFeature,
  (state: SiteState) => state.allLocations
);

export const getAllLocationsIsLoading = createSelector(
  selectFeature,
  (state: SiteState) => state.isAllLocationsLoading,
);

export const getLocationsWithGoogleApi = createSelector(
  selectFeature,
  (state: SiteState) => state.placesListWithGoogle
);

export const getLocationsWithGoogleApiIsLoading = createSelector(
  selectFeature,
  (state: SiteState) => state.isPlacesListWithGoogleLoading
);

export const getLocationExcel = createSelector(
  selectFeature,
  (state: SiteState) => state.locationExcelUpload
);

export const getStateList = createSelector(
  selectFeature,
  (state: SiteState) => state.allStateList
);
export const getStateFiltersPayload = createSelector(
  selectFeature,
  (state: SiteState) => {
    return state.stateFiltersPayload;
  }
);


export const getCountryList = createSelector(
  selectFeature,
  (state: SiteState) => state.allCountryList
);

export const getCountryListLoading = createSelector(
  selectFeature,
  (state: SiteState) => state.isAllCountryListLoading
);

export const getCountryFiltersPayload = createSelector(
  selectFeature,
  (state: SiteState) => state.countryFiltersPayload
);
export const getInternationalLocations = createSelector(
  selectFeature,
  (state: SiteState) => state.internationalLocations
);

export const getInternationLocationsIsLoading = createSelector(
  selectFeature,
  (state: SiteState) => state.isInternationalLocationLoading,
);