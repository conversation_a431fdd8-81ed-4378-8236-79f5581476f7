import { Component, EventEmitter, Input } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import {
  lockInPeriodList,
  noticePeriodList,
  securityDepositDates,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getAreaUnit } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'about-property',
  templateUrl: './about-property.component.html',
})
export class AboutPropertyComponent {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() propertyInfo: any;
  @Input() isPropertySoldOut: boolean;
  chevron: AnimationOptions = {
    path: 'assets/animations/chevron-down.json',
  };
  isView: boolean = true;
  @Input() areaSizeUnits: any;
  getAreaUnit = getAreaUnit;
  s3BucketPath: string = env.s3ImageBucketURL;
  securityDepositDates: any = securityDepositDates;
  lockInPeriodList: any = lockInPeriodList;
  noticePeriodList: any = noticePeriodList;
  globalSettingsDetails: any;
  isViewlisting: any;
  get lockInPeriod() {
    if (this.propertyInfo.lockInPeriod && lockInPeriodList) {
      return lockInPeriodList.find(
        (item: any) => item.value === this.propertyInfo.lockInPeriod
      )?.label;
    }
    return '';
  }
  get noticePeriod() {
    if (this.propertyInfo.noticePeriod && noticePeriodList) {
      return noticePeriodList.find(
        (item: any) => item.value === this.propertyInfo.noticePeriod
      )?.label;
    }
    return '';
  }

  get secuirtyDepositDate() {
    if (this.propertyInfo.securityDeposit && securityDepositDates) {
      return securityDepositDates.find(
        (item: any) => item.value === this.propertyInfo.securityDeposit
      )?.label;
    }
    return '';
  }

  constructor(private sanitizer: DomSanitizer,
    private store: Store<AppState>
  ) { }

  ngOnInit() {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
      });
  }

  getSanitizedHtml(html: string) {
    return this.sanitizer.bypassSecurityTrustHtml(html.replace(/\n/g, '<br>'));
  }
}
