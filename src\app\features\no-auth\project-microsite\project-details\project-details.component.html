<div class="mr-24">
    <div class="w-100 d-flex flex-wrap">
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10" *ngIf="projectInfo?.status != 0">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-ring"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">Project Status</div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200 text-truncate-1 break-all">
                    {{ProjectStatus[projectInfo?.status]}}</h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10" *ngIf="projectInfo?.possessionDate">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-key"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">Possession date
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200">
                    <span *ngIf="moment(projectInfo?.possessionDate) <= currentDate else date">
                        {{ 'PROPERTY.ready-to-move' | translate }}
                    </span>
                    <ng-template #date>
                        <span>{{ getTimeZoneDate(projectInfo.possessionDate, userBasicDetails?.timeZoneInfo?.baseUTcOffset, 'monthYear')  }}</span>
                    </ng-template>
                </h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10"
            *ngIf="!projectInfo?.facings?.includes(0) && projectInfo?.facings?.length">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-compass"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">{{projectInfo?.facings?.length>1?'Facings':'Facing'}}
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200 text-truncate-1 break-all d-flex flex-wrap">
                    {{facingList(projectInfo?.facings)}}
                </h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10"
            *ngIf="projectInfo?.projectType?.childType?.displayName">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-search-house"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">Project Sub Type
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200 text-truncate-1 break-all">
                    {{projectInfo?.projectType?.childType?.displayName}}
                </h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10" *ngIf="projectInfo?.area">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-diamond"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">Project Land Area
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200"> {{projectInfo?.area}}
                    {{getAreaUnit(projectInfo?.areaUnitId, areaSizeUnits)?.unit}}
                </h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10" *ngIf="projectInfo?.startDate">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-calendar-minus"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">Start Date
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200"> {{getTimeZoneDate(projectInfo?.startDate, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')}}
                </h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10" *ngIf="projectInfo?.endDate">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-calendar-minus"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">End Date
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200">{{ getTimeZoneDate(projectInfo?.endDate, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')}}
                </h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10" *ngIf="sortedUniqueNoOfBHK?.length">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-cot"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">Bedrooms
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200 flex-wrap">
                    <ng-container *ngFor="let bhk of sortedUniqueNoOfBHK; let last = last">
                        {{ getBHKDisplayString(bhk, true) }}{{ !last ? ',' : '' }}
                    </ng-container>BHK
                </h5>
            </div>
        </div>
        <div class="align-center w-20 tb-w-25 ip-w-33 ph-w-50 mt-10" *ngIf="projectInfo?.certificates">
            <div class="dot dot-lg bg-ash mr-6">
                <div class="icon ic-black-200 ic-xxs ic-certificate"></div>
            </div>
            <div>
                <div class="text-sm text-light-gray">Certificate
                </div>
                <h5 class="mt-4 mr-10 fw-700 text-black-200 text-truncate-1 break-all"> {{projectInfo?.certificates}}
                </h5>
            </div>
        </div>
    </div>
    <ng-container *ngIf="projectInfo?.description">
        <h4 class="fw-600 text-black-200 mt-20 text-decoration-underline">About Project</h4>
        <h6 class="fw-semi-bold text-black-200 mt-10">{{projectInfo?.description}}</h6>
    </ng-container>
    <ng-container *ngIf="projectInfo?.associatedBanks.length">
        <h4 class="fw-600 text-black-200 mt-20 text-decoration-underline">Associated Banks</h4>
        <marquee direction="left" behavior="scroll" onmouseover="stop();" onmouseout="start();">
            <div class="d-flex mt-10">
                <ng-container *ngFor="let bank of projectInfo?.associatedBanks">
                    <img *ngIf="bank.imageUrl" class="mr-30 obj-fill h-20px" [type]="'leadrat'" [appImage]="bank.imageUrl" [alt]="bank.name">
                    <div *ngIf="!bank.imageUrl" class="mr-30 bg-gray-dark br-4 px-12 flex-center h-20px fw-700">{{
                        bank.name }}</div>
                </ng-container>
            </div>
        </marquee>
    </ng-container>
    <div class="border-bottom my-10"></div>
</div>
<ng-container *ngIf="projectInfo?.links?.length">
    <h4 class="fw-600 text-decoration-underline mt-10">Project URL(s)</h4>
    <div class="align-center flex-wrap">
        <div *ngFor="let link of projectInfo?.links">
            <a href="{{link}}" target="_blank"
                class="flex-center px-8 py-4 border-accent-green br-20 mt-4 mr-8 shadow-hover bg-slate">
                <span class="icon ic-link ic-xs ic-black"></span><u class="break-all">{{link}}</u>
            </a>
        </div>
    </div>
</ng-container>