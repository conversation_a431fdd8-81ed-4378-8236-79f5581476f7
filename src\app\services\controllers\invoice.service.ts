import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class InvoiceService {
  serviceBaseUrl: string;
  constructor(private http: HttpClient) {
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}`;
  }

  getInvoiceData(leadId:string) {
    return this.http.get(`${this.serviceBaseUrl}lead/booked-details/${leadId}`);
  }

  addInvoiceData(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}lead/booked/${payload.leadId}`, payload);
  }

}
