<div class="mt-4 justify-between">
    <div title="Edit" class="bg-accent-green icon-badge" (click)="edit()">
        <span class="icon ic-pen m-auto ic-xxs" id="clkEdit" data-automate-id="clkEdit"></span>
    </div>
    <div title="Assign To" class="bg-blue-800 icon-badge" (click)="assignUsers(assign)">
        <span class="icon ic-assign-to m-auto ic-xxs" id="clkAssign" data-automate-id="clkAssign"></span>
    </div>
    <div title="Delete" class="bg-light-red icon-badge" (click)="delete()">
        <span class="icon ic-delete m-auto ic-xxs" id="clkAssign" data-automate-id="clkAssign"></span>
    </div>
</div>

<ng-template #assign>
    <form class="h-100vh text-coal">
        <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
            <h3 class="fw-semi-bold">Reference Assignment</h3>
            <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalRef.hide()"></div>
        </div>
        <div class="p-16 scrollbar min-w-350 max-w-350 h-100-108">
            <integration-assignment [isReferenceId]="true" [isBulkAssignModel]="true"
                [updatedIntegrationList]="updatedIntegrationList" [moduleId]="moduleId"
                [canAssignToAny]="permissions.has('Permissions.Users.AssignToAny')" [allActiveUsers]="allActiveUsers"
                [activeUsers]="activeUsers" [selectedAccountId]="params?.data?.id"
                [canEnableAllowDuplicates]="canEnableAllowDuplicates"
                [canEnableAllowSecondaryUsers]="canEnableAllowSecondaryUsers" [allUserList]="allUserList"
                [userList]="userList" (isShowAssignModalChanged)="modalRef.hide()"
                [selectedAccountName]="params?.data?.referenceId" [moduleId]="moduleId"></integration-assignment>
        </div>
    </form>
</ng-template>