<div class="bg-coal w-100 px-20 py-12 text-white brtl-10 brtr-10 flex-between">
    <h3 class="fw-semi-bold"><span class="text-capitalize">{{fieldType}}</span> - {{ 'LEADS.excel-upload-tracker' |
        translate }}</h3>
    <button class="btn btn-sm btn-linear-green align-center" (click)="updateTrackerList()">
        <span class="ic-refresh icon ic-xxs mr-8 ph-mr-0"></span>
        <span class="text-white text-normal ph-d-none">{{'BULK_LEAD.refresh-data' | translate}}</span>
    </button>
    <a class="ic-close-secondary ic-close-modal tb-ic-close-secondary" (click)="modalService.hide()"></a>
</div>
<div *ngIf="!isDataExcelListLoading; else gridLoader" class="max-h-100-176 scrollbar">
    <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize"
        [gridOptions]="gridOptions" [rowData]="rowData" [suppressPaginationPanel]="true"
        (gridReady)="onGridReady($event)"></ag-grid-angular>
</div>
<div *ngIf="!isDataExcelListLoading" class="flex-end m-20">
    <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]="getPages(totalUploadedCount,pageSize)"
        (pageChange)="onPageChange($event)">
    </pagination>
</div>
<ng-template #gridLoader>
    <div class="flex-center h-300">
        <application-loader></application-loader>
    </div>
</ng-template>