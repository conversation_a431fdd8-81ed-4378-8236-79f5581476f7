import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
    providedIn: 'root',
})
export class FieldsService extends BaseService<any> {
    serviceBaseUrl: string;

    constructor(private http: HttpClient) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    }

    getResourceUrl(): string {
        return 'fields';
    }

    getSelectedFields(){
        return this.http.get(`${this.serviceBaseUrl}/selected`);
    }

    updateForm(payload: any) {
        return this.http.put(`${this.serviceBaseUrl}/configure-lead-form`, payload);
    }

}