import {
  AfterViewInit,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  filter,
  skipWhile,
  take,
  takeUntil,
} from 'rxjs';

import { MAPS_KEY } from 'src/app/app.constants';
import { BHKType, EnquiryType, FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  getBHKDisplayString,
  getBRDisplayString,
  onPickerOpened,
  setTimeZoneDateWithTime,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  AddLeadProjects,
  FetchLeadAppointmentsByProjects,
  FetchLeadById,
  FetchLeadIdSuccess,
  FetchProjectList,
  MeetingOrVisitDone,
} from 'src/app/reducers/lead/lead.actions';
import {
  getActiveLead,
  getActiveLeadAppointments,
  getActiveLeadAppointmentsIsLoading,
  getActiveLeadIsLoading,
  getIsLeadCustomStatusEnabled,
  getIsMeetingOrVisitDoneLoading,
  getIsProjectAddIsLoading,
  getProjectList,
  getProjectListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { LeadPreviewSaved } from 'src/app/reducers/loader/loader.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'lead-appointment',
  templateUrl: './lead-appointment.component.html',
})
export class LeadAppointmentComponent
  implements OnInit, OnDestroy, AfterViewInit {
  data: any;
  closeModal: any;
  selectedProject: {
    id: string;
    name: string;
  };
  selectedProperty: {
    id: string;
    title: string;
  };
  EnquiryType = EnquiryType;
  BHKType = BHKType;
  formatBudget = formatBudget;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  appointments: any = {};
  appointmentsIsLoading: boolean = false;
  canShowDoneForm: boolean = false;
  projectsList: any = [];
  propertyList: any = [];
  propertyListMasterData: any = [];
  projectListMasterData: any = [];
  visitMeetingDetailsForm: FormGroup;
  siteVisitDoneById: string = null;
  documentsForm: FormGroup;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  preferredCountries = ['in'];
  @ViewChild('executivePhoneInput') executivePhoneInput: any;
  isManualLocation: boolean = false;
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  placesList: Array<Object> = [];
  currentLocation: string = '';
  defaultCurrency: string = 'INR';
  uploadedFiles: Array<string> = [];
  uploadedFilesName: Array<string> = [];
  fileFormatToBeUploaded: string =
    'application/pdf,image/x-png,image/gif,image/jpeg,image/tiff';
  selectedFile: string = '';
  isUpload: boolean = false;
  isNotesMandatoryMeeting: boolean;
  isNotesMandatorySiteVisit: boolean;
  isProjectsMandatorymeeting: boolean;
  isProjectsMandatorySiteVisit: boolean;
  isPropertyMandatorySiteVisit: boolean;
  isPropertyMandatorymeeting: boolean;
  isMeetingOrVisitDoneLoading: boolean;
  closeLeadPreviewModal: any;
  canShowAddProject: boolean = false;
  projectToAdd: string;
  addProjectsIsLoading: boolean = false;
  activeLeadIsLoading: boolean = false;
  projectListIsLaoding: boolean = true;
  propertyListIsLoading: boolean = true;
  isSaveAndUpdate: boolean = false;
  selectedFileName: string = null;
  previousProjectIndex: number = null;
  previousPropertyIndex: number = null;
  locationCategory: Array<{ dispName: string; value: string }> = [];
  selectedLocationType: string = 'google';
  getGlobalAnonymousIsLoading: boolean = true;
  isCustomStatusEnabled: boolean = false;
  globalSettingsData: any;
  activeUsers: any[] = [];
  filteredUsers: any[] = [];
  loggedInUserId = JSON.parse(localStorage.getItem('userDetails'))?.sub;

  minDate: Date;
  currentDate: Date = new Date();
  userBasicDetails: any;
  isPastDateSelectionEnabled: boolean = true;
  onPickerOpened = onPickerOpened;
  canUpdateLocation: boolean;
  isLocationMandatory: boolean = false;
  get addresses(): string {
    return (
      this.data?.enquiry?.addresses
        ?.map((address: any) =>
          address?.subLocality && address?.city
            ? address?.subLocality + ' ' + address?.city
            : address?.city || address?.subLocality
        )
        ?.join('; ') || '--'
    );
  }
  constructor(
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private formBuilder: FormBuilder,
    private s3UploadService: BlobStorageService,
    public modalRef: BsModalRef,
    private _notificationService: NotificationsService
  ) { }

  ngOnInit(): void {
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchUsersListForReassignment());

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        );
        this.minDate = this.currentDate;
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isPastDateSelectionEnabled = data?.isPastDateSelectionEnabled;
        this.isLocationMandatory = data?.generalSettings?.isLocationMandatory;
        this.updateLocationValidation();
      });

    const payload: any = {
      LeadId: this.data?.id,
      Projects: this.data?.projects?.map((project: any) => project.name),
    };

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.canUpdateLocation = permissions.includes('Permissions.UserLocation.Update');
        this.setLocationCategories();
      })

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((users: any) => {
        this.activeUsers = assignToSort(users, '');
        this.appointmentUsersList();
      });

    this._store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCustomStatusEnabled = isLoading;
      });

    navigator.geolocation.getCurrentPosition((position) => {
      const latitude = position.coords.latitude;
      const longitude = position.coords.longitude;

      // Call a reverse geocoding service (e.g., a REST API) to get the location name
      // Replace 'YOUR_API_KEY' with your actual API key if needed
      const apiKey = MAPS_KEY;
      const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;

      fetch(apiUrl)
        .then((response) => response.json())
        .then((data) => {
          // Assuming you want the formatted address
          const locationName = data.results[0]?.formatted_address;
          this.currentLocation = locationName;
        });
    });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.getGlobalAnonymousIsLoading = isLoading;
      });

    this._store.dispatch(new FetchLeadAppointmentsByProjects(payload));
    this._store
      .select(getActiveLeadAppointments)
      .pipe(takeUntil(this.stopper))
      .subscribe((appointments: any) => {
        appointments?.forEach((appointment: any) => {
          this.appointments[appointment.projectName] = appointment?.isDone;

          if (appointment?.projectName === this.selectedProject?.name) {
            this.siteVisitDoneById = appointment?.userId || null;
          }
        });
        this.selectedProjectChanged(
          this.data?.projects?.filter(
            (project: any) => this.appointments[project?.name] == undefined
          )?.[0] || this.data?.projects?.[0]
        );
      });
    this._store
      .select(getActiveLeadAppointmentsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.appointmentsIsLoading = isLoading;
      });

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectsList = data;
        this.projectListMasterData = data;
      });
    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.projectListIsLaoding = isLoading;
      });
    // this._store
    // .select(getPropertyList)
    // .pipe(takeUntil(this.stopper))
    // .subscribe((data: any) => {
    //   this.propertyListMasterData = data
    //   this.propertyList = data
    //     .slice()
    //     .sort((a: any, b: any) => a.localeCompare(b));
    // });
    // this._store
    // .select(getPropertyListIsLoading)
    // .pipe(takeUntil(this.stopper))
    // .subscribe((isLoading: boolean) => {
    //   this.propertyListIsLoading = isLoading;
    // });

    this._store.dispatch(new FetchLeadIdSuccess(this.data));
    this._store
      .select(getActiveLead)
      .pipe(takeUntil(this.stopper))
      .subscribe((activeLead: any) => {
        this.data = activeLead;
        this.appointmentUsersList();
        this.selectedProjectChanged(
          this.data?.projects?.filter(
            (project: any) => this.appointments[project?.name] == undefined
          )?.[0] || this.data?.projects?.[0]
        );
      });
    this._store
      .select(getActiveLeadIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.activeLeadIsLoading = isLoading;
      });

    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });

    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });
  }

  ngAfterViewInit() {
    this.visitMeetingDetailsForm = this.formBuilder.group({
      project: this.selectedProject?.name,
      userId: [null],
      notes: null,
      isManual: false,
      location: null,
      locality: null,
      city: null,
      state: null,
      country: null,
      appointmentDoneOn: null
    });

    this.documentsForm = this.formBuilder.group({
      docTitle: [''],
    });
    this.updateLocationValidation();

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code.toLowerCase()]
            : ['in']
          : ['in'];
        this.isNotesMandatoryMeeting =
          data?.leadNotesSetting?.isNotesMandatoryOnMeetingDone;
        this.isNotesMandatorySiteVisit =
          data?.leadNotesSetting?.isNotesMandatoryOnSiteVisitDone;
        this.isProjectsMandatorymeeting =
          data?.leadProjectSetting?.isProjectMandatoryOnMeetingDone;
        this.isProjectsMandatorySiteVisit =
          data?.leadProjectSetting?.isProjectMandatoryOnSiteVisitDone;
        // this.isPropertyMandatorymeeting = data?.leadPropertySetting?.isPropertyMandatoryOnMeetingDone;
        // this.isPropertyMandatorySiteVisit = data?.leadPropertySetting?.isPropertyMandatoryOnSiteVisitDone;
        if (this.data?.status?.displayName === 'Meeting Scheduled') {
          if (this.isNotesMandatoryMeeting) {
            this.visitMeetingDetailsForm.controls['notes']?.setValidators([
              Validators.required,
            ]);
          } else {
            this.visitMeetingDetailsForm.controls['notes']?.clearValidators();
          }
          if (this.isProjectsMandatorymeeting) {
            this.visitMeetingDetailsForm.controls['project']?.setValidators([
              Validators.required,
            ]);
          } else {
            this.visitMeetingDetailsForm.controls['project']?.clearValidators();
          }
          this.updateLocationValidation();
          // if (this.isPropertyMandatorymeeting) {
          //   this.visitMeetingDetailsForm.controls['property']?.setValidators([
          //     Validators.required,
          //   ])
          // } else {
          //   this.visitMeetingDetailsForm.controls['property']?.clearValidators();
          // }
        } else {
          if (this.isNotesMandatorySiteVisit) {
            this.visitMeetingDetailsForm.controls['notes']?.setValidators([
              Validators.required,
            ]);
          } else {
            this.visitMeetingDetailsForm.controls['notes']?.clearValidators();
          }
          if (this.isProjectsMandatorySiteVisit) {
            this.visitMeetingDetailsForm.controls['project']?.setValidators([
              Validators.required,
            ]);
          } else {
            this.visitMeetingDetailsForm.controls['project']?.clearValidators();
          }
          this.updateLocationValidation();
          // if (this.isPropertyMandatorySiteVisit) {
          //   this.visitMeetingDetailsForm.controls['property']?.setValidators([
          //     Validators.required,
          //   ])
          // } else {
          //   this.visitMeetingDetailsForm.controls['property']?.clearValidators();
          // }
        }
      });
  }

  setLocationCategories(): void {
    this.locationCategory = this.canUpdateLocation
      ? [
        { dispName: 'Auto', value: 'google' },
        { dispName: 'Search Location', value: 'search' },
        { dispName: 'Manual Location', value: 'manual' }
      ]
      : [{ dispName: 'Auto', value: 'google' }];
  }

  onLocationTypeChange(locationType: string): void {
    this.selectedLocationType = locationType;
    this.updateLocationValidation();
  }

  updateLocationValidation(): void {
    if (!this.visitMeetingDetailsForm) {
      return;
    }
    this.visitMeetingDetailsForm.controls['location']?.clearValidators();
    this.visitMeetingDetailsForm.controls['locality']?.clearValidators();
    this.visitMeetingDetailsForm.controls['city']?.clearValidators();
    this.visitMeetingDetailsForm.controls['state']?.clearValidators();
    this.visitMeetingDetailsForm.controls['country']?.clearValidators();
    if (this.isLocationMandatory) {
      if (this.selectedLocationType === 'search') {
        this.visitMeetingDetailsForm.controls['location']?.setValidators([
          Validators.required,
        ]);
      } else if (this.selectedLocationType === 'manual') {
        this.visitMeetingDetailsForm.controls['city']?.setValidators([
          Validators.required,
        ]);
        this.visitMeetingDetailsForm.controls['state']?.setValidators([
          Validators.required,
        ]);
        this.visitMeetingDetailsForm.controls['country']?.setValidators([
          Validators.required,
        ]);
        this.visitMeetingDetailsForm.controls['locality']?.setValidators([
          Validators.required,
        ]);
      }
    }
    this.visitMeetingDetailsForm.controls['location']?.updateValueAndValidity();
    this.visitMeetingDetailsForm.controls['locality']?.updateValueAndValidity();
    this.visitMeetingDetailsForm.controls['city']?.updateValueAndValidity();
    this.visitMeetingDetailsForm.controls['state']?.updateValueAndValidity();
    this.visitMeetingDetailsForm.controls['country']?.updateValueAndValidity();
  }

  filterProjects(canHaveCurretProject: boolean = false): void {
    this.projectsList = this.projectListMasterData?.filter(
      (project: any) =>
        !this.data?.projects?.some((item: any) => item?.name === project)
    );
    if (canHaveCurretProject && this.selectedProject?.name) {
      this.projectsList?.unshift(this.selectedProject?.name);
    }
  }

  addProject(filter: string = null) {
    const projectsToAdd =
      typeof this.projectToAdd === 'string'
        ? [this.projectToAdd]
        : this.projectToAdd;
    const payload: any = {
      leadId: this.data?.id,
      projects: Array.from([
        ...this.data?.projects?.map((project: any) => project?.name),
        ...projectsToAdd,
      ])?.filter((project: string) => project != filter),
    };

    this.previousProjectIndex = this.data?.projects
      ?.map((project: any) => project?.name)
      ?.indexOf(this.selectedProject?.name);
    if (filter) {
      delete this.appointments?.[filter];
      const index = this.data?.projects
        ?.map((project: any) => project?.name)
        ?.indexOf(filter);
      this.previousProjectIndex = index;
      if (index !== -1) {
        payload.projects = this.data.projects.map(
          (project: any) => project?.name
        );
        payload.projects[index] = projectsToAdd?.[0];
      }
    }

    this.addProjectsIsLoading = true;
    this.projectToAdd = null;
    this._store.dispatch(new AddLeadProjects(payload));
    this._store
      .select(getIsProjectAddIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.canShowAddProject = false;
        this.addProjectsIsLoading = false;
        this._store.dispatch(new FetchLeadById(this.data?.id));
        const payloadObj: any = {
          LeadId: this.data?.id,
          Projects: payload?.projects,
        };
        this.filterProjects();
        this._store.dispatch(new FetchLeadAppointmentsByProjects(payloadObj));
      });
  }

  getSelectedCountryCodeAlternateNo(): any {
    return this.executivePhoneInput?.selectedCountry;
  }

  contactNumberValidator(): ValidatorFn {
    let defaultCountry: any = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      const input = document.querySelector(
        '.executivePhoneInput > div > input'
      ) as HTMLInputElement;
      if (!input?.value?.length) {
        return null;
      }

      defaultCountry = this.getSelectedCountryCodeAlternateNo()?.dialCode;

      if (
        defaultCountry == '91' &&
        (input?.value?.length != 10 || +input?.value?.[0] < 6)
      ) {
        return { validatePhoneNumber: true };
      }

      try {
        const validNumber = isPossiblePhoneNumber(
          this.executivePhoneInput?.value || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  appointmentUsersList(): void {
    if (!this.data || !this.activeUsers) {
      this.filteredUsers = [];
      return;
    }
    const primaryOwnerId = this.data.assignTo;
    const secondaryOwnerId = this.data.secondaryUserId;
    this.filteredUsers = this.activeUsers.filter((user: any) => {
      return user.id === primaryOwnerId || user.id === secondaryOwnerId;
    });
  }

  updateNotDone(isDone: boolean) {
    let payload: any = {
      leadId: this.data?.id,
      isDone: isDone,
      meetingOrSiteVisit:
        this.data?.status?.status === 'meeting_scheduled' ? 1 : 2,
      projectName: this.selectedProject?.name,
    };
    if (
      this.isCustomStatusEnabled &&
      this.data?.status?.shouldOpenAppointmentPage
    ) {
      payload.meetingOrSiteVisit = this.data?.status?.shouldUseForMeeting
        ? 1
        : 2;
    }
    if (this.appointments?.[this.selectedProject?.name] != isDone) {
      this._store.dispatch(new MeetingOrVisitDone(payload));
      this.isMeetingOrVisitDoneLoading = true;
      this._store
        .select(getIsMeetingOrVisitDoneLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe((isLoading: boolean) => {
          this.isMeetingOrVisitDoneLoading = isLoading;
          this.appointments[this.selectedProject?.name] = isDone;
          this.navigateNextNotDone();
        });
    } else {
      this.navigateNextNotDone();
    }
  }

  navigateNextNotDone() {
    if (
      this.data?.projects?.indexOf(this.selectedProject) + 1 <
      this.data?.projects?.length
    )
      this.selectedProjectChanged(
        this.data?.projects?.[
        this.data?.projects?.indexOf(this.selectedProject) + 1
        ]
      );
    else if (
      (Object.keys(this.appointments)?.length == this.data?.projects?.length &&
        this.data?.projects?.indexOf(this.selectedProject) + 1 ==
        this.data?.projects?.length) ||
      !this.data?.projects?.length
    ) {
      this.openStatusModal();
    } else {
      this.closeModal();
    }
  }

  selectNextProject() {
    const index: number = this.data?.projects?.indexOf(this.selectedProject);
    index < this.data?.projects?.length - 1
      ? this.selectedProjectChanged(this.data?.projects[index + 1])
      : this.closeModal();
  }

  selectedProjectChanged(project: any) {
    this.visitMeetingDetailsForm?.reset();
    this.visitMeetingDetailsForm?.markAsPristine();
    this.visitMeetingDetailsForm?.markAsUntouched();
    this.selectedProject = project;
    this.filterProjects(true);
    this.canShowDoneForm = false;

    this._store.select(getActiveLeadAppointments).pipe(take(1)).subscribe((appointmentsData: any) => {
      const currentAppointment = appointmentsData?.find((appointment: any) => appointment.projectName === project?.name);
      this.siteVisitDoneById = currentAppointment?.userId || null;

      this.visitMeetingDetailsForm?.patchValue({
        project: project?.name,
        userId: this.siteVisitDoneById || null,
        notes: null,
        isManual: false,
        location: null,
        locality: null,
        city: null,
        state: null,
        country: null,
        appointmentDoneOn: null
      });
    });

    this.selectedLocationType = 'google';
    this.uploadedFiles = [];
    this.uploadedFilesName = [];
  }

  closeAppointmentModal() {
    this.closeModal();
  }

  onFileSelection(e: any) {
    this.selectedFile = e[0];
  }

  addDocument() {
    if (!this.documentsForm.valid || !this.selectedFile) {
      validateAllFormFields(this.documentsForm);
      return;
    }
    this.uploadedFiles.push(this.selectedFile);
    this.uploadedFilesName.push(
      this.documentsForm.value.docTitle || this.selectedFileName
    );
    this.isUpload = false;
    this.documentsForm.reset();
    this.selectedFile = null;
  }

  onClickRemoveDocument(docIndex: number) {
    this.uploadedFiles = this.uploadedFiles.filter(
      (img, index) => index !== docIndex
    );
    this.uploadedFilesName = this.uploadedFilesName.filter(
      (img, index) => index !== docIndex
    );
  }

  fileUploadToS3(
    isDone: boolean = false,
    isSaveAndClose: boolean = false,
    isSaveAndUpdate: boolean = false
  ) {
    this.isSaveAndUpdate = isSaveAndUpdate;
    this.previousProjectIndex = this.data?.projects
      ?.map((project: any) => project?.name)
      ?.indexOf(this.selectedProject?.name);
    this.updateLocationValidation();
    if (!this.visitMeetingDetailsForm?.valid) {
      validateAllFormFields(this.visitMeetingDetailsForm);
      return;
    }

    if (this.isLocationMandatory && this.selectedLocationType === 'google') {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (_position) => {
            this.proceedWithFileUpload(isDone, isSaveAndClose, isSaveAndUpdate);
          },
          (_error) => {
            this.triggerLocationAccessRequest(isDone, isSaveAndClose, isSaveAndUpdate);
          }
        );
        return;
      }
    }
    this.proceedWithFileUpload(isDone, isSaveAndClose, isSaveAndUpdate);
  }

  triggerLocationAccessRequest(
    isDone: boolean = false,
    isSaveAndClose: boolean = false,
    isSaveAndUpdate: boolean = false
  ) {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (_position) => {
          this.proceedWithFileUpload(isDone, isSaveAndClose, isSaveAndUpdate);
        },
        (_error) => {
          if (this.isLocationMandatory) {
            this._notificationService.warn(
              'Your location is off. Please turn on location access to save.',
            );
            return;
          } else {
            this.proceedWithFileUpload(isDone, isSaveAndClose, isSaveAndUpdate);
          }
        }
      );
    } else {
      if (this.isLocationMandatory) {
        return;
      } else {
        this.proceedWithFileUpload(isDone, isSaveAndClose, isSaveAndUpdate);
      }
    }
  }

  proceedWithFileUpload(
    isDone: boolean = false,
    isSaveAndClose: boolean = false,
    isSaveAndUpdate: boolean = false
  ) {
    this.isSaveAndUpdate = isSaveAndUpdate;
    if (this.isUpload) {
      this.addDocument();
    }
    if (this.uploadedFiles?.[0]?.includes('data:')) {
      this.s3UploadService
        .uploadImageBase64(this.uploadedFiles, FolderNamesS3.LeadDocument)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          if (response.data.length) {
            let documents: any;
            this.uploadedFilesName?.forEach((name: string, index: number) => {
              if (!documents) {
                documents = [];
              }
              documents.push({
                documentName: name,
                filePath: response.data[index].toString(),
              });
            });
            this.onSaveDetails(isDone, isSaveAndClose, documents);
          }
        });
    } else {
      this.onSaveDetails(isDone, isSaveAndClose);
    }
  }

  onSaveDetails(isDone = false, isSaveAndClose: boolean, docs?: any) {
    if (!this.visitMeetingDetailsForm.valid) {
      return;
    }

    let payload: any = {
      leadId: this.data?.id,
      isDone: isDone,
      meetingOrSiteVisit:
        this.data?.status?.status === 'meeting_scheduled' ? 1 : 2,
    };

    if (
      this.isCustomStatusEnabled &&
      this.data?.status?.shouldOpenAppointmentPage
    ) {
      payload.meetingOrSiteVisit = this.data?.status?.shouldUseForMeeting
        ? 1
        : 2;
    }

    if (isDone) {
      payload = {
        ...payload,
        projectName: this.visitMeetingDetailsForm.value.project,
        userId: this.visitMeetingDetailsForm.value.userId,
        isManual: this.selectedLocationType == 'google' ? false : true,
        notes: this.visitMeetingDetailsForm.value.notes,
        imagesWithName: docs,
        appointmentDoneOn: setTimeZoneDateWithTime(this.visitMeetingDetailsForm.value.appointmentDoneOn, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)
      };
    }

    if (this.selectedLocationType == 'search') {
      payload = {
        ...payload,
        address: {
          locationId:
            this.visitMeetingDetailsForm.value.location?.id ??
            this.visitMeetingDetailsForm.value.location?.id,
          placeId:
            this.visitMeetingDetailsForm.value.location?.placeId ??
            this.visitMeetingDetailsForm.value.location?.placeId,
        },
      };
    }

    if (this.selectedLocationType == 'manual') {
      payload = {
        ...payload,
        address: {
          sublocality:
            this.visitMeetingDetailsForm.value.locality ??
            this.visitMeetingDetailsForm.value.locality,
          city:
            this.visitMeetingDetailsForm.value.city ??
            this.visitMeetingDetailsForm.value.city,
          state:
            this.visitMeetingDetailsForm.value.state ??
            this.visitMeetingDetailsForm.value.state,
          country:
            this.visitMeetingDetailsForm.value.country ??
            this.visitMeetingDetailsForm.value.country,
        },
      };
    }

    if (navigator.geolocation && this.selectedLocationType == 'google') {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          payload = {
            ...payload,
            address: {
              latitude: String(position.coords.latitude),
              longitude: String(position.coords.longitude),
            },
          };
          this.updateDoneStatus(payload, isSaveAndClose);
        },
        () => {
          if (this.isLocationMandatory) {
            return;
          } else {
            this.openLocationConfirmationPopup(payload, isSaveAndClose);
          }
        }
      );
    } else if (
      (!this.visitMeetingDetailsForm.value.location &&
        this.selectedLocationType == 'search') ||
      (!this.visitMeetingDetailsForm.value.locality &&
        !this.visitMeetingDetailsForm.value.city &&
        !this.visitMeetingDetailsForm.value.state &&
        this.selectedLocationType == 'manual')
    ) {
      if (this.isLocationMandatory) {
        return;
      } else {
        this.openLocationConfirmationPopup(payload, isSaveAndClose);
      }
    } else {
      this.updateDoneStatus(payload, isSaveAndClose);
    }
  }

  openLocationConfirmationPopup(payload: any, isSaveAndClose: boolean) {
    let initialState: any = {
      message: 'LEADS.save-without-location',
    };
    const locationConfirmationPopup = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-300 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (locationConfirmationPopup?.onHide) {
      locationConfirmationPopup.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.updateDoneStatus(payload, isSaveAndClose);
        }
      });
    }
  }

  async updateDoneStatus(payload: any, isSaveAndClose: boolean) {
    this.appointments[this.selectedProject?.name || payload?.projectName] =
      payload?.isDone;
    if (!this.visitMeetingDetailsForm?.dirty) {
      this._store.dispatch(new MeetingOrVisitDone(payload));
      this.navigateNextDone(isSaveAndClose);
      return;
    }
    if (
      payload?.projectName != this.selectedProject?.name &&
      payload?.projectName
    ) {
      this.projectToAdd = payload?.projectName;
      this.addProject(this.selectedProject?.name);
      this.appointments[payload?.projectName] = true;
    }

    await this._store
      .select(getIsProjectAddIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    await this._store
      .select(getActiveLeadIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    await this._store
      .select(getActiveLeadIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    await this._store
      .select(getActiveLeadAppointmentsIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    this._store.dispatch(new MeetingOrVisitDone(payload));
    this.isMeetingOrVisitDoneLoading = true;
    this._store
      .select(getIsMeetingOrVisitDoneLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe((isLoading: boolean) => {
        this.isMeetingOrVisitDoneLoading = isLoading;
        this.navigateNextDone(isSaveAndClose);
      });
  }

  navigateNextDone(isSaveAndClose: boolean) {
    if (isSaveAndClose == false) {
      if (this.data?.projects?.length == 1) {
        this.selectedProject = this.data?.projects?.[0];
        this.openStatusModal();
        return;
      }
      if (
        (Object.keys(this.appointments)?.length ==
          this.data?.projects?.length &&
          this.data?.projects?.indexOf(this.selectedProject) + 1 ==
          this.data?.projects?.length) ||
        !this.data?.projects?.length ||
        this.isSaveAndUpdate
      ) {
        this.openStatusModal();
        return;
      } else if (
        this.data?.projects?.indexOf(this.selectedProject) + 1 <
        this.data?.projects?.length
      ) {
        this.selectedProjectChanged(
          this.data?.projects?.[this.previousProjectIndex + 1]
        );
      }

      this.canShowDoneForm = false;
      return;
    } else if (isSaveAndClose == true) {
      this.closeModal();
    }
  }

  openStatusModal() {
    this._store.dispatch(new LeadPreviewSaved());
    this.closeModal();
    this.closeLeadPreviewModal();
    let initialStateStatus: any = {
      data: this.data,
      selectedSection: 'Status',
      showOnlyPopup: false,
      fetchLeadsWhenClosed: true,
    };
    this.modalService.show(
      LeadPreviewComponent,
      Object.assign(
        {},
        {
          class: 'right-modal modal-550 ip-modal-unset',
          initialState: initialStateStatus,
        }
      )
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
