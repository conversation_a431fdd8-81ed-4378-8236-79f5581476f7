import { Action } from '@ngrx/store';
import { FetchPlacesListSuccess, FetchQRPlacesListSuccess, PlacesActionTypes } from './places.actions';

export interface Places {
  placeId: string;
  localityDisplayText: string;
  localitySubtitleText: string;
  city: string;
  state: string;
  country: string;
}

export type PlacesState = {
  placesList?: any[];
  qrPlacesList?: any[];
};

const initialState: PlacesState = {
  placesList: [],
  qrPlacesList: []
};

export function placesReducer(
  state: PlacesState = initialState,
  action: Action
): PlacesState {
  switch (action.type) {
    case PlacesActionTypes.FETCH_PLACES_LIST_SUCCESS:
      return {
        ...state,
        placesList: (action as FetchPlacesListSuccess).response || [],
      };
    case PlacesActionTypes.FETCH_QR_PLACES_LIST_SUCCESS:
      return {
        ...state,
        qrPlacesList: (action as FetchQRPlacesListSuccess).response || [],
      };
    default:
      return state;
  }
}
