import { Injectable } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { Subject } from 'rxjs';
import { WAWrapperDto } from 'src/app/core/interfaces/leads.interface';
import { getTenantName } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class SignalRService {
  private hubConnection: signalR.HubConnection;
  private messageReceived = new Subject<WAWrapperDto>();
  tenant: string = getTenantName();
  whatsAppBaseURL: string = env.whatsAppBaseURL;

  constructor() { }

  public startConnection = () => {
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(`${this.whatsAppBaseURL}chat-hub`, {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
      })
      .withAutomaticReconnect()
      .build();

    this.hubConnection
      .start()
      .then(() => {
        console.log('SignalR connection started');
        this.registerEventListeners();
        this.joinGroup();
      })
      .catch((err) =>
        console.error('Error while starting SignalR connection: ' + err)
      );
  };

  private registerEventListeners = () => {
    const eventHandlers: { [event: string]: (...args: any[]) => void } = {
      ReceiveMessageAsync: (message: WAWrapperDto) =>
        this.messageReceived.next(message),
      ReceiveMessage: (message: WAWrapperDto) =>
        this.messageReceived.next(message),
      RegisterForGroupJoinAsync: (groupName: string) =>
        console.log(`Client joined group: ${groupName}`),
      // 'SendMessageAsync': (message: WAWrapperDto) => console.log(`Message sent to all clients: ${message}`),
      // 'RequestClientDisconnect': () => console.log('Request to disconnect received'),
      // 'SendMessageToCallerAsync': (message: WAWrapperDto) => console.log(`Message sent to caller: ${message}`),
      // 'SendMessageToGroupAsync': (message: string, groupName: string) => console.log(`Message sent to group ${groupName}: ${message}`),
    };

    for (const event in eventHandlers) {
      this.hubConnection.on(event, eventHandlers[event]);
    }
  };

  private checkConnection = (callback: () => void) => {
    if (this.hubConnection.state === signalR.HubConnectionState.Connected) {
      callback();
    } else {
      console.error('Connection is not in the Connected state.');
    }
  };

  public stopConnection = () => {
    this.hubConnection
      .stop()
      .then(() => console.log('SignalR connection stopped'))
      .catch((err) =>
        console.error('Error while stopping SignalR connection: ' + err)
      );
  };

  public joinGroup = () => {
    this.checkConnection(() => {
      this.hubConnection
        .invoke('RequestForGroupJoinAsync', this.tenant)
        .then(() => console.log('Request for group join sent'))
        .catch((err) =>
          console.error('Error sending request for group join', err)
        );
    });
  };

  public leaveGroup = (groupName: string) => {
    this.checkConnection(() => {
      this.hubConnection
        .invoke('LeaveGroup', groupName)
        .then(() => console.log(`Left group ${groupName}`))
        .catch((err) => console.error(`Error leaving group ${groupName}`, err));
    });
  };

  public sendMessageToGroup = (
    groupName: string,
    waWrapperDto: WAWrapperDto
  ) => {
    this.checkConnection(() => {
      this.hubConnection
        .invoke('SendMessageToGroupAsync', groupName, waWrapperDto)
        .then(() => console.log('Message sent to group'))
        .catch((err) => console.error('Error sending message to group', err));
    });
  };

  public getMessageListener = () => {
    return this.messageReceived.asObservable();
  };
}
