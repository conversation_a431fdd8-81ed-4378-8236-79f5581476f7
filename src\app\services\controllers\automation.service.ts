import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class AutomationService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string;
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'automation';
  }

  getPriorityList(): Observable<Object> {
    return this.http.get(`${this.serviceBaseUrl}/assignment-module`);
  }

  updatePriorityList(payload: any): Observable<Object> {
    return this.http.put(`${this.serviceBaseUrl}/assignment-module`, payload);
  }

  getUserAssignment(): Observable<Object> {
    return this.http.get(`${this.serviceBaseUrl}/user-assignment`);
  }

  //single entity - multiple user assignments
  updateUserAssignment(payload: any, moduleName: string): Observable<Object> {
    return this.http.put(`${this.serviceBaseUrl}/user-assignment`, payload);
  }

  //multiple entity - multiple user assignments
  updateMultiUserAssignment(
    payload: any,
    moduleName: string
  ): Observable<Object> {
    return this.http.put(
      `${this.serviceBaseUrl}/user-assignment/multiple`,
      payload
    );
  }

  getUserAssignmentById(id: string): Observable<Object> {
    return this.http.get(`${this.serviceBaseUrl}/user-assignment/${id}`);
  }

  getUserAssignmentByEntity(id: string): Observable<Object> {
    return this.http.get(
      `${this.serviceBaseUrl}/user-assignment/entity?entityid=${id}`
    );
  }

  getIntegrationAssignment({ id, source }: any): Observable<Object> {
    return this.http.get(
      `${this.serviceBaseUrl}/integration-assignment?id=${id}&source=${source}`
    );
  }

  updateIntegrationAssignment(payload: any): Observable<Object> {
    return this.http.put(
      `${this.serviceBaseUrl}/integration-assignment`,
      payload
    );
  }

  updateMultipleIntegrationAssignment(payload: any): Observable<Object> {
    return this.http.put(
      `${this.serviceBaseUrl}/integration-assignment/multiple`,
      payload
    );
  }

  updateMultiProject(payload: any): Observable<Object> {
    return this.http.put(
      `${this.serviceBaseUrl}/integration/multiple/project`,
      payload
    );
  }

  updateMultiLocation(payload: any): Observable<Object> {
    return this.http.put(
      `${this.serviceBaseUrl}/integration/multiple/location`,
      payload
    );
  }

  updateMultiCountryCode(payload: any): Observable<Object> {
    return this.http.put(
      `${this.serviceBaseUrl}/integration/multiple/countrycode`,
      payload
    );
  }
}
