import { Component, EventEmitter, OnInit, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, firstValueFrom, skipWhile, take, takeUntil } from 'rxjs';
import { SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, changeCalendar, getAssignedToDetails, getPages } from 'src/app/core/utils/common.util';
import { FetchPriorityList } from 'src/app/reducers/automation/automation.actions';
import { getPriorityList } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getPropertyWithIdNameList } from 'src/app/reducers/property/property.reducer';
import { FetchReferenceExcelUploadedList, FetchReferenceListingSource, ReferenceBulkDelete, UpdateReferencePayload } from 'src/app/reducers/reference-id-management/reference-id-management.action';
import {
  getFiltersPayload,
  getReferenceIdCounts,
  getReferenceIdloaders,
  getReferenceIdLoaders,
  getReferenceIds,
  getReferenceListingSources,
  getReferenceTotalCounts
} from 'src/app/reducers/reference-id-management/reference-id-management.reducer';
import { FetchAdminsAndReportees } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { ExcelUploadedStatusComponent } from '../../leads/excel-uploaded-status/excel-uploaded-status.component';
import { AddReferenceIdComponent } from './add-reference-id/add-reference-id.component';
import { ReferenceIdActionsComponent } from './reference-id-actions/reference-id-actions.component';
import { ReferenceIdAdvancedFilterComponent } from './reference-id-advanced-filter/reference-id-advanced-filter.component';

@Component({
  selector: 'reference-id-management',
  templateUrl: './reference-id-management.component.html'
})
export class ReferenceIdManagementComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  gridOptions: GridOptions;
  gridApi: GridApi;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  showLeftNav: boolean;
  filtersForm: FormGroup;
  formFields: any = {
    SearchFilter: [null],
    PageNumber: [1],
    PageSize: [10],
    ListingSourceId: [null]
  };
  filtersPayload: any;
  permissions: any;
  globalSettings: any;
  selectedTrackerOption: string;
  selectedOption: string;
  rowData: any[] = [];
  userData: any;
  currentDate: Date;
  userList: any;
  loaders: any = {};
  listingSources: any = [];
  getPages = getPages;
  counts: any = {};
  totalCount: number = 0;
  referenceLabel: any = {
    SerialNo: 'Serial No',
    PropertyTitle: 'Property Title',
    RefrenceIds: 'Reference Id',
    ListingSourceIds: 'Listing Source',
    AssignedUserIds: 'Assigned To',
    AssociatedProperties: 'Associated Property'
  }
  propertyList: any = [];
  moduleId: string;
  canEnableAllowDuplicates: any;
  canEnableAllowSecondaryUsers: any;
  allUserList: any;
  allActiveUsers: any;
  activeUsers: any;
  reporteesAdminuserList: any;
  selectedReferences: any[];
  selectedNodes: any[]
  isBulkDeleteLoading: boolean = false;
  get showFilters(): boolean {
    const filters = this.getFilteredPayload();
    return Object.values(filters).some(value =>
      Array.isArray(value) ? value.length > 0 : (Boolean(value) || value === 0)
    )
  }

  get filteredFilters(): any {
    let filters = this.getFilteredPayload();
    return filters;
  }


  private getFilteredPayload(): any {
    const ignoredKeys = new Set([
      'PageSize', 'PageNumber', 'SearchFilter', 'ListingSourceId'
    ]);

    return Object.fromEntries(
      Object.entries(this.filtersPayload ?? {})
        .filter(([key, value]) => !ignoredKeys.has(key) && value != null)
    );
  }

  constructor(
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private shareDataService: ShareDataService,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    private gridOptionsService: GridOptionsService,
    private router: Router,
    public modalRef: BsModalRef,
    private bulkDeleteModalRef: BsModalRef,
  ) { }

  ngOnInit(): void {
    this.initializeForms();
    this.initializeComponent();
    this.initializeDispatches()
    this.initializeSubscriptions();
  }

  initializeForms() {
    this.filtersForm = this.fb.group(this.formFields);
  }

  initializeDispatches() {
    this._store.dispatch(new FetchReferenceListingSource())
  }

  initializeComponent(): void {
    this.metaTitle.setTitle('CRM | Reference-ID-Management');
    this.headerTitle.setLangTitle('Reference ID Management');
    this.shareDataService.showLeftNav$.subscribe(show => this.showLeftNav = show);
  }

  async initializeSubscriptions() {
    const userDetails: any = await firstValueFrom(this._store.select(getUserBasicDetails));
    this.userData = userDetails;
    this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset);
    const subscriptions = [
      {
        selector: getFiltersPayload,
        handler: (payload: any) => {
          this.filtersPayload = { ...payload };
          this.filtersForm.patchValue({
            ...this.filtersPayload
          }, { emitEvent: false })
        }
      },
      {
        selector: getReferenceIds,
        handler: (data: any) => {
          this.rowData = [...data]
          this.gridApi?.setRowData(this.rowData);
          this.initializeGridSettings()
        }
      },
      {
        selector: getReferenceListingSources,
        handler: (data: any) => {
          this.listingSources = data;
        }
      },
      {
        selector: getReferenceIdCounts,
        handler: (data: any) => {
          this.counts = data
        }
      },
      {
        selector: getReferenceTotalCounts,
        handler: (data: number) => {
          this.totalCount = data
        }
      },
      {
        selector: getReferenceIdLoaders,
        handler: (data: boolean) => {
          this.loaders = data
        },
      },
      {
        selector: getPermissions,
        handler: (permissions: any) => {
          if (permissions?.length) {
            this.permissions = new Set(permissions);
            if (!this.permissions.has('Permissions.Users.AssignToAny')) {
              this._store.dispatch(new FetchAdminsAndReportees());
            }
          }
        }
      },
      {
        selector: getUsersListForReassignment,
        handler: (data: any) => {
          this.userList = data?.map((user: any) => ({
            ...user,
            fullName: `${user.firstName} ${user.lastName}`,
          }));
          this.gridApi?.refreshCells();
        },
      },
      {
        selector: getPropertyWithIdNameList,
        handler: ((data: any) => {
          this.propertyList = data.slice().sort((a: any, b: any) => {
            const nameA = a.name || '';
            const nameB = b.name || '';
            return nameA.localeCompare(nameB);
          });
        })
      }
    ];

    subscriptions.forEach(({ selector, handler }) => {
      this._store.select(selector).pipe(takeUntil(this.stopper)).subscribe(handler);
    });
    this.filterFunction()
  }

  openAddReference() {
    this.modalService.show(AddReferenceIdComponent, {
      class: 'right-modal modal-400 ip-modal-unset',
    });
  }

  openReferenceTracker() {
    if (this.selectedTrackerOption === 'bulkUpload') {
      let initialState: any = {
        fieldType: 'reference',
      };
      this._store.dispatch(new FetchReferenceExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    }
    this.selectedTrackerOption = '';
  }

  openReferenceBulk() {
    if (this.selectedOption === 'bulkUpload') {
      this.router.navigate(['properties/reference-bulk-upload']);
    }
    this.selectedOption = '';
  }

  getFormValue(controlName: string) {
    return this.filtersForm.get(controlName)?.value
  }

  openAdvFiltersModal() {
    this.modalRef = this.modalService.show(ReferenceIdAdvancedFilterComponent, {
      class: 'modal-600 modal-dialog tb-modal-unset',
    })
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    this.gridOptions.columnDefs = [
      {

        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        maxWidth: 50,
        suppressMovable: true,
        lockPosition: 'left'
      },
      {
        headerName: 'Reference ID',
        field: 'Reference ID',
        cellClass: 'lock-pinned align-center h-100 ',
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        valueGetter: (params: any) => [params.data?.referenceId],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Portal name',
        minWidth: 195,
        field: 'Portal name',
        valueGetter: (params: any) => [
          params.data.listingSource?.displayName ?? '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Property title',
        field: 'Property title',
        minWidth: 200,
        valueGetter: (params: any) => [params.data?.propertyTitle ?? '--'],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Associated Property',
        field: 'Associated property',
        minWidth: 200,
        valueGetter: (params: any) => [
          params?.data?.associatedProperty?.title ?? '',
          params?.data?.associatedProperty?.serialNo
        ],
        cellRenderer: (params: any) => {
          const userName = JSON.parse(localStorage.getItem('userDetails') || '{}')?.preferred_username;
          const serialNo = params.data?.serialNo ?? '';

          return `<p class="fw-600 text-truncate-1 break-all mb-4 text-accent-green text-decoration-underline cursor-pointer" 
                    style="cursor: pointer;"
                    onclick="window.open('external/listing-preview/${userName}/${serialNo}', '_blank')">
                    ${params.value[0]}
                  </p>`;
        },
      },
      {
        headerName: 'Serial number of Associated property',
        field: 'Serial number of Associated property',
        valueGetter: (params: any) => [params?.data?.serialNo ?? '--'],
        cellRenderer: (params: any) => {
          return `<p> ${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        minWidth: 195,
        field: 'Assigned To',
        valueGetter: (params: any) => [
          params.data.assignedUserIds?.length ? params.data.assignedUserIds?.map((id: any) => getAssignedToDetails(id, this.userList, true) || '--') : '--',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2 break-all">${params.value[0]}</p>`;
        },
      },
    ];

    this.gridOptions.columnDefs.push({
      headerName: 'Actions',
      field: 'Actions',
      filter: false,
      cellRenderer: ReferenceIdActionsComponent,
      maxWidth: 250,
      minWidth: 250,
      menuTabs: [],
    });
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  isEmptyInput(event: any) {
    const searchTerm = (event.target as HTMLInputElement).value
    if ((searchTerm === '' || searchTerm === null) && this.getFormValue('SearchFilter')) {
      this.filtersForm.get('SearchFilter')?.setValue(null);
      this.filterFunction();
    }
  }

  onEnterKey(event: KeyboardEvent) {
    const searchValue = (event.target as HTMLInputElement).value;
    if (!searchValue && !this.getFormValue('SearchFilter')) return
    this.filtersForm.get('SearchFilter')?.setValue(searchValue);
    this.filterFunction()
  }

  filterFunction() {
    this.filtersPayload = {
      ...this.filtersPayload,
      ...this.filtersForm.value,
    };
    this._store.dispatch(new UpdateReferencePayload(this.filtersPayload))
  }

  onGridReady(params: any) {
    this.gridApi = params?.api;
  }

  isArray(value: any) {
    return Array.isArray(value)
  }

  getFilterValue(key: string, value: any) {
    if (key === 'AssignedUserIds') {
      return this.userList?.find((user: any) => user?.id === value)?.fullName
    }
    else if (key === 'AssociatedProperties') {
      return this.propertyList?.find((item: any) => item?.id === value)?.title
    } else if (key === 'ListingSourceIds') {
      return this.listingSources?.find((source: any) => source?.id === value)?.displayName
    }
    return value
  }

  onRemoveFilter(key: string, value: string, allValue: any) {
    const isArray = Array.isArray(allValue);
    this.filtersPayload[key] = isArray ? allValue.filter((item: string) => item !== value) : null;
    this.filtersForm.patchValue({
      ...this.filtersPayload, pageNumber: 1
    });
    this.filterFunction()
  }

  onClearAllFilters() {
    this.filtersPayload = Object.keys(this.filtersPayload).reduce((acc: any, key: string) => {
      acc[key] = this.filtersForm.controls[key] && key !== 'customFlags' ? this.filtersPayload[key] : null;
      return acc;
    }, {});

    this.filtersForm.patchValue({
      ...this.filtersPayload, pageNumber: 1
    });
    this.filterFunction()
  }

  removeReferenceId(id: string): void {
    const selectedNode = this.gridApi
      ?.getSelectedNodes()
      ?.find((ref: any) => ref?.data?.id === id);
    if (selectedNode) {
      this.gridApi?.deselectNode(selectedNode);
    }

    if (this.selectedNodes) {
      this.selectedNodes = this.selectedNodes?.filter(
        (prop: any) => prop.id !== id
      );

      if (this.selectedNodes?.length <= 0) this.bulkDeleteModalRef.hide();
    }
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    let selectedNodes = this.gridApi?.getSelectedNodes().map((ref: any) => {
      return ref?.data;
    });
    this.selectedNodes = selectedNodes;
    let initialState: any = {
      data: selectedNodes,
      class: 'right-modal modal-300',
    };
    this.bulkDeleteModalRef = this.modalService.show(BulkDeleteModal, initialState);
  }

  openConfirmDeleteModal(RefId: string, id: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: RefId,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeReferenceId(id);
        }
      });
    }
  }

  bulkDelete(): void {
    const ids = this.gridApi
      ?.getSelectedNodes()
      .map((dataNodes: any) => dataNodes?.data?.id);

    this.isBulkDeleteLoading = true;
    this._store.dispatch(new ReferenceBulkDelete(ids));
    this._store
      .select(getReferenceIdloaders)
      .pipe(
        skipWhile((data: any) => data?.isBulkDeleteLoading),
        take(1)
      )
      .subscribe(() => {
        this.isBulkDeleteLoading = false;
        this.bulkDeleteModalRef.hide();
      });
  }

  async bulkReassign(assignModal: any) {
    this.selectedReferences = this.gridApi?.getSelectedNodes()?.map((node: any) => node?.data)
    this._store.dispatch(new FetchPriorityList());
    this._store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'ReferenceId');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
    const globalSettings: any = await firstValueFrom(this._store.select(getGlobalSettingsAnonymous));
    this.canEnableAllowDuplicates =
      globalSettings?.duplicateFeatureInfo?.isFeatureAdded;
    this.canEnableAllowSecondaryUsers = globalSettings?.isDualOwnershipEnabled;

    this.permissions = new Set(await firstValueFrom(this._store.select(getPermissions)));
    const data: any = await firstValueFrom(this._store.select(getUsersListForReassignment).pipe(filter((data: any) => data?.length)));

    this.allUserList = data;
    this.allActiveUsers = data?.filter((user: any) => user.isActive).map((user: any) => ({
      ...user,
      fullName: user.firstName + ' ' + user.lastName,
    }));

    this.allActiveUsers = assignToSort(this.allActiveUsers, '');
    if (!this.permissions.has('Permissions.Users.AssignToAny')) {
      this.reporteesAdminuserList = await firstValueFrom(this._store.select(getAdminsAndReportees).pipe(filter((data: any) => data?.length)));

      this.activeUsers = this.reporteesAdminuserList
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));

      this.activeUsers = assignToSort(this.activeUsers, '');
    }
    this.modalRef = this.modalService.show(assignModal, {
      class: 'ip-modal-unset modal-dialog right-modal',
    })
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
