import { Component, EventEmitter, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'remove-document',
  templateUrl: './remove-document.component.html',
})
export class RemoveDocumentComponent {
  @Output() onSaveChanges: EventEmitter<any> = new EventEmitter<any>();
  @Output() onHide: EventEmitter<any> = new EventEmitter<any>();

  constructor(public modalRef: BsModalRef) {}


  saveChanges(e?: any) {
    e ? this.onSaveChanges.emit(e) : this.onSaveChanges.emit();
  }

  hide(e?: any) {
    e ? this.onHide.emit(e) : this.onHide.emit();
  }
}
