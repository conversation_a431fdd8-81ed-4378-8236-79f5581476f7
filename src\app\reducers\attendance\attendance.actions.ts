import { Action } from '@ngrx/store';

export enum AttendanceActionTypes {
  FETCH_ATTENDANCE_LIST = '[ATTENDANCE] Fetch Attendance List',
  FETCH_ATTENDANCE_LIST_SUCCESS = '[ATTENDANCE] Fetch Attendance List Success',
  FETCH_ATTENDANCE_LIST_NO_AUTH = '[ATTENDANCE] Fetch Attendance List No Auth',
  FETCH_ATTENDANCE_LIST_NO_AUTH_SUCCESS = '[ATTENDANCE] Fetch Attendance List No Auth Success',
  FETCH_ATTENDANCE_LIST_BY_ID = '[ATTENDANCE] Fetch Attendance By Id',
  FETCH_ATTENDANCE_LIST_BY_ID_SUCCESS = '[ATTENDANCE] Fetch Attendance By Id Success',
  UPDATE_FILTER_PAYLOAD = '[ATTENDANCE] Update Filter Payload',
  UPDATE_FILTER_PAYLOAD_NO_AUTH = '[ATTENDANCE] Update Filter Payload No Auth',
  CLOCK_IN = '[ATTENDANCE] Clock In',
  CLOCK_OUT = '[ATTENDANCE] Clock Out',
  EXPORT_ATTENDANCE = '[ATTENDANCE] Export Attendance through Excel',
  EXPORT_ATTENDANCE_SUCCESS = '[ATTENDANCE] Export Attendance through Excel Success',
  FETCH_ATTENDANCE_EXPORT_STATUS = '[ATTENDANCE] Fetch  Attendance Export Status List',
  FETCH_ATTENDANCE_EXPORT_STATUS_SUCCESS = '[ATTENDANCE] Fetch Attendance Export Status List Success',
  FETCH_ATTENDANCE_SETTING = '[ATTENDANCE] Fetch Attendance Setting',
  FETCH_ATTENDANCE_SETTING_SUCCESS = '[ATTENDANCE] Fetch Attendance Setting Success',
  UPDATE_ATTENDANCE_SETTING = '[ATTENDANCE] Update Attendance Setting',
  UPDATE_ATTENDANCE_SETTING_SUCCESS = '[ATTENDANCE] Update Attendance Setting Success',
  FETCH_NOTIFICATION = '[ATTENDANCE] Fetch Notification Setting',
  FETCH_NOTIFICATION_SUCCESS = '[ATTENDANCE] Fetch Notification Setting Success',
}

export class FetchAttendanceList implements Action {
  readonly type: string = AttendanceActionTypes.FETCH_ATTENDANCE_LIST;
  constructor() { }
}
export class FetchAttendanceListSuccess implements Action {
  readonly type: string = AttendanceActionTypes.FETCH_ATTENDANCE_LIST_SUCCESS;
  constructor(public response: any = {}) { }
}
export class FetchAttendanceListNoAuth implements Action {
  readonly type: string = AttendanceActionTypes.FETCH_ATTENDANCE_LIST_NO_AUTH;
  constructor() { }
}
export class FetchAttendanceListNoAuthSuccess implements Action {
  readonly type: string =
    AttendanceActionTypes.FETCH_ATTENDANCE_LIST_NO_AUTH_SUCCESS;
  constructor(public response: any = {}) { }
}
export class FetchAttendanceListById implements Action {
  readonly type: string = AttendanceActionTypes.FETCH_ATTENDANCE_LIST_BY_ID;
  constructor(public id: string, public timeZone: any) { }
}
export class FetchAttendanceListByIdSuccess implements Action {
  readonly type: string =
    AttendanceActionTypes.FETCH_ATTENDANCE_LIST_BY_ID_SUCCESS;
  constructor(public response: any) { }
}
export class UpdateFilterPayload implements Action {
  readonly type: string = AttendanceActionTypes.UPDATE_FILTER_PAYLOAD;
  constructor(public filter: any) { }
}
export class UpdateFilterPayloadNoAuth implements Action {
  readonly type: string = AttendanceActionTypes.UPDATE_FILTER_PAYLOAD_NO_AUTH;
  constructor(public filter: any) { }
}
export class ClockIn implements Action {
  readonly type: string = AttendanceActionTypes.CLOCK_IN;
  constructor(public payload: any, public component?: string) { }
}
export class ClockOut implements Action {
  readonly type: string = AttendanceActionTypes.CLOCK_OUT;
  constructor(public payload: any, public component?: string) { }
}

export class ExportAttendance implements Action {
  readonly type: string = AttendanceActionTypes.EXPORT_ATTENDANCE;
  constructor(public payload: any) { }
}
export class ExportAttendanceSuccess implements Action {
  readonly type: string = AttendanceActionTypes.EXPORT_ATTENDANCE_SUCCESS;
  constructor(public resp: string = '') { }
}

export class FetchAttendanceExportStatus implements Action {
  readonly type: string = AttendanceActionTypes.FETCH_ATTENDANCE_EXPORT_STATUS;
  constructor(public payload: any,public pageNumber:number,public pageSize:number) { }
}
export class FetchAttendanceExportStatusSuccess implements Action {
  readonly type: string =
    AttendanceActionTypes.FETCH_ATTENDANCE_EXPORT_STATUS_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class FetchAttendanceSetting implements Action {
  readonly type: string = AttendanceActionTypes.FETCH_ATTENDANCE_SETTING;
  constructor() { }
}
export class FetchAttendanceSettingSuccess implements Action {
  readonly type: string =
    AttendanceActionTypes.FETCH_ATTENDANCE_SETTING_SUCCESS;
  constructor(public response: any) { }
}
export class UpdateAttendanceSetting implements Action {
  readonly type: string = AttendanceActionTypes.UPDATE_ATTENDANCE_SETTING;
  constructor(public payload: any) { }
}
export class UpdateAttendanceSettingSuccess implements Action {
  readonly type: string =
    AttendanceActionTypes.UPDATE_ATTENDANCE_SETTING_SUCCESS;
  constructor(public response: any) { }
}
export class FetchNotification implements Action {
  readonly type: string = AttendanceActionTypes.FETCH_NOTIFICATION;
  constructor() { }
}
export class FetchNotificationSuccess implements Action {
  readonly type: string =
    AttendanceActionTypes.FETCH_NOTIFICATION_SUCCESS;
  constructor(public response: any) { }
}
