<div [ngClass]="{'gray-scale': isPropertySoldOut}">
    <div class="flex-between px-20 py-16 bg-coal brtl-4 brtr-4">
        <div class="flex-center">
            <div>
                <img [appImage]="s3BucketUrl+userData?.imageUrl" type="defaultAvatar"
                    class="obj-cover br-50 border-white mr-16 bg-white" width="42" height="42">
            </div>
            <div>
                <h4 class="text-white fw-semi-bold text-nowrap text-truncate-1 break-all">{{userData?.firstName}} {{userData?.lastName}}</h4>
                <div class="text-dark-gray">
                    <a [href]="'tel:'+userData.phoneNumber">{{userData.phoneNumber}}</a>
                </div>
            </div>
        </div>
        <div class="flex-center ml-12">
            <div>
                <img *ngIf="userData?.orgDetails?.logoImgUrl" [type]="'leadrat'" [appImage]="s3BucketUrl+userData?.orgDetails?.logoImgUrl"
                    class="obj-cover br-50 border-white mr-10 bg-white" width="30" height="30">
            </div>
            <div class="bg-white br-20 p-8" *ngIf="userData?.orgDetails?.displayName">
                <span class="text-truncate-1 break-all">
                    {{userData?.orgDetails?.displayName ? userData?.orgDetails?.displayName : 'Org Name'}}</span>
            </div>
        </div>
    </div>
    <div class="bg-white brbl-4 brbr-4 p-16 ph-br-0">
        <h3 class="fw-600">Enquire now</h3>
        <div class="mt-8">Fill up the form below to enquire now. The owner/ realtor will get in touch with you soon
        </div>
        <form [formGroup]="inquiryForm">
            <div class="flex-center w-100">
                <form-errors-wrapper [control]="inquiryForm.controls['name']" label="{{ 'GLOBAL.name' | translate }}"
                    class="w-50">
                    <div class="field-label-req">{{ 'GLOBAL.name' | translate }}</div>
                    <input type="text" formControlName="name" placeholder="Enter your name" />
                </form-errors-wrapper>
                <div class="w-50 ml-30">
                    <div class="field-label-req">Phone Number</div>
                    <form-errors-wrapper [control]="inquiryForm.controls['contactNo']" label="Phone Number">
                        <div *ngIf="!resetNumber">
                            <ngx-mat-intl-tel-input #contactNoInput *ngIf="hasInternationalSupport"
                                [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                [enableSearch]="true" formControlName="contactNo" class="no-validation contactNoInput">
                            </ngx-mat-intl-tel-input>
                            <ngx-mat-intl-tel-input #contactNoInput *ngIf="!hasInternationalSupport"
                                [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                                [enablePlaceholder]="true" [enableSearch]="true" formControlName="contactNo"
                                class="no-validation contactNoInput">
                            </ngx-mat-intl-tel-input>
                        </div>
                    </form-errors-wrapper>
                </div>
            </div>
            <label class="checkbox-container mt-16">
                <input type="checkbox" formControlName="agreeTerms" id="inpTerms" required data-automate-id="inpTerms"
                    class="mr-10">
                <span class="checkmark"></span>
                <span class="text-dark-gray text-sm">I agreed to receive communication regarding lead enquiry.</span>
            </label>
            <span *ngIf="!inquiryForm.controls['agreeTerms'].valid && inquiryForm.controls['agreeTerms'].touched"
                class="text-red mt-4 text-xs">In order to proceed, please check checkbox.</span>
            <h5 *ngIf="!MSleadIsLoading"
                class="mt-12 bg-coal fw-semi-bold br-4 py-10 text-center text-white cursor-pointer"
                (click)="postLead()">{{'BUTTONS.submit' | translate}}
            </h5>
            <ng-container *ngIf="MSleadIsLoading">
                <div class="flex-center">
                    <img src="assets/images/loader-rat.svg" class="rat-loader h-48 w-48" alt="loader">
                </div>
            </ng-container>
        </form>
    </div>
</div>