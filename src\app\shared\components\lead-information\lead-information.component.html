<div class="text-mud fw-600 br-10 p-16 bg-lead-info tb-w-100">
  <div>
    <h5 class="text-accent-green fw-700">{{'LEADS.lead-info' | translate}}</h5>
    <div class="align-center mt-4">
      <p class="w-50">{{'GLOBAL.name' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">{{leadInfo?.name ? leadInfo?.name : '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'INTEGRATION.primary-no' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">{{leadInfo?.contactNo ?
        leadInfo?.contactNo : '--'}}</p>
      <!-- <a [href]="'tel:'+leadInfo?.contactNo"
        [ngClass]="{'text-decoration-underline text-dark-hover': leadInfo?.contactNo, 'pe-none' : !leadInfo?.contactNo}"
        class="w-50 break-all fw-semi-bold text-truncate-1">{{leadInfo?.contactNo ?
        leadInfo?.contactNo : '--'}}</a> -->
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">{{leadInfo?.alternateContactNo ?
        leadInfo?.alternateContactNo : '--'}}</p>
      <!-- <a [href]="'tel:'+leadInfo?.alternateContactNo"
        [ngClass]="{'text-decoration-underline text-dark-hover': leadInfo?.alternateContactNo, 'pe-none' : !leadInfo?.alternateContactNo}"
        class="w-50 break-all fw-semi-bold text-truncate-1">{{ leadInfo?.alternateContactNo || '--' }}</a> -->
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'USER.email' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">{{leadInfo?.email ? leadInfo?.email : '--'}}</p>
    </div>
    <div class="mt-4" [ngClass]="isDualOwnershipEnabled?'d-flex':'align-center'">
      <p class="w-50">{{'LEADS.assign-to' | translate}}:</p>
      <div class="w-50 mt-4">
        <div class="d-flex">
          <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10"
            [ngClass]="isDualOwnershipEnabled?'':'d-none'">P</div>
          <p class="w-50 break-all fw-semi-bold text-truncate-1">{{getAssignedToDetails(leadInfo?.assignTo, allUsers,
            true) || '--'}}</p>
        </div>
        <div class="d-flex mt-6" [ngClass]="isDualOwnershipEnabled?'':'d-none'">
          <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">S</div>
          <p class="w-50 break-all fw-semi-bold text-truncate-1">{{getAssignedToDetails(leadInfo?.secondaryUserId,
            allUsers, true) || '--'}}</p>
        </div>
      </div>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEAD_FORM.referral-name' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">{{leadInfo?.referralName ? leadInfo?.referralName : '--'}}
      </p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50 text-nowrap">{{'LEAD_FORM.referral-phone-no' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">
        {{leadInfo?.referralContactNo ? leadInfo?.referralContactNo : '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEADS.created-by' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">{{getAssignedToDetails(leadInfo?.createdBy, allUsers, true)
        || '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEADS.created-date' | translate}}:</p>
      <p class="fw-semi-bold">
        {{ leadInfo?.createdOn ? getTimeZoneDate(leadInfo?.createdOn, userData?.timeZoneInfo?.baseUTcOffset ,
        'fullDateTime') : '--'}} </p>
    </div>
    <div class="align-center mt-4"
      *ngIf="userData?.shouldShowTimeZone && userData?.timeZoneInfo?.timeZoneName && leadInfo?.createdOn">
      <p class="w-50"></p>
      <p class="text-truncate-1 break-all text-sm">
        ({{userData?.timeZoneInfo?.timeZoneName }})</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEADS.modified-by' | translate}}:</p>
      <p class="w-50 break-all fw-semi-bold text-truncate-1">{{getAssignedToDetails(leadInfo?.lastModifiedBy, allUsers,
        true) || '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEADS.modified-date' | translate}}:</p>
      <p class="fw-semi-bold">
        {{leadInfo?.lastModifiedOn ?
        getTimeZoneDate(leadInfo?.lastModifiedOn,userData?.timeZoneInfo?.baseUTcOffset,
        'fullDateTime')
        : '--'}}</p>
    </div>
    <div class="align-center mt-4"
      *ngIf="userData?.shouldShowTimeZone && userData?.timeZoneInfo?.timeZoneName && leadInfo?.lastModifiedOn">
      <p class="w-50"></p>
      <p class="text-truncate-1 break-all text-sm">
        ({{userData?.timeZoneInfo?.timeZoneName }})</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEADS.picked-date' | translate}}:</p>
      <p class="fw-semi-bold">
        {{ leadInfo?.pickedDate ? getTimeZoneDate(leadInfo?.pickedDate,userData?.timeZoneInfo?.baseUTcOffset) :
        '--'}}</p>
    </div>
    <div class="align-center mt-4"
      *ngIf="userData?.shouldShowTimeZone && userData?.timeZoneInfo?.timeZoneName && leadInfo?.pickedDate">
      <p class="w-50"></p>
      <p class="text-truncate-1 break-all text-sm">
        ({{userData?.timeZoneInfo?.timeZoneName }})</p>
    </div>
  </div>
  <div class="my-20 border-bottom"></div>
  <div>
    <h5 class="text-accent-green fw-700">{{'LEADS.enquiry-info' | translate}}</h5>
    <div class="align-center mt-4" *ngIf="canViewLeadSource">
      <p class="w-50">{{'LEADS.source' | translate}}:</p>
      <p class="w-50 fw-semi-bold">{{leadInfo?.leadSource ? leadInfo?.leadSource : '--'}}</p>
    </div>
    <div class="align-center mt-4" *ngIf="canViewLeadSource">
      <p class="w-50">{{'LEADS.sub-source' | translate}}:</p>
      <p class="w-50 fw-semi-bold text-truncate-1 break-all">{{leadInfo?.subSource ? leadInfo?.subSource :
        '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'INTEGRATION.enquired-location' | translate}}:</p>
      <p class="w-50 fw-semi-bold break-all text-truncate-1 break-all" [title]="cities">{{cities}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">Min. {{'LEAD_FORM.budget' | translate}}:</p>
      <p class="w-50 fw-semi-bold">{{leadInfo?.lowerBudget ? leadInfo?.lowerBudget : '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">Max. {{'LEAD_FORM.budget' | translate}}:</p>
      <p class="w-50 fw-semi-bold">{{leadInfo?.upperBudget ? leadInfo?.upperBudget : '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEAD_FORM.enquired-for' | translate}}:</p>
      <p class="w-50 fw-semi-bold">{{enquiredFor}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LABEL.property' | translate}} {{'LABEL.type' | translate}}:</p>
      <p class="w-50 fw-semi-bold">{{leadInfo?.propertyType ?
        leadInfo?.propertyType : '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'PROPERTY.sub-type' | translate}}:</p>
      <p class="w-50 fw-semi-bold">
        {{leadInfo?.propertySubType ? leadInfo?.propertySubType : '--'}}</p>
    </div>
    <ng-container *ngIf="leadInfo?.propertyType == 'Residential' && leadInfo?.propertySubType !== 'Plot'">
      <div class="align-center mt-4">
        <p class="w-50">{{'PROPERTY.bhk' | translate}}:</p>
        <p class="w-50 fw-semi-bold">
          {{bhkNo}}
        </p>
      </div>
      <div class="align-center mt-4">
        <p class="w-50">{{'PROPERTY.bhk' | translate}} {{'LABEL.type' | translate}}:</p>
        <p class="w-50 fw-semi-bold">{{bhkTypes}}</p>
      </div>
    </ng-container>
    <div class="align-center mt-4">
      <p class="w-50">{{ 'SIDEBAR.project' | translate }}:</p>
      <p class="w-50 fw-semi-bold break-all text-truncate-1">
        {{leadInfo?.project ? leadInfo?.project : '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{ 'LABEL.property' | translate}}:</p>
      <p class="w-50 fw-semi-bold break-all text-truncate-1">
        {{leadInfo?.property ? leadInfo?.property : '--'}}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'INTEGRATION.agency-name' | translate}}:</p>
      <p class="w-50 fw-semi-bold break-all text-truncate-2">{{ getAgencyNames() }}</p>
    </div>
    <div class="align-center mt-4">
      <p class="w-50">{{'LEAD_FORM.campaign-name' | translate}}:</p>
      <p class="w-50 fw-semi-bold break-all text-truncate-2">{{ getCampaign() }}</p>
    </div>
  </div>
</div>
<div class="fw-600">
  <div *ngIf="leadInfo?.status?.displayName" class="align-center mt-20">
    <p>{{'GLOBAL.status' | translate}}:</p>
    <p class="pl-8 fw-semi-bold">{{leadInfo?.status.displayName}}</p>
  </div>
  <div *ngIf="leadInfo?.status?.childType?.displayName" class="align-center">
    <p>{{'LEAD_FORM.reason' | translate}}:</p>
    <p class="pl-8 fw-semi-bold">{{leadInfo?.status.childType.displayName}}</p>
  </div>
  <div *ngIf="leadInfo?.scheduledDate && showScheduledDate" class="d-flex">
    <p>{{'LEAD_FORM.scheduled-date' | translate}}:</p>
    <div class="pl-8">
      <p class="fw-semi-bold">{{
        getTimeZoneDate(leadInfo?.scheduledDate,userData?.timeZoneInfo?.baseUTcOffset)
        }}</p>
      <p class="text-truncate-1 break-all text-sm mt-4"
        *ngIf="userData?.shouldShowTimeZone && userData?.timeZoneInfo?.timeZoneName && leadInfo?.scheduledDate && showScheduledDate">
        ({{userData?.timeZoneInfo?.timeZoneName }})</p>
    </div>
  </div>
  <div class="my-8" *ngIf="leadInfo?.notes">
    <p>{{'TASK.notes' | translate}}:</p>
    <p class="fw-semi-bold max-w-350 word-break line-break">{{leadInfo?.notes}}</p>
  </div>
</div>