import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root'
})
export class ShifttimingService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'shifttiming';
  }

  getShiftTiming() {
    return this.http.get(`${this.serviceBaseUrl}/bulk`);
  }

  addShiftTiming(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/bulk`, payload);
  }
}
