<div class="h-100vh d-flex flex-column">
    <div class="flex-between fw-400 bg-dark px-24 py-16 text-white">
        <h5 class="fw-semi-bold">Information</h5>
        <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="px-24 py-10 bg-light-pearl scrollbar">
        <div *ngIf="highlightedData.length" class="mt-8">
            <div class="bg-white align-center px-30 no-validation py-10 br-20 border">
                <span class="justify-end search icon ic-search ic-xxs ic-slate-90"> </span>
                <input placeholder="Search by Information" (input)="highlightText();" [(ngModel)]="searchTerm"
                    class="border-0 outline-0 w-100 bg-white ml-4">
            </div>
        </div>
        <ng-container *ngFor="let info of highlightedData">
            <div class="mt-8 bg-white br-20 p-8">
                <div class="flex-between">
                    <div class="fw-semi-bold">
                        <h6 class="ml-4 header-6 text-black-100" [innerHTML]="info.key"></h6>
                    </div>
                    <div (click)="info.isOpen = !info.isOpen" class="w-26 h-26 br-20 flex-center bg-secondary">
                        <span [ngClass]="info.isOpen ? 'ic-triangle-down' : 'ic-triangle-up rotate-90'"
                            class="icon ic-x-xs ic-black cursor-pointer"></span>
                    </div>
                </div>
            </div>
            <div *ngIf="info.isOpen" class="bg-white br-10 px-10 py-12 mt-4">
                <h6 class="fw-400 text-sm text-black-200" [innerHTML]="info.value"></h6>
            </div>
            <div class="border-bottom-slate mt-8"></div>
        </ng-container>
        <div *ngIf="!highlightedData.length" class="align-center">
            <h6 class="fw-semi-bold">No information Found</h6>
        </div>
    </div>
</div>