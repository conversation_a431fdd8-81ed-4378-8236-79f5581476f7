<div class="coldt-layout border-top h-100">
    <div class="py-16 px-20 w-330 bg-white left-container lg trans-popup text-mud fw-semi-bold">
        <div class="align-center text-coal">
            <span class="icon ic-chevron-left ic-coal ic-xxs cursor-pointer" id="clkCloseAddGroupModal"
                data-automate-id="clkCloseAddGroupModal" (click)="hideAddGroup()"></span>
            <h4 class="fw-semi-bold ml-16">Workflows / <span class="fw-600">Lead Assignment Flow</span></h4>
        </div>
        <div class="align-center mt-40">
            <div class="dot dot-xl bg-light-violet mr-10">1</div>
            <h5 class="fw-600">Module</h5>
            <button class="btn btn-sm btn-accent-green px-12 ml-30">
                <span class="icon ic-person ic-xxs mr-8"></span>{{'GLOBAL.lead' | translate}}
            </button>
        </div>
        <div class="align-center mt-30">
            <div class="dot dot-xl bg-light-violet mr-10">2</div>
            <h5 class="fw-600">Select action type and trigger preference</h5>
        </div>
        <div class="d-flex ml-20 mt-16">
            <div class="form-check form-check-inline btn btn-transparent">
                <input class="radio-check-input" type="radio" name="immediate" id="inpImmediate"
                    data-automate-id="inpImmediate">
                <label class="pl-8" for="inpImmediate">immediate</label>
            </div>
        </div>
        <div class="d-flex">
            <div class="ml-40 mb-16 border-gray-dashed"></div>
            <div>
                <div class="align-center">
                    <div class="border-gray-bottom min-w-10"></div>
                    <div class="form-check form-check-inline">
                        <input class="radio-check-input" type="radio" name="lead" id="inpAdded"
                            data-automate-id="inpAdded">
                        <label class="pl-8" for="inpAdded">When lead is added</label>
                    </div>
                </div>
                <div class="align-center">
                    <div class="border-gray-bottom min-w-10"></div>
                    <div class="form-check form-check-inline">
                        <input class="radio-check-input" type="radio" name="lead" id="inpUpdated"
                            data-automate-id="inpUpdated">
                        <label class="pl-8" for="inpUpdated">When lead is updated</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="align-center mt-30">
            <div class="dot dot-xl bg-light-violet mr-10">3</div>
            <h5 class="fw-600">Set condition to filter records</h5>
        </div>
        <div class="d-flex mt-16 ml-20">
            <div class="form-check form-check-inline btn btn-transparent ">
                <input class="radio-check-input" type="radio" name="records" id="inpAllLeads"
                    data-automate-id="inpAllLeads">
                <label class="pl-8" for="inpAllLeads">All Leads</label>
            </div>
            <div class="form-check form-check-inline btn btn-transparent px-12">
                <input class="radio-check-input" type="radio" name="records" id="inpConditions"
                    data-automate-id="inpConditions">
                <label class="pl-8" for="inpConditions">Based on conditions</label>
            </div>
        </div>
        <div class="ml-20 mt-8 text-coal">This workflow will be applied to all the leads</div>
        <div class="align-center mt-16">
            <div class="mx-10">1</div>
            <div class="align-center w-100">
                <div class="w-50 mx-10">
                    <ng-select [virtualScroll]="true" placeholder="choose" ResizableDropdown>
                    </ng-select>
                </div>
                <div class="w-50 mx-10">
                    <ng-select [virtualScroll]="true" placeholder="choose" ResizableDropdown>
                    </ng-select>
                </div>
            </div>
            <div class="icon ic-plus ic-coal ic-xxs mx-10"></div>
        </div>
        <div class="align-center mt-30">
            <div class="dot dot-xl bg-light-violet mr-10">4</div>
            <h5 class="fw-600">Set actions to be performed</h5>
        </div>
        <div class="align-center mt-16">
            <div class="mx-10">1</div>
            <div class="align-center w-100">
                <div class="w-50 mx-10">
                    <ng-select [virtualScroll]="true" placeholder="choose" ResizableDropdown>
                    </ng-select>
                </div>
                <div class="w-50 mx-10">
                    <ng-select [virtualScroll]="true" placeholder="choose" ResizableDropdown>
                    </ng-select>
                </div>
            </div>
            <div class="icon ic-plus ic-coal ic-xxs mx-10"></div>
        </div>
    </div>
    <div class="zoom-area p-16 right-container">
        <div class="workflow-bg">
            <p class="flex-center">Hi there Zoom Me!!!!!</p>
        </div>
    </div>

    <div class="magnifier btn btn-transparent justify-between w-100px bg-white cursor-default">
        <div class="cursor-pointer" (click)="zoomIn()"><img src="assets/images/zoom-in.svg" alt="zoom-in"></div>
        <div class="cursor-pointer" (click)="zoomOut()"><img src="assets/images/zoom-out.svg" alt="zoom-out"></div>
    </div>
</div>