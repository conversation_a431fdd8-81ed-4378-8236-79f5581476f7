<span *ngIf="showFileUploader === 'first'" [ngClass]="buttonClass">
  <label>
    <input type="file" (change)="onFileSelected($event)" [accept]="requiredFileType"
      [attr.multiple]="allowMultipleFiles ? true: null" #fileUpload>
    <span class="button-browse cursor-pointer" id="uploadImages" data-automate-id="uploadImages">
      {{ labelText }}
    </span>
  </label>
</span>
<div *ngIf="showFileUploader === 'second'"
  class="custom-upload-button d-inline-block bg-white br-5 py-8 overflow-hidden h-50px w-100 text-center cursor-pointer border-dashed-2"
  [ngClass]="{'isUploadingImage': isUploadingImage}" (click)="fileUpload.click()">
  <div class="custom-button-wrapper flex-center">
    <div class="custom-icon-container mr-8" *ngIf="!isUploadingImage">
      <span class="icon ic-cloud-upload ic-large ic-black mr-8"></span>
    </div>
    <div>
      <label>
        <input type="file" (change)="onFileSelected($event)" [accept]="requiredFileType"
          [attr.multiple]="allowMultipleFiles ? true: null" #fileUpload>
        <span *ngIf="!isUploadingImage" class="button-browse cursor-pointer" id="uploadImages"
          data-automate-id="uploadImages" onclick="event.stopPropagation()">
          {{ labelText }}
        </span>
        <span *ngIf="isUploadingImage" class="uploading-text d-inline-block mr-28 h-50 mt-8">Uploading...</span>
      </label>
      <br>
      <p *ngIf="!isUploadingImage" class="text-light-slate mt-4 text-sm">Supported formats.Jpg, Png, Pdf</p>
    </div>
  </div>
  <span (click)="cancelUpload($event);$event.stopPropagation();$event.preventDefault()" *ngIf="isUploadingImage"
    class="position-absolute right-10 top-15 icon ic-close ic-red ic-xs">
  </span>
</div>