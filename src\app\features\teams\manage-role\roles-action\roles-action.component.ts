import { Component, EventEmitter, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { AddRoleComponent } from 'src/app/features/teams/manage-role/add-role/add-role.component';
import {
  getDeletePermissions,
  getEditPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import { DeleteRole } from 'src/app/reducers/teams/teams.actions';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'roles-action',
  templateUrl: './roles-action.component.html',
})
export class RolesActionComponent implements OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canEditRole: boolean = false;
  canDeleteRole: boolean = false;

  constructor(
    public modalService: BsModalService,
    private store: Store<AppState>,
    private modalRef: BsModalRef,
    public trackingService: TrackingService
  ) {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('Roles')) {
          this.canEditRole = true;
        }
      });

    this.store
      .select(getDeletePermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canDelete: any) => {
        if (canDelete?.includes('Roles')) {
          this.canDeleteRole = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editRole() {
    const initialState: any = {
      roleId: this.params.data.id,
      pageNumber: this.params.value[0],
      pageSize: this.params.value[1],
      search: this.params.value[2],
    };
    this.trackingService.trackFeature(`Web.RolePermission.Button.Edit.Click`)
    this.modalService.show(AddRoleComponent, {
      class: 'right-modal modal-400 ph-modal-unset',
      initialState,
    });
  }

  openDeleteModal(data: any) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: data?.name,
      fieldType: 'role',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(
            new DeleteRole(
              data.id,
              this.params.value[0],
              this.params.value[1],
              this.params.value[2]
            )
          );
          this.trackingService.trackFeature(`Web.RolePermission.Button.Delete.Click`)
        }
      });
    }
  }

  isReadOnlyRole(): boolean {
    const roleName = this.params?.data?.name;
    return (
      roleName === 'Basic' ||
      roleName === 'SalesExecutive' ||
      roleName === 'Admin' ||
      roleName === 'HR' ||
      roleName === 'Manager'
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
