import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import {
  AddCity,
  AddCountry,
  AddLocation,
  AddState,
  AddZone,
  BulkUpdateCity,
  BulkUpdateZone,
  DeleteCities,
  DeleteCity,
  DeleteCountry,
  DeleteLocation,
  DeleteState,
  DeleteZone,
  FetchAllCityList,
  FetchAllCityListSuccess,
  FetchAllLocations,
  FetchAllLocationsSuccess,
  FetchAllZoneList,
  FetchAllZoneListSuccess,
  FetchCityList,
  FetchCityListSuccess,
  FetchCountry,
  FetchCountrySuccess,
  FetchInternationalLocations,
  FetchInternationalLocationsSuccess,
  FetchLocationList,
  FetchLocationListSuccess,
  FetchLocationsWithGoogle,
  FetchLocationsWithGoogleSuccess,
  FetchState,
  FetchStateSuccess,
  FetchZoneList,
  FetchZoneListSuccess,
  LocationExcelUpload,
  LocationExcelUploadSuccess,
  SiteActionTypes,
  UpdateCity,
  UpdateCountry,
  UpdateLocation,
  UpdateState,
  UpdateZone,
} from 'src/app/reducers/site/site.actions';
import { getCityFiltersPayload, getCountryFiltersPayload, getLocationFiltersPayload, getStateFiltersPayload, getZoneFiltersPayload } from 'src/app/reducers/site/site.reducer';
import { SiteService } from 'src/app/services/controllers/site.service';
import { CommonService } from 'src/app/services/shared/common.service';

@Injectable()
export class SiteEffects {
  getLocationList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_LOCATION_LIST),
      map((action: FetchLocationList) => action),
      switchMap(() => {
        let filterPayload;
        this.store.select(getLocationFiltersPayload).subscribe((data: any) => {
          filterPayload = data;
        });
        return this.commonService.getModuleListByAdvFilter(filterPayload, true).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLocationListSuccess(resp);
            }
            return new FetchLocationListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addLocation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.ADD_LOCATION),
      switchMap((action: AddLocation) => {
        return this.siteService.addLocation(action.payload).pipe(
          map((resp: any) => {
            this._notificationService.success(resp?.message);
            return new FetchLocationList();
          }),
          catchError((err) => {
            this._notificationService.error(err?.error?.messages?.[0]);
            return of(new OnError(err));
          }));
      })
    )
  );

  updateLocation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.UPDATE_LOCATION),
      switchMap((action: UpdateLocation) => {
        return this.siteService.updateLocation(action.payload, action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Location updated successfully.`
              );
              return new FetchLocationList();
            }
            return new FetchLocationListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteLocation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.DELETE_LOCATION),
      map((action: DeleteLocation) => action),
      switchMap((data: DeleteLocation) => {
        return this.siteService.deleteLocation(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Location deleted successfully.`
              );
              return new FetchLocationList();
            }
            return new FetchLocationListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkUpdateCity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.BULK_UPDATE_CITY),
      switchMap((action: BulkUpdateCity) => {
        return this.siteService.bulkUpdateCity(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `City Updated Successfully`
              );
              return new FetchLocationList();
            }
            return new FetchLocationListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkUpdateZone$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.BULK_UPDATE_ZONE),
      switchMap((action: BulkUpdateZone) => {
        return this.siteService.bulkUpdateZone(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Zone Updated Successfully`
              );
              return new FetchLocationList();
            }
            return new FetchLocationListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAllCityList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_ALL_CITY_LIST),
      map((action: FetchAllCityList) => action),
      switchMap((data: any) => {
        return this.siteService.getAllCityList().pipe(
          map((resp: any) => {
            return new FetchAllCityListSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCityList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_CITY_LIST),
      map((action: FetchCityList) => action),
      switchMap((data: any) => {
        let filterPayload;
        this.store.select(getCityFiltersPayload).subscribe((data: any) => {
          filterPayload = data;
        });
        return this.commonService.getModuleList(filterPayload, true).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCityListSuccess(resp);
            }
            return new FetchCityListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addCity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.ADD_CITY),
      switchMap((action: AddCity) => {
        return this.siteService.addCity(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('City added Successfully');
            } else {
              this._notificationService.error(resp.message);
            }
            return new FetchCityList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateCity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.UPDATE_CITY),
      switchMap((action: UpdateCity) => {
        return this.siteService.updateCity(action.payload, action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`City updated successfully.`);
              return new FetchCityList();
            }
            return new FetchCityListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteCity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.DELETE_CITY),
      map((action: DeleteCity) => action),
      switchMap((action: DeleteCity) => {
        return this.siteService.deleteCity(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`City deleted successfully.`);
              return new FetchCityList();
            }
            return new FetchCityListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteCities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.DELETE_CITIES),
      map((action: DeleteCities) => action),
      switchMap((data: DeleteCities) => {
        return this.siteService.deleteCities(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Cities deleted successfully.`);
              return new FetchCityList();
            }
            return new FetchCityListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAllZoneList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_ALL_ZONE_LIST),
      map((action: FetchAllZoneList) => action),
      switchMap((data: any) => {
        return this.siteService.getAllZoneList().pipe(
          map((resp: any) => {
            return new FetchAllZoneListSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getZoneList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_ZONE_LIST),
      map((action: FetchZoneList) => action),
      switchMap(() => {
        let filterPayload;
        this.store.select(getZoneFiltersPayload).subscribe((data: any) => {
          filterPayload = data;
        });
        return this.commonService.getModuleListByAdvFilter(filterPayload, true).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchZoneListSuccess(resp);
            }
            return new FetchZoneListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addZone$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.ADD_ZONE),
      switchMap((action: AddZone) => {
        return this.siteService.addZone(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(resp.message);
              return new FetchZoneList();
            }
            return new FetchZoneListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateZone$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.UPDATE_ZONE),
      switchMap((action: UpdateZone) => {
        return this.siteService.updateZone(action.payload, action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Zone updated successfully.`);
              return new FetchZoneList();
            }
            return new FetchZoneListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteZone$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.DELETE_ZONE),
      map((action: DeleteZone) => action),
      switchMap((data: DeleteZone) => {
        return this.siteService.deleteZone(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Zone(s) deleted successfully.`);
              return new FetchZoneList();
            }
            return new FetchZoneListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAllLocations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_ALL_LOCATIONS),
      map((action: FetchAllLocations) => action),
      switchMap((data: any) => {
        return this.siteService.getAllLocations().pipe(
          map((resp: any) => {
            return new FetchAllLocationsSuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAllLocationsWithGoggleMap$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_LOCATIONS_WITH_GOOGLE_API),
      map((action: FetchLocationsWithGoogle) => action),
      switchMap((data: any) => {
        return this.siteService.getAllLocationsWithGoogleMap(data.payload, data.page, data.pagesize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLocationsWithGoogleSuccess(resp.items);
            }
            return new FetchLocationsWithGoogleSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addBulkLocationExcel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.LOCATION_EXCEL_UPLOAD),
      switchMap((action: LocationExcelUpload) => {
        return this.siteService.uploadExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(resp.message);
              return new LocationExcelUploadSuccess(resp.data);
            }
            return new LocationExcelUploadSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );


  // ............................. STATE ............................

  fetchStateList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_STATE),
      map((action: FetchState) => action),
      switchMap((data: any) => {
        let filterPayload;
        this.store.select(getStateFiltersPayload).subscribe((data: any) => {
          filterPayload = data;
        });
        return this.commonService.getModuleList(filterPayload, true).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchStateSuccess(resp);
            }
            return new FetchStateSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addState$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.ADD_STATE),
      switchMap((action: AddState) => {
        return this.siteService.addState(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('State added Successfully');
            } else {
              this._notificationService.error(resp.message);
            }
            return new FetchState();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateState$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.UPDATE_STATE),
      switchMap((action: UpdateState) => {
        return this.siteService.updateState(action.payload, action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`State updated successfully.`);
              return new FetchState();
            }
            return new FetchStateSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteState$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.DELETE_STATE),
      map((action: DeleteState) => action),
      switchMap((action: DeleteState) => {
        return this.siteService.deleteState(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`State deleted successfully.`);
              return new FetchState();
            }
            return new FetchStateSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );
  // -------------------- COUNTRY -------------------------
  fetchCountryList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_COUNTRY),
      map((action: FetchCountry) => action),
      switchMap((data: any) => {
        let filterPayload;
        this.store.select(getCountryFiltersPayload).subscribe((data: any) => {
          filterPayload = data;
        });
        return this.commonService.getModuleList(filterPayload, true).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCountrySuccess(resp);
            }
            return new FetchCountrySuccess(resp);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addCountry$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.ADD_COUNTRY),
      switchMap((action: AddCountry) => {
        return this.siteService.addCountry(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Country added Successfully');
            } else {
              this._notificationService.error(resp.message);
            }
            return new FetchCountry();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateCountry$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.UPDATE_COUNTRY),
      switchMap((action: UpdateCountry) => {
        return this.siteService.updateCountry(action.payload, action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Country updated successfully.`);
              return new FetchCountry();
            }
            return new FetchCountrySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteCountry$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.DELETE_COUNTRY),
      map((action: DeleteCountry) => action),
      switchMap((action: DeleteCountry) => {
        return this.siteService.deleteCountry(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Country deleted successfully.`);
              return new FetchCountry();
            }
            return new FetchCountrySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAllCustomLocations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SiteActionTypes.FETCH_INTERNATIONAL_LOCATIONS),
      map((action: FetchInternationalLocations) => action),
      switchMap((data: any) => {
        return this.siteService.getAllCustomLocations(data.payload, data.page, data.pagesize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchInternationalLocationsSuccess(resp.items);
            }
            return new FetchInternationalLocationsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private siteService: SiteService,
    private _notificationService: NotificationsService,
    private store: Store<AppState>,
    private commonService: CommonService
  ) { }
}
