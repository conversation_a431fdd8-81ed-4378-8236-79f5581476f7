<div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
        <h3>{{ 'LEADS.bulk' | translate }} {{ 'GLOBAL.update' | translate }} {{ 'GLOBAL.status' | translate }}</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="pr-20 flex-column h-100-97 scrollbar">
        <div class="pl-20">
            <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
                {{sameStatusRows?.length}}
            </div>
            <div class="scrollbar table-scrollbar ip-w-100-40" id="bulk-status-table">
                <table class="table standard-table no-vertical-border">
                    <thead>
                        <tr class="w-100 text-nowrap">
                            <th class="w-210">{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
                            <th class="w-120">{{ 'GLOBAL.status' | translate }}</th>
                            <th class="w-80">{{ 'GLOBAL.actions' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-secondary fw-semi-bold"
                        [ngClass]="!canUpdateBookedLead && isBookedLead()?'max-h-100-176':'max-h-100-433' ">
                        <ng-container>
                            <tr *ngFor="let lead of sameStatusRows">
                                <td class="w-210">
                                    <div class="text-truncate-1 break-all">{{ lead.name }}</div>
                                </td>
                                <td class="w-120">
                                    <div class="text-truncate-1 break-all">{{ lead.status?.displayName }}
                                    </div>
                                </td>
                                <td class="w-70px">
                                    <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)"
                                        class="bg-light-red icon-badge" id="clkDeleteBulkUpdateStatus"
                                        data-automate-id="clkDeleteBulkUpdateStatus">
                                        <span class="icon ic-delete m-auto ic-xxs"></span></a>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="w-100 mt-20 bulk-update">
            <custom-status-change *ngIf="isCustomStatusEnabled" [leadInfo]="sameStatusRows"
                (payload)="updateStatus($event)" [canUpdateStatus]="canUpdateStatus"></custom-status-change>
            <status-change *ngIf="!isCustomStatusEnabled" [leadInfo]="sameStatusRows" (payload)="updateStatus($event)"
                [canUpdateStatus]="canUpdateStatus"></status-change>
        </div>
    </div>
</div>

<ng-template #trackerInfoModal>
    <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white">Bulk Update Status</h5>
    <div class="p-20 flex-center-col">
        <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break">Bulk Update Status In progress.
        </h4>
        <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
            <span class="cursor-pointer text-accent-green header-3 fw-600"
                (click)="openBulkUpdatedStatus()">“Bulk Operation 
                Tracker”</span> to view updated status
        </h5>
        <button class="btn-green mt-30" (click)="modalService.hide()">
            {{'BULK_LEAD.got-it' | translate}}</button>
    </div>
  </ng-template>