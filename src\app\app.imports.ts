import { HttpClient, HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { IdlePreloadModule } from 'angular-idle-preload';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { AgGridModule } from 'ag-grid-angular';
import { SimpleNotificationsModule } from 'angular2-notifications';
import { ModalModule } from 'ngx-bootstrap/modal';
import { GoogleChartsModule } from 'angular-google-charts';
import { BrowserModule } from '@angular/platform-browser';
import { LottieModule } from 'ngx-lottie';
import {
  OwlDateTimeModule,
  OwlNativeDateTimeModule,
} from '@danielmoncada/angular-datetime-picker';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { DragScrollModule } from 'ngx-drag-scroll';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatNativeDateModule } from '@angular/material/core';
import { AngularFireModule } from '@angular/fire/compat';
import { AngularFirestoreModule } from '@angular/fire/compat/firestore';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatMenuModule } from '@angular/material/menu';
import { ServiceWorkerModule } from '@angular/service-worker';
import { CanvasJSAngularChartsModule } from '@canvasjs/angular-charts';

import { AppEffects } from 'src/app/app.effects';
import { reducer } from 'src/app/app.reducer';
import { environment as env } from 'src/environments/environment';
import { SharedModule } from 'src/app/shared/shared.module';
import { APP_VERSION } from 'src/app/app.constants';
import { environment } from 'src/environments/environment';
import { WebcamModule } from 'ngx-webcam';

export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(
    httpClient,
    './assets/i18n/',
    `.json?v=${APP_VERSION}`
  );
}

export function playerFactory() {
  return import('lottie-web');
}

let REDUX_DEVTOOLS: any[] = [];

if (!env.production) {
  REDUX_DEVTOOLS = [
    ...REDUX_DEVTOOLS,
    StoreDevtoolsModule.instrument({
      maxAge: 25,
    }),
  ];
}

export const APP_IMPORTS = [
  StoreModule.forRoot(reducer),
  EffectsModule.forRoot(AppEffects),
  ...REDUX_DEVTOOLS,
  ModalModule.forRoot(),
  GoogleMapsModule,
  MatNativeDateModule,
  AgGridModule,
  FormsModule,
  ReactiveFormsModule,
  WebcamModule,
  HttpClientModule,
  BrowserAnimationsModule,
  TranslateModule.forRoot({
    loader: {
      provide: TranslateLoader,
      useFactory: HttpLoaderFactory,
      deps: [HttpClient],
    },
  }),
  NgSelectModule,
  SimpleNotificationsModule.forRoot(),
  RouterModule,
  IdlePreloadModule.forRoot(),
  LottieModule.forRoot({ player: playerFactory }),
  OwlDateTimeModule,
  OwlNativeDateTimeModule,
  SharedModule,
  GoogleChartsModule.forRoot(),
  BrowserModule,
  NgCircleProgressModule.forRoot({}),
  DragScrollModule,
  AngularFireModule.initializeApp(env.firebaseConfig),
  AngularFirestoreModule,
  DragDropModule,
  MatMenuModule,
  CanvasJSAngularChartsModule,
  ServiceWorkerModule.register('ngsw-worker.js', {
    enabled: environment.production,
    // Register the ServiceWorker as soon as the application is stable
    // or after 30 seconds (whichever comes first).
    registrationStrategy: 'registerWhenStable:30000',
  }),
];
