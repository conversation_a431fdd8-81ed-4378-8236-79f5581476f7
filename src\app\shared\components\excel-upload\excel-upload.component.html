<div class="py-30 px-24 position-relative">
  <div class="flex-center-col">
    <div class="header-3 text-coal">Importing users becomes more easier</div>
    <div class="flex-center mt-20">
      <img src="../../../../assets/images/file-upload.svg">
      <span class="w-80 ip-w-45 ph-w-30px"
        [ngClass]="currentStep == 1 || currentStep == 2 ? 'border-dashed-bottom-2' : 'border-accent-green'"></span>
      <span class="w-80 ip-w-45 ph-w-30px"
        [ngClass]="currentStep !== 3 ? 'border-dashed-bottom-2' : 'border-accent-green'"></span>
      <img src="../../../../assets/images/review-import.svg" [ngClass]="{'gray-scale' : currentStep !== 3}">
    </div>
    <div class="flex-center">
      <div class="flex-center-col pl-16">
        <div class="fw-600 text-sm text-gray-90 mt-6">Step 1</div>
        <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">
          {{'PROJECTS_BLOCK_INFO.upload-file' | translate}}</div>
      </div>
      <span class="w-115 ip-w-45 ph-w-15"></span>
      <div class="flex-center-col">
        <div class="fw-600 text-sm text-gray-90 mt-6">Step 2</div>
        <div class="fw-semi-bold text-large text-black-100 mt-2 text-center">{{'LEADS.review-import' | translate
          }}
        </div>
      </div>
    </div>
  </div>
  <ng-container *ngIf="currentStep == 1 || currentStep == 2">
    <div class="mt-20 p-20 bg-white br-4">
      <div class="fw-700 header-3 text-black-100">{{'BULK_LEAD.importing-description' | translate }}</div>
      <div class="flex-between mt-20">
        <div class="d-flex">
          <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
          <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 1 :</span>
          <a [href]="excelTemplatePath" class="d-flex">
            <div class="border-accent-green br-50 mr-4">
              <span class="icon ic-down-to-line ic-accent-green ic-x-xs m-4"></span>
            </div>
            <span class="fw-700 text-accent-green text-large text-decoration-underline">Download
              template</span>
          </a>
        </div>
      </div>
      <div class="fw-semi-bold text-sm text-dark-gray mt-6 ml-60">
        {{'BULK_LEAD.download-description' | translate}}</div>
      <div class="border-bottom-slate-20 mt-12 ml-60"></div>
      <div class="d-flex mt-12">
        <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
        <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 2 :</span>
        <div>
          <div class="fw-700 text-black-200 text-large">{{'BULK_LEAD.prepare-import-file'| translate }}</div>
          <div class="fw-semi-bold text-sm text-dark-gray mt-6">change the dummy data in the sample file to
            your users details</div>
        </div>
      </div>
      <div class="border-bottom-slate-20 mt-12 ml-60"></div>
      <div class="d-flex mt-12">
        <span class="dot dot-xxs bg-slate-250 mr-8 mt-6"></span>
        <span class="text-nowrap fw-700 text-dark-gray text-large mr-10">step 3 :</span>
        <div>
          <div class="fw-700 text-black-200 text-large">{{'BULK_LEAD.upload-your-file'| translate }}</div>
          <div class="fw-semi-bold text-sm text-dark-gray mt-6">{{'BULK_LEAD.upload-description' | translate
            }}</div>
        </div>
      </div>
    </div>
    <ng-container *ngIf="currentStep == 1">
      <div class="version-two">
        <browse-drop-upload [allowedFileType]="'excel'" [isExcelFile]="true"
          (uploadedFile)="onFileSelection($event)"></browse-drop-upload>
      </div>
    </ng-container>
    <ng-container *ngIf="currentStep == 2">
      <div class="bg-white px-20 py-40">
        <div class="border-green-dashed-2 flex-center-col bg-green-150">
          <div class="align-center-col py-40 text-black-200">
            <div class="fw-semi-bold header-4  text-center">{{'BULK_LEAD.successfully-upload' | translate }}
            </div>
            <div class="mt-4 fw-700 header-3 text-truncate-1 break-all">{{selectedFile?.name}}</div>
            <a class="align-center fw-600 text-large mt-10 text-lowercase">
              <div class="text-red-450 mr-10 text-decoration-underline" (click)="currentStep = 1">
                {{'BUTTONS.delete' | translate }}
              </div>
              <div class="text-aqua-750 text-decoration-underline" (click)="replaceFile()">
                {{'BUTTONS.replace' | translate }}
              </div>
            </a>
            <input type="file" #fileInput (change)="onFileSelection($event.target.files[0])" />
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="currentStep == 1 || currentStep == 2">
      <div class="border-bottom-slate-20"></div>
      <div class="flex-end p-10 bg-white">
        <a class="fw-600 text-large text-black-200 text-decoration-underline mr-20" (click)="navigateToHome()">
          {{'BUTTONS.cancel' | translate}} Import</a>
        <span [ngClass]="currentStep == 1 ? 'btn-gray pe-none' : 'btn-green'" (click)="uploadFile()">
          {{'BUTTONS.proceed' | translate }}</span>
      </div>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="currentStep == 3">
    <div class="bg-white p-20 mt-20 br-4 scrollbar h-100-290">
      <div class="fw-700 header-3 text-black-100">Review & finalize your Import</div>
      <div class="fw-semi-bold header-4 text-black-100">please review the complete process of user
        importing and finish it.</div>
      <div class="align-center mt-20"> <span class="dot dot-xxs bg-slate-250 mr-8"></span><span
          class="fw-700 text-black-200 text-large mr-10 text-nowrap">File uploaded:</span>
        <span class="fw-600 text-large text-accent-green text-decoration-underline">{{selectedFile?.name}}</span>
      </div>
      <div class="border-bottom-slate-20 mt-8 ml-24"></div>
      <div class="mt-16">
        <div class="align-center">
          <span class="dot dot-xxs bg-slate-250 mr-8"></span><span class="fw-700 text-large text-black-200">
            Default password : 123Pa$$word!</span>
        </div>
      </div>
    </div>
    <div class="border-bottom-slate-20"></div>
    <div class="flex-end p-10 bg-white">
      <span class="fw-600 text-large text-black-200 text-decoration-underline mr-20 cursor-pointer"
        (click)="navigateToHome()">{{'BUTTONS.cancel' | translate}} Import</span>
      <span class="br-4 p-8 border mr-10 cursor-pointer" (click)="currentStep = 2"><span
          class="ic-chevron-left ic-light-gray ic-x-xs mr-8"></span><span
          class="fw-600 text-large text-dark-gray">{{'BUTTONS.back' | translate }}</span></span>
      <span class="btn-green w-140" (click)="sendDetails(trackerInfoModal)">Finish
        Importing</span>
    </div>
  </ng-container>
</div>
<ng-template #trackerInfoModal>
  <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white text-capitalize">User(s) Upload Scheduled</h5>
  <div class="p-20 flex-center-col">
    <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break text-capitalize">User(s) upload
      has been scheduled.
    </h4>
    <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
      <span class="cursor-pointer text-accent-green header-3 fw-600" (click)="openBulkUploadedStatusModal()">“Excel
        Upload Tracker”</span> to view upload status
    </h5>
    <button class="btn-green mt-30" (click)="navigateToHome();modalService.hide()">
      {{'BULK_LEAD.got-it' | translate}}</button>
  </div>
</ng-template>