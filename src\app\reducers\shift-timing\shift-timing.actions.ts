import { Action } from "@ngrx/store";


export enum ShiftTimingActionTypes {
    ADD_SHIFT_TIMING = '[SHIFT_TIMING] Add Shift Timing',
    ADD_SHIFT_TIMING_SUCCESS = '[SHIFT_TIMING] ADD Shift Timing Success',
    FETCH_BULK_SHIFT_TIMING = '[SHIFT_TIMING] Fetch Bulk Shift Timing',
    FETCH_BULK_SHIFT_TIMING_SUCCESS = '[SHIFT_TIMING] Fetch Bulk Shift Timing Success',
}

export class AddShiftTiming implements Action {
    readonly type: string = ShiftTimingActionTypes.ADD_SHIFT_TIMING;
    constructor(public payload: any) { }
}

export class AddShiftTimingSuccess implements Action {
    readonly type: string = ShiftTimingActionTypes.ADD_SHIFT_TIMING_SUCCESS;
    constructor() { }
}

export class FetchBulkShiftTiming implements Action {
    readonly type: string = ShiftTimingActionTypes.FETCH_BULK_SHIFT_TIMING;
    constructor() { }
}

export class FetchBulkShiftTimingSuccess implements Action {
    readonly type: string = ShiftTimingActionTypes.FETCH_BULK_SHIFT_TIMING_SUCCESS;
    constructor(public response: any) { }
}

