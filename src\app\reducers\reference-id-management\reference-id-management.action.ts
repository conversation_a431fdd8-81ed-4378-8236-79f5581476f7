import { Action } from '@ngrx/store';
import { LeadExcel, MapColumnsExcel } from 'src/app/core/interfaces/leads.interface';

export enum ReferenceIdManagementActionTypes {
    UPDATE_FILTERS_PAYLOAD = '[REFERENCE_ID_MANAGEMENT] Update Filters Payload',
    FETCH_REFERENCE_ID = '[REFERENCE_ID_MANAGEMENT] Fetch Reference Id',
    FETCH_REFERENCE_ID_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Fetch Reference Id Success',
    ADD_REFERENCE_ID = '[REFERENCE_ID_MANAGEMENT] Add Reference Id',
    ADD_REFERENCE_ID_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Add Reference Id Success',
    UPDATE_REFERENCE_ID = '[REFERENCE_ID_MANAGEMENT] Update Reference Id',
    DELETE_REFERENCE_ID = '[REFERENCE_ID_MANAGEMENT] Delete Reference Id',
    REFERENCE_EXCEL_UPLOAD = '[REFERENCE_ID_MANAGEMENT] Upload Reference Excel File',
    REFERENCE_EXCEL_UPLOAD_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Upload Reference Excel File Success',
    UPLOAD_MAPPED_COLUMNS = '[REFERENCE_ID_MANAGEMENT] Upload Mapped Column Data',
    FETCH_EXCEL_UPLOADED_LIST = '[REFERENCE_ID_MANAGEMENT] Fetch Excel Uploaded List',
    FETCH_EXCEL_UPLOADED_LIST_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Fetch Excel Uploaded List Success',
    FETCH_LISTING_SOURCE = '[REFERENCE_ID_MANAGEMENT] Fetch Listing Source',
    FETCH_LISTING_SOURCE_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Fetch Listing Source Success',
    FETCH_REFERENCE_ID_COUNT = '[REFERENCE_ID_MANAGEMENT] Fetch Reference Id Count',
    FETCH_REFERENCE_ID_COUNT_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Fetch Reference Id Count Success',
    FETCH_REFERENCE_ID_LIST = '[REFERENCE_ID_MANAGEMENT] Fetch Reference Id List',
    FETCH_REFERENCE_ID_LIST_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Fetch Reference Id List Success',
    DOES_REFERENCE_ID_EXIST = '[REFERENCE_ID_MANAGEMENT] Does Reference Id Exist',
    DOES_REFERENCE_ID_EXIST_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Does Reference Id Exist Success',
    REFERENCE_BULK_DELETE = '[REFERENCE_ID_MANAGEMENT] Bulk Delete Reference Id',
    REFERENCE_BULK_DELETE_SUCCESS = '[REFERENCE_ID_MANAGEMENT] Bulk Delete Reference Id Success',
}
export class UpdateReferencePayload implements Action {
    readonly type = ReferenceIdManagementActionTypes.UPDATE_FILTERS_PAYLOAD;
    constructor(public payload: any) { }
}

export class FetchAllReferenceIds implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID;
    constructor() { }
}

export class FetchAllReferenceIdsSuccess implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_SUCCESS;
    constructor(public response: any) { }
}

export class FetchAllReferenceIdsList implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_LIST;
    constructor() { }
}

export class FetchAllReferenceIdsListSuccess implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_LIST_SUCCESS;
    constructor(public response: any) { }
}

export class AddReferenceId implements Action {
    readonly type = ReferenceIdManagementActionTypes.ADD_REFERENCE_ID;
    constructor(public payload: any) { }
}

export class AddReferenceIdSuccess implements Action {
    readonly type = ReferenceIdManagementActionTypes.ADD_REFERENCE_ID_SUCCESS;
    constructor() { }
}

export class UpdateReferenceId implements Action {
    readonly type = ReferenceIdManagementActionTypes.UPDATE_REFERENCE_ID;
    constructor(public payload: any) { }
}

export class DeleteReferenceId implements Action {
    readonly type = ReferenceIdManagementActionTypes.DELETE_REFERENCE_ID;
    constructor(public id: any) { }
}

export class ReferenceExcelUpload implements Action {
    readonly type: string = ReferenceIdManagementActionTypes.REFERENCE_EXCEL_UPLOAD;
    constructor(public file: File) { }
}

export class ReferenceExcelUploadSuccess implements Action {
    readonly type: string = ReferenceIdManagementActionTypes.REFERENCE_EXCEL_UPLOAD_SUCCESS;
    constructor(public resp: LeadExcel) { }
}

export class UploadReferenceMappedColumns implements Action {
    readonly type: string = ReferenceIdManagementActionTypes.UPLOAD_MAPPED_COLUMNS;
    constructor(public payload: MapColumnsExcel) { }
}

export class FetchReferenceExcelUploadedList implements Action {
    readonly type: string = ReferenceIdManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST;
    constructor(public pageNumber: number, public pageSize: number) { }
}

export class FetchReferenceExcelUploadedSuccess implements Action {
    readonly type: string = ReferenceIdManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS;
    constructor(public response: any[] = []) { }
}

export class FetchReferenceListingSource implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_LISTING_SOURCE;
    constructor() { }
}

export class FetchReferenceListingSourceSuccess implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_LISTING_SOURCE_SUCCESS;
    constructor(public response: any) { }
}

export class FetchReferenceIdCounts implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_COUNT;
    constructor() { }
}

export class FetchReferenceIdCountsSuccess implements Action {
    readonly type = ReferenceIdManagementActionTypes.FETCH_REFERENCE_ID_COUNT_SUCCESS;
    constructor(public response: any) { }
}

export class DoesRefIdExist implements Action {
    readonly type = ReferenceIdManagementActionTypes.DOES_REFERENCE_ID_EXIST;
    constructor(public refId: string, public sourceId: string) { }
}

export class DoesRefIdExistSuccess implements Action {
    readonly type = ReferenceIdManagementActionTypes.DOES_REFERENCE_ID_EXIST_SUCCESS;
    constructor(public response: any) { }
}

export class ReferenceBulkDelete implements Action {
    readonly type = ReferenceIdManagementActionTypes.REFERENCE_BULK_DELETE;
    constructor(public ids: string[]) { }
}

export class ReferenceBulkDeleteSuccess implements Action {
    readonly type = ReferenceIdManagementActionTypes.REFERENCE_BULK_DELETE_SUCCESS;
    constructor(public response: any) { }
}
