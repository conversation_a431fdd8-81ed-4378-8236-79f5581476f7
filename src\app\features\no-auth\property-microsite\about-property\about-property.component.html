<ng-container *ngIf="propertyInfo?.links?.length" [ngClass]="{'gray-scale': isPropertySoldOut}">
    <div class="d-flex">
        <h4 class="text-mud mt-20 fw-semi-bold ph-br-mud">Property URL(s)</h4>
    </div>
    <div class="align-center flex-wrap">
        <div *ngFor="let link of propertyInfo?.links">
            <a href="{{link}}" target="_blank"
                class="flex-center px-8 py-4 border-accent-green br-20 mt-4 mr-8 shadow-hover bg-slate">
                <span class="icon ic-link ic-xs ic-black"></span><u class="break-all">{{link}}</u>
            </a>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="propertyInfo?.brochures?.length">
    <div class="d-flex">
        <h4 class="text-mud mt-20 fw-semi-bold ph-br-mud">Document(s)</h4>
    </div>
    <div class="align-center flex-wrap">
        <div *ngFor="let doc of propertyInfo?.brochures">
            <a [href]="s3BucketPath+doc?.url.replace(s3BucketPath, '')" target="_blank"
                class="flex-center px-8 py-4 border-accent-green br-20 mt-4 mr-8 shadow-hover bg-slate">
                <span class="icon ic-file ic-xs ic-black"></span><u class="break-all">{{doc?.name}}</u></a>
        </div>
    </div>
</ng-container>
<ng-container *ngIf="propertyInfo?.aboutProperty">
    <h5 class="mt-20 display-mble">
        <div class="d-flex">
            <h4 class="text-mud mt-20 fw-semi-bold ph-br-mud text-nowrap">
                {{ 'PROPERTY.about-property' | translate}}</h4>
        </div>
        <div class="mt-12 ml-8 fw-400 break-word break-all"
            [ngClass]="isView == false ? 'h-100' : 'max-h-60 overflow-hidden'"
            [innerHTML]="getSanitizedHtml(propertyInfo.aboutProperty)"></div>
        <div *ngIf="propertyInfo.aboutProperty.length > 150"
            class="justify-center fw-600 text-accent-green cursor-pointer">
            <a class="align-end" *ngIf="isView" (click)="isView = !isView">
                <span class="border-bottom-green mb-4">read more</span>
                <ng-lottie [options]='chevron' height='35px' width="35px"></ng-lottie>
            </a>
            <a class="align-start" *ngIf="!isView" (click)="isView = !isView"><span
                    class="border-bottom-green mb-4">read less</span>
                <ng-lottie [options]='chevron' height='35px' width="35px" style="transform: rotate(180deg)"></ng-lottie>
            </a>
        </div>
    </h5>
    <div class="display-web">
        <h4 class="text-mud mt-20 fw-semi-bold">{{ 'PROPERTY.about-property' | translate }}</h4>
        <h5 class="mt-8 fw-400 break-word break-all"
            [ngClass]="isView == false ? 'h-100' : 'max-h-100px overflow-hidden'"
            [innerHTML]="getSanitizedHtml(propertyInfo.aboutProperty)"></h5>
        <div *ngIf="propertyInfo.aboutProperty.length > 200"
            class="justify-center fw-600 text-accent-green cursor-pointer">
            <a class="align-end" *ngIf="isView" (click)="isView = !isView">
                <span class="border-bottom-green mb-4">read more</span>
                <ng-lottie [options]='chevron' height='35px' width="35px"></ng-lottie>
            </a>
            <a class="align-start" *ngIf="!isView" (click)="isView = !isView"><span
                    class="border-bottom-green mb-4">read less</span>
                <ng-lottie [options]='chevron' height='35px' width="35px" style="transform: rotate(180deg)"></ng-lottie>
            </a>
        </div>
    </div>
</ng-container>
<ng-contaier *ngIf="propertyInfo?.regulatoryInfomation">
    <h5 class="text-mud my-8 fw-600">Regulatory Information</h5>
    <div class="flex-col">
        <div *ngIf="propertyInfo?.regulatoryInfomation?.reference" class="d-flex mt-2">
            <h6 class="text-mud">Reference -</h6>
            <h6 class="ml-2 text-accent-green">{{propertyInfo?.regulatoryInfomation?.reference}}</h6>
        </div>
        <div *ngIf="propertyInfo?.regulatoryInfomation?.listingExpireDate" class="d-flex mt-2">
            <h6 class="fw-semi-bold text-mud">Listing ExpireDate -
            </h6>
            <h6 class="ml-2 text-accent-green">{{propertyInfo?.regulatoryInfomation?.listingExpireDate}}
            </h6>
        </div>
        <div *ngIf="propertyInfo?.regulatoryInfomation?.brokerName?.trim()" class="d-flex mt-2">
            <h6 class="text-mud">Broker Name - </h6>
            <h6 class="ml-2 text-accent-green">{{propertyInfo?.regulatoryInfomation?.brokerName}}</h6>
        </div>
        <div *ngIf="propertyInfo?.regulatoryInfomation?.brokerLicenseNo" class="d-flex mt-2">
            <h6 class="text-mud">Broker LicenseNo - </h6>
            <h6 class="text-accent-green">{{propertyInfo?.regulatoryInfomation?.brokerLicenseNo}}</h6>
        </div>
        <div *ngIf="propertyInfo?.regulatoryInfomation?.dldPermitNumber" class="d-flex mt-2">
            <h6 class="text-mud">DLD Permit Number - </h6>
            <h6 class="ml-2 text-accent-green">{{propertyInfo?.regulatoryInfomation?.dldPermitNumber}}</h6>
        </div>
        <div *ngIf="propertyInfo?.regulatoryInfomation?.dtcmPermitNumber" class="d-flex mt-2">
            <h6 class="text-mud">DTMC Permit Number - </h6>
            <h6 class="ml-2 text-accent-green">{{propertyInfo?.regulatoryInfomation?.dtcmPermitNumber}}
            </h6>
        </div>
        <div *ngIf="propertyInfo?.regulatoryInfomation?.adrecPermitNumber" class="d-flex mt-2">
            <h6 class="text-mud">ADREC Permit Number - </h6>
            <h6 class="ml-2 text-accent-green">{{propertyInfo?.regulatoryInfomation?.adrecPermitNumber}}
            </h6>
        </div>
    </div>
</ng-contaier>
<ng-container
    *ngIf="propertyInfo?.dimension?.area || propertyInfo?.dimension?.carpetArea || propertyInfo?.dimension?.buildUpArea || propertyInfo?.dimension?.saleableArea">
    <div class="d-flex">
        <h4 class="text-mud mt-20 fw-semi-bold ph-br-mud">Area</h4>
    </div>
</ng-container>
<div class="d-flex flex-wrap">
    <div *ngIf="propertyInfo?.dimension?.area" class="mr-40 mt-4 w-100px">
        <div class="text-sm">Property Area</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo.dimension?.area}}
            {{getAreaUnit(propertyInfo.dimension?.areaUnitId, areaSizeUnits)?.unit}}
        </h5>
    </div>
    <div *ngIf="propertyInfo?.dimension?.carpetArea" class="mr-40 mt-4 w-100px">
        <div class="text-sm">Carpet Area</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo.dimension?.carpetArea}}
            {{getAreaUnit(propertyInfo.dimension?.carpetAreaId, areaSizeUnits)?.unit}}
        </h5>
    </div>
    <div *ngIf="propertyInfo?.dimension?.buildUpArea" class="mr-40 mt-4 w-100px">
        <div class="text-sm">Built-up Area</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo.dimension?.buildUpArea}}
            {{getAreaUnit(propertyInfo.dimension?.buildUpAreaId, areaSizeUnits)?.unit}}
        </h5>
    </div>
    <div *ngIf="propertyInfo?.dimension?.saleableArea" class="mr-40 mt-4 w-100px">
        <div class="text-sm">Saleable Area</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo.dimension?.saleableArea}}
            {{getAreaUnit(propertyInfo.dimension?.saleableAreaId, areaSizeUnits)?.unit}}
        </h5>
    </div>
    <ng-container *ngIf="globalSettingsDetails?.shouldEnablePropertyListing">
        <div *ngIf="propertyInfo?.dimension?.netArea" class="mt-4">
            <div class="text-sm">Net Area</div>
            <h5 class="fw-semi-bold text-accent-green">{{propertyInfo.dimension?.netArea}}
                {{getAreaUnit(propertyInfo.dimension?.netAreaUnitId, areaSizeUnits)?.unit}}
            </h5>
        </div>
    </ng-container>
</div>
<!-- ----   Additional Info ---- -->
<ng-container
    *ngIf="propertyInfo?.securityDeposit || propertyInfo?.monetaryInfo?.monthlyRentAmount || propertyInfo?.dimension?.commonAreaCharges || lockInPeriod || noticePeriod || propertyInfo?.coWorkingOperatorName || propertyInfo?.coWorkingOperatorPhone || propertyInfo?.tenantContactInfo?.name || propertyInfo?.tenantContactInfo?.phone || propertyInfo?.noOfFloorsOccupied?.length || propertyInfo?.coWorkingOperator">
    <div class="d-flex">
        <h4 class="text-mud mt-20 fw-semi-bold ph-br-mud">Additional info</h4>
    </div>
</ng-container>
<div class="d-flex flex-wrap">
    <div *ngIf="propertyInfo?.securityDeposit" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Security Deposit</div>
        <h5 class="fw-semi-bold text-accent-green">{{secuirtyDepositDate}}</h5>
    </div>
    <div *ngIf="propertyInfo?.monetaryInfo?.monthlyRentAmount" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Rent amount per month</div>
        <h5 class="fw-semi-bold text-accent-green">
            {{propertyInfo?.monetaryInfo?.monthlyRentAmount}}{{propertyInfo?.monetaryInfo?.currency}}</h5>
    </div>
    <div *ngIf="propertyInfo?.dimension?.commonAreaCharges" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Common Area charges</div>
        <h5 class="fw-semi-bold text-accent-green">
            {{propertyInfo.dimension?.currency}}{{propertyInfo.dimension?.commonAreaCharges}} per
            {{getAreaUnit(propertyInfo.dimension?.commonAreaChargesId, areaSizeUnits)?.unit}}
        </h5>
    </div>
    <div *ngIf="lockInPeriod" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Lock in period</div>
        <h5 class="fw-semi-bold text-accent-green">{{lockInPeriod}}</h5>
    </div>
    <div *ngIf="noticePeriod" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Notice period</div>
        <h5 class="fw-semi-bold text-accent-green">{{noticePeriod}}</h5>
    </div>
    <div *ngIf="propertyInfo?.coWorkingOperator" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Co Working Operator</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo?.coWorkingOperator}}</h5>
    </div>
    <div *ngIf="propertyInfo?.coWorkingOperatorName" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Operator POC name</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo?.coWorkingOperatorName}}</h5>
    </div>
    <div *ngIf="propertyInfo?.coWorkingOperatorPhone" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Operator POC phone</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo?.coWorkingOperatorPhone}}</h5>
    </div>
    <div *ngIf="propertyInfo?.tenantContactInfo?.name" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Tenant POC name</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo?.tenantContactInfo?.name}}</h5>
    </div>
    <div *ngIf="propertyInfo?.tenantContactInfo?.phone" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">Tenant POC phone</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo?.tenantContactInfo?.phone}}</h5>
    </div>
    <!-- <div *ngIf="propertyInfo?.noOfFloorsOccupied" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap">No. of floor occupied</div>
        <h5 class="fw-semi-bold text-accent-green">{{propertyInfo?.noOfFloorsOccupied}}</h5>
    </div> -->

    <div *ngIf="propertyInfo?.noOfFloorsOccupied?.length" class="mr-40 mt-10 w-100px">
        <div class="text-sm text-nowrap w-100">No. of floor occupied</div>
        <drag-scroll class="scrollbar scroll-hide">
            <div class="fw-semi-bold text-accent-green" [title]="propertyInfo?.noOfFloorsOccupied">
                {{propertyInfo?.noOfFloorsOccupied}}</div>
        </drag-scroll>
    </div>

</div>