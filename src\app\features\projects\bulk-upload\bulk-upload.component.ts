import {
  Component,
  ElementRef,
  <PERSON><PERSON><PERSON>ter,
  On<PERSON><PERSON><PERSON>,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import {
  PROJECT_EXCEL_TEMPLATE,
  ProjectDisplayColumns,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  getAppName,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { FetchProjectExcelUploadedList, ProjectExcelUpload, UploadProjectMappedColumns } from 'src/app/reducers/project/project.action';
import { getProjectColumnHeadings } from 'src/app/reducers/project/project.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'bulk-upload',
  templateUrl: './bulk-upload.component.html',
})
export class ProjectBulkUploadComponent implements OnDestroy {
  @ViewChild('validModal') validModal: any;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  excelTemplatePath: string = PROJECT_EXCEL_TEMPLATE;
  projectData = ProjectDisplayColumns;
  projectMappingForm: FormGroup;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  isValidModal: boolean = false;
  selectedFile: File;
  formKeys: Array<string> = [];
  s3BucketKey: string;
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  selectedTitle: string;
  userData: any;
  getAppName = getAppName;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private router: Router,
    private fb: FormBuilder,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title
  ) {
    this.metaTitle.setTitle('CRM | Import Projects');
    this.headerTitle.setLangTitle('Import Projects');
    this.projectMappingForm = this.fb.group({
      Name: [null, Validators.required],
      ProjectType: [null],
      ProjectSubType: [null],
      ProjectStatus: [null],
      LandArea: [null],
      AreaUnit: [null],
      Certificate: [null],
      Facing: [null],
      TotalBlocks: [null],
      TotalUnits: [null],
      TotalFloors: [null],
      Discription: [null],
      BuilderName: [null],
      CountryCode: [null],
      BuilderContactNumber: [null],
      pointofContactcountrycode: [null],
      PointofContact: [null],
      RERANumber: [null],
      Currency: [null],
      MinPrice: [null],
      MaxPrice: [null],
      BrokerageAmount: [null],
      BrokerageCurrency: [null],
      PossesionType: [null],
      PossessionDate: [null],
      AssociatedBanks: [null],
      Location: [null],
      City: [null],
      State: [null],
      Amenities: [null],
      Notes: [null],
      ImageUrls: [null],
    });
  }

  ngOnInit() {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.projectData = ProjectDisplayColumns;
  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep =
      this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  uploadFile() {
    this._store.dispatch(new ProjectExcelUpload(this.selectedFile));
    this._store
      .select(getProjectColumnHeadings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.excelSheets = data?.multiSheetColumnNames;
        if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
        this.selectedSheet = this.sheetNames[0];
        this.formKeys = data?.columnNames;
        this.selectedTitle = this.formKeys?.find(
          (key) => key === 'Project Name'
        );
        this.s3BucketKey = data?.s3BucketKey;
        if (this.formKeys?.length) {
          this.currentStep = 3;
        }
        this.resetForms();
        this.onAutoMapChange();
      });
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  isValidForm() {
    if (!this.selectedSheet || !this.projectMappingForm.valid) {
      validateAllFormFields(this.projectMappingForm);
      return;
    } else {
      this.isValidModal = true;
      this.modalRef = this.modalService.show(this.validModal, {
        class: 'modal-350 modal-dialog-centered ip-modal-unset',
        keyboard: false,
      });
    }
  }

  confirmSheet(trackerInfoModal: TemplateRef<any>) {
    this.modalRef.hide();
    this.sendMappingDetails(trackerInfoModal);
  }

  sendMappingDetails(trackerInfoModal: TemplateRef<any>) {
    if (!this.selectedSheet || !this.projectMappingForm.controls['Name'].value) {
      return;
    }
    const mappedColumnsData: { [key: string]: string } = {};
    ProjectDisplayColumns.forEach((item: any) => {
      if (this.projectMappingForm.value[item.value]) {
        let keyWithoutSpaces = item.value.replace(/\s+/g, '');
        mappedColumnsData[keyWithoutSpaces] = this.projectMappingForm.value[item.value];
      }
    });

    const payload = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnsData: mappedColumnsData,
      timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      sheetName: this.selectedSheet || this.sheetNames[0],
    };

    this._store.dispatch(new UploadProjectMappedColumns(payload));
    this._store.dispatch(new FetchProjectExcelUploadedList(1, 10));

    this.modalRef = this.modalService.show(
      trackerInfoModal,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  onAutoMapChange() {
    const mappingColumns = ProjectDisplayColumns;

    const form = this.projectMappingForm;
    const nameControl = 'Name';
    const nameKey = 'Project Name';
    const mappingNameForTitle = this.formKeys?.find((key) => key === nameKey);

    mappingColumns.forEach((column) => {
      if (
        column?.displayName &&
        form?.controls[column?.value] &&
        this.formKeys?.includes(column?.displayName)
      ) {
        form.patchValue({
          [column?.value]: column?.displayName,
        });
      }
    });

    if (mappingNameForTitle) {
      form.patchValue({
        [nameControl]: mappingNameForTitle,
      });
    }
  }

  openBulkUploadedStatusModal() {
    this.navigateToHome();
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      fieldType: 'project',
    };
    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState,
    });
  }

  onSheetSelection() {
    this.formKeys = this.excelSheets?.[this.selectedSheet];
  }

  navigateToHome() {
    this.router.navigate(['projects']);
  }

  resetForms() {
    if (this.projectMappingForm) this.projectMappingForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}