<div *ngIf="canUserView" class="py-12 px-20">
    <!-- responsiveness -->
    <div class="position-absolute top-13 z-index-1021 tb-left-175"
        [ngClass]="showLeftNav ? 'left-300' : 'left-200'">
        <ul class="align-center top-nav-bar text-nowrap">
            <ng-container *ngFor="let visibilityImage of visibilityList; let i = index">
                <div [ngClass]="visibility" (click)="currentVisibility(visibilityImage.name)" class="cursor-pointer">
                    <div class="align-center ph-mb-4">
                        <a [class.active]="LeadVisibility[filtersPayload.Visibility] == visibilityImage.name"><img
                                [type]="'leadrat'" [appImage]="s3BucketUrl + visibilityImage.image" alt=""></a>
                        <span [class.active]="LeadVisibility[filtersPayload.Visibility] == visibilityImage.name"
                            class="text-large fw-semi-bold mx-8 d-flex">{{ visibilityImage.name }}</span>
                    </div>
                </div>
            </ng-container>
        </ul>
    </div>
    <div class="bg-white">
        <div class="align-center w-100 border-gray ph-flex-col ph-flex-between-unset"
            [ngClass]="{'mb-30' : !showFilters}">
            <div class="align-center border-end flex-grow-1 no-validation ph-border-bottom">
                <div class="align-center w-100 px-10 py-12">
                    <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ip-mr-4"> </span>
                    <input (keydown)="onSearch($event)" (input)="isEmptyInput($event)" [(ngModel)]="searchTerm"
                        autocomplete="off" placeholder="type to search" name="search" class="border-0 outline-0 w-100">
                </div>
                <small class="text-muted text-nowrap ip-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate
                    }})</small>
            </div>
            <div class="align-center">
                <div class="filters-grid clear-padding border-end h-100">
                    <div class="align-center h-100 ml-16">
                        <div class="manage-select datefilter-scroll">
                            <ng-select [virtualScroll]="true" placeholder="All" [(ngModel)]="dateType"
                                [searchable]="false" class="lead-date ip-max-w-80px min-w-60" ResizableDropdown
                                (change)="dateChange()">
                                <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                                    [value]="dType">{{dType}}</ng-option>
                            </ng-select>
                        </div>
                        <div class="date-picker border-start-0 align-center">
                            <span class="ic-appointment icon ic-xxs ic-black"
                                [owlDateTimeTrigger]="dt1"></span>
                            <input type="text" readonly placeholder="ex. 19-06-2025 - 29-06-2025"
                                class="pl-20 text-large" [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                [selectMode]="'range'" (ngModelChange)="filterDate = $event; dateChange()"
                                [ngModel]="filterDate" />
                            <owl-date-time [pickerType]="'calendar'" #dt1
                                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </div>
                    </div>
                </div>
                <div *ngIf="filtersPayload?.FromDate" (click)="onResetDateFilter()"
                    class="bg-coal text-white px-10 py-12 w-50px ip-w-30px h-100 align-center cursor-pointer">
                    <span class="ip-d-none">{{ 'GLOBAL.reset' | translate }}</span> <span
                        class="ic-refresh d-none ip-d-block"></span>
                </div>
                <div class="px-10 align-center cursor-pointer" (click)="openAdvFiltersModal()">
                    <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
                    <span class="fw-600 ip-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
                </div>
            </div>
        </div>
        <div class="p-4 tb-w-100-40 w-100-180 ph-w-100-50" *ngIf="showFilters">
            <div class="bg-secondary flex-between">
                <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                    <div class="d-flex" *ngFor="let filter of filtersPayload | keyvalue">
                        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                            *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                            {{filtersKeyLabel[filter.key] || filtersKeyLabel[(filter.key)?.toLowerCase()] ||
                            filter.key}}: {{
                            filter.key === 'UserIds' ? getUserName(value) :
                            filter.key === 'TextByIds' ? getUserName(value) :
                            filter.key === 'StatusesIds' ? getStatusName(value) :
                            filter.key === 'SubStatusesIds' ? getSubStatusName(value) :
                            filter.key === 'SourceIds' ? sourceIdMap?.[value] :
                            value }}
                            <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                (click)="onRemoveFilter(filter.key, value)"></span>
                        </div>
                    </div>
                </drag-scroll>
                <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                    (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
            </div>
        </div>
        <ng-container *ngIf="!whatsAppDataItems?.length && !whatsAppListIsLoading; else whatsAppScreen">
            <div class="flex-center h-100-160">
                <div class="flex-column">
                    <ng-lottie [options]='noDataFound'></ng-lottie>
                    <h3 class="header-5 fw-600 text-center">No Conversation Found</h3>
                </div>
            </div>
        </ng-container>
        <ng-template #whatsAppScreen>
            <div *ngIf="whatsAppListIsLoading && !whatsAppDataItems?.length" class="pt-40">
                <ng-container *ngTemplateOutlet="gridLoader"></ng-container>
            </div>
            <div class="d-flex h-100-155 w-100 ph-h-100-220"
                *ngIf="!whatsAppListIsLoading || whatsAppDataItems?.length">
                <div class="w-25 border scrollbar min-w-300 tb-w-50 ip-w-100">
                    <div *ngFor="let data of whatsAppDataItems">
                        <div class="h-70px border-bottom justify-between px-10 cursor-pointer position-relative"
                            (click)="data?.childLeadsCount ? toggleChildData(data) : fetchConversation(data)">
                            <div class="d-flex w-100">
                                <div class="flex-center-col">
                                    <h4
                                        class="dot dot-xxl bg-green-50 text-accent-green fw-600 text-uppercase position-relative border-2">
                                        {{data?.name[0]}}
                                        <img *ngIf="canViewLeadSource" [type]="'leadrat'" [appImage]="getImageUrl(data.leadSource)"
                                            [title]="LeadSource[data.leadSource]" alt=""
                                            class="position-absolute nleft-3 nbottom-4">
                                    </h4>
                                </div>
                                <div class="justify-center-col ml-10 w-100">
                                    <div class="justify-end">
                                        <div class="text-light-gray text-xs text-truncate-1 break-all"
                                            *ngIf="data.assignTo">{{getAssignedToDetails(data.assignTo, allUserList,
                                            true) || 'Unassigned lead'}}</div>
                                    </div>
                                    <h4 class="fw-600 text-dark text-truncate-1 break-all">{{data.name}}</h4>
                                    <div class="text-sm text-dark text-truncate-1 break-all mt-2">{{data.contactNo}}
                                    </div>
                                    <div class="flex-between text-xs mt-2">
                                        <div class="text-dark-gray text-truncate-1 break-all mr-2">{{data.waMessage}}
                                        </div>
                                        <div class="text-nowrap fw-semi-bold">{{data.waLastModifiedOn | relativeTime }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ng-container *ngIf="expandedItemId === data.id">
                            <div *ngFor="let childData of whatsAppChildData; let first = first, let last = last"
                                class="align-center" [ngClass]="{'border-bottom' : last}">
                                <div class="w-24 text-dark-gray">
                                    <div class="border-right position-relative"
                                        [ngClass]="last?'h-20px ntop-15':'h-50px'">
                                        <div
                                            [ngClass]="first? 'icon ic-xxs ic-dark-400 ic-circle position-absolute nright-6':''">
                                        </div>
                                        <div class="icon ic-xxs ic-dark-400 position-absolute nright-12 top-18"
                                            [ngClass]="last?'ic-turn-right':'ic-arrow-right'">
                                        </div>
                                    </div>
                                </div>
                                <div class="h-50px justify-between px-12 cursor-pointer position-relative w-100"
                                    [ngClass]="{'border-bottom' : !last}">
                                    <div class="d-flex w-100">
                                        <div class="flex-center-col">
                                            <h4
                                                class="dot dot-xxl bg-green-50 text-accent-green fw-600 text-uppercase position-relative border-2">
                                                {{childData?.name[0]}}
                                                <img *ngIf="canViewLeadSource" [type]="'leadrat'" [appImage]="getImageUrl(childData.leadSource)"
                                                    [title]="LeadSource[childData.leadSource]" alt=""
                                                    class="position-absolute nleft-3 nbottom-4">
                                            </h4>
                                        </div>
                                        <div class="justify-center-col ml-10 w-100">
                                            <h4 class="fw-600 text-dark text-truncate-1 break-all mt-10">
                                                {{childData.name}}
                                            </h4>
                                            <div class="justify-end">
                                                <div class="text-light-gray text-xs text-truncate-1 break-all"
                                                    *ngIf="childData.assignTo">
                                                    {{childData.assignTo != EMPTY_GUID
                                                    ? (getAssignedToDetails(childData.assignTo, allUserList)?.firstName
                                                    ||
                                                    '') +
                                                    '
                                                    ' +
                                                    (getAssignedToDetails(childData.assignTo, allUserList)?.lastName[0]
                                                    ||
                                                    '') :
                                                    'Unassigned lead'}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                    <div *ngIf="!whatsAppListIsLoading else ratLoader" inView (inView)="onInView($event)"></div>
                </div>
                <ng-container *ngIf="(canShowChatScreen && clickedData) else selectItem">
                    <div class="w-35 border position-relative tb-w-50 ip-d-none">
                        <whatsapp-chat *ngIf="(clickedData?.id === currentId) else gridLoader"
                            [clickedData]="clickedData" [whatsAppComp]="true"></whatsapp-chat>
                    </div>
                    <div class="w-40pr border tb-d-none">
                        <lead-preview *ngIf="(clickedData?.id === currentId) else gridLoader"
                            [clickedData]="clickedData" [whatsAppComp]="true"></lead-preview>
                    </div>
                </ng-container>
                <ng-template #selectItem>
                    <h3 class="w-75 border flex-center tb-w-50 ip-d-none">Select an item to read and preview</h3>
                </ng-template>
            </div>
        </ng-template>
    </div>
</div>
<ng-template #gridLoader>
    <div class="flex-center h-100-182">
        <application-loader></application-loader>
    </div>
</ng-template>
<ng-template #ratLoader>
    <div class="flex-center">
        <img src="assets/images/loader-rat.svg" class="rat-loader h-40px w-30px" alt="loader">
    </div>
</ng-template>