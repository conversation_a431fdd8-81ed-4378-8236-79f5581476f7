<div class="flex-row d-block">
    <label class="w-100">
        <div class="btn br-50px btn-accent-green btn-text-white flex-center w-100 ip-px-40">
            <file-upload [requiredFileType]="allowedFileFormat" [allowMultipleFiles]=false [isExcelUpload]="isExcelFile"
                [isImageGallery]=false [labelText]="'GLOBAL.browse' | translate"
                (uploadedFileSize)="sendFileSize($event)" (uploadedFile)="onFileSelection($event)"
                (uploadedFileName)="sendFileName($event)">
            </file-upload>
        </div>
    </label>
    <p class="m-10 text-center ip-d-none">{{'GLOBAL.or' | translate}}</p>
    <ngx-file-drop dropZoneClassName="btn w-100 br-50px border-green-dashed flex-center text-nowrap ip-d-none"
        dropZoneLabel="Drag and Drop file here" (onFileDrop)="onFileDrop($event)">
    </ngx-file-drop>
</div>
<div class="bg-white py-40 px-20 fw-semi-bold header-3 text-black-200 d-none w-100">
    <div class="border-dashed-2">
        <label class="w-100 cursor-pointer">
            <ngx-file-drop dropZoneClassName="" dropZoneLabel="Drag and Drop or" (onFileDrop)="onFileDrop($event)"
                (uploadedFileSize)="sendFileSize($event)">
                <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
                    <div class="flex-col py-30">
                        <div class="flex-wrap flex-center mt-6 mb-10">
                            <span class="flex-nowrap" *ngIf="!fileMessage">Drag and drop or</span>
                            <span class="flex-nowrap text-center" *ngIf="fileMessage">Drop your file(s) here or <span
                                    class="text-accent-green">browse</span></span>
                            <div class="fw-700 text-accent-green mx-4 flex-nowrap"> <file-upload
                                    [requiredFileType]="allowedFileFormat" [allowMultipleFiles]=false
                                    [isExcelUpload]="isExcelFile" [isImageGallery]=false
                                    [labelText]="fileMessage ? '' : 'Choose a file'"
                                    (uploadedFile)="onFileSelection($event)" (uploadedFileSize)="sendFileSize($event)"
                                    (uploadedFileName)="sendFileName($event)" (click)="trackingService.trackFeature('Web.Leads.Button.FileuploadCard.Click')"></file-upload></div>
                            <span class="flex-nowrap" *ngIf="!fileMessage">{{fileMessage ? fileMessage :
                                'BULK_LEAD.upload-lead-file' | translate }}.</span>
                        </div>
                        <div class="flex-center ph-ml-10 text-center">{{allowedFileFormat ? allowedFileFormat :
                            'BULK_LEAD.file-type-acceptable' | translate }}.</div>
                    </div>
                </ng-template>
            </ngx-file-drop>
        </label>
    </div>
</div>