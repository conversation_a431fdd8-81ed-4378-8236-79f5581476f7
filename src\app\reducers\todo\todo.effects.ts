import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { NotificationsService } from 'angular2-notifications';

import {
  FetchTodoList,
  FetchTodoListSuccess,
  AddTodo,
  DeleteTodo,
  UpdateTodo,
  TaskActionTypes,
} from 'src/app/reducers/todo/todo.actions';
import { OnError } from 'src/app/app.actions';
import { FetchTasksResponse } from 'src/app/core/interfaces/todo.interface';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { getTodoFilters } from './todo.reducer';
import { TodoService } from 'src/app/services/controllers/todo.service';
import { CommonService } from 'src/app/services/shared/common.service';

@Injectable()
export class TodoEffects {
  getTodoList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TaskActionTypes.FETCH_TODO_LIST),
      map((action: FetchTodoList) => action.payload),
      switchMap((data: any) => {
        let filterPayload;
        this._store.select(getTodoFilters).subscribe((filterData: any) => {
          filterPayload = filterData;
        });
        return this.commonService.getModuleList(filterPayload).pipe(
          map((resp: FetchTasksResponse) => {
            if (resp.succeeded) {
              return new FetchTodoListSuccess(resp);
            }
            return new FetchTodoListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      }
      )
    ));

  addTodo$ = createEffect(() =>
  this.actions$.pipe(
    ofType(TaskActionTypes.ADD_TODO),
    switchMap((action: AddTodo) => {
      return this.api.add(action.payload).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this._notificationService.success(`Task created successfully.`);
            return new FetchTodoList();
          }
          return new FetchTodoListSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ));

  deleteTodo$ = createEffect(() =>
  this.actions$.pipe(
    ofType(TaskActionTypes.DELETE_TODO),
    switchMap((action: DeleteTodo) => {
      return this.api.delete(action.id).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this._notificationService.success(`Task deleted successfully.`);
            return new FetchTodoList();
          }
          return new FetchTodoListSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ));

  updateTodo$ = createEffect(() =>
  this.actions$.pipe(
    ofType(TaskActionTypes.UPDATE_TODO),
    switchMap((action: UpdateTodo) => {
      return this.api.update(action.payload, action.id).pipe(
        map((resp: any) => {
          if (resp.succeeded) {
            this._notificationService.success(`Task updated successfully.`);
            return new FetchTodoList();
          }
          return new FetchTodoListSuccess();
        }),
        catchError((err) => of(new OnError(err)))
      );
    })
  ));

  constructor(
    private actions$: Actions,
    private api: TodoService,
    private _notificationService: NotificationsService,
    private commonService: CommonService,
    private _store: Store<AppState>,
  ) {}
}
